/**
 * Tax calculation types
 * @readonly
 * @enum {string}
 */
const TAX_CALCULATION_TYPES = {
  /** Calculate taxes in sequence, where each tax is applied on top of the previous tax */
  SEQUENTIAL: "sequential",
  /** Calculate taxes in parallel, where each tax is applied to the base amount */
  PARALLEL: "parallel",
};

/**
 * Tax types
 * @readonly
 * @enum {string}
 */
const TAX_TYPES = {
  PERCENTAGE: "percentage",
  FLAT: "flat",
};

/**
 * Tax scopes - defines where the tax can be applied
 * @readonly
 * @enum {string}
 */
const TAX_SCOPES = {
  INVOICE: "invoice",
  BILL: "bill",
  ALL: "all",
};

/**
 * Tax applicable types - defines what entity type the tax is being applied to
 * @readonly
 * @enum {string}
 */
const TAX_APPLICABLE_TYPES = {
  INVOICE: "invoice",
  BILL: "bill",
  PAYMENT: "payment",
};

module.exports = {
  TAX_CALCULATION_TYPES,
  TAX_TYPES,
  TAX_SCOPES,
  TAX_APPLICABLE_TYPES,
};
