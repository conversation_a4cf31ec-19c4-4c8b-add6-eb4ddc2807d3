const ResponseService = require("../services/response");
const { ValidationError } = require("../utils/error.utils");
const AccountValidator = require("../validators/account.validator");
const BankAccountService = require("../services/bank");
const SettingsService = require("../services/settings");
const Sanitizer = require("../utils/sanitizer");
const AccountService = require("../services/account.service");
const Providers = require("../services/providers");

module.exports = {
  async create(req, res) {
    // Validate payload
    const { error } = AccountValidator.create.validate(req.body);
    if (error) throw new ValidationError(error.message);
    const { id: company, code: companyCode } = req.company;
    // create
    const { payment } = SettingsService.get("providers");
    const provider = payment[req.company.code] || payment.defaultProvider;
    const createdBankAccount = await BankAccountService.generateVirtualAccount({ ...req.body, user: req.user.id, company, companyCode, provider });

    return ResponseService.success(res, "Bank Account created successfully", Sanitizer.sanitizeBankAccount(createdBankAccount));
  },

  async migrateAccount(req, res) {
    const { error } = AccountValidator.migrate.validate(req.body);
    if (error) throw new ValidationError(error.message);
    const { message } = await AccountService.migrateAccount({ ...req.body });
    return ResponseService.success(res, message);
  },

  async processAccountMigration(req, res) {
    const { error } = AccountValidator.processMigration.validate(req.body);
    if (error) throw new ValidationError(error.message);
    ResponseService.success(res, "OK");
    await AccountService.createProviderAccount({ ...req.body, accountMigration: req.params.code });
  },

  async initiateTranfer(req, res) {
    const { error } = AccountValidator.initiateTransfer.validate(req.body);
    if (error) throw new ValidationError(error.message);
    const { message } = await AccountService.initiateAccountFunding({ ...req.body, accountMigration: req.params.code });
    return ResponseService.success(res, message);
  },

  async finalizeMigration(req, res) {
    const { error } = AccountValidator.finalizeMigration.validate({ accountMigration: req.params.code });
    if (error) throw new ValidationError(error.message);
    const { message } = await AccountService.finalizeMigration({ accountMigration: req.params.code });
    return ResponseService.success(res, message);
  },

  async createProviderVirtualAccount(req, res) {
    const { externalBankAccountId, trial, provider, company } = req.body;
    await Providers[provider].virtualAccount.createVirtualAccount({ company, externalIdentifier: externalBankAccountId, trial });
    return ResponseService.success(res, "Ok");
  },
};
