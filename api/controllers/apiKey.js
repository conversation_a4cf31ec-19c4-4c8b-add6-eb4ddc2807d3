const { <PERSON><PERSON><PERSON><PERSON> } = require("../models");
const { STATUSES } = require("../models/status");
const ResponseService = require("../services/response");
const { parseJSON } = require("../utils");
const { getStatusById } = require("../utils/sanitizer");

module.exports = {
  async getKey(req, res) {
    const { scope } = req.body;
    const [apiKey] = await ApiKey.findOrCreate({
      where: {
        company: req.company.id,
        ...(scope && { scope }),
      },
    });
    return ResponseService.success(res, "<PERSON><PERSON><PERSON><PERSON> successfully retrived", {
      key: apiKey.key,
      scope: parseJSON(apiKey.scope),
      expiresOn: apiKey.expiresOn,
      status: getStatusById(STATUSES, apiKey.status),
      code: apiKey.code,
    });
  },
  async disableKey(req, res) {
    await ApiKey.update(
      { status: STATUSES.INACTIVE },
      {
        where: {
          code: req.params.code,
        },
      }
    );
    return ResponseService.success(res, "Key disabled");
  },
};
