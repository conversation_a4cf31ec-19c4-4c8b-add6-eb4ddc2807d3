const ResponseService = require("../services/response");
const ApprovalService = require("../services/approval.service");
const HelperService = require("../services/helper.service");
const Sanitizer = require("../utils/sanitizer");
const { ApprovalValidator } = require("../validators");
const { ValidationError, NotFoundError } = require("../utils/error.utils");
const { ApprovalRuleRepo, ApprovalRequestRepo } = require("../repositories/index.repo");
const { STATUSES } = require("../models/status");
const FundRequestService = require("../services/fundRequest.service");
const ReimbursementService = require("../services/reimbursementService");

module.exports = {
  /**
   * To list all transactions types
   * @method GET /approvals/types
   * @param { object } req
   */
  async getTransactionTypes(req, res) {
    const { message, data } = await ApprovalService.getTransactionTypes();
    return ResponseService.success(res, message, Sanitizer.sanitizeModelInstances(data, "TransactionType"));
  },

  /**
   * To list all users according to role
   * @method GET /approvers
   * @param { object } req
   */
  async getApprovers(req, res) {
    const query = { company: req.company.id };
    const { message, data } = await ApprovalService.getApprovers(query);
    return ResponseService.success(res, message, Sanitizer.sanitizeModelInstances(data, "Approvers"));
  },

  /**
   * To Create a new approval rule
   * @method POST /approvals
   * @param { object } req
   * @param { number } req.company.id company id
   */
  async createApprovalRule(req, res) {
    const { conditions, reviews, name } = req.body;

    const { error } = ApprovalValidator.create.validate(req.body);
    if (error) throw new ValidationError(error.message);

    // checks rank order
    ApprovalService.rankCheck(reviews);
    // Check if all conditions meet the expected requirements
    await ApprovalService.checkApprovalConditions({ conditions });

    const conflicts = await ApprovalService.conflictDetector({ conditions }, req.company.id);

    if (conflicts.length) {
      return ResponseService.json(res, {
        statusCode: 400,
        error: true,
        status: true,
        message: "Conflicts with other rules detected. Please create more conditions for this rule",
        data: Sanitizer.sanitizeModelInstances(conflicts, "ApprovalRule"),
      });
    }

    const payload = { company: req.company.id, user: req.user.id, name };

    const { message: ruleMessage, data: approvalRule } = await ApprovalService.createRule(payload);

    const data = { ...payload, rule: approvalRule.id };

    await ApprovalService.callService("createApprovalCondition", conditions, {
      ...data,
    });
    await ApprovalService.callService("createRuleApprovers", reviews, {
      ...data,
    });

    await ApprovalRuleRepo.updateRule({
      queryParams: { id: approvalRule.id },
      updateFields: { status: STATUSES.ACTIVE },
    });

    return ResponseService.success(res, ruleMessage, Sanitizer.sanitizeApprovalRule(approvalRule));
  },

  /**
   * To delete an approval rule
   * @method DELETE /approvals/:code
   * @param { object } req
   */
  async deleteRule(req, res) {
    const approvalRule = await ApprovalService.findApprovalRule(req.params.code);

    // Check if an Approval Request is currently acting on this Approval Rule
    await ApprovalService.pendingApprovalRequestChecker(approvalRule.id);

    const { message } = await ApprovalService.deleteRule(approvalRule);

    return ResponseService.success(res, message, Sanitizer.sanitizeApprovalRule(approvalRule));
  },

  /**
   * To add an approval condition to a rule
   * @method POST /approvals/:code/condition
   * @param { object } req
   */
  async addCondition(req, res) {
    const approvalRule = await ApprovalService.findApprovalRule(req.params.code);

    const payload = { rule: approvalRule.id, ...req.body };

    const { message, data } = await ApprovalService.createApprovalCondition(payload);

    return ResponseService.success(res, message, Sanitizer.sanitizeApprovalCondition(data));
  },

  /**
   * To remove an approval condition or approver from a rule
   * @method DELETE /approvals/condition/:code
   * @param { object } req
   */
  async removeCondition(req, res) {
    const { message, data } = await ApprovalService.dropApprovalCondition(req.params.code, req.query.type);
    return ResponseService.success(
      res,
      message,
      Sanitizer[req.query.type === "condition" ? "sanitizeApprovalCondition" : "sanitizeApproverCondition"](data)
    );
  },

  /**
   * To remove different approvers from a condition
   * @method DELETE /approvals/:code/approver
   * @param { object } req
   */
  async removeApprover(req, res) {
    const { message, data } = await ApprovalService.dropApprover({
      item: req.params.code,
    });

    return ResponseService.success(res, message, Sanitizer.sanitizeApprover(data));
  },

  /**
   * To add multiple approvers to a condition
   * @method POST /approvals/:code/approver
   * @param { object } req
   */
  async addApprover(req, res) {
    const payload = {
      code: req.params.code,
      company: req.company.id,
      approvers: req.body.approvers,
    };

    const { error } = ApprovalValidator.uniqueApprover.validate(req.body.approvers);
    if (error) throw new ValidationError(error.message);

    const { message } = await ApprovalService.addApprovers(payload);
    return ResponseService.success(res, message);
  },

  /**
   * Get approvers for a vendor
   * @method GET /approvals/vendor-approvers
   * @param { object } req
   */
  async vendorApprovers(req, res) {
    const payload = {
      ...req.query,
      company: req.company.id,
    };

    const { error } = ApprovalValidator.vendorApprovers.validate(req.query);
    if (error) throw new ValidationError(error.message);

    const { message, data } = await ApprovalService.vendorApprovers(payload);
    return ResponseService.success(res, message, data);
  },

  /**
   * To list all approval requests
   * @method GET /approvals/pending
   * @param { object } req
   */
  async listApprovalRequests(req, res) {
    const payload = {
      company: req.company.id,
      status: req.query.status || "pending",
      user: req.user.id,
    };
    const { message, data } = await ApprovalService.getApprovalRequests(payload);
    return ResponseService.success(res, message, Sanitizer.sanitizeModelInstances(data, "ApprovalRequest"));
  },

  /**
   * To list all approval requests as a group
   * @method GET /group-requests
   * @param { object } req
   */
  async groupApprovalRequests(req, res) {
    const payload = {
      ...req.query,
      company: req.company.id,
      status: req.query.status,
      user: req.query.user || req.user.id,
      loggedInUser: req.user.id,
    };

    if (req.isEmployeeRole) payload.user = req.user.id;
    const {
      message,
      data: { meta, approvalRequests },
    } = await ApprovalService.groupApprovalRequests(payload);
    const summary = await ApprovalRequestRepo.approvalRequestSummary({ filter: payload });
    return ResponseService.success(res, message, {
      approvalRequests: Sanitizer.sanitizeModelInstances(approvalRequests, "ApprovalRequest"),
      meta,
      summary: HelperService.groupSummaryByCurrency(summary),
    });
  },

  /**
   * To get one approval requests
   * @method GET /approvals/pending
   * @param { object } req
   */
  async getApprovalRequest(req, res) {
    const payload = {
      company: req.company.id,
      code: req.params.code,
      user: req.user.id,
    };
    const { message, data } = await ApprovalService.getApprovalRequest(payload);
    return ResponseService.success(res, message, Sanitizer.sanitizeApprovalRequest(data));
  },
  /**
   * To get one grouped approval request
   * @method GET /group-requests/:code
   * @param { object } req
   */
  async groupApprovalRequest(req, res) {
    const payload = {
      company: req.company.id,
      code: req.params.code,
      user: req.user.id,
    };
    const {
      message,
      data: { relatedApprovalRequests, approvalRequest },
    } = await ApprovalService.groupApprovalRequest(payload);
    return ResponseService.success(res, message, {
      approvalRequest: Sanitizer.sanitizeApprovalRequest(approvalRequest),
      relatedApprovalRequests: Sanitizer.sanitizeModelInstances(relatedApprovalRequests, "ApprovalRequest"),
    });
  },

  /**
   * To list all the approval history belonging to one request
   * @method GET /approvals/:request/history
   * @param { object } req
   */
  async listApprovalHistory(req, res) {
    const payload = { company: req.company.id, code: req.params.code };
    const { message, data } = await ApprovalService.getApprovalHistory(payload);
    return ResponseService.success(res, message, Sanitizer.sanitizeModelInstances(data, "Approval"));
  },

  /**
   * To view one approval
   * @method GET /approvals/history/:code
   * @param { object } req
   */
  async getApproval(req, res) {
    const payload = { company: req.company.id, code: req.params.code };
    const { message, data } = await ApprovalService.getApproval(payload);
    return ResponseService.success(res, message, Sanitizer.sanitizeApproval(data));
  },

  /**
   * To review a pending approval request
   * @method POST /approvals/requests/:code/review
   * @param { object } req
   */
  async reviewApprovalRequest(req, res) {
    const payload = {
      company: req.company.id,
      code: req.params.code,
      user: req.user.id,
      ...req.body,
    };
    const { message, data } = await ApprovalService.reviewApprovalRequest(payload);
    return ResponseService.success(res, message, Sanitizer.sanitizeApprovalRequest(data));
  },

  /**
   * To review a pending group approval request
   * @method POST /approvals/group-requests/:code/review
   * @param { object } req
   */
  async reviewGroupedApprovalRequest(req, res) {
    const { pin, ...rest } = req.body;
    const payload = {
      company: req.company.id,
      code: req.params.code,
      user: req.user.id,
      ...rest,
      actionLater: req.body.actionLater,
    };
    const { error } = ApprovalValidator.reviewApprovalRequest.validate(rest);
    if (error) throw new ValidationError(error.message);
    const { message, data } = await ApprovalService.reviewGroupedApprovalRequest(payload);
    return ResponseService.success(res, message, Sanitizer.sanitizeApprovalRequest(data));
  },

  /**
   * To replay a pending approval request
   * @method POST /approvals/requests/:code/replay
   * @param { object } req
   */
  async replayApprovalRequest(req, res) {
    const payload = {
      companyCode: req.body.companyCode,
      code: req.params.code,
    };
    const { message, data } = await ApprovalService.replayApprovalRequest(payload);
    return ResponseService.success(res, message, Sanitizer.sanitizeApprovalRequest(data));
  },

  /**
   * To list all approval rules
   * @method GET /approvals
   * @param { object } req
   */
  async listApprovalRules(req, res) {
    const query = { company: req.company.id, ...req.query };

    const { message, data } = await ApprovalService.getApprovalRules(query);
    return ResponseService.success(res, message, {
      rules: Sanitizer.sanitizeModelInstances(data.rules, "ApprovalRule"),
      meta: data.meta,
    });
  },

  /**
   * To update an approval rule
   * @method PATCH /approvals/:code
   * @param { object } req
   */
  async editApprovalRule(req, res) {
    const { name, condition, approver } = req.body;

    const { error } = ApprovalValidator.update.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const approvalRule = await ApprovalService.findApprovalRule(req.params.code);

    if (!approvalRule) throw new NotFoundError("Rule");
    const payload = { name, rule: approvalRule, company: req.company.id };

    // Check if an Approval Request is currently acting on this Approval Rule
    await ApprovalService.pendingApprovalRequestChecker(approvalRule.id);

    // All Checks are handled here
    if (name) await ApprovalService.existingApprovalRuleChecker(name, req.company.id);
    if (condition) {
      const conflicts = await ApprovalService.conflictDetector({ conditions: condition.newConditions }, req.company.id, approvalRule.code);

      if (conflicts.length) {
        return ResponseService.json(res, {
          statusCode: 400,
          error: true,
          status: true,
          message: "This rule conflicts with other existing rules. Please add more conditions for this rule",
          data: Sanitizer.sanitizeModelInstances(conflicts, "ApprovalRule"),
        });
      }
      if (condition.oldConditions && condition.oldConditions.length)
        await ApprovalService.checkApprovalConditionsBeforeDropping(approvalRule, condition.oldConditions);
      if (condition.newConditions && condition.newConditions.length)
        await ApprovalService.checkApprovalConditionsBeforeAdding(approvalRule, condition, true);
    }
    if (approver) {
      if (approver.oldApprovers && approver.oldApprovers.length)
        await ApprovalService.callService("dropApproverChecker", approver.oldApprovers, {
          approvalRule,
          ...(approver.newApprovers &&
            approver.newApprovers.length && {
              incomingApprovers: approver.newApprovers,
            }),
          oldApprovers: approver.oldApprovers,
        });
      if (approver.newApprovers && approver.newApprovers.length)
        await ApprovalService.callService("addApproversChecker", approver.newApprovers, { approvalRule });
    }

    // Complete Editing
    if (condition) {
      const { oldConditions, newConditions } = condition;
      if (oldConditions && oldConditions.length) await ApprovalService.dropConditions(approvalRule, oldConditions, false);
      if (newConditions && newConditions.length) await ApprovalService.addApprovalConditions(approvalRule, newConditions, false);
    }
    if (approver) {
      const { oldApprovers, newApprovers } = approver;
      if (oldApprovers && oldApprovers.length) await ApprovalService.callService("dropApprover", oldApprovers);
      if (newApprovers && newApprovers.length)
        await ApprovalService.callService("addApprovers", newApprovers, {
          company: req.company.id,
          useChecker: false,
        });
    }
    if (name) await ApprovalService.editRule(payload, false);

    return ResponseService.success(res, "Approval Rule successfully updated");
  },

  /**
   * To remove different approval condition from a rule
   * @method DELETE /approvals/:code
   * @param { object } req
   */
  async removeConditions(req, res) {
    const approvalRule = await ApprovalService.findApprovalRule(req.params.code);

    const { message } = await ApprovalService.dropConditions(approvalRule, req.body.conditions);
    return ResponseService.success(res, message || `Approval condition successfully dropped`);
  },

  /**
   * To get one approval rule
   * @method GET /approvals/:code
   * @param { object } req
   */
  async getApprovalRule(req, res) {
    const query = { company: req.company.id, code: req.params.code };

    const { message, data } = await ApprovalService.getApprovalRule(query);
    return ResponseService.success(res, message, Sanitizer.sanitizeApprovalRule(data));
  },

  async addApproversToRule(req, res) {
    const { error } = ApprovalValidator.createApproverLevels.validate(req.body.approvers);
    if (error) throw new ValidationError(error.message);

    const approvalRule = await ApprovalService.findApprovalRule(req.params.code);
    const { message } = await ApprovalService.createApprovers({
      company: req.company.id,
      approvers: req.body.approvers,
      approvalRule,
    });
    return ResponseService.success(res, message);
  },

  async dropApproversFromRule(req, res) {
    const { error } = ApprovalValidator.approverLevel.validate(req.body.approver);
    if (error) throw new ValidationError(error.message);

    const approvalRule = await ApprovalService.findApprovalRule(req.params.code);
    const { id: rule } = approvalRule;

    // Check if an Approval Request is currently acting on this Approval Rule
    await ApprovalService.pendingApprovalRequestChecker(rule);

    const { data: approverLevel } = await ApprovalService.getApproverLevelForARule({
      approverLevelCode: req.body.approver,
      rule,
    });
    await ApprovalService.reAdjustApproverLevelForARule({
      rule,
      rank: approverLevel.rank,
    });
    await ApprovalService.deletApproversForADroppedApproverLevel({
      approverLevelId: approverLevel.id,
    });

    return ResponseService.success(res, "Approver Level Succefully dropped");
  },

  async createApproverLevelAcrossRelatedTables(req, res) {
    const { message } = await ApprovalService.createApproverLevelFromApproverConditions({
      companyCode: req.body.company,
    });
    return ResponseService.success(res, message);
  },

  async multipleRequestAction(req, res) {
    const { error } = ApprovalValidator.multipleAction.validate(req.body);
    if (error) throw new ValidationError(error.message);
    const { message } = await ApprovalService.multipleRequestAction({ ...req.body, company: req.company, user: req.user });
    return ResponseService.success(res, message);
  },

  async resendApprovalRequestNotificationReminder(req, res) {
    const { error } = ApprovalValidator.resendNotification.validate(req.params);
    if (error) throw new ValidationError(error.message);

    const { message } = await ApprovalService.resendApprovalRequestNotificationReminder({
      ...req.params,
      company: req.company.id,
      user: req.user.id,
      FundRequestService,
      ReimbursementService,
    });

    return ResponseService.success(res, message);
  },
};
