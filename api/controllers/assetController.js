const AssetService = require("../services/asset");
const { parseInvoice } = require("../services/openai.service");
const ResponseService = require("../services/response");
const NotFoundError = require("../utils/not-found-error");
const { BackgroundService } = require("../services");
const { BACKGROUND } = require("../mocks/constants.mock");

module.exports = {
  async create(req, res) {
    AssetService.validateAssetCreationPayload(req.body);
    const payload = {
      company: req.company.id,
      ...req.body,
    };
    const asset = await AssetService.createAsset(payload);

    // add to be garbage collected, if not linked to a resource.
    BackgroundService.addToQueue(
      BACKGROUND.QUEUES.ASSET_CLEANUP,
      BACKGROUND.JOBS.ASSET_CLEANUP,
      {
        code: asset.code,
      },
      {
        delay: 3600 * 1000 * 2, // 2 hours delay
      }
    );

    let scanned = {};
    if (req.query.scan) scanned = await parseInvoice(asset);
    return ResponseService.success(res, "Asset created successfully", {
      asset: {
        code: asset.code,
        name: asset.name,
        url: AssetService.getAssetDownloadURL({ key: asset.key }),
        scannedData: scanned,
      },
    });
  },
  async get(req, res) {
    const { code } = req.params;
    AssetService.validateAssetGetPayload({ code });
    const asset = await AssetService.getAsset(code);
    if (!asset) throw new NotFoundError("Asset");
    return ResponseService.success(res, "Asset created successfully", {
      asset: {
        code: asset.code,
        name: asset.name,
        url: asset.url,
      },
    });
  },
};
