const { Op } = require("sequelize");
const ValidationError = require("../utils/validation-error");
const { Response, Balance: BalanceService, Budget: BudgetService } = require("../services");
const BalanceValidator = require("../validators/balance.validator");
const Utils = require("../utils/utils");
const BalanceTenureService = require("../services/balanceTenureService");
const BalanceRepo = require("../repositories/balance.repo");
const { STATUSES } = require("../models/status");
const Sanitizer = require("../utils/sanitizer");
const { BalanceLedgerRepo, BankAccountRepo, TransactionRepo } = require("../repositories/index.repo");
const { NotFoundError } = require("../utils/error.utils");
const Providers = require("../services/providers");
const Settings = require("../services/settings");
const { ACCOUNT_TYPES } = require("../models/balance");
const { Mandate, DirectDebit, AccountLinking } = require("../models");
const { TRANSACTION_TYPES } = require("../models/transaction");
const BankService = require("../services/bank");
const SettingsService = require("../services/settings");
const QueueService = require("../services/queue.service");
const VirtualCardService = require("../services/virtualcards.service");
const HelperService = require("../services/helper.service");
const { addPaymentToQueue } = require("../services/approval.service");
const { CARD_ISSUER } = require("../models/cardissuer");
const BillingService = require("../services/billing");

module.exports = {
  /**
   * Fund your USD Balance from naira Balance
   * @param {Object} body
   * @param {Number} body.amount amount to fund
   * @param {Number} body.baseCurrency currency you're converting from {optional}
   * @param {*} res
   * @returns
   */
  async fundUSDBalanceFromNairaBalance(req, res) {
    const {
      company: { id: company },
    } = req;
    const { error } = BalanceValidator.fundUSDWithNaira.validate(req.body);
    if (error) throw new ValidationError(error.message);
    const { baseCurrency: currency = "NGN", amount, source } = req.body;
    // Get available balance for a currency
    const availableBalance = await BalanceRepo.getAvailableBalance({
      company,
      currency,
      ...(source && { code: source }),
    });

    // GET EXCHANGE RATE
    const { rate } = await BalanceService.getExchangeRate();
    const debitAmount = parseInt(amount, 10) * parseInt(rate, 10);
    if (availableBalance > 0 && availableBalance < debitAmount) throw new ValidationError(`Your wallet balance is not sufficient`);
    const payload = { amount, company, debitAmount };
    // DEBIT FROM YOUR NAIRA BALANCE AND CREATE/TOP YOUR USD BALANCE
    await BalanceService.fundUSDBalanceFromNairaBalance(payload);
    return Response.success(res, "Wallet funded successfully");
  },

  /**
   * Getting a dollar account to transfer money to
   * POST /balances/virtual-usd-account
   * @param {Object} body
   * @param {Number} body.amount amount to fund
   * @return {Object} response
   */
  async getUSDTransferAccountDetails(req, res) {
    const { error } = BalanceValidator.getUSDAccountDetails.validate(req.body);
    if (error) throw new ValidationError(error.message);
    let data;
    const { code: company } = req.company;
    const { amount } = req.body;
    const payload = { company, amount };
    if (!Utils.isProd()) {
      data = {
        amount,
        account_number: **********,
        reference: "UX1111LV",
        bank_name: "Evolve Bank & Trust",
        routing_number: "*********",
        account_name: "Relentless Labs Inc",
        address: "421 South Brookhurst Street, St Anaheim. CA 92804",
      };
    } else data = await BalanceService.getUSDTransferAccountDetails(payload);
    return Response.success(res, "Account number fetched", data);
  },

  async getAvailableBalances(req, res) {
    const {
      params: { code },
      query: { includeCardBalances },
      isMobile,
    } = req;

    const allBalances = {
      balances: [],
      cardBalances: [],
    };

    const extraFilters = { page: 1, ...req.query, ...(code && { parent: code, type: "subaccounts" }) };

    if (!req.isManager) {
      extraFilters.user = req.user.id;
    }
    const { balances } = await BalanceTenureService.getAvailableBalances(req.company.id, extraFilters, isMobile);
    allBalances.balances = balances.map(({ bankAccountId, members = [], ...balance }) => {
      const nipCode = balance.bankCode && Utils.getNIPCodeFromBankCodeNoError(balance.bankCode);
      const isLinked = !!balance.isLinked;
      return {
        ...balance,
        ...(members.length && { members: Sanitizer.sanitizeAccountMembers(members) }),
        balance: isLinked ? balance.linkedBalance : balance.balance,
        isLinked,
        bankCode: balance.bankCode,
        syncStatus: balance.syncStatus && Sanitizer.getStatusById(STATUSES, balance.syncStatus),
        status: balance.status && Sanitizer.getStatusById(STATUSES, balance.status),
        logo: nipCode ? `https://cdn.jsdelivr.net/gh/wovenfinance/cdn@main/logos/${nipCode}.png` : null,
        subaccount: !!balance.subaccount,
        // eslint-disable-next-line no-use-before-define
        accountType: getAccountType(balance),
      };
    });
    allBalances.summary = allBalances.balances.reduce((groupedBalances, balance) => {
      // eslint-disable-next-line no-param-reassign
      groupedBalances[balance.currency] = (groupedBalances[balance.currency] || 0) + balance.balance;
      return groupedBalances;
    }, {});

    const criteria = {
      company: req.company.id,
      status: "active",
      ...((req.isEmployeeRole || (req.query.isReimbursement && !req.isAdmin)) && { user: req.user.id }),
    };
    if (req.query.type === "subaccounts") {
      return Response.success(res, "Balances fetched successfully", {
        ...allBalances,
      });
    }
    const { budgets } = await BudgetService.listBudgets(criteria);
    const sanitizedBudgets = Sanitizer.sanitizeBudgets(budgets, {
      ...(req.isEmployeeRole && { currentBeneficiary: req.user }),
      userId: req.user.id,
    });
    const getSpendable = (isFunded, balance, amount, spent, buffer = 0) => {
      if (isFunded) return balance;
      const remaining = amount + buffer - spent;
      if (remaining > 0 && spent) return remaining;
      return 0;
    };
    const hasOverSpent = (spent, amount) => {
      return spent > amount;
    };
    allBalances.budgets = sanitizedBudgets.map(
      ({ amount, buffer, spent, isFunded, available: balance, code: budgetCode, currency, name, beneficiaries }) => {
        const spendable = getSpendable(isFunded, balance, amount, spent, buffer);
        return {
          code: budgetCode,
          balance: spendable,
          ...(isMobile && { amount, spent }),
          currency,
          name,
          type: "budget",
          ...(isMobile && { beneficiaries }),
          isFunded,
          overSpent: hasOverSpent(spent, amount),
        };
      }
    );

    allBalances.budgetSummary = allBalances.budgets.reduce((groupedBudgets, budget) => {
      // eslint-disable-next-line no-param-reassign
      groupedBudgets[budget.currency] = (groupedBudgets[budget.currency] || 0) + budget.balance;
      return groupedBudgets;
    }, {});

    if (includeCardBalances) {
      const cardBalances = await VirtualCardService.getCardBalances(req.company.id);
      allBalances.cardBalances = HelperService.groupAmountsByCurrency(cardBalances);
    }

    allBalances.totalBalances = HelperService.mergedTotalsByCurrency([
      allBalances.summary,
      allBalances.budgetSummary,
      allBalances.cardBalances || {},
    ]);
    return Response.success(res, "Balances fetched successfully", {
      ...allBalances,
    });
  },

  async getSubaccounts(req, res) {
    const { code } = req.params;
    const foundBalance = await BalanceRepo.getBalance({
      filter: {
        code,
      },
    });
    if (!foundBalance) {
      throw new NotFoundError("Balance");
    }
    const allBalances = {
      balances: [],
    };

    const extraFilters = { page: 1, ...req.query, ...(code && { parent: foundBalance.bankAccount, type: "subaccounts" }) };
    if (!req.isManager) {
      extraFilters.user = req.user.id;
    }
    const { balances } = await BalanceTenureService.getAvailableSubaccountsBalances(req.company.id, extraFilters);
    allBalances.balances = balances.map(({ bankAccountId, ...balance }) => {
      const nipCode = balance.bankCode && Utils.getNIPCodeFromBankCodeNoError(balance.bankCode);
      const isLinked = !!balance.isLinked;
      return {
        ...balance,
        isLinked,
        bankCode: balance.bankCode,
        syncStatus: balance.syncStatus && Sanitizer.getStatusById(STATUSES, balance.syncStatus),
        logo: nipCode ? `https://cdn.jsdelivr.net/gh/wovenfinance/cdn@main/logos/${nipCode}.png` : null,
        subaccount: !!balance.subaccount,
        status: balance.status && Sanitizer.getStatusById(STATUSES, balance.status),
        // eslint-disable-next-line no-use-before-define
        accountType: getAccountType(balance),
      };
    });
    allBalances.summary = allBalances.balances.reduce((groupedBalances, balance) => {
      // eslint-disable-next-line no-param-reassign
      groupedBalances[balance.currency] = (groupedBalances[balance.currency] || 0) + balance.balance;
      return groupedBalances;
    }, {});

    return Response.success(res, "Subaccounts fetched successfully", {
      ...allBalances,
    });
  },

  async getSingleBalance(req, res) {
    const { error } = BalanceValidator.viewBalance.validate(req.params);
    if (error) throw new ValidationError(error.message);

    const balance = await BalanceTenureService.getSingleBalance(req.company.id, req.params.code, { user: req.user, isManager: req.isManager });

    const nipCode = balance.bankCode && Utils.getNIPCodeFromBankCodeNoError(balance.bankCode);

    const sanitizedBalance = Sanitizer.sanitizeNewBalance({
      ...balance,
      logo: nipCode ? `https://cdn.jsdelivr.net/gh/wovenfinance/cdn@main/logos/${nipCode}.png` : null,
    });

    Response.success(res, "Balance fetched successfully", sanitizedBalance);

    if (sanitizedBalance.isLinked) {
      // eslint-disable-next-line no-use-before-define
      await refreshLinkedAccount(sanitizedBalance, req.company);
    }
  },

  async listPayouts(req, res) {
    const { code } = req.params;
    const { id: company } = req.company;
    const balance = await BalanceRepo.getBalance({ filter: { code, company } });
    if (!balance) throw new ValidationError("Sorry, we cannot retrieve your account at the moment.");

    const balanceLedgers = await BalanceLedgerRepo.findLedgersForPayouts(balance.id, req.query, { addRecipient: true });
    const { page, perPage, ...countFilters } = req.query;

    const count = await BalanceLedgerRepo.count({ balance: balance.id, amount: { [Op.lt]: 0 }, ...countFilters });

    const sanitizeLedgers = Sanitizer.sanitizeBalanceLedgers(balanceLedgers);
    return Response.success(res, "Your payouts", {
      data: sanitizeLedgers,
      meta: {
        total: count,
        page: req.query.page || 1,
        nextPage: Number(req.query.page) + 1,
        hasMore: count > Number(req.query.page) * Number(req.query.perPage || 50),
      },
    });
  },

  async listPayins(req, res) {
    const { code } = req.params;
    const { id: company } = req.company;

    const balance = await BalanceRepo.getBalance({ filter: { code, company } });
    if (!balance) throw new ValidationError("Sorry, we cannot retrieve your account at the moment.");

    const { page = 1, perPage } = req.query;

    const { count, balanceLedgers } = await BalanceLedgerRepo.findLedgersForPayIns(balance.id, req.query);
    const sanitizeLedgers = Sanitizer.sanitizeBalanceLedgers(balanceLedgers);
    return Response.success(res, `Your payins ${balanceLedgers.length}`, {
      data: sanitizeLedgers,
      meta: {
        total: count,
        page,
        nextPage: Number(page) + 1,
        hasMore: count > Number(page) * Number(perPage || 50),
      },
    });
  },

  async listLedgerEntries(req, res) {
    const { code } = req.params;
    const { id: company } = req.company;

    const balance = await BalanceRepo.getBalance({ filter: { code, company } });
    if (!balance) throw new ValidationError("Sorry, we cannot retrieve your account at the moment.");

    req.query.perPage = Math.min(req.query.perPage || 50, 100);

    const { page = 1, perPage } = req.query;

    const { count, balanceLedgers } = await BalanceLedgerRepo.findLedgers(balance.id, req.query, { addRecipient: true });
    const sanitizeLedgers = Sanitizer.sanitizeBalanceLedgers(balanceLedgers);
    return Response.success(res, `Your ledger entries ${balanceLedgers.length}`, {
      data: sanitizeLedgers,
      meta: {
        total: count,
        page,
        nextPage: Number(page) + 1,
        hasMore: count > Number(page) * Number(perPage || 50),
        perPage,
      },
    });
  },

  async addFundsToBalance(req, res) {
    const { destination, amount, source, currency } = req.body;
    const destinationAccount = await BalanceRepo.getBalance({ filter: { code: destination }, includeAccount: true });
    if (!destinationAccount?.BankAccount) throw new NotFoundError("Receiving account");

    let sourceAccount;
    if (source.startsWith("blc_")) sourceAccount = await BalanceRepo.getBalance({ filter: { code: source }, includeAccount: true });
    else {
      const foundAccount = await BankAccountRepo.getOneBankAccount({
        queryParams: { code: source },
        selectOptions: ["currency", "number", "bankCode", "issuer"],
        addBalance: true,
      });
      sourceAccount = { id: foundAccount.Balance?.id, BankAccount: foundAccount };
    }
    if (!sourceAccount?.BankAccount) {
      throw new NotFoundError("Source account");
    }

    // deduce direct debit
    const existingMandate = await Mandate.findOne({
      where: {
        bankAccount: sourceAccount.BankAccount.id,
        status: "granted",
        isReadyForDebit: true,
      },
    });

    if (existingMandate) {
      // eslint-disable-next-line no-use-before-define
      await processDirectDebitTopUp({ destinationAccount, existingMandate, req, amount, sourceAccount });
      return Response.success(res, "Account top up in progress");
    }

    const isNonAnchorMovement = [sourceAccount?.BankAccount?.issuer, destinationAccount?.BankAccount?.issuer].some(
      (issuer) => issuer !== CARD_ISSUER.Anchor
    );

    if (isNonAnchorMovement) {
      // Use NIP
      const { cbnFee, bujetiFee } = await BillingService.computeFees({
        amount,
        companyCode: req.company.code,
        currency: sourceAccount?.BankAccount?.currency,
        plan: Utils.parseJSON(req.company?.paymentPlan?.configuration),
        isDirectDebit: !!existingMandate,
      });

      const createdTrx = await TransactionRepo.createTransaction({
        data: {
          amount,
          description: `Top up to ${destinationAccount.name}`,
          payer: req.user.id,
          company: req.company.id,
          currency: sourceAccount.BankAccount.currency,
          bank_account: destinationAccount.BankAccount.id,
          status: STATUSES.PROCESSING,
          narration: `Top up to ${destinationAccount.name}`,
          recipient: destinationAccount.BankAccount.owner,
          recipient_type: destinationAccount.BankAccount.ownerType,
          processor_fee: cbnFee,
          bujeti_fee: bujetiFee,
          balance: sourceAccount.id,
          type: TRANSACTION_TYPES.PAYMENT,
        },
      });

      await addPaymentToQueue(createdTrx.code);
      return Response.success(res, "Account top up in progress");
    }

    const bookTransferPayload = {
      recipientId: destinationAccount.BankAccount.externalBankAccountId,
      recipientType: "DepositAccount",
      senderId: sourceAccount.BankAccount.externalBankAccountId,
      senderType: "DepositAccount",
      currency,
      amount: Math.abs(amount),
      reason: `Top up to ${destinationAccount.name}`,
      reference: Utils.generateRandomString(17).toLowerCase(),
      company: req.company.id,
      purpose: "BALANCE_TOP_UP",
      source,
      destination,
      paidBy: req.user.id,
    };

    const { payment } = Settings.get("providers");
    const providerToUse = payment[req.company.code] || payment.defaultProvider;
    const { error, data } = await Providers[providerToUse].virtualAccount.bookTransfer(bookTransferPayload);
    // eslint-disable-next-line new-cap
    if (error) Providers[providerToUse].throwProviderError(data);
    return Response.success(res, "Account top up in progress");
  },

  async update(req, res) {
    const { error } = BalanceValidator.updateBalance.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const { name, type, status } = req.body;
    if (!(name || type || status)) return Response.failure(res, "Kindly specidy a name, a type or a status");

    await BalanceService.updateBalance({ filter: { code: req.params.code }, payload: { ...req.body, user: req.user.id } });

    return Response.success(res, "Account updated successfully");
  },

  async getProviderBalance(req, res) {
    Response.success(res, "Ok");
    const { provider, ...rest } = req.body;
    await Providers[provider].getProviderBalance({ ...rest });
  },
};
async function processDirectDebitTopUp({ destinationAccount, existingMandate, req, amount, sourceAccount }) {
  const { bankCode, number: accountNumber, currency } = destinationAccount.BankAccount;

  const { cbnFee, bujetiFee } = await BillingService.computeFees({
    amount,
    companyCode: req.company.code,
    currency: sourceAccount?.BankAccount?.currency,
    plan: Utils.parseJSON(req.company?.paymentPlan?.configuration),
    isDirectDebit: true,
  });

  const directDebit = await DirectDebit.create({
    mandate: existingMandate.id,
    company: req.company.id,
    amount: Math.abs(amount),
    beneficiaryAccountNumber: accountNumber,
    beneficiaryBankCode: bankCode,
    narration: `Direct debit top up to ${accountNumber}`,
    status: STATUSES.PENDING,
    reference: Utils.generateRandomString(14),
  });

  const transactionPayload = {
    amount: Math.abs(amount),
    description: directDebit.narration,
    payer: req.user.id,
    company: req.user.company,
    currency,
    bank_account: destinationAccount.BankAccount.id,
    status: STATUSES.PENDING,
    narration: directDebit.narration,
    type: TRANSACTION_TYPES.DIRECT_DEBIT,
    directDebitId: directDebit.id,
    balance: sourceAccount.id,
    processor_fee: cbnFee,
    bujeti_fee: bujetiFee,
  };

  const { transaction } = await BankService.directDebit(req.company, {
    bankCode: directDebit.beneficiaryBankCode,
    accountNumber: directDebit.beneficiaryAccountNumber,
    narration: directDebit.narration,
    amount: directDebit.amount,
    createdDirectDebit: directDebit,
    transactionPayload,
  });

  transaction.status = STATUSES.PROCESSING;
  await transaction.save();
}

// Helper functions
async function refreshLinkedAccount(balance, company) {
  const foundBankAccount = await BankAccountRepo.getOneBankAccount({
    queryParams: { code: balance.bankAccount },
    selectOptions: ["owner"],
  });

  if (!foundBankAccount) return;

  const configuration = company.PaymentPlan?.configuration;
  if (!configuration) return;

  const { connectedAccounts } = Utils.parseJSON(configuration) || {};
  if (!connectedAccounts) return;

  const accountLinked = await AccountLinking.findOne({ where: { bankAccount: foundBankAccount.id } });
  // eslint-disable-next-line no-use-before-define
  const canRefresh = canRefreshAccount(connectedAccounts, accountLinked);

  if (canRefresh) {
    // eslint-disable-next-line no-use-before-define
    await performAccountRefresh(balance, foundBankAccount, accountLinked, company, connectedAccounts);
  }
}

function canRefreshAccount(connectedAccounts, accountLinked) {
  const { canSync, syncWindow } = connectedAccounts;

  if (!canSync) return false;

  const yourLastSyncDate = accountLinked?.lastSyncedAt ? new Date(accountLinked.lastSyncedAt) : null;
  const nextSyncDate = yourLastSyncDate ? Utils.addMinutes(yourLastSyncDate, syncWindow) : new Date();
  const syncWindowNotExceeded = Utils.firstDateIsAfterSecondDate(new Date(), nextSyncDate);

  if (accountLinked?.syncStatus === STATUSES.PROCESSING && syncWindowNotExceeded) return false;

  return !yourLastSyncDate || !syncWindowNotExceeded;
}

async function performAccountRefresh(balance, foundBankAccount, accountLinked, company, connectedAccounts) {
  const hasNewData = await BankService.initiateManualSync(foundBankAccount.code, {
    owner: foundBankAccount.owner,
    doNotSendReAuthMail: true,
  });

  // eslint-disable-next-line no-use-before-define
  if (shouldUpdateProcessedStatus(hasNewData, accountLinked, connectedAccounts)) {
    // eslint-disable-next-line no-use-before-define
    await updateAccountLinking(foundBankAccount.id, company.id, STATUSES.PROCESSED);
    return;
  }

  // eslint-disable-next-line no-use-before-define
  await queueSyncJob(balance, foundBankAccount, company);
  // eslint-disable-next-line no-use-before-define
  await updateAccountLinking(foundBankAccount.id, company.id, STATUSES.PROCESSING);
}

function shouldUpdateProcessedStatus(hasNewData, accountLinked, connectedAccounts) {
  const yourLastSyncDate = accountLinked?.lastSyncedAt ? new Date(accountLinked.lastSyncedAt) : null;
  const nextSyncDate = yourLastSyncDate ? Utils.addMinutes(yourLastSyncDate, connectedAccounts.syncWindow) : new Date();
  const isNotNewSync = Utils.firstDateIsBeforeSecondDate(new Date(), nextSyncDate);
  const isFirstEverSync = !yourLastSyncDate && !isNotNewSync;

  return typeof hasNewData === "boolean" && !hasNewData && (isNotNewSync || isFirstEverSync);
}

async function updateAccountLinking(bankAccountId, companyId, status) {
  await AccountLinking.update(
    {
      syncStatus: status,
      ...(status === STATUSES.PROCESSED && { lastSyncedAt: new Date() }),
    },
    { where: { bankAccount: bankAccountId, company: companyId } }
  );
}

async function queueSyncJob(balance, foundBankAccount, company) {
  const { connect } = SettingsService.get("providers");
  const provider = connect[company.id] || connect.defaultProvider;

  const SQSPayload = {
    idempotencyKey: `sync:${balance.code}`,
    path: `/bank-sync/${provider}/accounts/${foundBankAccount.code}`,
    key: process.env.INTRA_SERVICE_KEY,
  };

  QueueService.addJob({ tag: `data-sync:${balance.code}` }, SQSPayload, `sync:${balance.code}`, Utils.isProd() ? 10 : 60);
}

function getAccountType(balance) {
  if (balance.isLinked) return ACCOUNT_TYPES.LINKED;
  if (balance.mandateStatus) return ACCOUNT_TYPES.DIRECT_DEBIT;
  return ACCOUNT_TYPES.INTERNAL;
}
