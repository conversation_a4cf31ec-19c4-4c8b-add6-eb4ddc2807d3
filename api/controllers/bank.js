const { Op } = require("sequelize");
const Sanitizer = require("../utils/sanitizer");
const BankService = require("../services/bank");
const BankValidator = require("../validators/bank");
const ValidationError = require("../utils/validation-error");
const { Response, Redis } = require("../services");
const QueueService = require("../services/queue.service");
const Utils = require("../utils/utils");
const SettingsService = require("../services/settings");
const { ForbiddenError, NotFoundError } = require("../utils/error.utils");
const { BankAccountRepo, BalanceRepo } = require("../repositories/index.repo");
const Providers = require("../services/providers");
const { AccountLinking } = require("../models");
const { STATUSES } = require("../models/status");
const { syncAccountTransactions } = require("../services/mono");
const { syncBankAccount } = require("../utils/zoho.utils");
const { getPaystackBanks } = require("../services/bank");
const { VIRTUAL_ACCOUNT_PROVIDERS } = require("../mocks/constants.mock");
const Provider = require("../services/providers");

module.exports = {
  async addBank(req, res) {
    const { user } = req;
    const { error } = BankValidator.create.validate(req.body);
    if (error) throw new ValidationError(error.message);
    const bankAccount = await BankService.createBankAccount({ ...req.body, owner: user.id, ownerType: "user", type: "real" });
    return Response.success(res, "Bank account added successfully", Sanitizer.sanitizeBankAccount(bankAccount));
  },

  async createVirtualAccount(req, res) {
    const { error } = BankValidator.createVirtualAccount.validate(req.body);
    if (error) throw new ValidationError(error.message);
    let parentAccount;

    if (req.body.parent) {
      parentAccount = await BankAccountRepo.getOneBankAccount({
        queryParams: {
          code: req.body.parent,
        },
      });
      if (!parentAccount) throw new NotFoundError("Parent account");
    }

    let providerToUse;

    const isPayStackViban =
      req.body.bankCode?.toLowerCase() === VIRTUAL_ACCOUNT_PROVIDERS.PAYSTACK ||
      req.body.bankName?.toLowerCase() === VIRTUAL_ACCOUNT_PROVIDERS.PAYSTACK;

    if (!isPayStackViban) {
      const { payment } = SettingsService.get("providers");
      providerToUse = payment[req.company.code] || payment.defaultProvider;
    } else {
      providerToUse = VIRTUAL_ACCOUNT_PROVIDERS.PAYSTACK;
    }

    const response = await BankService.createVirtualAccount({
      ...req.body,
      parent: parentAccount?.id,
      provider: providerToUse,
      company: req.company.id,
      companyCode: req.company.code,
      owner: req.company.id,
      ownerType: "company",
      type: "virtual",
      user: req.user.id,
      isManager: req.isManager,
    });

    const { body: { data: { id: externalIdentifier } = {} } = {} } = response || {};
    Redis.set(
      `createAccount:${req.company.code}:additionalAccount`,
      JSON.stringify({
        ...req.body,
        company: req.company.id,
        parent: parentAccount?.id,
      })
    );
    if (externalIdentifier) {
      const { error: fetchError, message, body } = await Providers[providerToUse].virtualAccount.getAccount(externalIdentifier);
      if (fetchError) return Response.failure(res, message);

      const { included } = body;
      if (!included) return Response.success(res, "Account is being generated");
      return Response.success(res, "Account added successfully");
    }
    Redis.delete(`createAccount:${req.company.code}:additionalAccount`);
    return Response.success(res, response?.message || response?.body?.message || "Account generated", response);
  },

  async list(req, res) {
    const { id: owner } = req.user;
    const criteria = { owner, ownerType: "user", type: "real" };
    const bankAccounts = await BankService.list(criteria);
    return Response.success(res, "Bank account fetched successfully", Sanitizer.sanitizeBankAccounts(bankAccounts));
  },

  async getConnectedAccounts(req, res) {
    const { error } = BankValidator.getAccounts.validate(req.query);
    if (error) throw new ValidationError(error.message);
    const criteria = { ...req.query, owner: req.company.id };
    const { accounts, meta } = await BankService.getConnectedAccounts(criteria);
    return Response.success(res, "Connected accounts fetched successfully", { accounts: Sanitizer.sanitizeBankAccounts(accounts), meta });
  },

  async getConnectedAccount(req, res) {
    const { error } = BankValidator.getAccount.validate(req.params);
    if (error) throw new ValidationError(error.message);
    const criteria = { owner: req.company.id, paymentPlan: req.company.PaymentPlan };

    const result = await BankService.getConnectedAccount(req.params.code, criteria);
    return Response.success(res, "Connected account fetched successfully", Sanitizer.sanitizeBankAccount(result));
  },

  async getConnectedAccountStatement(req, res) {
    const { error } = BankValidator.getAccount.validate(req.params);
    if (error) throw new ValidationError(error.message);
    const criteria = { ...req.query, owner: req.company.id, paymentPlan: req.company.PaymentPlan };

    const result = await BankService.getConnectedAccountStatement(req.params.code, criteria);
    return Response.success(res, "Connected account statement fetched successfully", result);
  },

  async initiateManualSync(req, res) {
    if (!req.isManager) throw new ForbiddenError("Action not allowed");
    const { error } = BankValidator.getAccount.validate(req.params);
    if (error) throw new ValidationError(error.message);

    const payload = { owner: req.company.id, paymentPlan: req.company.PaymentPlan };

    const response = await BankService.initiateManualSync(req.params.code, payload);

    if (response.requiresReauth) return Response.plainJson(res, { code: 400, message: "Reauth required", customerCode: response.id });

    return Response.success(res, "Bank sync in progress", { status: response });
  },

  async createMandateForDirectDebit(req, res) {
    const { error } = BankValidator.createMandateForDirectDebit.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const result = await BankService.createMandateForDirectDebit(req.company, req.body);
    return Response.success(res, result?.message || "Mandate created successfully", result);
  },

  async defaultBankAccount(req, res) {
    const payload = { bankCode: req.params.code, user: req.user, defaultBank: req.body.defaultBank };
    const bankAccounts = await BankService.defaultBankAccount(payload);
    return Response.success(res, bankAccounts.message, Sanitizer.sanitizeBankAccounts(bankAccounts));
  },

  async deleteBankAccount(req, res) {
    const payload = { bankCode: req.params.code, owner: req.user.id };
    const bankAccounts = await BankService.deleteBankAccount(payload);
    return Response.success(res, bankAccounts.message, Sanitizer.sanitizeBankAccounts(bankAccounts));
  },

  async updateBankAccount(req, res) {
    const { user } = req;
    const { error } = BankValidator.update.validate(req.body);
    if (error) throw new ValidationError(error.message);
    const { body } = req;
    await BankService.updateBankAccount(user, body);
    return Response.success(res, "Bank account updated successfully");
  },

  async verifyAccountName(req, res) {
    const { error } = BankValidator.verifyAccountNumber.validate(req.body);
    if (error) throw new ValidationError(error.message);
    const { accountNumber } = req.body;
    // MOCK FAILURE
    if (accountNumber === "**********") {
      return Response.failure(res, "Something went wrong, please try again later.");
    }
    // RETURN DUMMY DATA FOR ALL CALLS ON STAGING AND DEMO
    if (!Utils.isLive()) {
      const randomNames = [
        "Achille Arouko",
        "Samy Chiba",
        "Abeng Emmanuel",
        "Emily Brown",
        "David Wilson",
        "Sarah Davis",
        "Robert Taylor",
        "Jennifer Anderson",
        "William Thomas",
        "Lisa Martinez",
        "Bujeti Nigeria",
        "Rasonnad",
      ];
      const randomName = randomNames[Math.floor(Math.random() * randomNames.length)];
      const response = {
        account_number: accountNumber,
        account_name: randomName,
      };
      return Response.success(res, "Account name fetched successfully", response);
    }
    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[req.company.code] || payment.defaultProvider;
    const response = await BankService.verifyAccountName({ ...req.body, company: req.company.id }, providerToUse);
    return Response.success(res, "Account name fetched successfully", response);
  },

  async verifyAccountNameInternally(req, res) {
    const { error } = BankValidator.internalAccountNumberValidator.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const response = await BankService.verifyAccountNameFromProvider(req.body, req.body.provider, { isInternal: true });
    return Response.success(res, "Account name fetched successfully", response);
  },

  async bulkVerifyAccount(req, res) {
    const { error } = BankValidator.bulkVerifyAccount.validate(req.body);
    if (error) throw new ValidationError(error.message);

    // RETURN DUMMY DATA FOR ALL CALLS ON STAGING AND DEMO
    if (!Utils.isLive()) {
      const response = Array.from(req.body.accounts).map((account) => {
        return {
          account_number: account.accountNumber,
          account_name: "Bujeti Nigeria",
          verified: true,
        };
      });
      return Response.success(res, "Account name fetched successfully", response);
    }

    const response = await BankService.bulkVerifyAccount(req.body.accounts, req.company.id);
    return Response.success(res, "Account name fetched successfully", response);
  },

  async cancelMandateOrSync(req, res) {
    const payload = { ...req.params, ...req.body };
    const { error } = BankValidator.cancelMandateOrSync.validate({ ...req.params, ...req.body });
    if (error) throw new ValidationError(error.message);
    const result = await BankService.cancelMandateOrSync(req.company, payload);
    return Response.success(res, result?.message || "Update successful", result);
  },

  async initiateDataSync(req, res) {
    const { code } = req.params;
    const foundBalance = await BalanceRepo.getBalance({ filter: { code }, includeAccount: true });
    if (!foundBalance || !foundBalance.BankAccount) throw NotFoundError("Account");
    const { connect } = SettingsService.get("providers");
    const provider = connect[req.company.code] || connect.defaultProvider;

    let syncedAtLeastOnce = null;
    let yourLastSyncDate = null;
    let nextSyncDate = null;

    if (req.company.PaymentPlan?.configuration) {
      const { connectedAccounts: { canSync, maxRealTimeDataPerDay, syncWindow, canRequestRealTimeData } = {} } =
        Utils.parseJSON(req.company.PaymentPlan.configuration) || {};
      if (!canSync) return Response.success(res, "Your plan does not allow you to sync your data");

      const accountLinked = await AccountLinking.findOne({ where: { bankAccount: foundBalance?.BankAccount?.id } });

      if (accountLinked?.syncStatus === STATUSES.PAUSE) return Response.success(res, "Sync is paused, please reactivate sync to continue");

      yourLastSyncDate = accountLinked?.lastSyncedAt ? new Date(accountLinked?.lastSyncedAt) : null;
      nextSyncDate = yourLastSyncDate ? Utils.addMinutes(yourLastSyncDate, syncWindow) : new Date();

      const syncWindowNotExceeded = Utils.firstDateIsBeforeSecondDate(new Date(), nextSyncDate);
      if (accountLinked?.syncStatus === STATUSES.PROCESSING && syncWindowNotExceeded) {
        return Response.success(res, "Sync already in progress, give it a minute...", {
          lastSyncedAt: accountLinked?.lastSyncedAt,
          nextSync: nextSyncDate,
        });
      }

      if (yourLastSyncDate && Utils.firstDateIsBeforeSecondDate(new Date(), nextSyncDate)) {
        return Response.success(res, "You have already used all your sync attempts for the day, please wait till the next window...", {
          lastSyncedAt: accountLinked?.lastSyncedAt,
          nextSync: nextSyncDate,
        });
      }
      syncedAtLeastOnce = accountLinked.lastSyncedAt;
    } else {
      return Response.success(res, "You are not allowed to use this feature, please upgrade your subscription");
    }
    if (foundBalance.BankAccount.requiresReauth) {
      const { error, message, body, data } = await Providers[provider].getReauthorizationToken(
        foundBalance.company,
        foundBalance.BankAccount.externalIdentifier
      );
      return Response.plainJson(res, {
        code: 400,
        message: "Reauth required",
        data: {
          reconnectId: body?.token,
        },
      });
    }
    try {
      if (Utils.isProd()) {
        const hasNewData = await BankService.initiateManualSync(foundBalance.BankAccount.code, { owner: foundBalance.BankAccount.owner });
        const isNotNewSync = Utils.firstDateIsBeforeSecondDate(new Date(), nextSyncDate);
        // has no previous sync, and is a new sync attempts;
        const isFirstEverSync = !yourLastSyncDate && !isNotNewSync;

        if (typeof hasNewData === "boolean" && !hasNewData && (isNotNewSync || isFirstEverSync)) {
          await AccountLinking.update(
            { syncStatus: STATUSES.PROCESSED, lastSyncedAt: new Date() },
            { where: { bankAccount: foundBalance.BankAccount.id, company: req.company.id } }
          );
          return Response.success(res, "Data successfully synced...");
        }
      }
    } catch (error) {
      // handle case where sync fails
      await AccountLinking.update({ syncStatus: STATUSES.FAILED }, { where: { bankAccount: foundBalance.BankAccount.id, company: req.company.id } });
    }

    const SQSPayload = {
      idempotencyKey: `sync:${code}`,
      path: `/bank-sync/${provider}/accounts/${foundBalance.BankAccount.code}`,
      key: process.env.INTRA_SERVICE_TOKEN,
    };
    QueueService.addJob({ tag: `data-sync:${code}` }, SQSPayload, `sync:${code}`, Utils.isProd() ? 10 : 60);
    await AccountLinking.update({ syncStatus: STATUSES.PROCESSING }, { where: { bankAccount: foundBalance.BankAccount.id } });
    return Response.success(res, "Sync in progress, give it a minute...");
  },

  async syncData(req, res) {
    const { account } = req.params;
    const { shouldFetchLiveData } = req.body;

    const foundBankAccount = await BankAccountRepo.getOneBankAccount({ queryParams: { code: account } });
    if (!foundBankAccount) return Response.success(res, "OK");
    const linkedAccount = await AccountLinking.findOne({ where: { bankAccount: foundBankAccount.id, status: { [Op.ne]: STATUSES.PAUSE } } });
    if (!linkedAccount) return Response.success(res, "OK");

    const { name: accountName, id: bankAccount } = foundBankAccount;

    const foundBalance = await BalanceRepo.findOrCreate(
      Utils.stripUndefinedAndNullDeep({
        bankAccount,
        company: linkedAccount.company,
        currency: foundBankAccount.currency || "NGN",
        type: await BalanceRepo.getBalanceTypes("expenses"),
        name: accountName,
        purpose: "expenses",
      })
    );

    if (!foundBalance) return Response.success(res, "OK");

    syncAccountTransactions({
      company: linkedAccount.company,
      externalIdentifier: linkedAccount.externalIdentifier,
      id: foundBankAccount.id,
      balance: foundBalance.id,
      balanceName: foundBalance.name,
      currency: foundBalance.currency || foundBankAccount.currency,
      lastSyncedAt: linkedAccount.lastSyncedAt,
      foundBankAccount,
      shouldFetchLiveData,
    });
    return Response.success(res, "OK");
  },

  async addMembers(req, res) {
    const { error } = BankValidator.addMembers.validate(req.body);
    if (error) throw new ValidationError(error.message);
    const payload = { ...req.body, code: req.params.code, user: req.user, company: req.company.id, isManager: req.isManager };
    await BankService.addMembers(payload);
    return Response.success(res, "Members added successfully");
  },

  async listMembers(req, res) {
    const criteria = {
      ...req.params,
      ...req.query,
      company: req.company.id,
    };
    const { members, meta } = await BankService.listMembers(criteria);

    return Response.success(res, "Members listed successfully", { members: Sanitizer.sanitizeAccountMembers(members), meta });
  },

  async removeMember(req, res) {
    const { error } = BankValidator.removeMember.validate(req.params);
    if (error) throw new ValidationError(error.message);
    const payload = { ...req.params, user: req.user.id, company: req.company.id, isManager: req.isManager };
    await BankService.removeMember(payload);
    return Response.success(res, "Members removed successfully");
  },

  async getMandateStatus(req, res) {
    const { error } = BankValidator.getMandateStatus.validate(req.params);
    if (error) throw new ValidationError(error.message);
    const payload = { ...req.params, company: req.company.id };
    const result = await BankService.getMandateStatus(payload);
    return Response.success(res, "Mandate status fetched successfully", result);
  },

  async syncZoho(req, res) {
    const { codes = [] } = req.body;
    const [code] = codes;

    const foundBalance = await BalanceRepo.getBalance({
      filter: { code },
      includeAccount: true,
    });
    if (!foundBalance) return Response.failure(res, "Account not found");

    const foundBankAccount = foundBalance.BankAccount;
    if (!foundBankAccount) return Response.failure(res, "Account not found");
    if (foundBankAccount.zohoIdentifier) return Response.success(res, "Account already synced");
    syncBankAccount(foundBankAccount.code, req.company.id);
    return Response.success(res, "Account sync in progress");
  },

  async getProviderBanks(req, res) {
    const response = await BankService.getProviderBanks(req.query);
    return Response.success(res, "Banks fetched successfully", response);
  },
};
