const { Op, ValidationErrorItem } = require("sequelize");
const Sanitizer = require("../utils/sanitizer");
const {
  User: UserService,
  Budget: BudgetService,
  Beneficiary: BeneficiaryService,
  Response: ResponseService,
  UserBudget: UserBudgetService,
  TeamMember: TeamMemberService,
  BillingService,
  Bank: BankService,
  Balance: BalanceService,
  Approval: ApprovalService,
  Redis: RedisService,
} = require("../services");
const ValidationError = require("../utils/validation-error");
const { STATUSES } = require("../models/status");
const { BeneficiaryValidator } = require("../validators");
const NotFoundError = require("../utils/not-found-error");
const { RoleRepo, UserRepo } = require("../repositories/index.repo");
const { getStatusValues } = require("../utils");
const teamRepo = require("../repositories/team.repo");
const { BILLING_ADDONS } = require("../mocks/constants.mock");
const { Subscription } = require("../models");
const { chargeForAdditionalUsers, createBillingAddon } = require("../services/helper.service");
const { updateBudgetUsersAmount } = require("../services/budget");

module.exports = {
  async listBeneficiaries(req, res) {
    const budgetOwner = req.user.id;
    const { error } = BeneficiaryValidator.listBeneficiaries.validate({ ...req.query });
    if (error) throw new ValidationError(error.message);
    const beneficiaries = await BeneficiaryService.fetchBeneficiaries(budgetOwner, { company: req.company.id, ...req.query });
    return ResponseService.success(res, "Your beneficiaries", beneficiaries);
  },

  async getBeneficiary(req, res) {
    const beneficiary = await BeneficiaryService.getSingleBeneficiary({ code: req.params.code });
    if (!beneficiary) throw new NotFoundError("User");
    const user = await UserService.fetchUser(beneficiary.user);
    if (!user) throw new NotFoundError("User");
    beneficiary.user = user;
    const [teams = null] = await teamRepo.listUserTeams(user.id, user.company);
    const sanitizedBeneficiary = Sanitizer.sanitizeBeneficiary(beneficiary);
    sanitizedBeneficiary.user.team = Sanitizer.sanitizeTeams(teams);

    return ResponseService.success(res, "Beneficiary retrieved successfully", sanitizedBeneficiary);
  },

  /**
   * Get all beneficiaries for exports
   * @param req
   * @param res
   * @returns {Promise<void>}
   */
  async allBeneficiaries(req, res) {
    const beneficiaries = await BeneficiaryService.allBeneficiaries({ owner: req.user.id, company: req.company.id, ...req.query });
    return ResponseService.success(res, "Your beneficiaries", beneficiaries);
  },

  /**
   * Create a new beneficiary
   * @param req
   * @param res
   * @returns {Promise<void>}
   */
  async addBeneficiary(req, res) {
    BeneficiaryService.validateBeneficiaryPayload(req.body);

    const { company, user, additionalUsersCost, additionalUsersCount, isNotFreeUser } = req;
    const budgetOwner = user;
    const { paymentPlan } = company;

    const { method, balance, budget, directDebit } = req.body;

    await BeneficiaryService.checkIfExtraSeatsProcessing(additionalUsersCount, company);

    let { budgets = [] } = req.body;
    if (budgets && budgets.length > 0) {
      const { budgets: foundBudgets } = await BudgetService.listBudgets({
        code: budgets,
        company: req.company.id,
      });
      if (!foundBudgets.length) {
        throw new ValidationError("Some budgets were not found or insufficient");
      }
      budgets = foundBudgets;
    }

    let associatedUser = await UserRepo.getOneUser({ queryParams: { email: req.body.email, status: { [Op.ne]: STATUSES.DELETED } } });
    if (associatedUser && associatedUser.company) throw new ValidationError("User already exist with this email");
    const { role } = req.body;
    if (!role) throw new ValidationError("Please select a role");

    const roleCriteria = {
      company: {
        [Op.or]: [null, req.user.company],
      },
    };
    if (role.startsWith("rol_")) roleCriteria.code = role;
    else roleCriteria.name = role;
    const foundRole = await RoleRepo.getRole({ queryParams: roleCriteria });
    if (req.body.manager) {
      const foundManager = await UserRepo.getOneUser({ queryParams: { code: req.body.manager, company: req.user.company }, selectOptions: ["id"] });
      req.body.manager = foundManager && foundManager.id;
    }
    const createUser = {
      ...req.body,
      role_id: foundRole && foundRole.id,
      role: foundRole && foundRole.name,
    };

    const createdTransaction = await chargeForAdditionalUsers({
      additionalUsersCost,
      company,
      paymentPlan,
      additionalUsersCount,
      user,
      method,
      budget,
      balance,
      directDebit,
      BillingService,
      BudgetService,
      BalanceService,
      BankService,
      ApprovalService,
    });

    const usedCompanyCredit = createdTransaction?.usedCompanyCredit;

    const activeSubscription = await Subscription.findOne({
      where: {
        company: company.id,
        status: STATUSES.ACTIVE,
        plan: paymentPlan.id,
      },
      attributes: ["id", "code", "additionalSeats"],
    });

    const hasEnoughReservedSeats = (activeSubscription?.additionalSeats || 0) >= 1;

    // we set to processing, and move to the usual state when payment is successful
    const userStatus = createdTransaction && !usedCompanyCredit && !hasEnoughReservedSeats ? STATUSES.PROCESSING : STATUSES.PENDING;

    if (!associatedUser) {
      ({ data: associatedUser } = await UserService.registerUser({ ...createUser, company: company.id, status: userStatus }));
    } else {
      associatedUser.status = userStatus;
      await UserService.updateUser(associatedUser.id, { ...createUser, company: company.id, status: userStatus });
    }

    const { code } = await BeneficiaryService.addBeneficiary(budgetOwner.id, associatedUser.id, company.id, foundRole.id, userStatus);

    if (budgets.length) {
      await BudgetService.addBeneficiaryToBudgets(budgets, associatedUser.id, false);
    }
    const newBeneficiary = await BeneficiaryService.getByCode(code);

    if (createdTransaction || hasEnoughReservedSeats) {
      await createBillingAddon({
        transaction: usedCompanyCredit ? null : createdTransaction?.transaction,
        transfer: usedCompanyCredit ? null : createdTransaction?.transfer,
        company,
        subscription: activeSubscription,
        entity: BILLING_ADDONS.BENEFICIARIES,
        entityIds: [{ entityId: newBeneficiary.id, ...(hasEnoughReservedSeats && { entityStatus: STATUSES.SUCCESS }) }],
        user,
        ...(usedCompanyCredit && { status: STATUSES.SUCCESS, creditsUsed: additionalUsersCost }),
      });

      const shouldSendInvite = usedCompanyCredit || hasEnoughReservedSeats;

      if (shouldSendInvite) {
        BeneficiaryService.sendBeneficiaryInvitation(budgetOwner, associatedUser, budgets, company);
      }

      if (usedCompanyCredit) {
        const usersNotCoveredByReservation = additionalUsersCount - 1;
        const remainingSeats = Math.max(usersNotCoveredByReservation, 0);

        if (remainingSeats) {
          await Subscription.increment("additionalSeats", {
            by: remainingSeats,
            where: {
              code: activeSubscription.code,
            },
          });
        }
      }

      if (createdTransaction && !usedCompanyCredit) {
        RedisService.setex(
          `extra_seats:${company.code}`,
          JSON.stringify({
            additionalUsersCount,
            additionalUsersCost,
            subscription: activeSubscription.code,
          }),
          1 * 60 * 60 // 1 hour
        );
      }
    } else {
      BeneficiaryService.sendBeneficiaryInvitation(budgetOwner, associatedUser, budgets, company);
    }

    if (isNotFreeUser && hasEnoughReservedSeats) {
      await Subscription.decrement("additionalSeats", {
        by: 1,
        where: {
          code: activeSubscription.code,
        },
      });
    }

    const serialized = Sanitizer.sanitizeBeneficiary(newBeneficiary);

    const message = createdTransaction && !usedCompanyCredit && !hasEnoughReservedSeats ? "Beneficiary creation in progress" : "Beneficiary added";
    return ResponseService.success(res, message, serialized);
  },

  async createBulkBeneficiaries(req, res) {
    BeneficiaryService.validateBulkBeneficiaryPayload(req.body);

    const { company, user, additionalUsersCost, additionalUsersCount, isNotFreeUser } = req;
    const { paymentPlan } = company;

    const { method, balance, budget, directDebit } = req.body;

    await BeneficiaryService.checkIfExtraSeatsProcessing(additionalUsersCount, company);

    if (req.body.beneficiaries.length) {
      const existingUsers = await UserRepo.getAllUsers({
        queryParams: { email: req.body.beneficiaries.map((beneficiary) => beneficiary.email), status: { [Op.ne]: STATUSES.DELETED } },
      });

      if (existingUsers.length) {
        throw new ValidationError(`User(s) with email ${existingUsers.map((existingUser) => existingUser.email).join(", ")} already exist`);
      }
    }

    const createdTransaction = await chargeForAdditionalUsers({
      additionalUsersCost,
      company,
      paymentPlan,
      additionalUsersCount,
      user,
      method,
      budget,
      balance,
      directDebit,
      BillingService,
      BudgetService,
      BalanceService,
      BankService,
      ApprovalService,
    });

    const usedCompanyCredit = createdTransaction?.usedCompanyCredit;

    const activeSubscription = await Subscription.findOne({
      where: {
        company: company.id,
        status: STATUSES.ACTIVE,
        plan: paymentPlan.id,
      },
      attributes: ["id", "code", "additionalSeats"],
    });

    const reservedSeats = activeSubscription?.additionalSeats || 0;
    const maxSeatsToReserve = Math.min(reservedSeats, req.body.beneficiaries?.length);

    const usersCoveredByReservation = maxSeatsToReserve ? req.body.beneficiaries?.slice(0, maxSeatsToReserve) : [];

    const usersCoveredByReservationMap = usersCoveredByReservation.reduce((acc, item) => {
      acc[item?.email] = item;
      return acc;
    }, {});

    const createdBeneficiaries = await BeneficiaryService.bulkCreateBeneficiary({
      budgetOwner: req.user,
      beneficiaries: req.body.beneficiaries,
      company: req.company,
      UserService,
      TeamMemberService,
      transaction: createdTransaction,
      usersCoveredByReservationMap,
    });

    if (createdTransaction || maxSeatsToReserve) {
      await createBillingAddon({
        transaction: usedCompanyCredit ? null : createdTransaction?.transaction,
        transfer: usedCompanyCredit ? null : createdTransaction?.transfer,
        company,
        subscription: activeSubscription,
        entity: BILLING_ADDONS.BENEFICIARIES,
        entityIds: createdBeneficiaries.map((beneficiary) => ({
          entityId: beneficiary.id,
          ...(usersCoveredByReservationMap[beneficiary.email] && { entityStatus: STATUSES.SUCCESS }),
        })),
        user,
        ...(usedCompanyCredit && { status: STATUSES.SUCCESS, creditsUsed: additionalUsersCost }),
      });

      if (createdTransaction && !usedCompanyCredit) {
        RedisService.setex(
          `extra_seats:${company.code}`,
          JSON.stringify({
            additionalUsersCount,
            additionalUsersCost,
            subscription: activeSubscription.code,
          }),
          1 * 60 * 60 // 1 hour
        );
      }
    }

    if (usedCompanyCredit) {
      const usersNotCoveredByReservation = req.body.beneficiaries.length - maxSeatsToReserve;
      const remainingSeats = Math.max(additionalUsersCount - usersNotCoveredByReservation, 0);

      if (remainingSeats) {
        await Subscription.increment("additionalSeats", {
          by: remainingSeats,
          where: {
            code: activeSubscription.code,
          },
        });
      }
    }

    if (isNotFreeUser && maxSeatsToReserve) {
      await Subscription.decrement("additionalSeats", {
        by: maxSeatsToReserve,
        where: {
          code: activeSubscription.code,
        },
      });
    }

    const wasCharged = createdTransaction && !usedCompanyCredit;

    let message = wasCharged ? "Beneficiaries creation in progress" : "Beneficiaries added";

    const hasNoBeneficiaries = !req.body.beneficiaries?.length;

    if (hasNoBeneficiaries) {
      message = wasCharged ? "Extra seats processing" : "Extra seats processed";
    }

    return ResponseService.success(res, message);
  },

  /**
   * Update a beneficiary data
   * @param req
   * @param res
   * @returns {Promise<void>}
   */
  async updateBeneficiary(req, res) {
    const { code } = req.params;
    const { budgets: budgetsCodes = [], role, manager } = req.body;
    const { error } = BeneficiaryValidator.update.validate(req.body);
    let { status } = req.body;

    if (error) throw new ValidationError(error.message);

    if (status) BeneficiaryService.validateBeneficiaryStatus(status);
    const beneficiary = await BeneficiaryService.getByCode(code);
    status = getStatusValues(status);

    const userPayload = {};
    if ((status && beneficiary.status !== STATUSES.INVITED) || status === STATUSES.DELETED) {
      await BeneficiaryService.updateBeneficiary({ id: beneficiary.id }, status);
      userPayload.status = status;
    }

    if (role) {
      const criteria = {
        company: {
          [Op.or]: [null, req.company.id],
        },
      };
      if (role.startsWith("rol_")) criteria.code = role;
      else criteria.name = role;
      const foundRole = await RoleRepo.getRole({ queryParams: criteria });
      userPayload.role_id = foundRole && foundRole.id;
      userPayload.role = foundRole && foundRole.name;
    }
    if (manager) {
      const foundManager = await UserRepo.getOneUser({ queryParams: { code: manager, company: req.user.company }, selectOptions: ["id"] });
      userPayload.manager = foundManager && foundManager.id;
    }
    if (userPayload) {
      await UserService.updateUser(beneficiary.user, userPayload);
    }

    if (budgetsCodes) {
      let budgets = [];
      if (budgetsCodes.length) {
        // ENSURE ALL BUDGET CODE EXISTS
        budgets = await BudgetService.fetchBudgetsOnly({
          company: req.company.id,
          code: budgetsCodes,
        });
        if (budgets && budgetsCodes.length !== budgets.length) throw new ValidationError("Some budgets could not be found");
      }

      // get all current budget code
      const { data: userBudgets } = await UserBudgetService.getUserBudget({ employee: beneficiary.user, status: STATUSES.ACTIVE });
      const currentBudgetCodes = Array.from(userBudgets).map((value) => value.Budget.code);
      // check the difference
      if (budgetsCodes.length !== currentBudgetCodes.length) {
        const budgetToRemove = currentBudgetCodes.filter((value) => !budgetsCodes.includes(value));
        const budgetToAdd = budgetsCodes.filter((value) => !currentBudgetCodes.includes(value));

        if (budgetToAdd) {
          const budgetToAddArray = budgets.filter((budgetObject) => budgetToAdd.includes(budgetObject.code));
          await BudgetService.addBeneficiaryToBudgets(budgetToAddArray, beneficiary.user, true);
        }

        if (budgetToRemove) {
          let budgetToRemoveArray = userBudgets.filter((budgetObject) => budgetToRemove.includes(budgetObject.Budget.code));
          budgetToRemoveArray = budgetToRemoveArray.map((value) => value.Budget);
          await BudgetService.removeBeneficiaryFromBudgets(budgetToRemoveArray, beneficiary.user);
        }
      }
    }
    return ResponseService.success(res, "Beneficiary updated");
  },

  async search(req, res) {
    const beneficiaries = await BeneficiaryService.search(req.company.id, req.query);
    return ResponseService.success(res, "Your beneficiaries", beneficiaries);
  },

  async resendInvitation(req, res) {
    const admin = req.user;
    const { beneficiary: code } = req.params;

    const beneficiary = await BeneficiaryService.getByCode(code);
    if (!beneficiary) throw new NotFoundError("Beneficiary");
    if (![STATUSES.INVITED, STATUSES.PENDING].includes(beneficiary.status))
      throw new ValidationError("You can only perform this action on beneficiary with invited or pending status");

    BeneficiaryService.sendBeneficiaryInvitation(admin, beneficiary.User, [], req.company);

    return ResponseService.success(res, "Invitation sent to beneficiary");
  },

  async analyticsOfTransactionByBeneficiary(req, res) {
    const criteria = {
      company: req.company.id,
      from: req.query.from,
      to: req.query.to,
    };
    const { message, data } = await BeneficiaryService.analyticsOfTransactionByBeneficiary(criteria);
    const { beneficiaryTransactionAnalytics, highestTransactionVolume, highestTransactionCount, percentageSpentByBeneficiary } = data;

    return ResponseService.success(res, message, {
      percentageSpentByBeneficiary: Sanitizer.sanitizeBeneficiaryTransactionsForAnalytics(percentageSpentByBeneficiary),
      highestTransactionCount: Sanitizer.sanitizeBeneficiaryTransactionForAnalytics(highestTransactionCount),
      highestTransactionVolume: Sanitizer.sanitizeBeneficiaryTransactionForAnalytics(highestTransactionVolume),
      analytics: Sanitizer.sanitizeBeneficiaryTransactionsForAnalytics(beneficiaryTransactionAnalytics),
    });
  },

  async deleteBeneficiary(req, res) {
    const { code } = req.params;
    const {
      company: { id: company },
    } = req;

    const criteria = { code, company };
    await BeneficiaryService.deleteBeneficiary(criteria);
    ResponseService.success(res, "Beneficiary deleted successfully");
  },

  async finalizeAdditionalBeneficiaries(req, res) {
    await BeneficiaryService.finalizeAdditionalBeneficiaries(req.body);
    return ResponseService.success(res, "Beneficiary finalized");
  },
};
