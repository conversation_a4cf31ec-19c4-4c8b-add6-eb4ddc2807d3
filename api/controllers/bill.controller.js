const { ValidationError } = require("../utils/error.utils");
const { BillValidator } = require("../validators");
const BillService = require("../services/bill.service");
const Sanitizer = require("../utils/sanitizer");
const ResponseService = require("../services/response");

module.exports = {
  async createBill(req, res) {
    const { error } = BillValidator.createBill.validate(req.body);

    if (error) throw new ValidationError(error.message);

    const {
      user: { id: user, firstName, lastName },
      company: { id: company, name: companyName },
    } = req;

    const {
      message,
      data: { bill },
    } = await BillService.createBill({
      ...req.body,
      company,
      user,
      firstName,
      lastName,
      companyName,
    });

    ResponseService.success(res, message, Sanitizer.sanitizeBill(bill));
  },

  async list(req, res) {
    const { id: company } = req.company;
    const query = { ...req.query, company };
    const { error } = BillValidator.listBills.validate(query);
    if (error) throw new ValidationError(error.message);

    const { bills, meta, summary } = await BillService.list(query);
    ResponseService.success(res, "Bills fetched successfully", {
      bills: Sanitizer.sanitizeModelInstances(bills, "Bill"),
      summary,
      meta,
    });
  },

  async view(req, res) {
    const {
      params: { code },
      company: { id: company },
      query: { includeTimeline = false },
    } = req;

    const { error } = BillValidator.viewBill.validate({ code });
    if (error) throw new ValidationError(error.message);

    const bill = await BillService.view({ company, code, includeTimeline });
    const timeline = Boolean(includeTimeline) && (await BillService.buildBillTimeline(bill));
    ResponseService.success(res, "Bill fetched successfully", { ...Sanitizer.sanitizeBill(bill), ...(timeline && { timeline }) });
  },

  async update(req, res) {
    const {
      params: { code },
      company: { id: company },
    } = req;
    const { error } = BillValidator.updateBill.validate({
      code,
      ...req.body,
    });
    if (error) throw new ValidationError(error.message);
    const { message, data: bill } = await BillService.updateBill({ filter: { code, company }, payload: req.body });

    ResponseService.success(res, message);
    BillService.completeBillEditing({ bill, payload: req.body });
  },

  async remove(req, res) {
    const { code } = req.params;

    const { error } = BillValidator.viewBill.validate({ code });
    if (error) throw new ValidationError(error.message);

    await BillService.removeBill({ code, company: req.company.id });
    ResponseService.success(res, "Bill deleted successfully");
  },

  async completeApproval(req, res) {
    await BillService.completeApproval({ ...req.params, ...req.body, company: req.company });
    ResponseService.success(res, "Ok");
  },

  async markBillAsPaid(req, res) {
    const {
      params: { code },
      company: { id: company },
      user,
    } = req;
    const { error } = BillValidator.markBillAsPaid.validate({ ...req.params, ...req.body });
    if (error) throw new ValidationError(error.message);

    await BillService.markBillAsPaid({ filter: { code, company }, payload: req.body, extras: { user } });
    ResponseService.success(res, "Bill marked as paid successfully");
  },

  async getBillStats(req, res) {
    const {
      company: { id: company },
    } = req;
    const { error } = BillValidator.getBillStats.validate({ ...req.query });
    if (error) throw new ValidationError(error.message);

    const summary = await BillService.getBillAnalytics({ ...req.query, company });
    ResponseService.success(res, "Bill analytics fetched successfully", summary);
  },

  async void(req, res) {
    const { code } = req.params;

    const { error } = BillValidator.viewBill.validate({ code });
    if (error) throw new ValidationError(error.message);

    await BillService.voidBill({ code });
    ResponseService.success(res, "Bill voided successfully");
  },

  async finalizeBill(req, res) {
    await BillService.finalizeBill(req.body);
    ResponseService.success(res, "Bill finalized successfully");
  },

  async payBill(req, res) {
    const { code } = req.params;

    const { error } = BillValidator.payBill.validate({ ...req.body, code });
    if (error) throw new ValidationError(error.message);

    await BillService.payBill({ ...req.body, ...req.params, company: req.company, user: req.user });
    ResponseService.success(res, "Bill payment initiated successfully");
  },

  async syncZoho(req, res) {
    const { codes = [] } = req.body;

    if (!codes.length) {
      return ResponseService.failure(res, "Select a bill to sync");
    }

    const { error } = BillValidator.syncZoho.validate({ codes });
    if (error) throw new ValidationError(error.message);

    const syncedBills = await BillService.syncZoho({
      codes,
      company: req.company.id,
    });

    console.log()

    if (syncedBills) {
      return ResponseService.success(res, syncedBills);
    }

    return ResponseService.success(res, "Sync in progress");
  },
};
