const Sanitizer = require("../utils/sanitizer");
const {
  Response,
  Budget: BudgetService,
  Charge: ChargeService,
  BillingService,
  Bank: BankService,
  Approval: ApprovalService,
} = require("../services");
const { STATUSES } = require("../models/status");
const { NotFoundError } = require("../utils/error.utils");
const { User, Company } = require("../models");

module.exports = {
  async getPaymentPlan(req, res) {
    const plan = await BillingService.getPaymentPlan(req.params.code, req.company.id);

    if (!plan) throw new NotFoundError("Payment plan");

    return Response.success(res, "Payment plan successfully fetched", Sanitizer.sanitizePlan(plan));
  },
  async listPlans(req, res) {
    const plans = await BillingService.listPaymentPlans(req.company.id, req.company.paymentPlan);
    return Response.success(res, "Payment plans successfully fetched", {
      plans: Sanitizer.sanitizePlans(plans),
    });
  },
  async listBillingHistory(req, res) {
    const { id: company } = req.company;
    const history = await BillingService.listBillingHistory({ ...req.query, company });
    return Response.success(res, "Billing history successfully retrieved", {
      billingHistory: Sanitizer.sanitizeBillingHistory(history),
    });
  },
  async subscribe(req, res) {
    req.body = { ...req.body, ...req.params };
    BillingService.validatePayload(req.body, "subscribe");
    const { code } = req.params;
    const { method, budget, balance, directDebit, contactEmail, billingPeriod, address, state, country, companyName, city, firstName, lastName } =
      req.body;
    const plan = await BillingService.getPaymentPlan(code, req.company.id);
    if (!plan) throw new NotFoundError("Payment plan");

    const { company, user } = req;
    company.activeEmployees = await User.count({ where: { company: company.id, status: STATUSES.ACTIVE } });
    const { message, ...rest } = await BillingService.subscribe({
      plan,
      company,
      method,
      budget,
      balance,
      directDebit,
      user,
      contactEmail,
      address,
      state,
      country,
      companyName,
      city,
      firstName,
      lastName,
      billingPeriod,
      BudgetService,
      ChargeService,
      BankService,
      ApprovalService,
    });
    return Response.success(res, message, { ...rest });
  },
  async freeTrial(req, res) {
    req.body = { ...req.body, ...req.params };
    BillingService.validatePayload(req.body, "freeTrial");

    const company = await Company.findOne({
      where: {
        code: req.body.company,
      },
    });
    if (!company) throw new NotFoundError("Company");

    const plan = await BillingService.getPaymentPlan(req.params.code, company.id);
    if (!plan) throw new NotFoundError("Payment plan");

    const { message, ...rest } = await BillingService.freeTrial({ ...req.body, company, plan, BudgetService });

    return Response.success(res, message, { ...rest });
  },

  async cancelSubscription(req, res) {
    req.body = { ...req.body, ...req.params };
    BillingService.validatePayload(req.body, "cancelSubscription");
    const { body, company } = req;
    await BillingService.cancelSubscription(body, company.id);
    return Response.success(res, "Subscription cancelled successfully");
  },

  async updateSubscription(req, res) {
    req.body = { ...req.body, ...req.params };
    BillingService.validatePayload(req.body, "updateSubscription");
    const { body, company } = req;
    await BillingService.updateSubscription(body, company.id, { BudgetService });
    return Response.success(res, "Subscription updated successfully");
  },

  async getSubscriptionDetails(req, res) {
    const { company } = req;
    const result = await BillingService.getSubscriptionDetails(company.id, { BudgetService });
    return Response.success(res, "Subscription details", Sanitizer.sanitizeSubscription(result));
  },

  async finalizeBilling(req, res) {
    const { body } = req;

    await BillingService.finalizeBilling(body);
    return Response.success(res, "Subscription finalized successfully");
  },

  async createCustomPlan(req, res) {
    req.body = { ...req.body, ...req.params };
    BillingService.validatePayload(req.body, "createCustomPlan");
    const { body } = req;
    await BillingService.createCustomPlan(body);
    return Response.success(res, "Custom plan created successfully");
  },

  async updateCustomPlan(req, res) {
    req.body = { ...req.body, ...req.params };
    BillingService.validatePayload(req.body, "updateCustomPlan");
    const { body } = req;
    await BillingService.updateCustomPlan(body);
    return Response.success(res, "Custom plan updated successfully");
  },

  async deleteCustomPlan(req, res) {
    req.body = { ...req.body, ...req.params };
    BillingService.validatePayload(req.body, "updateCustomPlan");
    const { body } = req;
    body.status = "deleted";
    await BillingService.updateCustomPlan(body);
    return Response.success(res, "Custom plan deleted successfully");
  },
};
