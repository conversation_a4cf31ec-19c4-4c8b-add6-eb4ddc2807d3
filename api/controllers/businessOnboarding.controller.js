/* eslint-disable default-case */
const ResponseService = require("../services/response");
const BusinessOnboardingService = require("../services/businessOnboarding.service");
const BalanceService = require("../services/balance");
const PhoneNumberRepo = require("../repositories/phonenumber.repo");
const ExistsError = require("../utils/exists-error");
const { BusinessOnboardingValidator } = require("../validators");
const { OnboardingInviteRepo, BalanceRepo } = require("../repositories/index.repo");
const { ValidationError, NotFoundError } = require("../utils/error.utils");
const Sanitizer = require("../utils/sanitizer");

module.exports = {
  async createBusinessInformation(req, res) {
    const payload = {
      ...req.body,
      id: req.user.id,
      company_code: req.company.code,
    };
    const { message, data } = await BusinessOnboardingService.createBusinessInformation(payload);
    return ResponseService.success(res, message, data);
  },

  async createBusinessAddress(req, res) {
    const payload = {
      ...req.body,
      id: req.user.id,
      company_code: req.company.code,
    };
    const { message, data } = await BusinessOnboardingService.createBusinessAddress(payload);
    return ResponseService.success(res, message, data);
  },

  async addBusinessDocs(req, res) {
    const type = req.body.type;
    const { document, ...body } = req.body;

    const {
      utilityBill,
      bnDocument,
      bvn,
      idType,
      idNumber,
      idCopy,
      cacITForm1,
      certificateOfTrustees,
      scumlCertificate,
      cac,
      bnNumber,
      cacBn1,
      rcNumber,
      incorporationCertificate,
      shareholdersDocument,
      companyRegistrationDate,
      memorandumOfAssociation,
      taxIdentificationNumber,
      cacForm3,
      cacForm2,
      cacForm7,
      cacStatusReport,
      cacITNumber,
      cacForm1,
    } = document;
    const payload = {
      ...body,
      id: req.user.id,
      company_code: req.company.code,
    };

    let response;
    switch (type) {
      case "enterprise":
        payload.document = { companyRegistrationDate };
        response = await BusinessOnboardingService.storeEnterpriseDocs(payload);
        break;
      case "partnership":
        payload.document = {
          bnNumber,
          companyRegistrationDate,
          bnDocument,
          bvn,
          idType,
          idNumber,
          idCopy,
          cacBn1,
          utilityBill,
        };
        response = await BusinessOnboardingService.storePartnershipDocs(payload);
        break;
      case "ngo":
        payload.document = {
          cacITForm1,
          companyRegistrationDate,
          scumlCertificate,
          cacITNumber,
          certificateOfTrustees,
          utilityBill,
        };
        response = await BusinessOnboardingService.storeNgoDocs(payload);
        break;
      case "sole proprietorship":
        payload.document = { cac, companyRegistrationDate, bnNumber, cacBn1, utilityBill };
        response = await BusinessOnboardingService.storeSoleProprietorshipDocs(payload);
        break;
      case "limited liability":
        payload.document = {
          rcNumber,
          incorporationCertificate,
          companyRegistrationDate,
          memorandumOfAssociation,
          taxIdentificationNumber,
          cacForm3,
          cacForm2,
          cacForm7,
          cacStatusReport,
          cacForm1,
          utilityBill,
        };
        response = await BusinessOnboardingService.storeLLCDocs(payload);
        break;
    }

    // CREATE BALANCE
    const balancePayload = {
      company: req.company.id,
      currency: "NGN",
      name: "Main balance",
      type: 1, // expenses
    };
    BalanceService.findOrCreate(balancePayload);

    const { message, data } = response;
    return ResponseService.success(res, message, data);
  },

  async getDocs(req, res) {
    // eslint-disable-next-line camelcase
    const { addDocs, company_code, addAddress } = req.query;
    const payload = {
      id: req.user.id,
      addDocs,
      // eslint-disable-next-line camelcase
      company_code,
      addAddress,
    };
    const { message, data } = await BusinessOnboardingService.getDocs(payload);
    return ResponseService.success(res, message, data);
  },

  async reviewDocs(req, res) {
    const { docsCode } = req.query;
    const payload = {
      id: req.user.id,
      company_code: req.company.code,
      docsCode,
    };
    const { message, data } = await BusinessOnboardingService.reviewDocs(payload);
    return ResponseService.success(res, message, data);
  },

  async rejectedDocs(req, res) {
    const payload = {
      id: req.user.id,
      company_code: req.company.code,
    };
    const { message, data } = await BusinessOnboardingService.rejectedDocs(payload);
    return ResponseService.success(res, message, data);
  },

  async docsResubmission(req, res) {
    const payload = {
      id: req.user.id,
      company_code: req.company.code,
      ...req.body,
    };
    const { message, data } = await BusinessOnboardingService.docsResubmission(payload);
    return ResponseService.success(res, message, data);
  },

  async saveDirector(req, res) {
    const { error } = BusinessOnboardingValidator.saveDirector.validate({
      ...req.body,
    });
    if (error) throw new ValidationError(error.message);

    const { message, data } = await BusinessOnboardingService.inviteSubmission({
      payload: req.body,
      company: req.company,
    });
    return ResponseService.success(res, message, data);
  },

  async inviteSubmission(req, res) {
    const { error } = BusinessOnboardingValidator.inviteSubmission.validate({ ...req.body }, { allowUnknown: true });
    if (error) throw new ValidationError(error.message);

    const invite = await OnboardingInviteRepo.getOnboardingInvite({
      queryParams: { code: req.body.invite },
    });
    if (!invite) throw new NotFoundError("Invite");

    const { message, data } = await BusinessOnboardingService.inviteSubmission({
      payload: req.body,
      company: invite.Company,
    });
    return ResponseService.success(res, message, Sanitizer.sanitizeCompany(data));
  },

  async reviewOnboardingDocument(req, res) {
    const { error } = BusinessOnboardingValidator.reviewOnboardingDocument.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const { message } = await BusinessOnboardingService.reviewOnboardingDocument(req.body);
    return ResponseService.success(res, message);
  },
};
