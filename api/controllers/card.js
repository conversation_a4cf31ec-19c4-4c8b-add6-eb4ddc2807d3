const NotFoundError = require("../utils/not-found-error");

const CardService = require("../services/card");
const Sanitizer = require("../utils/sanitizer");
const ResponseService = require("../services/response");
const ChargeService = require("../services/charge");
const NotificationService = require("../services/notification");
const { Budget: BudgetService, Beneficiary: BeneficiaryService } = require("../services");
const ValidationError = require("../utils/validation-error");
const { STATUSES } = require("../models/status");
// const HttpStatus = require("../utils/httpStatus.utils");
const Utils = require("../utils/utils");
const CompanyService = require("../services/company");
const SettingsService = require("../services/settings");
const company = require("./company");
const physicalCardValidator = require("../validators/physical-card.validator");
const CardRequestRepo = require("../repositories/cardRequest.repo");

module.exports = {
  async activateCard(req, res) {
    const { error } = physicalCardValidator.activate.validate(req.body);
    if (error) throw new ValidationError(error.message);
    const response = await CardService.activateCard({
      cardPan: req.body.cardPan,
      bvn: req.body.bvn,
      company: req.company,
      user: req.user,
      cardRequest: req.body.cardRequest,
    });
    ResponseService.success(res, response.message, response);
  },
  async addCard(req, res) {
    CardService.validateCardCreationPayload(req.body);
    let { budget: budgetCode, beneficiary: beneficiaryCode, type } = req.params;
    const { currency } = req.body;
    let amount = parseInt(req.body.amount, 10);
    let userBudget = null;
    let budget = null;
    if (!(budgetCode && beneficiaryCode)) {
      ({ budget: budgetCode, beneficiary: beneficiaryCode, type } = req.body);
    }

    if (budgetCode) {
      budget = await BudgetService.getBudget({
        code: budgetCode,
        company: req.company.id,
        status: STATUSES.ACTIVE,
      });
      if (!budget) throw new NotFoundError("Budget");
      budget = { id: budget.id, ...Sanitizer.sanitizeBudget(budget) };
      budget.amount = await BudgetService.getBudgetTransferableBalance(budget);
      if (budget.currency !== currency) throw new ValidationError(`Card currency is not the same as budget currency`);
    }

    if (beneficiaryCode) {
      const beneficiary = await BeneficiaryService.getByCode(beneficiaryCode);

      if (beneficiary.status !== STATUSES.ACTIVE || beneficiary.User.status !== STATUSES.ACTIVE) {
        throw new ValidationError("Cannot generate card for this beneficiary");
      }
      if (beneficiaryCode.startsWith("bnf_")) userBudget = await BudgetService.getUserBudgetByBudgetCode(budgetCode, beneficiary.User.id);
      else userBudget = await BudgetService.getUserBudgetByCode(beneficiaryCode);

      if (userBudget.status !== STATUSES.ACTIVE) {
        throw new ValidationError("Cannot generate card for inactive/blocked beneficiary");
      }

      amount = amount || userBudget.amount;
    }

    const hasEnoughAllocatedAmount = (userBudget && userBudget.amount && userBudget.amount >= amount) || (budget && budget.amount >= amount);

    if (!hasEnoughAllocatedAmount) {
      throw new ValidationError("Insufficient fund, please update it or request a top up");
    }

    let method;
    let cardPayload;
    if (type === "virtual") {
      method = "createVirtualCard";
      cardPayload = await CardService.getVirtualCardPayload({
        company: req.company.id,
        disposable: false,
        user: beneficiaryCode && userBudget && userBudget.user,
        currency: req.body.currency,
        amount,
      });
    } else {
      method = "createPhysicalCard";
    }
    try {
      await CardService.topUpWallet(req.company.id, amount, req.body.currency, "mono");
    } catch (error) {
      console.error(error);
    }
    const card = await CardService.preSaveCreditCard({
      externalIdentifier: null,
      company: req.company.id,
      user: (userBudget && userBudget.user) || req.user.id,
      budget: budget && budget.id,
      type: type === "virtual" ? 0 : 1,
      name: req.body.name,
      issuer: 1,
      currency: req.body.currency,
      status: STATUSES.PROCESSING,
      amount: 0,
    });
    ResponseService.success(res, "Card creation is being processed, you will be notified when it is created", {
      code: card.code,
    });
    // noinspection JSCheckFunctionSignatures
    const { error, message = "Card creation failed, please retry later or reach out to us", body } = await CardService[method](cardPayload);

    if (error) {
      const payload = {
        firstName: req.user.firstName,
        company: req.company.name,
        failed: true,
      };
      NotificationService.notifyUser({ email: SettingsService.get("notification_email") }, "failed-card", payload, {
        subject: `We could not create card for ${req.user.firstName}(${req.user.code})/${req.company.name}[${req.company.code}]`,
      });
      throw new Error("Card creation error");
    }
    CardService.updateCard({ where: { code: card.code } }, { externalIdentifier: body.id });
    const { Users: companyAdmins } = await CompanyService.getCompanyWithAdmins({
      id: req.company.id,
    });
    Array.from(companyAdmins).forEach((admin) => {
      const notificationPayload = {
        company: req.company.id,
        user_id: admin.id,
        type: `info`,
        badge: `info`,
        title: `Card Request`,
        message: `${Utils.toTitle(req.user.firstName)} has requested for a virtual card`,
        table: {
          code: card.code,
          entity: "CreditCard",
        },
        body: {
          code: card.code,
          entity: "CreditCard",
        },
        event: "cardRequest",
      };
      NotificationService.saveNotification(notificationPayload);
    });
  },

  async requestCard(req, res) {
    CardService.validateCardCreationPayload(req.body);
    const { budget: code, beneficiary: beneficiaryCode } = req.params;
    const beneficiary = await BeneficiaryService.getByCode(beneficiaryCode);

    if (beneficiary.status !== STATUSES.ACTIVE || beneficiary.User.status !== STATUSES.ACTIVE) {
      throw new ValidationError("Cannot issue card for this beneficiary");
    }
    let userBudget;
    if (beneficiaryCode.startsWith("bnf_")) userBudget = await BudgetService.getUserBudgetByBudgetCode(code, beneficiary.User.id);
    else userBudget = await BudgetService.getUserBudgetByBudgetCode(beneficiaryCode);
    if (userBudget.status !== STATUSES.ACTIVE || userBudget.amount <= 0) {
      throw new ValidationError("Cannot issue card for this beneficiary");
    }
    const amount = userBudget.amount;
    const {
      error: virtualAccountError,
      message: virtualAccountErrorMessage,
      body: { id: accountId },
    } = await CardService.createVirtualCard();
    // noinspection JSCheckFunctionSignatures
    const { error, message = "Card creation failed, please retry later or reach out to us" } = await CardService.createVirtualAccount({
      accountId,
      user: userBudget.User,
      currency: userBudget.Budget.currency,
      ...req.body,
    });
    if (error) return ResponseService.failure(res, message);
    return ResponseService.success(res, "Card issuance is being processed");
  },

  async removeCard(req, res) {
    const { code } = req.params;
    const card = await CardService.getCard({ code, user: req.user.id });
    await CardService.deleteCard(req.user, card);
    await BudgetService.createBudgetLedger({
      currency: card.currency,
      user: beneficiary.User.id,
      amount: card.amount - card.spent,
      budget: card.budget,
      description: `Card deletion`,
      status: STATUSES.PENDING,
    });
    return ResponseService.success(res, "Card deleted");
  },

  async fundCard(req, res) {
    const { code, amount, budget } = req.body;
    const { externalIdentifier: token, CardIssuer: issuer } = await CardService.validateFunding(code, amount, budget);
    CardService.fundCard(token, amount, (issuer && issuer.name.toLowerCase()) || undefined);
    return ResponseService.success(res, "Charge is being processed");
  },

  async chargeCard(req, res) {
    const { code, amount, budget = null } = req.body;
    const {
      card: { token },
    } = await CardService.validateCharge(code, amount);
    CardService.chargeCard(token, amount, budget);
    return ResponseService.success(res, "Charge is being processed");
  },

  async checkTransactionStatus(req, res) {
    const { reference } = req.params;
    const { id: user } = req.user;
    const { code: statusCode, status, message } = await ChargeService.getTransactionStatus(user, reference);
    return ResponseService.json(res, { statusCode, data: { status, message } });
  },

  async getCard(req, res) {
    const { code } = req.params;
    const { id: company } = req.company;
    const criteria = { code, company };
    if (req.isEmployeeRole) criteria.user = req.user.id;
    const card = await CardService.getCard(criteria);
    if (!card) throw new NotFoundError("Card not found");

    const decryptedCard = await CardService.decryptCard(card, !!card.pan);
    return ResponseService.success(res, "Card retrieved", Sanitizer.sanitizeCreditCard(decryptedCard));
  },

  async getCards(req, res) {
    const { id: user } = req.user;
    const { id: company } = req.company;
    const criteria = {
      company,
      ...req.query,
    };
    if (req.isMobile) {
      criteria.user = user;
    }
    if (req.isEmployeeRole) criteria.user = user;
    const { cards, meta } = await CardService.fetchCards(criteria);
    const decryptedCards = await Promise.all(
      cards.map(async (card) => {
        return CardService.decryptCard(card, !!card.pan);
      })
    );
    return ResponseService.success(res, "Cards retrieved", {
      cards: Sanitizer.sanitizeCreditCards(decryptedCards),
      meta,
    });
  },

  async freezeCard(req, res) {
    const { code } = req.params;
    const { id: owner } = req.user;
    const card = await CardService.freezeCard(code, owner, "virtual");
    if (!card) throw new NotFoundError("Virtual card not found");
    return ResponseService.success(res, "Virtual card frozen", Sanitizer.sanitizeCreditCard(card));
  },

  async liquidateCard(req, res) {
    const { code } = req.params;
    const { amount } = req.body;
    const { id: user } = req.user;
    const criteria = {
      code,
      company: req.company.id,
    };
    if (company.isEmployeeRole) {
      criteria.user = req.user.id;
    }
    const card = await CardService.getCard(criteria);
    if (![STATUSES.ACTIVE, STATUSES.FREEZE].includes((card.Status && card.Status.id) || card.status)) {
      throw new ValidationError("Card should either be active or frozen to be liquidated");
    }
    const remainingAmount = amount || card.amount - card.spent;
    criteria.externalIdentifier = card.externalIdentifier;
    await CardService.liquidateCard(criteria, amount && remainingAmount);
    await BudgetService.createBudgetLedger({
      currency: card.currency,
      user,
      amount: remainingAmount,
      budget: card.budget,
      description: `Card liquidation`,
      status: STATUSES.PENDING,
    });
    return ResponseService.success(res, `${card.currency}${(remainingAmount / 100).toLocaleString()} removed from the card`);
  },

  async getCardDefaultPin(req, res) {
    const {
      user: { id: user },
      company: { id: company },
    } = req;
    const { code } = req.params;
    const criteria = { code, company };
    if (req.isEmployeeRole) criteria.user = user;
    const response = await CardService.getCardDefaultPin({ criteria });
    ResponseService.success(res, "Card default pin fetched", response);
  },
};
