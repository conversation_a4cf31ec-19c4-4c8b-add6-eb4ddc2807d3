const ResponseService = require("../services/response");
const { CardRequest: CardRequestService } = require("../services");
const ValidationError = require("../utils/validation-error");
const cardRequestController = require("../validators/cardRequest.validator");
const Sanitizer = require("../utils/sanitizer");
const ApprovalService = require("../services/approval.service");
const BalanceService = require("../services/balance");
const BillingService = require("../services/billing");
const BudgetService = require("../services/budget");
const HelperService = require("../services/helper.service");
const RedisService = require("../services/redis");
const BankService = require("../services/bank");
const Utils = require("../utils");
const { CompanyRepo, TeamMemberRepo, CardRequestRepo } = require("../repositories/index.repo");
const { NotFoundError } = require("../utils/error.utils");
const { STATUSES } = require("../models/status");
const { METHODS } = require("../mocks/constants.mock");

module.exports = {
  async createCardRequest(req, res) {
    const { error } = cardRequestController.createCardRequest.validate(req.body);
    if (error) throw new ValidationError(error.message);
    const response = await CardRequestService.createCardRequest({
      ...req.body,
      user: req.user,
      company: req.company,
      ApprovalService,
      BudgetService,
      BalanceService,
    });
    ResponseService.success(res, "Card request created successfully", Sanitizer.sanitizeCardRequest(response));
  },

  async list(req, res) {
    const {
      company: { id: company },
      user: { id: user },
    } = req;
    const criteria = req.query;
    if (req.isEmployeeRole) criteria.owner = user;

    if (criteria.status) {
      criteria.status = Utils.getStatusValues(criteria.status);
    }

    const { meta, cardRequests } = await CardRequestService.getAllCardRequestsByUser({
      ...criteria,
      company,
    });

    ResponseService.success(res, "Card requests fetched successfully", {
      meta,
      cardRequests: Sanitizer.sanitizeCardRequests(cardRequests),
    });
  },

  async getOneCardRequestByCode(req, res) {
    const response = await CardRequestService.getOneCardRequestByCode({
      cardCode: req.params.code,
    });
    ResponseService.success(res, "Card request fetched successfully", Sanitizer.sanitizeCardRequest(response));
  },

  async deleteCardRequest(req, res) {
    const {
      company: { id: company },
      params: { code },
      user: { id: user },
    } = req;

    const filter = { code, company };

    if (req.isEmployeeRole) filter.owner = user;

    const { message } = await CardRequestService.deleteCardRequest({
      filter,
    });

    ResponseService.success(res, message);
  },

  // eslint-disable-next-line consistent-return
  async processCardRequestCharge(req, res) {
    const {
      params: { code },
    } = req;

    ResponseService.success(res, "OK"); // Send OK to SQS

    return CardRequestService.processCardRequestCharge({ code, BalanceService, BudgetService, BankService, BillingService, ApprovalService });
  },

  async finalizeCardRequestCharge(req, res) {
    const {
      data: { cardRequest },
    } = req.body;

    const foundCardRequest = await CardRequestRepo.findOne({
      conditions: { code: cardRequest },
      addCountry: true,
      addCompany: true,
    });

    if (!foundCardRequest) throw new NotFoundError("Card Request");

    // Notify support;
    const { Team: team } = (await TeamMemberRepo.getTeamMember({ user: foundCardRequest.requestedBy })) || {};

    const address = {
      billingAddress: foundCardRequest.billingAddress,
      city: foundCardRequest.city,
      state: foundCardRequest.state,
      postalCode: foundCardRequest.postalCode,
      country: foundCardRequest.Country?.name,
    };

    CardRequestService.notifySupport({
      company: foundCardRequest.Company,
      address,
      country: foundCardRequest.Country,
      user: foundCardRequest.cardRequester,
      team,
      foundCardRequest,
    });

    ResponseService.success(res, "OK"); // Send OK to SQS
  },

  async completeApproval(req, res) {
    // Respond with 200 to SQS
    ResponseService.success(res, "OK");

    // Handle approval completion
    const {
      data: { id, status },
    } = req.body;
    // Add a key in Redis to prevent multile call
    const redisKey = `cardRequest_complete_approval:${id}`;
    const redisValue = Utils.parseJSON(await RedisService.get(redisKey)) || null;
    if (redisValue) return true; // Already handled

    await CardRequestService.completeCardRequest({ id, status });

    return RedisService.setex(redisKey, 1, 1 * 60 * 60); // Keep in Redis for One hour
  },
};
