const { STATUSES } = require("../models/status");
const { Category } = require("../models");
const CategoryService = require("../services/category.service");
const ResponseService = require("../services/response");
const Sanitizer = require("../utils/sanitizer");
const { ExistsError, NotFoundError, ValidationError } = require("../utils/error.utils");

let SYSTEM_CATEGORIES = null;

const Service = {
  /**
   * Add categories
   * POST /categories
   * @param  {string} req.body.name
   * @param  {string} req.body.description
   * @param  {string} req.body.slug
   * @param  {} res
   */
  async createCategory(req, res) {
    CategoryService.validateCreationPayload(req.body);
    const payload = {
      ...req.body,
      company: req.company.id,
    };
    const foundCategory = await CategoryService.getCategory({ ...(payload.name && { name: payload.name }), company: req.company.id });
    if (foundCategory) {
      throw new ExistsError("Category");
    }

    if (payload.parent) {
      let foundParentCategory = await CategoryService.getCategory({ code: payload.parent, company: req.company.id });
      if (!foundParentCategory) throw new NotFoundError("Parent category");
      if (!foundParentCategory.company) foundParentCategory = await CategoryService.cloneCategory(payload.parent, req.company.id);
      payload.parent = foundParentCategory.id;
    }

    const newCategory = await CategoryService.createCategory(payload);
    return ResponseService.success(res, "Category added", { category: Sanitizer.sanitizeCategory(newCategory) });
  },
  /**
   * List categories
   * GET /categories
   * @param {*} req.query.name
   * @param {*} req.query.status
   * @param {*} res
   * @returns
   */
  async listCategories(req, res) {
    const { status } = req.query;
    const filter = {
      company: req.company.id,
      status: STATUSES.ACTIVE,
      paginate: true,
      ...req.query,
      ...(status && { status: STATUSES[String(status).toUpperCase()] }),
    };
    const { categories, meta } = await CategoryService.listCategories(filter);
    return ResponseService.success(res, "Categories successfully retrieved", {
      categories: Sanitizer.sanitizeCategories(categories),
      meta,
    });
  },
  /**
   * View a category
   * GET /categories/:code
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async getCategory(req, res) {
    const { code } = req.params;
    const filter = {
      code,
      company: req.company.id,
    };
    const categories = await CategoryService.getCategory(filter);
    return ResponseService.success(res, "Category successfully retrieved", {
      categories: Sanitizer.recursiveSanitizeCategory(categories),
    });
  },
  /**
   * Update a category
   * PUT /categories/:code
   * @param {*} req.body.name
   * @param {*} req.body.description
   * @param {*} req.body.slug
   * @returns
   */
  async updateCategory(req, res) {
    const payload = {
      ...req.body,
      ...(req.body.status && { status: STATUSES[String(req.body.status).toUpperCase()] }),
    };
    const { code } = req.params;
    const { id: company } = req.company;
    const criteria = {
      code,
      company,
    };

    const existingCategory = await CategoryService.getCategory({ code });

    if (existingCategory) criteria.id = existingCategory.id;

    let foundParentCategory;
    if (payload.parent) {
      foundParentCategory = await CategoryService.getCategory({ code: payload.parent, company: req.company.id });
      if (!foundParentCategory) throw new NotFoundError("Parent category");

      if (existingCategory?.code === foundParentCategory.code) throw new ValidationError("Self referencing a category is not allowed");
    }

    if (existingCategory && existingCategory.company !== company) {
      const { id, code, ...rest } = existingCategory.toJSON();

      const data = {
        ...rest,
        ...payload,
        company,
      };
      CategoryService.validateCreationPayload(data);
      if (foundParentCategory?.id) {
        payload.parent = foundParentCategory.id;
      }
      await CategoryService.createCategory(data);
    } else {
      CategoryService.validateUpdatePayload(payload);
      if (foundParentCategory?.id) {
        payload.parent = foundParentCategory.id;
      }
      await CategoryService.updateCategory(criteria, payload, existingCategory);
    }
    return ResponseService.success(res, "Category successfully updated");
  },
  /**
   * Delete a category
   * DELETE /categories/:code
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async deleteCategory(req, res) {
    const payload = {
      status: STATUSES.DELETED,
    };
    const criteria = {
      code: req.params.code,
      company: req.company.id,
    };

    CategoryService.validateUpdatePayload(payload);

    const existingCategory = await CategoryService.getCategory({ code: req.params.code });

    if (existingCategory) criteria.id = existingCategory.id;

    await CategoryService.updateCategory(criteria, payload);
    return ResponseService.success(res, "Category successfully deleted");
  },

  async massDeleteCategories(req, res) {
    CategoryService.validateMassDeletePayload(req.body);

    await CategoryService.massDeleteCategories(req.body);
    return ResponseService.success(res, "Categories successfully deleted");
  },

  async getCategoriesWithTransactions(req, res) {
    const {
      company: { id: company },
    } = req;
    const filters = {
      company,
      status: STATUSES.ACTIVE,
      ...req.query,
    };
    const { categories, meta } = await CategoryService.getCategoriesWithTransactions(filters);
    return ResponseService.success(res, "Categories with transactions successfully retrieved", {
      categories: Sanitizer.sanitizeCategories(categories),
      meta,
    });
  },

  async analyticsCategory(req, res) {
    const criteria = {
      company: req.company.id,
      from: req.query.from,
      to: req.query.to,
    };
    const { message, data } = await CategoryService.analyticsCategory(criteria);
    const { categoriesedAnalytics, highestTransactionVolume, highestTransactionCount, ...response } = data;

    return ResponseService.success(res, message, {
      ...response,
      highestTransactionCount: Sanitizer.sanitizeCategoryTransactionForAnalytics(highestTransactionCount),
      highestTransactionVolume: Sanitizer.sanitizeCategoryTransactionForAnalytics(highestTransactionVolume),
      analytics: Sanitizer.sanitizeCategoryTransactionsForAnalytics(categoriesedAnalytics),
    });
  },

  async analyticsBreakDownForCategory(req, res) {
    const criteria = {
      company: req.company.id,
      from: req.query.from,
      to: req.query.to || new Date().toISOString(),
    };

    const result = await CategoryService.analyticsBreakDownForCategory({ ...criteria, category: req.params.code });

    return ResponseService.success(res, "Category analytics retrieved successfully", result);
  },

  async createCategorizationRule(req, res) {
    const criteria = {
      user: req.user.id,
      company: req.company.id,
    };
    req.body = { ...req.body, category: req.params.code };
    const { value } = CategoryService.validateCategorizationRulePayload(req.body, "create");
    const { error, data } = (await CategoryService.createCategorizationRule({ ...value, ...criteria })) || {};

    if (error) {
      return ResponseService.json(res, {
        statusCode: 409,
        error: true,
        status: false,
        message: error,
        data: Sanitizer.sanitizeCategorizationRules(data.map(({ rule }) => rule)),
      });
    }
    return ResponseService.success(res, "Categorization rule created successfully");
  },

  async updateCategorizationRule(req, res) {
    const criteria = {
      user: req.user.id,
      company: req.company.id,
    };
    req.body = { ...req.body, category: req.params.code, rule: req.params.rule };
    const { value } = CategoryService.validateCategorizationRulePayload(req.body, "update");
    const { error, data } = (await CategoryService.updateCategorizationRule({ ...value, ...criteria })) || {};
    if (error) {
      return ResponseService.json(res, {
        statusCode: 409,
        error: true,
        status: false,
        message: error,
        data: Sanitizer.sanitizeCategorizationRules(data.map(({ rule }) => rule)),
      });
    }
    return ResponseService.success(res, "Categorization rule updated successfully");
  },

  async listCategorizationRules(req, res) {
    const criteria = {
      company: req.company.id,
      category: req.params.code,
      ...req.query,
    };

    CategoryService.validateCategorizationRulePayload(req.query, "listCategorizationRules");
    const { categorizationRules, meta } = await CategoryService.listCategorizationRules(criteria);

    return ResponseService.success(res, "Categorization rules retrieved successfully", {
      categorizationRules: Sanitizer.sanitizeCategorizationRules(categorizationRules),
      meta,
    });
  },

  async getACategorizationRule(req, res) {
    const criteria = { category: req.params.code, rule: req.params.rule };

    CategoryService.validateCategorizationRulePayload(criteria, "getACategory");

    const foundCategorizationRule = await CategoryService.getACategorizationRule({ ...criteria, company: req.company.id });

    return ResponseService.success(res, "Categorization rule retrieved successfully", Sanitizer.sanitizeCategorizationRule(foundCategorizationRule));
  },

  async deleteCategorizationRule(req, res) {
    const criteria = { category: req.params.code, rule: req.params.rule };

    CategoryService.validateCategorizationRulePayload(criteria, "getACategory");

    await CategoryService.deleteCategorizationRule({ ...criteria, company: req.company.id });

    return ResponseService.success(res, "Categorization rule deleted successfully");
  },

  async bulkCreateCategories(req, res) {
    CategoryService.validatePayload(req.body, "bulkCreate");

    await CategoryService.bulkCreate({ ...req.body, company: req.company.id });

    return ResponseService.success(res, "Categories created successfully");
  },

  async bulkUpdateCategories(req, res) {
    CategoryService.validatePayload(req.body, "bulkUpdate");

    await CategoryService.bulkUpdate({ ...req.body, company: req.company.id });

    return ResponseService.success(res, "Categories updated successfully");
  },

  async handleSystemCategory(req, res, next) {
    const { code } = req.params;
    const isDeleting = req.method === "DELETE" || req.originalUrl.includes("mass-delete");
    const isUpdating = req.method === "PUT";
    const { categories } = req.body;

    if (!SYSTEM_CATEGORIES) {
      const systemCategories = await Category.findAll({ where: { company: null } });
      SYSTEM_CATEGORIES = systemCategories.reduce((acc, category) => {
        acc[category.code] = category;
        return acc;
      }, {});
    }

    if (categories) {
      if (isDeleting) {
        if (req.body.categories.some(({ code: toModifyCode }) => !!SYSTEM_CATEGORIES[toModifyCode]));
        return res.status(400).json({ message: "You cannot delete default categories, kindly remove them from the list", error: true });
      }
      if (isUpdating) {
        const clashingCategories = categories.filter(({ code: clashingCode }) => !!SYSTEM_CATEGORIES[clashingCode]);
        const nonClashingCategories = categories.filter(({ code: nonClashingCode }) => !SYSTEM_CATEGORIES[nonClashingCode]);

        if (!clashingCategories.length) return next();

        const clonedCategories = await Promise.all(
          clashingCategories.map(async (category) => {
            const clonedCategory = await CategoryService.cloneCategory(category.code, req.company.id);
            return {
              ...category,
              code: clonedCategory.code,
            };
          })
        );
        req.body.categories = [...nonClashingCategories, ...clonedCategories];
      }
    }

    if (!code) {
      return next();
    }
    if (SYSTEM_CATEGORIES[code] && !isDeleting) {
      const clonedCategory = await CategoryService.cloneCategory(code, req.company.id);
      req.params.code = clonedCategory.code;
      return next();
    }
    return next();
  },
};

module.exports = Service;
