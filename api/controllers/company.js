const { Op } = require("sequelize");
const Sanitizer = require("../utils/sanitizer");
const BankService = require("../services/bank");
const CompanyService = require("../services/company");
const ResponseService = require("../services/response");
const RedisService = require("../services/redis");
const TransferService = require("../services/transfer");
const { STATUSES } = require("../models/status");
const NotificationService = require("../services/notification");
const SettingsService = require("../services/settings");
const AnalyticService = require("../services/analytics");
const CardService = require("../services/card");
const Mono = require("../services/mono");
const BalanceService = require("../services/balance");
const Utils = require("../utils");
const Sanitize = require("../utils/sanitizer");
const { ValidationError, NotFoundError, ForbiddenError } = require("../utils/error.utils");
const BankAccountRepo = require("../repositories/bankAccount.repo");
const { BalanceRepo, DocumentRepo, BeneficiaryRepo, CompanyRepo, UserRepo } = require("../repositories/index.repo");
const { CompanyValidator } = require("../validators");
const { paginate } = require("./utils");
const { CARD_ISSUER } = require("../models/cardissuer");
const WrapperService = require("../services/wrapper.service");
const AssetService = require("../services/asset");
const BalanceTenureService = require("../services/balanceTenureService");
const BeneficiaryService = require("../services/beneficiary.service");
const HelperService = require("../services/helper.service");
const BackgroundService = require("../services/backgroundService");
const { BACKGROUND } = require("../mocks/constants.mock");

module.exports = {
  async stats(req, res) {
    const {
      company: { id: company },
      query: filters = { currency: "NGN" },
    } = req;
    const statistics = await AnalyticService.getCompanyStats(company, req.user.id, filters);
    return ResponseService.success(res, "Company stats", statistics);
  },

  async moneyMovements(req, res) {
    const {
      company: { id: company },
      query: filters = { currency: "NGN" },
    } = req;
    const charts = await AnalyticService.getMoneyMovementCharts(company, filters);
    return ResponseService.success(res, "Money movements charts", charts);
  },

  async view(req, res) {
    const {
      params: { code },
      user: { role = undefined },
      query: { includeDocuments = false, includeSteps = false },
    } = req;

    const foundCompany = await CompanyService.getCompanyByCriteria({
      criteria: { code },
      ...(includeDocuments === "true" && { includeDocuments: true }),
    });

    if (includeSteps) {
      foundCompany.steps = await HelperService.buildCompanyOnboardingSteps({ company: foundCompany.id });
    }

    const { balances: bankAccounts = [] } = await BalanceTenureService.getAvailableBalances(req.company.id, { page: 1, ...req.query });
    let logoUrl = null;
    if (foundCompany.Asset) {
      logoUrl = await AssetService.getAssetDownloadURL(foundCompany.Asset);
    }
    return ResponseService.success(res, "Company retrieved", {
      ...Sanitize.sanitizeCompanyBaseOnRole(foundCompany, role),
      ...(logoUrl && { logoUrl }),
      accounts: Sanitizer.sanitizeBalancesAccounts(bankAccounts),
      ...(Boolean(includeDocuments) && { documents: foundCompany.documents }),
      ...(Boolean(includeSteps) && { onboardingSteps: foundCompany.steps }),
    });
  },

  async update(req, res) {
    const { code } = req.params;

    const { error } = CompanyValidator.update.validate(req.body);
    if (error) throw new ValidationError(error.message);

    // Check if payment plan and if it's staging
    if (req.body.paymentPlan && Utils.isProd()) throw new ValidationError("Cannot update payment plan");
    const { message } = await CompanyService.update({ filter: { code }, payload: req.body });
    return ResponseService.success(res, message);
  },

  async getVirtualAccount(req, res) {
    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[req.company.code] || payment.defaultProvider;
    try {
      const { bankAccount: virtualAccount, balance } = await BankService.getVirtualAccount(req.company.id, providerToUse, {
        user: req.user,
        isManager: req.isManager,
        BalanceTenureService,
      });
      return ResponseService.success(res, "Account created successfully", {
        ...Sanitizer.sanitizeBankAccount(virtualAccount),
        balance: Sanitizer.sanitizeNewBalance(balance),
      });
    } catch (error) {
      // eslint-disable-next-line default-case
      switch (error.message) {
        case "Bank Account not found":
        case "No account holder found":
          // eslint-disable-next-line no-case-declarations
          const {
            error: providerError,
            message,
            account: virtualAccount,
          } = await BankService.generateVirtualAccount({
            company: req.company.id,
            user: req.user.id,
            companyCode: req.company.code,
            provider: providerToUse,
            balanceType: "Expenses",
          });
          if (providerError) return ResponseService.failure(res, message);

          if (providerToUse === "anchor") {
            return ResponseService.success(res, "Your account is being generated");
          }

          if (message === "Your account is being generated") return ResponseService.success(res, message);

          return ResponseService.success(res, "Account created successfully", Sanitizer.sanitizeBankAccount(virtualAccount));
      }
      throw error;
    }
  },

  async reviewCompany(req, res) {
    const { error: payloadValidationError } = CompanyValidator.reviewCompany.validate(req.body);
    if (payloadValidationError) throw new ValidationError(payloadValidationError.message);

    const { decision, reason, company, document } = req.body;
    const payload = {
      message: "fully onboarded",
      failed: false,
    };
    const sourceConfig = {
      subject: `Onboarding Completed Notification`,
      from: "Bujeti Onboarding",
    };
    const destination = {
      email: SettingsService.get("customer_experience_email"),
    };
    const template = "company-verification-notification";
    try {
      const { success, data, message } = await CompanyService.reviewCompany({ decision, reason, company, document });
      const {
        admins,
        company: { name, code },
      } = data;

      sourceConfig.subject += `(${name})`;
      NotificationService.notifyUser(destination, template, { ...payload, name, code, success }, sourceConfig);
      admins.forEach((admin) => {
        NotificationService.notifyUser(
          { email: admin.email },
          "company-onboarding-start",
          { firstName: admin.firstName, companyName: name },
          {
            subject: `Good news ${admin.firstName}! We just started reviewing your submission, you are almost set`,
            from: "Bujeti Onboarding",
          }
        );
      });
      ResponseService.success(res, message);
    } catch (error) {
      const foundCompany = await CompanyService.getCompany({ code: company });
      if (!foundCompany) throw new NotFoundError(`Company(${company})`);
      payload.failed = true;
      payload.message = "declined onboarding";
      sourceConfig.subject += `(${foundCompany.name})`;
      NotificationService.notifyUser(
        destination,
        template,
        {
          ...payload,
          name: foundCompany.name,
          code: company,
          description: error.message,
        },
        sourceConfig
      );
      throw error;
    }
  },

  async fund(req, res) {
    if (Utils.isProd()) throw new ValidationError("Invalid request");
    const { amount, currency } = req.body;
    const { error, message, body } = await CardService.fundBalance(amount, currency);
    if (error) return ResponseService.success(res, message);
    return ResponseService.success(res, "Redirect to link to conclude funding", body);
  },

  async fundBank(req, res) {
    if (Utils.isProd()) throw new ValidationError("Invalid request");
    const { code } = req.params;
    const { id: company } = await CompanyService.getCompany({ code });
    const { amount, bank } = req.body;
    const { error, message, body } = await BankService.fundBankAccount({
      company,
      amount,
      bank,
    });
    if (error) {
      return ResponseService.success(res, message);
    }
    return ResponseService.success(res, "Redirect to link to conclude funding", body);
  },

  /**
   * List balance history for a company
   * @param {*} req
   * @param {*} res
   */
  async getBalanceHistory(req, res) {
    const { code, currency } = req.params;
    const foundCompany = await CompanyService.getCompanyByCriteria({ criteria: { code } });
    if (!foundCompany) throw new NotFoundError("Company");

    const ledgers = await CompanyService.getBalanceHistory(foundCompany.id, currency);
    const availableBalance = await BalanceRepo.getAvailableBalance({
      company: foundCompany.id,
      currency,
    });
    const data = {
      name: foundCompany.name,
      code: foundCompany.code,
      description: foundCompany.description,
      currency,
      currentBalance: availableBalance / 100,
      balanceHistory: Sanitizer.sanitizeBalanceLedgers(ledgers),
    };
    return ResponseService.success(res, "Balance history retrieved", data);
  },

  /**
   * Handler to reconcile a linked account to the right company on our side
   * @param {*} req
   * @param {*} res
   * @returns
   */

  async linkAndReconcileAccount(req, res) {
    const { code, accountCode } = req.params;
    if (req.company.code !== code) throw new ValidationError("Action cannot be carried out");
    const data = await Mono.reconcileLinkedAccount(accountCode, req.company);
    return ResponseService.success(res, "Account successfully linked", data);
  },

  /**
   * Generate payment link for direct debit
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async generatePaymentLink(req, res) {
    const { account, currency, amount } = req.body;
    const bankAccount = await BankAccountRepo.getOneBankAccount({
      queryParams: {
        code: account,
        owner: req.company.id,
        ownerType: "company",
        type: "real",
        status: STATUSES.ACTIVE,
        subType: "linked",
      },
      selectOptions: ["externalIdentifier"],
    });
    if (!bankAccount) throw new NotFoundError("Account");
    const reference = Utils.generateRandomString(17);
    RedisService.set(`directdebit:${reference}:${currency}:${amount}`, req.company.id);

    const {
      body: { payment_link: paymentLink },
      error,
    } = await Mono.generatePaymentLink(req.company, {
      account: bankAccount.externalIdentifier,
      currency,
      amount,
      description: "Funding funding",
      type: "onetime-debit",
      reference,
      redirect_url: `${Utils.getDashboardURL()}?funding=true`,
      // redirect_url: `http://localhost:3000?funding=true`,
      meta: { reference },
    });
    if (error) throw new ValidationError("We couldn't get you a payment link, please retry later");
    return ResponseService.success(res, "Use this payment link to pay", {
      paymentLink,
    });
  },

  /**
   * Verify a payment after it is made on mono connect
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async verifyPayment(req, res) {
    const { reference } = req.params;
    const { body, error } = await Mono.verifyPayment(req.company, reference);
    if (error) throw new ValidationError("Transaction could not be confirmed");
    const { status, amount, currency } = body;
    const redisKey = `directdebit:${reference}:${currency}:${amount}`;
    const transactionPending = await RedisService.get(redisKey);
    if (status === "successful") {
      if (!transactionPending) return ResponseService.failure(res, "Funding already processed");
      // record funding
      const { description, id: transferReference, fee: processorFee } = body;
      // create a transfer record maybe
      const transfer = await TransferService.createTransfer({
        amount,
        description,
        currency,
        status: STATUSES.PROCESSED,
        company: req.company.id,
        reference,
        processor_fee: processorFee,
        bujeti_fee: 0,
        narration: `Direct Debit(${transferReference})`,
      });
      const balance = await BalanceRepo.findOrCreate({
        company: req.company.id,
        currency,
      });
      await BalanceService.createLedger({
        amount,
        description,
        currency,
        balance: balance.id,
        status: STATUSES.PENDING,
        company: req.company.id,
        transfer: transfer.id,
      });
      RedisService.delete(redisKey);
      return ResponseService.success(res, "Funding successfull", {
        status: "success",
        amount,
        currency,
      });
    }
    RedisService.delete(redisKey);
    return ResponseService.failure(res, "Account could not be funded", {
      status: "failed",
      amount,
      currency,
    });
  },

  /**
   * Get an authorization token if the account needs to be reauthorized
   * @param {*} req
   * @param {*} res
   * @returns {Object} { token }
   */
  async getBankReauthorization(req, res) {
    const { code, bankAccountCode } = req.params;
    if (req.company.code !== code) throw new ForbiddenError();

    const bankAccount = await BankAccountRepo.getOneBankAccount({
      queryParams: {
        code: bankAccountCode,
        owner: req.company.id,
        ownerType: "compnay",
        type: "real",
        status: STATUSES.ACTIVE,
        subType: "linked",
      },
      selectOptions: ["externalIdentifier"],
    });
    if (!bankAccount) throw new NotFoundError("Bank Account");
    const { body, error } = await Mono.getReauthorizationToken(req.company, bankAccount.externalIdentifier);
    if (error) throw new ValidationError("Could not reauthenticate your account");
    return ResponseService.success(res, "Reauthentication code", body);
  },

  async generateStatement(req, res) {
    const { id } = req.company;
    let { startDate, endDate, accountType, account, statusType, page, perPage } = req.query;
    let result;
    if (!startDate) startDate = new Date("1990-01-01 00:00:00").toISOString();
    if (!endDate) endDate = new Date().toISOString();

    const paginator = paginate(req);
    if (!["expense", "revenue"].includes(account)) {
      throw new ValidationError("Invalid account selected");
    }
    if (account === "expense") {
      result = await CompanyService.expenseAccountStatement({
        start_date: startDate,
        end_date: endDate,
        account_type: accountType,
        status_type: statusType,
        companyId: id,
        paginate: paginator,
      });
    }
    if (account === "revenue") {
      result = await CompanyService.collectionAccountStatement({
        start_date: startDate,
        end_date: endDate,
        account_type: accountType,
        paginate: paginator,
        companyId: id,
      });
    }
    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[req.company.code] || payment.defaultProvider;
    const bankAccount = await BankAccountRepo.getOneBankAccount({
      queryParams: {
        owner: id,
        type: "virtual",
        status: STATUSES.ACTIVE,
        ownerType: "company",
        issuer: CARD_ISSUER[providerToUse],
      },
      selectOptions: ["externalBankAccountId", "number", "accountName"],
    });
    const results = WrapperService.statementWrapper({ type: result.type, result: result.result.rows }, bankAccount);
    (page = Number(page)), (perPage = Number(perPage));
    const count = Number(result.result.count);
    const numberOfPages = Number(result.result.number_of_pages);
    return ResponseService.success(res, "Statement generated", {
      transaction: results,
      meta: {
        total: count,
        hasMore: (page || 1) < numberOfPages,
        page: page || 1,
        perPage: perPage || 20,
        nextPage: count > (page || 1) * (perPage || 20) ? (page || 1) + 1 : 1,
      },
    });
  },

  async downloadStatement(req, res) {
    const { id } = req.company;
    let { startDate, endDate, accountType, account, statusType } = req.query;
    let result;
    if (!startDate) startDate = new Date("1990-01-01 00:00:00").toISOString();
    if (!endDate) endDate = new Date().toISOString();

    if (account === "expense") {
      result = await CompanyService.expenseDownloadAccountStatement({
        start_date: startDate,
        end_date: endDate,
        account_type: accountType,
        status_type: statusType,
        companyId: id,
      });
    }
    if (account === "revenue") {
      result = await CompanyService.collectionDownloadAccountStatement({
        start_date: startDate,
        end_date: endDate,
        account_type: accountType,
        companyId: id,
      });
    }
    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[req.company.code] || payment.defaultProvider;
    const bankAccount = await BankAccountRepo.getOneBankAccount({
      queryParams: {
        owner: id,
        type: "virtual",
        status: STATUSES.ACTIVE,
        ownerType: "company",
        issuer: CARD_ISSUER[providerToUse],
      },
      selectOptions: ["externalBankAccountId", "number", "accountName"],
    });

    const results = WrapperService.statementWrapper(result, bankAccount);
    return ResponseService.success(res, "Statement generated", {
      transaction: results,
    });
  },

  async resubmitDocuments(req, res) {
    const { message } = await CompanyService.documentSubmission(req.params.code);
    return ResponseService.success(res, message);
  },

  async initiateAccountStatement(req, res) {
    const {
      company: { id: company },
      user: { id: user },
    } = req;
    const { error } = CompanyValidator.generateAccountStatementNew.validate(req.query);
    if (error) throw new ValidationError(error.message);

    const { message } = await CompanyService.initiateAccountStatement({ filter: { ...req.query, user, company } });
    return ResponseService.success(res, message);
  },

  async addStatementRequestToWorker(req, res) {
    BackgroundService.addToQueue(
      BACKGROUND.QUEUES.STATEMENT_QUEUE,
      BACKGROUND.JOBS.STATEMENT_JOB,
      { ...req.body.data },
      {
        delay: 3 * 1000, // 10 secs delay
      }
    );
    return ResponseService.success(res, "Statement is been generated");
  },

  async generateCompanyStatement(req, res) {
    const { id: company, code: companyCode, document_reference: reference } = req.company;
    const { error } = CompanyValidator.generateAccountStatementNew.validate(req.query);
    if (error) throw new ValidationError(error.message);

    let transactions;
    const source = req.query?.source;

    if (!source) throw new ValidationError("Please specify a source");

    const criteria = { ...req.query, company, reference, companyCode };
    if (source.includes("bdg_")) {
      ({ transactions } = await CompanyService.generateBudgetStatement(criteria));
    } else if (source.includes("crd_")) {
      ({ transactions } = await CompanyService.generateCardStatement(criteria));
    } else {
      transactions = await CompanyService.generateCompanyStatement(criteria);
    }
    return ResponseService.success(res, "Account statement generated", transactions);
  },

  async exportCompanyStatement(req, res) {
    const { id: company, code: companyCode, document_reference: reference } = req.company;
    const { error } = CompanyValidator.generateAccountStatementNew.validate(req.query);
    if (error) throw new ValidationError(error.message);

    let transactions;

    const source = req.query?.source;

    if (!source) throw new ValidationError("Please specify a source");

    const criteria = { ...req.query, company, reference, companyCode };

    if (source.includes("bdg_")) {
      transactions = await CompanyService.generateBudgetStatement(criteria);
    } else if (source.includes("crd_")) {
      transactions = await CompanyService.generateCardStatement(criteria);
    } else {
      transactions = await CompanyService.exportAccountStatement({ ...criteria, perPage: 1000000 });
    }
    return ResponseService.success(res, "Account statement generated", transactions);
  },

  async listCompanies(req, res) {
    const foundUserCompanies = await BeneficiaryRepo.getAllBeneficiaries({
      queryParams: {
        user: req.user.id,
        status: STATUSES.ACTIVE,
      },
      selectOptions: ["company"],
      addOwner: false,
    });

    const companyIDs = foundUserCompanies.map((element) => element.company);
    // this is wrong, the function must handle the steps above(Refactor)
    const companies = companyIDs.length ? await CompanyService.getCompaniesUserBelongTo(companyIDs) : [];
    return ResponseService.success(res, "Companies fetched successfully", {
      companies,
    });
  },

  async createCompany(req, res) {
    const { error } = CompanyValidator.create.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const { message, data: createdCompany } = await CompanyService.createCompany(req.body);

    await Promise.all([
      BeneficiaryService.addBeneficiary(req.user.id, req.user.id, createdCompany.id, req.user.role_id, STATUSES.ACTIVE),
      !req.user.company && UserRepo.updateUser({ queryParams: { id: req.user.id }, updateFields: { company: createdCompany.id } }),
    ]);

    // Send Notification to Customer experience
    NotificationService.sendEmail(
      [SettingsService.get("customer_experience_email")],
      "new-company-registration",
      {
        company: createdCompany.name,
        name: req.user.firstName,
        contact: req.user.email,
      },
      { subject: "New Registration 🚀🚀" }
    );

    return ResponseService.success(res, message, { code: createdCompany.code });
  },

  async createCompanySubsidiary(req, res) {
    const { error } = CompanyValidator.createSubsidiary.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const { message, data: createdCompany } = await CompanyService.createCompanySubsidiary({ payload: { ...req.body, user: req.user } });

    NotificationService.sendEmail(
      [SettingsService.get("customer_experience_email")],
      "new-company-registration",
      {
        company: createdCompany.name,
        name: req.user.firstName,
        contact: req.user.email,
      },
      { subject: "New Registration 🚀🚀" }
    );

    return ResponseService.success(res, message, { code: createdCompany.code });
  },

  async listSubsidiaries(req, res) {
    const subsidiaries = (await CompanyService.getSubsidiaries(req.company.id, req.query)) || [];
    return ResponseService.success(res, "Subsidiaries fetched successfully", subsidiaries);
  },

  async viewCompanyStatementInternal(req, res) {
    const { error } = CompanyValidator.generateAccountStatementInternal.validate(req.query);
    if (error) throw new ValidationError(error.message);

    const foundCompany = await CompanyService.getCompany({ code: req.query.company });

    let transactions;
    if (req.query?.source && String(req.query.source).includes("bdg_")) {
      const criteria = { ...req.query, company: foundCompany.id, reference: foundCompany.document_reference, companyCode: foundCompany.code };
      transactions = await CompanyService.generateBudgetStatement(criteria);
    } else {
      transactions = await CompanyService.exportAccountStatement({
        ...req.query,
        company: foundCompany.id,
        reference: foundCompany.document_reference,
        companyCode: foundCompany.code,
        perPage: 1000000,
      });
    }

    return ResponseService.success(res, "Account statement generated", transactions);
  },

  async getGeneralStatement(req, res) {
    const {
      company: { id: company },
    } = req;
    const { error } = CompanyValidator.generateGeneralStatement.validate({ ...req.query });
    if (error) throw new ValidationError(error.message);
    const criteria = { ...req.query, company };
    const transactions = await CompanyService.generateStatement(criteria);
    return ResponseService.success(res, "Account statement generated", transactions);
  },

  async getGeneralStatementInternal(req, res) {
    const {
      query: { code: company },
    } = req;
    if (!company) throw new ValidationError("Company code must be specified");
    const foundCompany = await CompanyService.getCompany({ code: company });
    if (!foundCompany) throw new NotFoundError(`Company(${company})`);
    const { error } = CompanyValidator.generateGeneralStatement.validate({ ...req.query }, { allowUnknown: true });
    if (error) throw new ValidationError(error.message);
    const criteria = { ...req.query, company: foundCompany.id };
    const transactions = await CompanyService.generateStatement(criteria);
    return ResponseService.success(res, "Account statement generated internally", transactions);
  },

  async addDirectors(req, res) {
    const { error } = CompanyValidator.addDirector.validate(req.body);
    if (error) throw new ValidationError(error.message);
    const { message } = await CompanyService.createCompanyDirector({ payload: { ...req.body, company: req.company.code } });
    return ResponseService.success(res, message);
  },

  async updateDirector(req, res) {
    const { error } = CompanyValidator.updateDirector.validate(req.body);
    if (error) throw new ValidationError(error.message);
    const { message } = await CompanyService.updateDirector({ filter: { ...req.params }, payload: { ...req.body } });
    return ResponseService.success(res, message);
  },

  async listDirectors(req, res) {
    const { message, data } = await CompanyService.listCompanyDirectors({ queryParams: { code: req.company.code } });
    const sanitizedDirectors = Sanitizer.sanitizeIndividuals(data);
    return ResponseService.success(res, message, sanitizedDirectors);
  },

  async inviteDirector(req, res) {
    const { error } = CompanyValidator.inviteDirector.validate(req.body);
    if (error) throw new ValidationError(error.message);
    const { message, data } = await CompanyService.inviteDirector({ queryParams: { code: req.company.code }, payload: req.body });
    return ResponseService.success(res, message, data);
  },

  async getSingleDirector(req, res) {
    const { error } = CompanyValidator.getSingleDirector.validate(req.params);
    if (error) throw new ValidationError(error.message);
    const { message, data } = await CompanyService.getDirector({ filter: { ...req.params } });
    const sanitizedDirector = Sanitizer.sanitizeIndividual(data);
    return ResponseService.success(res, message, sanitizedDirector);
  },

  async deleteSingleDirector(req, res) {
    const { error } = CompanyValidator.getSingleDirector.validate(req.params);
    if (error) throw new ValidationError(error.message);
    const { message } = await CompanyService.deleteDirector({ filter: { ...req.params } });
    return ResponseService.success(res, message);
  },

  async resendDirectorInvite(req, res) {
    const { company, director } = req.params;
    const { error } = CompanyValidator.resendDirectorInvite.validate({ company, director });
    if (error) throw new ValidationError(error.message);
    const { message } = await CompanyService.resendDirectorInvite({ filter: { company, director } });
    return ResponseService.success(res, message);
  },

  async getInvitedDirector(req, res) {
    const { code = null } = req;
    if (!code) throw new ValidationError("Invalid authentication");
    const { message, data } = await CompanyService.getInvitedDirector({ queryParams: { code } });
    return ResponseService.success(res, message, Sanitizer.sanitizeDirector(data));
  },

  async invitedDirectorSubmission(req, res) {
    const {
      code = null,
      company: { code: compayCode },
    } = req;
    if (!code) throw new ValidationError("Invalid authentication");

    const { error } = CompanyValidator.invitedDirectorSubmission.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const { message } = await CompanyService.updateDirector({ filter: { director: code, company: compayCode }, payload: { ...req.body } });
    return ResponseService.success(res, message);
  },

  async uploadCompanyDocument(req, res) {
    const { error } = CompanyValidator.uploadCompanyDocuments.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const {
      company: { code },
    } = req;

    const { message } = await CompanyService.uploadCompanyDocuments({ filter: { code }, payload: { ...req.body } });
    return ResponseService.success(res, message);
  },

  async sendInvitedDirectorsReminder(req, res) {
    ResponseService.success(res, "Ok");
    await CompanyService.sendInvitedDirectorsReminder(req.body);
  },

  async getBudgetStatementInternal(req, res) {
    const {
      query: { company },
    } = req;
    if (!company) throw new ValidationError("Company code must be specified");
    const foundCompany = await CompanyService.getCompany({ code: company });
    if (!foundCompany) throw new NotFoundError(`Company(${company})`);
    const { error } = CompanyValidator.generateBudgetStatement.validate({ ...req.query }, { allowUnknown: true });
    if (error) throw new ValidationError(error.message);
    const criteria = { ...req.query, company: foundCompany.id, reference: foundCompany.document_reference };
    const transactions = await CompanyService.generateBudgetStatement(criteria);
    return ResponseService.success(res, "Account statement generated internally", transactions);
  },

  async regenerateInviteCode(req, res) {
    const { error } = CompanyValidator.companyCode.validate({
      code: req.params.code,
    });
    if (error) throw new ValidationError(error.message);

    const company = await CompanyService.regenerateInviteCode(req.params.code);
    return ResponseService.success(res, "Company invite code regenerated successfully", {
      companyName: company.name,
      inviteCode: company.inviteCode,
    });
  },

  async verifyInviteCode(req, res) {
    const { error } = CompanyValidator.inviteCode.validate({
      inviteCode: req.query.code,
    });
    if (error) throw new ValidationError(error.message);

    const company = await CompanyService.verifyInviteCode(req.query.code);
    return ResponseService.success(res, "Company invite code verified", {
      companyName: company.name,
      logo: company.logo,
    });
  },
};
