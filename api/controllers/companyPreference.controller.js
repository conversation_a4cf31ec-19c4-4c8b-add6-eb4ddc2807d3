const { Response: ResponseService, CompanyPreferences: CompanyPrefencesService } = require("../services");
const { NotFoundError } = require("../utils/error.utils");
const Sanitizer = require("../utils/sanitizer");

module.exports = {
  async getPreferences(req, res) {
    const { feature } = req.query;
    const foundPreferences = await CompanyPrefencesService.listPreferences({
      company: req.company.id,
      ...(feature && { feature }),
    });
    const sanitized = Sanitizer.sanitizeModelInstances(foundPreferences, "CompanyPreference");
    const preferences = await CompanyPrefencesService.fetchAssociatedEntities(sanitized);
    return ResponseService.success(res, "Preferences fetched successfully", preferences);
  },

  async updatePreferences(req, res) {
    const { code } = req.params;

    const foundPreference = await CompanyPrefencesService.getPreference(code);
    if (!foundPreference) throw new NotFoundError("Preference");
    const payload = {
      feature: foundPreference.feature,
      value_type: foundPreference.value_type,
      key: foundPreference.key,
      ...req.body,
    };

    await CompanyPrefencesService.updatePreference(code, payload);
    return ResponseService.success(res, "Preferences updated successfully");
  },
};
