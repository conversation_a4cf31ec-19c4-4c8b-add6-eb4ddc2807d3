const ResponseService = require("../services/response");
const CronService = require("../services/cron.service");

module.exports = {
  async createCron(req, res) {
    const { title, schedule, method = "get", body } = req.body;
    const response = await CronService.addCron(title, schedule, method, body);
    const { message, data, error } = response;
    return ResponseService.success(res, message || error, data);
  },

  async listCron(req, res) {
    const response = await CronService.listCron();
    const { message, data, error } = response;
    return ResponseService.success(res, message || error, data);
  },

  async getCron(req, res) {
    const { id } = req.params;
    const response = await CronService.getCron(id);
    const { message, data, error } = response;
    return ResponseService.success(res, message || error, data);
  },

  async getCronHistory(req, res) {
    const { id } = req.params;
    const response = await CronService.cronHistory(id);
    const { message, data, error } = response;
    return ResponseService.success(res, message || error, data);
  },

  async updateCron(req, res) {
    const { id } = req.params;
    const { activate } = req.body;
    const response = await CronService.updateCron(id, req.body);
    const { message, data, error } = response;
    return ResponseService.success(res, message || error, data);
  },

  async deleteCron(req, res) {
    const { id } = req.params;
    const response = await CronService.deleteCron(id);
    const { message, data, error } = response;
    return ResponseService.success(res, message || error, data);
  },
};
