const { ValidationError, NotFoundError } = require("../utils/error.utils");
const { CustomerValidator } = require("../validators");
const { Customer: CustomerService, Response: ResponseService, Helper: HelperService } = require("../services");
const Sanitizer = require("../utils/sanitizer");
const { CategoryRepo, CustomerRepo } = require("../repositories/index.repo");
const { stripUndefinedAndNullDeep } = require("../utils");

module.exports = {
  async search(req, res) {
    const { id: company } = req.company;
    const query = { ...req.query, company };

    const { error } = CustomerValidator.search.validate(query);
    if (error) throw new ValidationError(error.message);

    const customers = await CustomerService.search(query);
    ResponseService.success(res, "Customers fetched successfully", {
      customers: Sanitizer.sanitizeModelInstances(customers, "Customer"),
    });
  },

  async create(req, res) {
    const { id: company } = req.company;

    const { error } = CustomerValidator.create.validate(req.body);
    if (error) throw new ValidationError(error.message);

    if (req.body.firstName && req.body.lastName) {
      const { lastName, firstName, ...rest } = req.body;
      req.body = { ...rest, name: `${firstName} ${lastName}` };
    }

    if (req.body.category) {
      const foundCategory = await CategoryRepo.getCategory({ queryParams: { code: req.body.category } });
      if (!foundCategory) throw new NotFoundError("Category");
      req.body.category = foundCategory.id;
    }

    const customer = await CustomerService.createCustomer(
      stripUndefinedAndNullDeep({
        ...req.body,
        taxIdentificationNumber: undefined,
        tin: req.body.taxIdentificationNumber,
        company,
      })
    );

    ResponseService.success(res, "Customer created successfully", Sanitizer.sanitizeCustomer(customer));
  },

  async update(req, res) {
    const { id: company } = req.company;
    const { code } = req.params;

    const { error } = CustomerValidator.update.validate(req.body);
    if (error) throw new ValidationError(error.message);

    if (req.body.firstName && req.body.lastName) {
      const { lastName, firstName, ...rest } = req.body;
      req.body = { ...rest, name: `${firstName} ${lastName}` };
    }

    if (req.body.category) {
      const foundCategory = await CategoryRepo.getCategory({ queryParams: { code: req.body.category } });
      if (!foundCategory) throw new NotFoundError("Category");
      req.body.category = foundCategory.id;
    }

    const customer = await CustomerService.updateCustomer({
      ...req.body,
      taxIdentificationNumber: undefined,
      tin: req.body.taxIdentificationNumber,
      company,
      code,
    });

    ResponseService.success(res, "Customer updated successfully", Sanitizer.sanitizeCustomer(customer));
  },

  async massCreate(req, res) {
    const { id: company } = req.company;

    const { error } = CustomerValidator.massCreate.validate(req.body);
    if (error) throw new ValidationError(error.message);

    await CustomerService.massCreate({
      ...req.body,
      company,
    });

    ResponseService.success(res, "Customers created successfully");
  },

  async delete(req, res) {
    const { id: company } = req.company;
    const { code } = req.params;

    const { error } = CustomerValidator.delete.validate(req.params);
    if (error) throw new ValidationError(error.message);

    await CustomerService.deleteCustomer({ company, code });
    return ResponseService.success(res, "Customer archived successfully");
  },

  async getAll(req, res) {
    const { id: company } = req.company;
    const { customers, meta } = await CustomerService.getAll({
      filter: { ...req.query, company },
    });
    const { totalDebt, ...rest } = await CustomerRepo.customerSummary({ filter: { company } });
    return ResponseService.success(res, "Customers Fetched", {
      customers: Sanitizer.sanitizeModelInstances(customers, "Customer"),
      meta,
      summary: {
        ...rest,
        totalDebt: HelperService.groupAmountsByCurrency(totalDebt),
      },
    });
  },

  async getOne(req, res) {
    const { id: company } = req.company;
    const { code } = req.params;
    const result = await CustomerService.getOne({ company, code });
    const { customer, ...rest } = result;
    return ResponseService.success(res, "Success", {
      customer: Sanitizer.sanitizeCustomer(customer),
      ...rest,
    });
  },

  async getCustomerTransactions(req, res) {
    const {
      company: { id: company },
      params: { code: customer },
    } = req;

    const { message, data } = await CustomerService.getCustomerTransactions({ queryParams: { ...req.query, customer, company } });

    const { transactions, meta, chart } = data;

    return ResponseService.success(res, message, {
      transaction: Sanitizer.sanitizeCustomerTransactions(transactions),
      meta,
      chart: chart || {},
    });
  },

  async export(req, res) {
    const { id: company } = req.company;
    const customers = await CustomerService.export({
      filter: {
        ...req.query,
        company,
      },
    });
    return ResponseService.success(res, "Customers exported successfully", {
      customers: Sanitizer.sanitizeModelInstances(customers, "Customer"),
    });
  },

  async syncZoho(req, res) {
    const {
      company: { id: company },
      body: { codes: customerCodes = [] } = {},
    } = req;
    if (!customerCodes.length) return ResponseService.failure(res, "Please select one or multiple customers");
    const r = await CustomerService.syncCustomers({ filter: { company, code: customerCodes } });
    ResponseService.success(res, "Sync in progress", r);
  },
};
