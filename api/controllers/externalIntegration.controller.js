const ResponseService = require("../services/response");
const ExternalIntegrationService = require("../services/externalIntegration.service");
const { STATUSES } = require("../models/status");
const Sanitizer = require("../utils/sanitizer");
const { ValidationError } = require("../utils/error.utils");
const { CategoryMapping } = require("../validators");
const QueueService = require("../services/queue.service");
const { CompanyRepo, ExternalIntegrationRepo } = require("../repositories/index.repo");
const integrations = require("../integrations");
const { toTitle } = require("../utils");
const { revokeAccess } = require("../utils/zoho.utils");

const Handler = {
  async createAccessToken(req, res) {
    try {
      const { company, id: user } = req.user;
      const { publicToken } = req.body;
      const { status, ...rest } = await ExternalIntegrationService.createAccessToken(user, company, publicToken);
      const ExternalIntegrationConnection = {
        ...rest,
        status: Sanitizer.getStatusById(STATUSES, status).toLowerCase(),
      };
      return ResponseService.success(res, "Account successfully connected", ExternalIntegrationConnection);
    } catch (error) {
      return ResponseService.failure(res, error.message);
    }
  },

  async getQuickBookCategories(req, res) {
    try {
      const {
        company: { id: company },
      } = req;
      const fetchUserQuickBookCategories = await ExternalIntegrationService.fetchQuickBookAccountCategories(company);
      return ResponseService.success(res, "Quickbook Category List", fetchUserQuickBookCategories);
    } catch (error) {
      return ResponseService.failure(res, error.message);
    }
  },

  async getQuickBookAccounts(req, res) {
    try {
      const {
        company: { id: company },
      } = req;
      const fetchUserQuickBookAccounts = await ExternalIntegrationService.fetchQuickBookAccounts(company);
      return ResponseService.success(res, "Quickbook Accounts List", fetchUserQuickBookAccounts);
    } catch (error) {
      return ResponseService.failure(res, error.message);
    }
  },

  async getQuickBookVendors(req, res) {
    try {
      const {
        company: { id: company },
      } = req;
      const fetchUserQuickBookVendors = await ExternalIntegrationService.fetchQuickBookVendors(company);
      return ResponseService.success(res, "Quickbook Vendors List", fetchUserQuickBookVendors);
    } catch (error) {
      return ResponseService.failure(res, error.message);
    }
  },

  async disconnectQuickBook(req, res) {
    try {
      const {
        company: { id: company },
      } = req;
      await ExternalIntegrationService.disconnectQuickBookConnection(company);
      // eslint-disable-next-line no-empty
    } catch (error) {}
    return ResponseService.success(res, "QuickBook Successfully Disconnected");
  },

  async disconnectIntegration(req, res) {
    const { platform } = req.params;
    if (platform === "quickbooks") return Handler.disconnectQuickBook(req, res);
    try {
      const {
        company: { id: company },
      } = req;
      await ExternalIntegrationService.disconnectIntegration(company, platform);
      if (platform === "zoho") {
        const foundIntegration = await ExternalIntegrationRepo.getIntegration({
          filter: {
            platform,
            company,
            status: STATUSES.ACTIVE,
          },
          selectedOptions: ["metadata", "access_token", "refresh_token"],
        });
        revokeAccess(foundIntegration);
      }
      // eslint-disable-next-line no-empty
    } catch (error) {}
    return ResponseService.success(res, `${toTitle(platform)} successfully disconnected`);
  },

  async getIntegrations(req, res) {
    try {
      const {
        company: { id: company },
      } = req;
      const integration = await ExternalIntegrationService.getIntegrations(company);
      const sanitizedData = Sanitizer.sanitizeIntegrations(integration).sort(({ connected: first }, { connected: second }) =>
        first && first === second ? -1 : 1
      );
      return ResponseService.success(res, "Integrations successfully fetched", sanitizedData);
    } catch (error) {
      return ResponseService.failure(res, error.message);
    }
  },

  async createTransactionCategories(req, res) {
    const {
      company: { id: company },
      user: { id: user },
      body,
    } = req;
    const { error } = CategoryMapping.createCategoryMapping.validate(body);
    if (error) throw new ValidationError(error.message);
    const { categories } = body;
    if (!Array.isArray(categories) || !categories.length) {
      return ResponseService.failure(res, "Please select a category");
    }
    await ExternalIntegrationService.createCategoryMapping(categories, company, user);
    ResponseService.success(res, "Transactions are being synced");
    // Prepare an SQS payload for the synchronization job.
    const SQSPayload = {
      idempotencyKey: req.company.code,
      path: `/integrations/quickbooks/sync/transactions`,
      key: process.env.INTRA_SERVICE_TOKEN,
    };
    // Add a synchronization job to the queue using QueueService.
    // Tag the job as "QuickBookSynchronization" and associate the SQS payload.
    return QueueService.addJob({ tag: "QuickBookSynchronization" }, SQSPayload, `sync:${req.company.code}`);
  },

  // eslint-disable-next-line consistent-return
  async syncTransactions(req, res) {
    if (!req.company && req.body.idempotencyKey) {
      req.company = await CompanyRepo.getCompany({ queryParams: { code: req.body.idempotencyKey } });
    }
    if (!req.company) {
      return ResponseService.failure(res, "Company not found");
    }
    const { id: company } = req.company;
    const categories = await ExternalIntegrationService.getAllCategoryMapping(company);
    const formattedCategories = categories.map(({ bujetiCategory: { code }, quickBooksCategory }) => ({
      bujetiCategory: code,
      quickBooksCategory,
    }));
    console.log(`Synching ${formattedCategories.length} categories transactions`);
    const treated = await ExternalIntegrationService.createQuickBookCategoryTransactions(company, formattedCategories);
    ResponseService.success(res, "Transactions are being synced");
    // If any transactions are being treated, add a synchronization job to the queue.
    if (treated !== 0) {
      const SQSPayload = {
        idempotencyKey: req.company.code,
        path: `/integrations/quickbooks/sync/transactions`,
        key: process.env.INTRA_SERVICE_TOKEN,
      };
      QueueService.addJob({ tag: "QuickBookSynchronization" }, SQSPayload, `sync:${req.company.code}`);
    }
  },

  async processTransactions(req, res) {
    const { transaction, company } = req.body;
    await ExternalIntegrationService.processTransaction(transaction, company);
    ResponseService.success(res, "Transactions are being processed to QuickBooks");
  },

  async getTransactionCategories(req, res) {
    try {
      const {
        company: { id: company },
      } = req;
      const categories = await ExternalIntegrationService.getAllCategoryMapped(company);
      return ResponseService.success(res, "List Categories Successfully", categories);
    } catch (error) {
      return ResponseService.failure(res, error.message);
    }
  },

  async authorizeZoho(req, res) {
    try {
      const { company, id: user } = req.user;
      const { status, ...rest } = await ExternalIntegrationService.createIntegrationForZoho(user, company, {
        ...req.body,
        metadata: {
          scope: req.body.scope,
        },
      });
      const data = {
        ...rest,
        status: Sanitizer.getStatusById(STATUSES, status).toLowerCase(),
      };
      return ResponseService.success(res, "Account successfully connected", data);
    } catch (error) {
      return ResponseService.failure(res, error.message);
    }
  },
};

module.exports = Handler;
