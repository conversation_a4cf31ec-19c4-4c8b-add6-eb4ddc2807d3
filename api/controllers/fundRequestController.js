const ResponseService = require("../services/response");
const FundRequestService = require("../services/fundRequest.service");
const SanitizerService = require("../utils/sanitizer");
const { STATUSES } = require("../models/status");
const FundRequestValidator = require("../validators/fundrequest.validator");
const { ValidationError } = require("../utils/error.utils");
const FundRequestRepo = require("../repositories/fundRequest.repo");
const HelperService = require("../services/helper.service");

module.exports = {
  async create(req, res) {
    FundRequestService.validateCreationPayload(req.body);
    const { id: company } = req.company;
    const payload = {
      company,
      user: req.user.id,
      ...req.body,
    };

    await FundRequestService.createFundRequest({
      ...payload,
      plan: req.company.paymentPlan.configuration,
    });
    ResponseService.success(res, "Funds requested successfully");
  },
  async list(req, res) {
    FundRequestService.validateGetListPayload(req.query);
    const { id: company } = req.company;
    const criteria = { isMobile: req.isMobile, company, ...req.query };

    if (req.isEmployeeRole || !req.hasFundRequestEditPermission) criteria.user = req.user.id;

    criteria.loggedInUser = req.user.id;
    const { meta, fundRequests } = await FundRequestService.listFundRequests(criteria);

    const summary = await FundRequestRepo.fundRequestSummary({ filter: criteria });

    ResponseService.success(res, "Fund requests fetched successfully", {
      fundRequests: SanitizerService.sanitizeModelInstances(fundRequests, "FundRequest"),
      meta,
      summary: HelperService.groupSummaryByCurrency(summary),
    });
  },
  async view(req, res) {
    const { code } = req.params;
    const { id: company } = req.company;
    const criteria = { code, company };
    if (req.isEmployeeRole) criteria.user = req.user.id;
    const fundRequest = await FundRequestService.getFundRequest(criteria);
    ResponseService.success(res, "Fund request fetched successfully", { fundRequest: SanitizerService.sanitizeFundRequest(fundRequest) });
  },
  async update(req, res) {
    FundRequestService.validateUpdatePayload(req.body);

    if (req.isEmployee) delete req.body.status;
    const { id: company } = req.company;
    const payload = {
      ...req.body,
      company,
    };

    if (!(req.hasFundRequestEditPermission || req.isManager)) {
      payload.user = req.user.id;
    }

    const fundRequest = await FundRequestService.updateFundRequest(req.params.code, payload);

    ResponseService.success(res, "Fund request updated successfully", { fundRequest: SanitizerService.sanitizeFundRequest(fundRequest) });
  },

  async disable(req, res) {
    const payload = {
      status: STATUSES.DELETED,
      company: req.company.id,
    };

    if (!req.isManager) {
      payload.user = req.user.id;
    }

    await FundRequestService.updateFundRequest(req.params.code, payload);
    ResponseService.success(res, "Fund request deleted successfully");
  },
  async approve(req, res) {
    const { actionLater, schedule } = req.body;
    const { code } = req.params;
    const { id: company } = req.company;
    const { id: reviewer } = req.user;
    const { fundRequest } = await FundRequestService.validateFundRequest({
      code,
      company,
      decision: "approve",
      actionLater,
      schedule,
    });

    const { success, message, error } = await FundRequestService.approveFundRequest({
      code,
      company,
      reviewer,
      recipient: fundRequest.User,
      amount: fundRequest.amount,
      currency: fundRequest.currency,
      budget: fundRequest.sourceBudget,
      plan: req.company.paymentPlan.configuration,
      balance: fundRequest.balance,
      actionLater,
      schedule,
    });

    return ResponseService[success ? "success" : "failure"](res, message || error);
  },

  async decline(req, res) {
    const { code } = req.params;
    const { reason: note } = req.body;
    const { id: company } = req.company;
    const { id: reviewer } = req.user;

    const { fundRequest } = await FundRequestService.validateFundRequest({
      code,
      company,
      decision: "decline",
      note,
    });
    await FundRequestService.declineFundRequest({
      code,
      company,
      reviewer,
      note,
      recipient: fundRequest.User,
      amount: fundRequest.amount,
      currency: fundRequest.currency,
    });
    return ResponseService.success(res, "Fund Request declined successfully");
  },
  async finalize(req, res) {
    const { code, transaction, company, transaction_code } = req.body;
    await FundRequestService.finalizeFundRequest({ code, company, transaction, transaction_code });
    return ResponseService.success(res, "OK");
  },

  async complete(req, res) {
    await FundRequestService.completeFundRequest(req.body);
    return ResponseService.success(res, "OK");
  },

  async requestForMoreInfo(req, res) {
    FundRequestService.validateMoreInfoPayload(req.body);
    const { id: company } = req.company;
    const { note } = req.body;
    const { code } = req.params;
    const { id: reviewer } = req.user;
    await FundRequestService.requestForMoreInfo({ code, company, note, reviewer });
    return ResponseService.success(res, "More info requested successfully");
  },

  async multipleFundRequestAction(req, res) {
    const {
      company: { id: company, paymentPlan },
      user: { id: reviewer },
    } = req;

    const { error } = FundRequestValidator.multipleFundRequestAction.validate(req.body);
    if (error) throw new ValidationError(error.message);
    if (req.body.decision === "decline" && !req.body.reason) throw new ValidationError("Give a reason for declining the request(s)");
    const { message } = await FundRequestService.multipleRequestAction({ ...req.body, company, reviewer, configuration: paymentPlan.configuration });
    return ResponseService.success(res, message);
  },

  async massDelete(req, res) {
    const {
      company: { id: company },
      user: { id: reviewer },
    } = req;

    const payload = {
      ...req.body,
      company,
      status: STATUSES.DELETED,
    };

    if (!req.isManager) {
      payload.user = reviewer;
    }

    const { error } = FundRequestValidator.massDelete.validate(req.body);
    if (error) throw new ValidationError(error.message);
    await FundRequestService.massDelete(payload);
    return ResponseService.success(res, "Fund requests deleted successfully");
  },
};
