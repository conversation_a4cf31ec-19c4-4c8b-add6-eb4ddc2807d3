/* eslint-disable global-require */
module.exports = {
  Asset: require("./assetController"),
  Card: require("./card"),
  Company: require("./company"),
  User: require("./user"),
  Beneficiary: require("./beneficiary.controller"),
  Transaction: require("./transaction"),
  Budget: require("./budget"),
  Webhook: require("./webhook"),
  Vendor: require("./vendorController"),
  Utils: require("./utils"),
  MockController: require("./mockData.controller"),
  BusinessOnboardingController: require("./businessOnboarding.controller"),
  IntegrationController: require("./integration.controller"),
  Reimbursement: require("./reimbursementController"),
  Bank: require("./bank"),
  Payment: require("./paymentController"),
  userBudgetController: require("./userBudget.controller"),
  Recipient: require("./recipient.controller"),
  Balance: require("./balance.controller"),
  RoleController: require("./role.controller"),
  CategoryController: require("./category.controller"),
  Notification: require("./notification.controller"),
  Team: require("./team.controller"),
  OnboardingInvite: require("./onboardingInvite.controller"),
  BillingController: require("./billing.controller"),
  CronController: require("./crons.controller"),
  InvoiceController: require("./invoice.controller"),
  VirtualCardController: require("./virtualcard.controller"),
  SettlementAccountController: require("./settlementAccount.controller"),
  ExternalIntegrationController: require("./externalIntegration.controller"),
  SettlementController: require("./settlement.controller"),
  FundRequest: require("./fundRequestController"),
  Reports: require("./report.controller"),
  CardRequestController: require("./cardRequest.controller"),
  SpendController: require("./spend.controller"),
  Public: require("./public"),
  ApiKey: require("./apiKey"),
  CompanyPreferences: require("./companyPreference.controller"),
  TaxController: require("./tax.controller"),
};
