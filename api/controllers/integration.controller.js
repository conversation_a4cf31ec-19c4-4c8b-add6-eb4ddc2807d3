/* eslint-disable camelcase */
/* eslint-disable no-undef */
const { addHours } = require("date-fns");

const axios = require("axios");
const ResponseService = require("../services/response");
const IntegrationService = require("../services/integration.service");
const { ExternalIntegration: ExternalIntegrationService } = require("../services");
const { exchangeCodeForAccessToken, fetchAccountDetails, fetchAdditionalAccountDetails, fetchChartOfAccounts } = require("../utils/zoho.utils");
const { STATUSES } = require("../models/status");
const { toTitle } = require("../utils");
const { getSlackChannels, joinChannel } = require("../utils/slack.utils");
const SlackMappingService = require("../services/slackMapping.service");
const { log, Log } = require("../utils/logger.utils");
const { PLATFORM } = require("../constants/platforms");
const { setupSlackIntegrationTasks } = require("../services/integration.service");

module.exports = {
  async smileId(req, res) {
    const { document, ...body } = req.body;
    const payload = {
      ...body,
      id: req.user.id,
      company_code: req.company.code,
      document: {
        bvn: document.bvn,
        idType: document.idType,
        idNumber: document.idNumber,
        idCopy: document.idCopy,
        utilityBill: document.utilityBill,
      },
    };
    const response = await IntegrationService.smileId(payload);

    const { message, data } = response;
    return ResponseService.success(res, message, data);
  },

  async getByPlatform(req, res) {
    const { platform } = req.params;
    const upperPlatform = platform.toUpperCase();
    const isZoho = upperPlatform === PLATFORM.ZOHO;
    const isSlack = upperPlatform === PLATFORM.SLACK;

    // Fetch integration or use a default empty object
    const integration = (await IntegrationService.getByPlatform({
      company: req.company.id,
      platform: upperPlatform,
      status: STATUSES.ACTIVE,
    })) || { toJSON: () => ({}) };

    const { id, status, ...sanitizedIntegration } = integration.toJSON();

    if (isZoho) {
      // Ensure organization/account details are present
      if (integration.code && !integration?.metadata?.organizationId) {
        await fetchAccountDetails(integration.code);
        await fetchAdditionalAccountDetails(integration.code);
        return ResponseService.failure(
          res,
          `${toTitle(platform)} integration needs to be configured`,
          {
            openIntegrationPage: true,
            configurationInProgress: true,
          }
        );
      }
      // Ensure chart of accounts are present
      if (integration.code && !integration?.metadata?.chartofaccounts) {
        await fetchChartOfAccounts(integration.code);
      }

      sanitizedIntegration.redirectURL = `${process.env.ZOHO_URL}/auth?client_id=${process.env.ZOHO_CLIENT_ID}&scope=${process.env.ZOHO_SCOPE}&response_type=code&access_type=offline&prompt=consent&redirect_uri=${process.env.ZOHO_REDIRECT_URL}/settings/integrations/zoho`;
    }

    if (isSlack) {
      const { channels = [] } = await getSlackChannels(
        integration.access_token
      );
      sanitizedIntegration.metadata = {
        ...sanitizedIntegration.metadata,
        channels,
      };
    }

    return ResponseService.success(
      res,
      req.message || "Integration successfully fetched",
      {
        ...sanitizedIntegration,
        status: "active",
      }
    );
  },

  async setDefaultChannels(req, res) {
    const { code } = req.params;
    const { channel, channels = null } = req.body;
    const criteria = {
      ...(String(code).startsWith("int_") && { code }),
      ...(!String(code).startsWith("int_") && {
        status: STATUSES.ACTIVE,
        company: req.company.id,
        platform: PLATFORM.SLACK,
      }),
    };
    const foundIntegration = await ExternalIntegrationService.findIntegrations(criteria);

    if (!foundIntegration) {
      return ResponseService.failure(res, "Integration not found");
    }

    const channelName = foundIntegration.metadata?.channels?.find(({ id }) => id === channel)?.name_normalized || null;

    await ExternalIntegrationService.updateIntegration(
      {
        code,
      },
      {
        metadata: {
          ...(foundIntegration.metadata || {}),
          channel,
          channelName,
          channels,
        },
      }
    );

    if (channel && foundIntegration.access_token) {
      await joinChannel(foundIntegration.access_token, channel);
    }

    return ResponseService.success(res, `Integration successfully updated`);
  },
  // eslint-disable-next-line consistent-return
  async setDefaultOrganization(req, res) {
    const { code, organizationId } = req.params;
    const isIntegrationCode = String(code).startsWith("int_");
    const criteria = isIntegrationCode
      ? { code }
      : {
          status: STATUSES.ACTIVE,
          company: req.company.id,
          platform: PLATFORM.SLACK,
        };

    const foundIntegration = await ExternalIntegrationService.findIntegrations(
      criteria
    );
    const orgs = foundIntegration?.metadata?.organizations || [];
    const foundOrganization = orgs.find(
      ({ organization_id }) => organizationId === organization_id
    );

    if (foundIntegration?.metadata?.organizationId === organizationId) {
      return ResponseService.success(
        res,
        "Integration already set to this organization."
      );
    }

    const newMetadata = {
      ...foundIntegration.metadata,
      chartofaccounts: null,
      currencies: null,
      organizationId: foundOrganization?.organization_id,
      organizationName: foundOrganization?.name,
    };

    await ExternalIntegrationService.updateIntegration(
      { code },
      {
        metadata: newMetadata,
        status: STATUSES.PROCESSING,
      }
    );

    ResponseService.success(
      res,
      "Integration successfully updated. Please wait a few seconds while we refresh your integration"
    );

    // Refresh in parallel (no need to await here)
    await Promise.all([
      fetchAdditionalAccountDetails(code, true),
      fetchChartOfAccounts(code, true),
    ]);

    // Set status to ACTIVE after refresh
    await ExternalIntegrationService.updateIntegration(
      { code },
      { status: STATUSES.ACTIVE }
    );
  },

  async authorizeSlack(req, res) {
    const { code } = req.query;

    const tokenUrl = process.env.SLACK_OAUTH_TOKEN_URL;
    const params = {
      client_id: process.env.SLACK_CLIENT_ID,
      client_secret: process.env.SLACK_CLIENT_SECRET,
      code,
      redirect_uri: process.env.SLACK_REDIRECT_URI,
    };

    try {
      const tokenResponse = await axios.post(tokenUrl, null, { params });
      const response = tokenResponse.data;

      if (!response.ok) {
        log(Log.bg.red, {
          message: "Slack OAuth exchange failed",
          error: response.error,
          company: req.company.id,
        });

        return ResponseService.failure(res, "Failed to connect to Slack. Please try again.");
      }

      const { access_token: accessToken, bot_user_id, app_id, team } = response;

      const { code: integrationCode } = await ExternalIntegrationService.createIntegrationForSlack(req.user.id, req.company.id, {
        access_token: accessToken,
        metadata: {
          bot_user_id,
          app_id,
          team,
        },
      });

      log(Log.fg.green, {
        message: `Integration created successfully with code: ${integrationCode}`,
      });

      log(Log.fg.blue, {
        message: "Starting async operations for Slack setup",
      });

      await setupSlackIntegrationTasks(req.company.id, integrationCode, accessToken, bot_user_id, app_id, team);

      return ResponseService.success(res, "Account successfully connected");
    } catch (error) {
      log(Log.bg.red, {
        message: "Error exchanging code for token",
        data: {
          company: req.company?.id,
          error: error.message,
          statusCode: error.response?.status || 500,
        },
      });

      return ResponseService.failure(res, "Failed to connect to Slack. Please try again.");
    }
  },
  async authorizeZoho(req, res) {
    let response;
    try {
      const { company, id: user } = req.user;
      const { code, location } = req.query;
      response = await exchangeCodeForAccessToken(code, req.company.code, location);

      if (!response.access_token || !response.refresh_token)
        return ResponseService.failure(res, "Failed to connect to your Zoho account. Please retry again", {
          code: !response.access_token ? 10002 : 10003,
          // message: No access/refresh token returned from zoho
        });

      const { code: integrationCode } = await ExternalIntegrationService.createIntegrationForZoho(user, company, {
        access_token: response.access_token,
        refresh_token: response.refresh_token,
        expirationDate: addHours(new Date(), 1),
        metadata: {
          apiDomain: response.api_domain,
          location,
        },
        status: STATUSES.PROCESSING,
      });
      ResponseService.success(res, "Account successfully connected");

      await fetchAccountDetails(integrationCode);
      await fetchAdditionalAccountDetails(integrationCode);
      return fetchChartOfAccounts(integrationCode);
    } catch (error) {
      return ResponseService.failure(res, error.message, response);
    }
  },
};
