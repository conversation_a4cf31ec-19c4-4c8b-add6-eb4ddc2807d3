const { ValidationError, NotFoundError } = require("../utils/error.utils");
const { InvoiceValidator } = require("../validators");
const {
  Invoice: InvoiceService,
  Response: ResponseService,
  Customer: CustomerService,
  Notification: NotificationService,
  ScannerService,
  ScheduledInvoice: ScheduledInvoiceService,
  CompanyPreferences: CompanyPreferencesService,
} = require("../services");
const Sanitizer = require("../utils/sanitizer");
const {
  BalanceRepo,
  CustomerRepo,
  InvoiceRepo,
  BankAccountRepo,
  CategoryRepo,
  InvoiceInstallmentRepo,
  BudgetRepo,
  InvoiceTemplateRepo,
  CompanyPreferencesRepo,
} = require("../repositories/index.repo");
const { STATUSES } = require("../models/status");
const Utils = require("../utils/utils");
const AssetService = require("../services/asset");
const { FEATURES } = require("../models/companyPreference");
const { syncInvoiceToZoho } = require("../utils/zoho.utils");
const integration = require("../models/integration");

module.exports = {
  // eslint-disable-next-line consistent-return
  async createInvoice(req, res) {
    const { error } = InvoiceValidator.createInvoice.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const {
      user: { id: user, firstName, lastName },
      company: { id: company, name: companyName },
    } = req;
    const { products, scheduleInvoice, isDraft, customer, installments, settlementAccount, attachments, template, ...rest } = req.body;
    if (customer.category) {
      const foundCategory = await CategoryRepo.getCategory({ queryParams: { code: customer.category } });
      if (!foundCategory) throw new NotFoundError("Customer category");
      customer.category = foundCategory.id;
    }

    // Calculate the sum of the products
    let invoiceAmount = Array.from(products).reduce((prev, current) => prev + Utils.calculateProductPrice(current), 0);
    // Calculate discount on invoice
    if (rest.discount) invoiceAmount -= Utils.calculateDiscount({ discount: rest.discount, discountType: rest.discount_type, amount: invoiceAmount });
    // Add Vat to invoice amount
    if (rest.vat) {
      const vatAmount = Utils.calculateVat({ vat: rest.vat, amount: invoiceAmount });
      rest.vatAmount = vatAmount;
      invoiceAmount += vatAmount;
    }

    if (installments) Utils.validateInstallmentsPayload({ installments, amount: invoiceAmount });

    if (settlementAccount) {
      if (String(settlementAccount).includes("blc_")) {
        const foundBalance = await BalanceRepo.getBalance({
          filter: { code: settlementAccount, company },
        });
        if (!foundBalance) throw new NotFoundError("Balance");
        rest.balance = foundBalance.id;
      } else {
        const foundBudget = await BudgetRepo.getOneBudget({
          queryParams: { code: settlementAccount, company },
        });
        if (!foundBudget) throw new NotFoundError("Budget");
        rest.budget = foundBudget.id;
      }
    }

    let invoiceCustomer;
    if (typeof customer === "object") {
      invoiceCustomer = await CustomerService.createCustomer({
        ...customer,
        firstName: undefined,
        lastName: undefined,
        taxIdentificationNumber: undefined,
        tin: customer.taxIdentificationNumber,
        company,
      });
    } else {
      invoiceCustomer = await CustomerRepo.getCustomer({
        filter: { code: customer },
        addAccount: true,
      });
      if (!invoiceCustomer.BankAccounts || !invoiceCustomer.BankAccounts.length)
        CustomerService.createCustomerAccount({ payload: { company, customer: invoiceCustomer.id } });
    }
    let templateCode;
    if (template) {
      templateCode = template;
    } else {
      const [{ value } = {}] = await CompanyPreferencesRepo.listPreferences({
        company: req.company.id,
        feature: "invoices",
        key: "template",
      });
      if (value) templateCode = Utils.parseJSON(value);
    }
    const foundTemplate = templateCode ? await InvoiceTemplateRepo.getTemplateByCode(templateCode) : null;
    rest.template = foundTemplate?.id || 1; // default one;
    const invoicePayload = {
      company,
      user,
      ...rest,
      customer: invoiceCustomer.id,
      amount: invoiceAmount,
      ...(isDraft && { status: STATUSES.DRAFT }),
    };

    if (scheduleInvoice) {
      const { message } = await ScheduledInvoiceService.createScheduledInvoice({ invoicePayload, products, scheduleInvoice, installments });
      return ResponseService.success(res, message);
    }
    // Create Invoice
    const {
      message,
      data: { invoice, hasApproval },
    } = await InvoiceService.createInvoice({ data: invoicePayload, user: req.user });
    await InvoiceService.completeCreationOfInvoice({ invoice, products, installments });

    ResponseService.success(res, message, Sanitizer.sanitizeInvoice(invoice));

    if (attachments) {
      InvoiceService.addAttachmentToInvoice(invoice.id, attachments);
    }

    if (!settlementAccount) {
      const collectionAccount = await BankAccountRepo.getCollectionAccount({ queryParams: { company, currency: invoice.currency } });
      if (!collectionAccount) await InvoiceService.createCollectionAccount({ company });
    }

    const isInvoiceCreated = !(hasApproval || isDraft);

    if (isInvoiceCreated && invoice.shouldSplitTax) InvoiceService.createTaxAccount({ company });

    if (isInvoiceCreated && invoiceCustomer.email) {
      const emailPayload = {};
      if (installments) {
        const invoiceInstallments = await InvoiceInstallmentRepo.listInvoiceInstallment({ filter: { invoice: invoice.id } });
        const { installmentAmountResponse } = Utils.prepareInvoiceInstallmentPayloadForEmail(invoiceInstallments);
        Object.assign(emailPayload, installmentAmountResponse);
      }

      if (customer.address || invoiceCustomer.Address) {
        emailPayload.billingAddress = Utils.formatBillingAddress(customer.address || invoiceCustomer.Address);
      }

      const { subTotal, discount } = Utils.getInvoiceAmountBreakdown({
        currency: invoice.currency,
        amount: invoice.amount,
        vat: invoice.vat,
        discount: invoice.discount,
        discountType: invoice.discount_type,
      });

      const { due_date: dueDate, vat } = invoice;
      const invoiceId = Utils.generatePublicId(invoice.code);
      NotificationService.notifyUser({ email: invoiceCustomer.email }, "create-invoice", {
        recipientName: invoiceCustomer.name,
        creatorName: `${firstName} ${lastName}`,
        companyName,
        currency: invoice.currency,
        invoiceId: invoiceId.substring(invoiceId.length, 14),
        totalAmount: (parseInt(invoiceAmount, 10) / 100).toLocaleString(),
        invoiceUrl: `${Utils.getWebsiteURL()}/invoices/${invoiceId}`,
        recipientEmail: invoiceCustomer.email,
        year: new Date().getFullYear(),
        ...(vat && { vat }),
        ...(dueDate && { dueDate: Utils.formatHumanReadableDate(dueDate) }),
        discount,
        subTotal,
        ...emailPayload,
      });

      // Sync Invoice to Zoho
      // syncInvoiceToZoho(invoice.code, invoice.company);
    }
  },

  async list(req, res) {
    const { id: company } = req.company;
    const query = { ...req.query, company };
    const { error } = InvoiceValidator.listInvoices.validate(query);
    if (error) throw new ValidationError(error.message);

    const { invoices, meta, summary } = await InvoiceService.list(query);
    ResponseService.success(res, "Invoice fetched successfully", {
      invoices: Sanitizer.sanitizeModelInstances(invoices, "Invoice"),
      summary,
      meta,
    });
  },

  async view(req, res) {
    const {
      params: { code },
      company: { id: company },
      query: { includeTimeline = false },
    } = req;

    const { error } = InvoiceValidator.viewInvoice.validate({ code });
    if (error) throw new ValidationError(error.message);

    const invoice = await InvoiceService.view({ company, code, includeTimeline });
    const timeline = Boolean(includeTimeline) && InvoiceService.buildInvoiceTimeline(invoice);
    const attachments = await AssetService.getAssets({
      entityType: "Invoice",
      entityId: invoice.id,
    });

    const meta = {};
    const preferences = await CompanyPreferencesRepo.listPreferences({ company: invoice.company, feature: "invoices" });
    const enhancedPreferences = await CompanyPreferencesService.fetchAssociatedEntities(preferences);
    enhancedPreferences.forEach(({ key, value }) => {
      meta[key] = value;
    });
    ResponseService.success(res, "Invoice fetched successfully", {
      ...Sanitizer.sanitizeInvoice(invoice),
      ...(timeline && { timeline }),
      attachments: Sanitizer.sanitizeEntityAssets(attachments),
      meta,
    });
  },

  async update(req, res) {
    const {
      params: { code },
      company: { id: company },
    } = req;
    const { error } = InvoiceValidator.updateInvoice.validate({
      code,
      ...req.body,
    });
    if (error) throw new ValidationError(error.message);
    const { message, data: invoice } = await InvoiceService.updateInvoice({ filter: { code, company }, payload: req.body });

    ResponseService.success(res, message);
    InvoiceService.completeInvoiceEditing({ invoice, payload: req.body });
  },

  async remove(req, res) {
    const { code } = req.params;

    const { error } = InvoiceValidator.viewInvoice.validate({ code });
    if (error) throw new ValidationError(error.message);

    await InvoiceService.removeInvoice({ code });
    ResponseService.success(res, "Invoice deleted successfully");
  },

  async getInvoiceForCustomer(req, res) {
    const { code } = req;

    const { error } = InvoiceValidator.viewInvoice.validate({ code });
    if (error) throw new ValidationError(error.message);

    const { invoice, meta } = await InvoiceService.viewPublic({ code });
    const {
      Company: { Asset = null },
    } = invoice;
    let logoUrl;
    if (Asset) {
      const url = await AssetService.getAssetDownloadURL(Asset);
      logoUrl = url;
    }
    const sanitizedPayload = Sanitizer.sanitizeInvoice(invoice, "public");
    if (logoUrl) sanitizedPayload.company.logo = logoUrl;
    ResponseService.success(res, "Invoice fetched successfully", { ...sanitizedPayload, meta });
  },

  async shareInvoice(req, res) {
    const { code } = req.params;
    const { error } = InvoiceValidator.shareInvoice.validate({
      code,
      ...req.body,
    });
    if (error) throw new ValidationError(error.message);

    await InvoiceService.shareInvoice({ code, ...req.body });
    ResponseService.success(res, "Invoice shared successfully");
  },

  async finalizeInvoice(req, res) {
    await InvoiceService.logOrReverseInvoicePayment(req.body);
    ResponseService.success(res, "Ok");
  },

  async generateInvoiceAccountDetails(req, res) {
    const { code } = req;
    const { error } = InvoiceValidator.validateCode.validate({
      code,
    });
    if (error) throw new ValidationError(error.message);

    const invoice = await InvoiceRepo.getSingleInvoice({
      filter: { code },
      addCompany: true,
    });
    if (!invoice) throw new NotFoundError("Invoice");

    if (invoice.status === STATUSES.PAID) throw new ValidationError("Invoice has already been paid");
    const {
      company,
      Customer: { id: customer, BankAccounts = [] },
    } = invoice;

    const collectionAccount = await BankAccountRepo.getCollectionAccount({
      queryParams: { company },
    });
    if (!collectionAccount) await InvoiceService.createCollectionAccount({ company });

    let customerAccount;
    if (!BankAccounts.length) customerAccount = await CustomerService.createCustomerAccount({ payload: { customer, company } });
    else [customerAccount] = BankAccounts;

    const sanitizedAccounnt = Sanitizer.sanitizeInvoiceAccount(customerAccount);

    ResponseService.success(res, "Invoice account fetched successfully", sanitizedAccounnt);
  },

  async markInvoiceAsPaid(req, res) {
    const {
      params: { code },
      company: { id: company },
      user,
    } = req;
    const { error } = InvoiceValidator.markInvoiceAsPaid.validate({ ...req.params, ...req.body });
    if (error) throw new ValidationError(error.message);

    await InvoiceService.markInvoiceAsPaid({ filter: { code, company }, payload: req.body, extras: { user } });
    ResponseService.success(res, "Invoice marked as paid successfully");
  },

  /**
   * POST /:code/send-reminder
   * @param {*} req the request
   * @param {*} res the response
   */
  async sendReminder(req, res) {
    const { code } = req.params;
    const { error } = InvoiceValidator.validateCode.validate(req.params);
    if (error) throw new ValidationError(error.message);

    const invoice = await InvoiceRepo.getSingleInvoice({ filter: { code }, addCompany: true });
    if (!invoice) throw new NotFoundError("Invoice");

    InvoiceService.sendReminderToCustomer({ invoice });
    ResponseService.success(res, "Invoice reminder sent");
  },

  /**
   * Scan invoices
   * POST /invoices/scan
   */
  async scan(req, res) {
    const asset = await AssetService.getAsset(req.params.code);
    const data = await ScannerService.scan(asset, req.company.id);
    return ResponseService.success(res, "File scanned", data);
  },

  async completeApproval(req, res) {
    await InvoiceService.completeApproval({ ...req.params, ...req.body });
    ResponseService.success(res, "Ok");
  },

  async claimPayment(req, res) {
    await InvoiceService.claimPayment({ filter: { code: req.code }, payload: req.body });
    ResponseService.success(res, "Ok");
  },

  async generatePaymentLink(req, res) {
    const { error } = InvoiceValidator.generatePaymentReference.validate({ ...req.body, code: req.code });
    if (error) throw new ValidationError(error.message);

    const { message, data } = await InvoiceService.generatePaymentLink({ filter: { code: req.code }, payload: req.body });
    ResponseService.success(res, message, data);
  },

  async verifyPaymentReference(req, res) {
    const { error } = InvoiceValidator.validateReference.validate({ ...req.body, code: req.code });
    if (error) throw new ValidationError(error.message);

    const { message, data } = await InvoiceService.verifyPaymentReference({ filter: { code: req.code }, payload: req.body });
    ResponseService.success(res, message, data);
  },

  async syncZoho(req, res) {
    const {
      company: { id: company },
      body: { codes: invoiceCodes = [] } = {},
    } = req;
    if (!invoiceCodes.length) return ResponseService.failure(res, "Please select one or muliple invoices");
    await InvoiceService.syncInvoices({ filter: { company, code: invoiceCodes, status: [STATUSES.PENDING, STATUSES.PAID] } });
    return ResponseService.success(res, "Sync in progress");
  },
};
