const { STATUSES } = require("../models/status");
const { InvoiceTemplateRepo } = require("../repositories/index.repo");
const { Response: ResponseService } = require("../services");
const { sanitizeInvoiceTemplates } = require("../utils/sanitizer");

module.exports = {
  /**
   * List the templates
   * @param {*} res
   * @returns
   */
  async list(req, res) {
    const templates = await InvoiceTemplateRepo.getTemplates({ status: STATUSES.ACTIVE });
    return ResponseService.success(res, "Invoice templates fetched successfully", sanitizeInvoiceTemplates(templates));
  },
};
