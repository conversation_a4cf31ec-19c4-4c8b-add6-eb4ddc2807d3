const ResponseService = require("../services/response");
const MockDataService = require("../services/mockData.service");

module.exports = {
  async getIndustries(req, res) {
    const { message, data } = await MockDataService.getIndustries(req.user.id);
    return ResponseService.success(res, message, data);
  },
  async getCountries(req, res) {
    const countryCode = req.query.country_code;
    let response;
    if (countryCode) {
      response = await MockDataService.getACountry({
        country_code: countryCode,
      });
    } else response = await MockDataService.getCountries();
    const { message, data } = response;
    return ResponseService.success(res, message, data);
  },
  async getBusinessSize(req, res) {
    const { message, data } = await MockDataService.getBusinessSize(req.user.id);
    return ResponseService.success(res, message, data);
  },
  async getACountry(req, res) {
    const payload = {
      id: req.user.id,
      country_code: req.params.code,
    };
    const { message, data } = await MockDataService.getACountry(payload);
    return ResponseService.success(res, message, data);
  },
  async getCompanyType(req, res) {
    const { message, data } = await MockDataService.getCompanyType(req.user.id);
    return ResponseService.success(res, message, data);
  },
  async userIdType(req, res) {
    const { message, data } = await MockDataService.userIdType(req.user.id);
    return ResponseService.success(res, message, data);
  },
};
