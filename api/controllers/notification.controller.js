const Sanitizer = require("../utils/sanitizer");
const { Notification: NotificationService, Response: ResponseService } = require("../services");
const Sanitize = require("../utils/sanitizer");
const NotificationValidator = require("../validators/notification.validator");
const { ValidationError } = require("../utils/error.utils");

module.exports = {
  /**
   * List all Notification for a user
   * @method GET notification/
   * @param { object } req.query
   * @returns
   */
  async list(req, res) {
    const { company, id: user } = req.user;
    const criteria = { company, user, ...req.query };
    const { notifications, meta } = await NotificationService.listNotifications(criteria);
    ResponseService.success(res, "Notifications fetched successfully", {
      notifications: Sanitize.sanitizeNotifications(notifications),
      meta,
    });
  },

  /**
   * Marks a notification as read
   * @method GET notifications/${notification}
   * @param { object } req.params
   * @param { object } req.params.notification The notification to mark as read
   * @returns
   */
  async markAsRead(req, res) {
    const { notification } = req.params;
    const { id: user } = req.user;
    await NotificationService.markNotificationAsRead(user, notification);
    ResponseService.success(res, "Notification marked as read");
  },

  /**
   * Marks all notification as read
   * @method PATCH /notifications
   * @returns
   */
  async markAllAsRead(req, res) {
    const { id: user } = req.user;
    await NotificationService.markAllNotificationAsRead(user);
    ResponseService.success(res, "All Notifications marked as read");
  },

  async sendNotification(req, res) {
    const { error } = NotificationValidator.sendEmailNotification.validate(req.body);
    if (error) throw new ValidationError(error.message);
    const { success, error: notificationErrorMessage, message } = await NotificationService.sendNotification(req.body);
    ResponseService[success ? "success" : "failure"](res, message || notificationErrorMessage || "Notification sent successfully");
  },
};
