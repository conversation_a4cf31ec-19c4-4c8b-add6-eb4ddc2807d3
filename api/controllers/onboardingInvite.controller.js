const { ValidationError } = require('../utils/error.utils');
const { OnboardingInviteValidator } = require('../validators');
const { OnboardingInvite: OnboardingInviteService, Response: ResponseService } = require('../services');
const Sanitizer = require("../utils/sanitizer");


module.exports = {

    /**
     * Creates a new invitee to help in Onboarding
     * @method POST /invites
     * @param { Object } req.body
     * @return { Object }
     */
    async create(req, res) {
        const { id: company } = req.company;
        
        const { error } = OnboardingInviteValidator.create.validate(req.body);
        if(error) throw new ValidationError(error.message);

        const invitee = await OnboardingInviteService.create({ ...req.body, company });

        ResponseService.success(res, 'Invitation sent successfully', Sanitizer.sanitizeOnboardingInvite(invitee))
    },

    /**
     * List all invited persons to help in Onboarding
     * @method GET /invites
     * @param { Object } req.query
     * @return { Object } 
     */

     async list(req, res) {
        const invites = await OnboardingInviteService.list({ company: req.company.id, ...req.query });
        ResponseService.success(res, 'Invites fetched successfully', Sanitizer.sanitizeModelInstances(invites, 'OnboardingInvite'));
    },

    /**
     * Get a single invited person
     * @method GET /invites/:hash
     * @param
     */
    async view(req, res){
        const query = { company: req.company.id, hash: req.params.hash };

        const {invite, fields} = await OnboardingInviteService.view(query)
        const sanitizedInvite = Sanitizer.sanitizeOnboardingInvite(invite);
        ResponseService.success(res, 'Invite fetched successfully,', { ...sanitizedInvite, fields })
    },

    /**
     * Delete a single invite
     * @method DELETE /invites/:code
     * @param String req.params.code The code of the invite to delete
     */
     async remove(req, res){
        
        const { error } = OnboardingInviteValidator.delete.validate({ code: req.params.code });
        if(error) throw new ValidationError(error.message);

        const query = { company: req.company.id, code: req.params.code };
        const { message } = await OnboardingInviteService.removeInvite(query);
        ResponseService.success(res, message);
    },

    /**
     * Update a single invite
     * @method PATCH /invites/:code
     * @param String req.params.code The code of the invite to update
     */
     async update(req, res){
        
        const { error } = OnboardingInviteValidator.update.validate({ code: req.params.code, ...req.body });
        if(error) throw new ValidationError(error.message);

        const query = { company: req.company.id, code: req.params.code };
        const { message } = await OnboardingInviteService.updateInvite(query, req.body);
        ResponseService.success(res, message);
    },

    /**
     * Resend code to invited director
     * @method POST /invites/resend
     * @param { Object } req.body
     * @param String req.body.invite
     * @return { Object } {  }
     */

    async resend(req, res){
        const { error } = OnboardingInviteValidator.resend.validate(req.body);
        if(error) throw new ValidationError(error.message);

        OnboardingInviteService.resendDirectorInvite(req.body.invite);
        
        ResponseService.success(res, 'Invitation resent successfully');
    }
}