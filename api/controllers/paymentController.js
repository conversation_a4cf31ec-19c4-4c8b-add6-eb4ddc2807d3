const Sanitizer = require("../utils/sanitizer");
const {
  Bank: BankService,
  Response: ResponseService,
  Budget: BudgetService,
  Payment: Paymentservice,
  TeamMember: TeamMemberService,
  Teams: TeamService,
  Helper: HelperService,
  Balance: BalanceService,
  Redis,
} = require("../services");
const VendorService = require("../services/vendorService");
const TransactionService = require("../services/transaction");
const Sanitize = require("../utils/sanitizer");
const paymentValidator = require("../validators/payment.validator");
const { ValidationError, NotFoundError } = require("../utils/error.utils");
const PolicyService = require("../services/policy.service");
const CategoryService = require("../services/category.service");
const BillingService = require("../services/billing");
const SettingsService = require("../services/settings");
const ApprovalService = require("../services/approval.service");
const ScheduleService = require("../services/schedule.service");
const { STATUSES } = require("../models/status");
const {
  BatchTransactionRepo,
  VendorRepo,
  BankAccountRepo,
  BalanceRepo,
  UserRepo,
  BudgetRepo,
  CategorizationRuleRepo,
} = require("../repositories/index.repo");
const QueueService = require("../services/queue.service");
const AssetService = require("../services/asset");
const Utils = require("../utils");
const { DirectDebit } = require("../models");
const { ScheduledTransactionValidator } = require("../validators");
const { Transaction } = require("../models");
const { PROVIDERS } = require("../mocks/constants.mock");

const Controller = {
  async create(req, res, next) {
    let cbnFee = 0;
    let bujetiFee = 0;
    const { company, user, isManager } = req;
    const { impactBudget, ...payload } = req.body;
    const { amount, currency, saveAsDraft, recipientEmail = null, scheduleTransaction, directDebit } = payload;
    const { error } = paymentValidator.create.validate(payload);
    if (error) throw new ValidationError(error.message);
    const isScheduledPayment = scheduleTransaction; // scheduled payments should be allowed to go through pending funding
    const needForBalanceCheck = !(saveAsDraft || isScheduledPayment);
    const { minimumAmount } = SettingsService.get("pricing_config")[currency];
    if (amount < minimumAmount) {
      throw new ValidationError(`Minimum amount is ${currency}${minimumAmount / 100}`);
    }
    let existingMandate;
    let budget;

    if (payload.balance) {
      const {
        data: { bankAccount: directDebitAccount, isDirectDebit, existingMandate: mandate },
      } = await HelperService.isDirectDebitAccount({ balance: payload.balance });

      if (isDirectDebit) {
        payload.directDebit = {
          bankAccount: directDebitAccount.code,
        };
        existingMandate = mandate;
      }
    }

    // eslint-disable-next-line prefer-destructuring
    const paymentPlan = company.paymentPlan;
    const planConfiguration = Utils.parseJSON((paymentPlan && paymentPlan.configuration) || SettingsService.get("pricing_config"));
    const fees = BillingService.computeFees({
      amount,
      companyCode: req.company.code,
      currency,
      plan: planConfiguration,
      isDirectDebit: !!payload.directDebit,
    });

    cbnFee = fees.cbnFee;
    bujetiFee = fees.bujetiFee;

    const totalAmount = Number(payload.amount) + cbnFee + bujetiFee;

    // Check onboarding level and payment limit
    const willReachLimit = Utils.checkTransactionLimit({ company, amount, currency });
    if (!saveAsDraft && willReachLimit) throw new ValidationError("You have reached your transaction limit. Please complete your onboarding.");

    if (payload.budget) {
      // CHECK BUDGET FIRST
      const { data } = await BudgetService.canBudgetHandleTransaction({
        budget: payload.budget,
        amount,
        totalAmount,
        shouldCheckBalance: needForBalanceCheck,
      });
      budget = data.budget;

      const userTeamIds = await TeamMemberService.getUserTeamIds({
        user: user.id,
      });
      if (userTeamIds && userTeamIds.length) {
        const teamsBudgets = await TeamService.getTeamsBudgets(userTeamIds);
        const foundBudget = teamsBudgets.find((teamBudget) => teamBudget.Budget.code === payload.budget);
        if (foundBudget) payload.team = foundBudget.team;
      }

      // Check limit for budget owner
      if (!isManager) {
        await HelperService.checkBudgetOwnerLimit({ budget: budget.id, user: user.id, amount });
      }

      payload.budget = budget.id;
      payload.budgetCode = budget.code;
    }

    // existingMandate can be set above if balance is passed instead of directDebit
    if (directDebit && !existingMandate) {
      // DD is source of payment
      const { data } = await BankService.canAccountProcessPayment({ directDebit, amount, shouldCheckBalance: needForBalanceCheck });

      payload.balance = data.balance;
      existingMandate = data.existingMandate;
    } else if (payload.balance) {
      // Balance is source of payment
      const { data: isDirectDebitResponse } = await HelperService.isDirectDebitAccount({ balance: payload.balance });
      const { bankAccount = null, isDirectDebit = false } = isDirectDebitResponse || {};

      if (isDirectDebit) {
        if (!bankAccount) throw new NotFoundError("Bank Account");

        const { data } = await BankService.canAccountProcessPayment({
          directDebit: { bankAccount: bankAccount.code },
          amount,
          shouldCheckBalance: needForBalanceCheck,
        });

        payload.balance = data.balance;
        existingMandate = data.existingMandate;
      } else {
        const {
          data: { balance },
        } = await BalanceService.canBalanceHandleTransaction({
          balance: payload.balance,
          amount,
          totalAmount,
          company: company.id,
          shouldCheckBalance: needForBalanceCheck,
        });
        // attached the balance to the payment for reconciliation
        payload.balance = balance.id;
      }
    }

    const bankPayload = typeof payload.bank_account === "string" ? { code: payload.bank_account } : payload.bank_account;

    if (payload.category) {
      let foundCategory = await CategoryService.getCategory({
        code: payload.category,
      });
      if (!foundCategory) throw new NotFoundError("Category");
      if (!foundCategory.company) foundCategory = await CategoryService.cloneCategory(payload.category, req.company.id);
      payload.category = foundCategory.id;
    } else {
      const autoCategory = await CategorizationRuleRepo.autoCategorizer(payload.description, company.id);
      if (autoCategory) {
        payload.category = autoCategory.id;
      }
    }

    if (String(payload.recipient).startsWith("usr_") || String(payload.recipient).startsWith("vdr_")) {
      // RECIPIENT IS USER CODE
      if (String(payload.recipient).startsWith("usr_")) {
        const foundUser = await UserRepo.getOneUser({
          queryParams: { code: payload.recipient, company: company.id },
          selectOptions: ["firstName"],
        });
        if (!foundUser) throw new NotFoundError("User");
        const { id: recipientId, firstName } = foundUser;

        await BankAccountRepo.updateABankAccount({
          queryParams: {
            number: bankPayload.number,
            bankCode: bankPayload.bankCode,
            company: company.id,
          },
          updateFields: { ownerType: "user" },
        });

        payload.recipient = recipientId;
        payload.recipient_type = "user";
        payload.recipientName = firstName;
      }

      // RECIPIENT IS VENDOR CODE
      if (String(payload.recipient).startsWith("vdr_")) {
        const {
          id: recipientId,
          name,
          BankAccounts,
        } = await VendorService.getVendor(
          {
            code: payload.recipient,
            company: company.id,
          },
          { includeBankAccount: true }
        );

        const foundVendorBankAccount = Array.isArray(BankAccounts) && BankAccounts.length > 0 ? BankAccounts[0] : null;

        if (!foundVendorBankAccount) throw new NotFoundError("Recipient bank account");

        await VendorService.createVendorCategory(recipientId, payload.category);
        if (recipientEmail) await VendorRepo.updateVendor({ queryParams: { id: recipientId }, updateFields: { email: payload.recipientEmail } });

        payload.recipient = recipientId;
        payload.recipient_type = "vendor";
        payload.recipientName = name;
      }
    } else {
      // CREATE NEW VENDOR
      const { id: recipientId, name } = await VendorRepo.findOrCreateVendor({
        name: payload.recipient,
        company: company.id,
        industry: 1,
      });
      await VendorService.createVendorCategory(recipientId, payload.category);
      payload.recipient = recipientId;
      payload.recipient_type = "vendor";
      payload.recipientName = name;
    }

    if (payload.team && String(payload.team).startsWith("tms_")) {
      const foundTeam = await TeamService.getTeam({ code: payload.team });
      if (payload.budget) {
        const foundTeamBudget = await TeamService.getTeamBudget({ team: foundTeam.id, budget: payload.budget });
        if (!foundTeamBudget) throw new ValidationError(`${foundTeam.name} is not allowed to spend on this budget`);
      }
      payload.team = foundTeam.id;
    }

    if (typeof payload.bank_account === "object") {
      payload.bank_account.owner = payload.recipient;
      payload.bank_account.ownerType = payload.recipient_type;
      payload.bank_account.type = "real";
    }
    let {
      id: bankId,
      number: accountNumber,
      bankCode,
      isVerified: isBankVerified = false,
      accountName,
    } = await BankService.findOrCreate({ ...bankPayload, company: company.id, status: STATUSES.ACTIVE });
    if (!isBankVerified) {
      ({
        id: bankId,
        number: accountNumber,
        bankCode,
        isVerified: isBankVerified = false,
        accountName,
      } = await BankService.verifyBank({ bankCode, accountNumber, bankId }));
    }

    // exclude limit check for payments coming to bujeti
    if (!accountName?.includes(PROVIDERS.BUJETI)) {
      // check plan payout limit
      await Utils.checkPlanPayoutLimit({ company, amount, currency, Transaction });
    }

    if (payload.receipt && !Array.isArray(payload.receipt)) {
      await Paymentservice.callService("getReceiptOrThrowError", payload.receipt);
    }

    const policyStatus = await PolicyService.policyEnforcer({
      company: company.id,
      entity: {
        receipt: payload.receipt,
        description: payload.description,
        category: payload.category,
        budget: budget?.id,
        amount,
        user: req.user.id,
        team: payload.team,
        account: payload.balance,
        currency,
        vendor: payload.recipient,
        type: "payment",
      },
    });

    const narration = `${company.name.substring(0, 30)}/Payment of ${currency}${(parseInt(amount, 10) / 100).toLocaleString()}`.substring(0, 100); // the description also becomes the narration of the trx
    let message = "Payment created successfully";

    let createdDirectDebit;

    if (directDebit || existingMandate) {
      createdDirectDebit = await DirectDebit.create({
        mandate: existingMandate.id,
        company: company.id,
        amount: payload.amount,
        beneficiaryAccountNumber: accountNumber,
        beneficiaryBankCode: bankCode,
        narration,
        status: STATUSES.PENDING,
        reference: Utils.generateRandomString(14),
      });
    }

    if (payload.scheduleTransaction) {
      const { scheduleTransaction: diffScheduleTransaction, category, description } = payload;
      const { recurring, schedule, startDate, expiryDate } = diffScheduleTransaction;

      if (schedule === "hourly") {
        if (Utils.isLive()) {
          throw new ValidationError("hourly schedule not supported in this environment");
        }
      }
      const scheduledTransaction = await ScheduleService.createScheduleTransaction({
        currency,
        amount,
        budget: payload.budgetCode,
        balance: payload.balance,
        recurring,
        schedule,
        startDate,
        expiryDate,
        company: company.id,
        user: user.id,
        cronBody: {},
        payload: {
          bank_account: bankId,
          recipient: payload.recipient,
          recipientType: payload.recipient_type,
          bujeti_fee: bujetiFee,
          processor_fee: cbnFee,
          narration,
          category,
          description,
          directDebitId: createdDirectDebit?.id,
        },
      });

      payload.scheduledTransaction = (scheduledTransaction && scheduledTransaction.data) || {};

      if (Array.isArray(payload.receipt) && payload.receipt.length)
        await Paymentservice.callService("updateAssetWithEntity", payload.receipt, {
          entityId: scheduledTransaction.data && scheduledTransaction.data.id,
          entityType: "ScheduledTransaction",
        });

      const payNow = startDate && Utils.isLessThanOrEqualToNow(ScheduleService.combineDateTime(startDate.date, startDate.timestamp));
      if (!recurring || !payNow) return ResponseService.success(res, message);
    }

    payload.user = user;
    payload.bank_account = bankId;
    payload.company = company.id;
    payload.payer = user.id;
    payload.bujeti_fee = bujetiFee;
    payload.processor_fee = cbnFee;
    payload.narration = narration; // the description also becomes the narration of the trx
    payload.directDebitId = createdDirectDebit?.id;

    // TODO At this point we should be creating a pending transaction
    const response = await Paymentservice.createPayment(payload);

    if (policyStatus) {
      PolicyService.trackPolicyViolations({
        transaction: response?.policyViolationTransactionId,
        policyViolations: policyStatus,
      });
    }
    if (response.error) {
      message = response.data?.message || response.message || "Unexpected error, please retry later";
    }
    ResponseService[response.error ? "failure" : "success"](res, message, {
      transaction: response.code,
      requiresApproval: response.requiresApproval,
    });
    if (impactBudget) {
      Redis.set(`transaction:${response.code}:impactbudget`, impactBudget);
    }
    return next();
  },

  /**
   * POST /payments/calculate-fee
   * @param {*} req
   * @param {Object} req.body
   * @param {String} req.body.currency
   * @param {Number} req.body.amount
   * @param {*} res
   * @returns
   */
  async getFees(req, res) {
    const { amount, currency, isDirectDebit } = req.body;
    const { code: companyCode } = req.company;

    // eslint-disable-next-line prefer-destructuring
    const paymentPlan = req.company.paymentPlan;
    let cbnFee = 0;
    let bujetiFee = 0;
    const planConfiguration = Utils.parseJSON((paymentPlan && paymentPlan.configuration) || SettingsService.get("pricing_config"));
    if (Array.isArray(amount)) {
      amount.forEach((currentAmount) => {
        const { cbnFee: temporaryCbnFee, bujetiFee: temporaryBujetFee } = BillingService.computeFees({
          amount: currentAmount,
          companyCode,
          currency,
          plan: planConfiguration,
          isDirectDebit,
        });
        cbnFee += temporaryCbnFee;
        bujetiFee += temporaryBujetFee;
      });
    } else ({ cbnFee, bujetiFee } = BillingService.computeFees({ amount, companyCode, currency, plan: planConfiguration, isDirectDebit }));
    return ResponseService.success(res, "Fee computed", {
      fee: cbnFee + bujetiFee,
      currency,
    });
  },

  async list(req, res) {
    const { id: company } = req.company;
    const criteria = { company, ...req.query };
    if (req.isEmployee) criteria.user = req.user.id;
    const { meta, payments } = await Paymentservice.listPayment(criteria);
    ResponseService.success(res, "Payments fetched successfully", {
      payments: Sanitize.sanitizePayments(payments),
      meta,
    });
  },

  async batchPayment(req, res) {
    const { company, user } = req;
    const payload = req.body;
    const { transactions, name, budget, balance, receipt, ...leftOfPayload } = payload;

    const { error } = paymentValidator.bulkPay.validate(payload, {
      allowUnknown: true,
    });
    if (error) throw new ValidationError(error.message);

    // Checks there's a source for all transactions in the Batch...
    const transactionsWithSource = await Paymentservice.batchTransactionSourceCheck({ transactions, data: { budget, balance }, company });

    const response = await Paymentservice.createBulkPayment({
      ...payload,
      company,
      user,
    });

    await Paymentservice.callService("createPaymentData", transactionsWithSource, {
      payload: leftOfPayload,
      createdBatch: response,
      company,
      user,
    });

    const { id: batchId, code: batchCode } = response.batchtransaction;
    await BatchTransactionRepo.updateBatchTransaction({
      queryParams: { id: batchId },
      updateFields: { status: STATUSES.PROCESSING },
    });

    const { success } = await ApprovalService.conditionDetector({
      bulk: batchId,
      type: "batchPayment",
      company: company.id,
      user,
    });
    if (!success) await Paymentservice.makeBatchTransactionsPending(batchId);
    else {
      //  Send to queue for processing
      const SQSPayload = {
        batchCode,
        id: batchCode,
        path: `/batch/${batchCode}/process-bulk`,
        key: process.env.INTRA_SERVICE_TOKEN,
      };
      await QueueService.addJob(req, SQSPayload, `batchPayment:${batchCode}`);
    }
    ResponseService.success(res, "Payment created successfully");
  },

  async listBatchPayment(req, res) {
    const {
      company: { id: company },
    } = req;
    const criteria = { ...req.query, company, loggedInUser: req.user.id };
    if (req.isEmployeeRole) criteria.user = req.user.id;
    const response = await Paymentservice.listBatchTransactions(criteria);

    ResponseService.success(res, "Batch Payments fetched successfully", Sanitizer.sanitizeModelInstances(response, "BatchTransaction"));
  },

  async listScheduledPayment(req, res) {
    const { company } = req;
    const { error } = ScheduledTransactionValidator.listScheduledTransactions.validate(req.query);
    if (error) throw new ValidationError(error.message);

    const criteria = { company, ...req.query };

    if (!req.isManager) criteria.user = req.user.id;

    if (criteria.category) {
      const foundCategory = await CategoryService.getCategory({
        code: criteria.category,
      });
      if (!foundCategory) throw new NotFoundError("Category");
      criteria.category = foundCategory.id;
    }
    const { meta, scheduledTransactions } = await Paymentservice.listScheduledTransactions({
      ...criteria,
    });

    ResponseService.success(res, "Scheduled Payments fetched successfully", {
      scheduledTransactions: Sanitizer.sanitizeModelInstances(scheduledTransactions, "ScheduledTransaction"),
      meta,
    });
  },

  async addReceiptToBatch(req, res) {
    const { code } = req.params;

    const { error } = paymentValidator.paymentValidator.validate(req.body);
    if (error) throw new ValidationError(error.message);

    await Paymentservice.addRecieptToBatch({
      receipt: req.body.receipt,
      code,
    });

    ResponseService.success(res, "Receipt added to batch successfully");
  },

  async updateBatchPayment(req, res) {
    const { balance, source } = req.body;
    const payload = {};

    const existingBatch = await BatchTransactionRepo.getBatchTransaction({
      queryParams: { code: req.params.code },
    });

    if (!existingBatch) return ResponseService.failure(res, "Batch not found");
    if (existingBatch.status === STATUSES.PAID) return ResponseService.failure(res, "Batch already paid");
    if (existingBatch.status === STATUSES.DECLINED) return ResponseService.failure(res, "Batch already declined");

    if (balance || source) {
      const foundBalance = await BalanceRepo.getBalance({
        filter: { code: balance || source, status: STATUSES.ACTIVE },
      });
      if (!foundBalance) return ResponseService.failure(res, "Funding source not found, select another one");
      payload.balance = foundBalance.id;
      const isTiedToBalanceBudget = await BudgetRepo.getOneBudget({
        queryParams: { balance: foundBalance.id },
      });

      payload.budget = isTiedToBalanceBudget ? isTiedToBalanceBudget.id : null;
    }
    if (req.body.deadLine) payload.deadLine = req.body.deadLine;
    await BatchTransactionRepo.updateBatchTransaction({
      queryParams: {
        code: req.params.code,
      },
      updateFields: payload,
    });
    return ResponseService.success(res, "Batch payment successfully updated");
  },

  async getScheduledPayment(req, res) {
    const { company } = req;
    const { code } = req.params;
    const scheduledTransaction = await Paymentservice.getScheduledTransaction({
      company,
      code,
    });

    ResponseService.success(res, "Scheduled Payment fetched successfully", Sanitizer.sanitizeScheduledTransaction(scheduledTransaction));
  },

  async updateScheduledPayment(req, res) {
    const { company, user } = req;
    const payload = { company, user, ...req.body, ...req.params };

    const response = await Paymentservice.updateScheduledTransaction(payload);

    ResponseService.success(res, response.message);
  },

  async cancelScheduledPayments(req, res) {
    const { error } = ScheduledTransactionValidator.listScheduledTransactions.validate(req.query);
    if (error) throw new ValidationError(error.message);
    const { company, body } = req;

    const payload = { company, ...body };

    const response = await Paymentservice.cancelScheduledPayments(payload);

    ResponseService.success(res, response.message);
  },

  async updateScheduledPaymentReceipt(req, res) {
    const { receipt } = req.body;
    const { company, user } = req;

    const { code } = req.params;

    const { isInitiator, isAdminOrManager, scheduledTransaction } = await TransactionService.canUpdateScheduledTransactionPayer({
      code,
      company,
      user,
    });

    if (isInitiator || isAdminOrManager) {
      if (receipt) {
        const foundAsset = await AssetService.getAsset(receipt);
        if (!foundAsset) throw new NotFoundError("File");
        await AssetService.updateAsset({
          updateFields: {
            entityType: "ScheduledTransaction",
            entityId: scheduledTransaction.id,
          },
          queryParams: { code: receipt },
        });
      }
    }

    return ResponseService.success(res, "Scheduled transaction successfully updated");
  },

  async declineATransactionInABatch(req, res) {
    const { id: company } = req.company;
    const { code, transactionCode } = req.params;
    const { message } = await Paymentservice.declineATransactionInABatch({
      company,
      code,
      transactionCode,
    });

    ResponseService.success(res, message);
  },

  async addRecieptToExistingBatch(req, res) {
    const { code, assetCode } = req.params;
    const { message } = await Paymentservice.addRecieptToExistingBatch({
      assetCode,
      code,
    });

    ResponseService.success(res, message);
  },

  async processSingleBatchPayment(req, res) {
    const { body } = req;
    const { idempotencyKey, ...rest } = body;
    await ApprovalService.completePayment({ ...rest, id: idempotencyKey });
    ResponseService.success(res, "Ok");
  },

  async requeryBatchTransactions(req, res) {
    const {
      params: { code },
    } = req;

    const { Transactions: batchTransactions } = await BatchTransactionRepo.getBatchTransaction({
      queryParams: { code, status: STATUSES.PROCESSING },
      getTransaction: true,
    });
    const pendingTransactions = Array.from(batchTransactions).filter((transaction) => !transaction.externalIdentifier);

    // eslint-disable-next-line array-callback-return
    Array.from(pendingTransactions).map((transaction) => {
      const transactionData = transaction.toJSON();
      const SQSPayload = {
        ...transactionData,
        id: transactionData.id,
        idempotencyKey: transactionData.id,
        path: `/batch/${code}/transactions/${transactionData.code}/process`,
        key: process.env.INTRA_SERVICE_TOKEN,
      };
      QueueService.addJob(req, SQSPayload, `batchPayment:${transactionData.code}`);
    });
    ResponseService.success(res, "Ok");
  },

  async processSinglePayment(req, res) {
    await Paymentservice.processPaymentInQueue({ transaction: req.params.code, ...req.body });
    ResponseService.success(res, "Ok");
  },

  async requeryProcessingPayment(req, res) {
    await Paymentservice.requeryProcessingPayment(req.body);
    ResponseService.success(res, "Ok");
  },

  async chargeFees(req, res) {
    const { notify, message } = await Paymentservice.chargeFees(req.body);
    ResponseService.plainJson(res, {
      code: notify ? 402 : 200,
      message,
    });
  },

  async processBulkTransaction(req, res) {
    const { message } = await Paymentservice.processBulkTransaction(req.params);
    ResponseService.success(res, message || "OK");
  },

  async log(req, res) {
    const { company, user } = req;
    const payload = req.body;
    const { amount, currency, fees } = payload;
    const { error } = paymentValidator.log.validate(payload);
    if (error) throw new ValidationError(error.message);
    let policyStatus;
    // eslint-disable-next-line prefer-destructuring
    if (payload.budget) {
      // CHECK BUDGET FIRST
      const budget = await BudgetService.getBudget({ code: payload.budget });
      const userTeamIds = await TeamMemberService.getUserTeamIds({
        user: user.id,
      });
      if (userTeamIds && userTeamIds.length) {
        const teamsBudgets = await TeamService.getTeamsBudgets(userTeamIds);
        const foundBudget = teamsBudgets.find((teamBudget) => teamBudget.Budget.code === payload.budget);
        if (foundBudget) payload.team = foundBudget.team;
      }
      if (!budget) throw new NotFoundError("Budget");
      payload.budget = budget.id;
      payload.balance = budget.balance;

      // Checks Policies add [strictDefault: true] to make it bypass the Violations
      policyStatus = await PolicyService.policyBudgetEnforcer(
        {
          budget: budget.id,
          amount,
          currency,
          company: company.id,
          reciept: payload.receipt,
        },
        false
      );
    }

    const balance = await BalanceRepo.getBalance({
      filter: {
        company: company.id,
        currency: payload.currency,
        ...(payload.balance && typeof payload.balance === "string" && { code: payload.balance }),
        ...(payload.balance && typeof payload.balance === "number" && { id: payload.balance }),
      },
    });
    if (!balance) throw new NotFoundError("Balance");

    // attached the balance to the payment for reconciliation
    payload.balance = balance.id;

    const bankPayload = typeof payload.bank_account === "string" ? { code: payload.bank_account } : payload.bank_account;

    if (payload.category) {
      const foundCategory = await CategoryService.getCategory({
        code: payload.category,
      });
      if (!foundCategory) throw new NotFoundError("Category");
      payload.category = foundCategory.id;
    }

    if (String(payload.recipient).startsWith("usr_") || String(payload.recipient).startsWith("vdr_")) {
      // RECIPIENT IS USER CODE
      if (String(payload.recipient).startsWith("usr_")) {
        const foundUser = await UserRepo.getOneUser({
          queryParams: { code: payload.recipient, company: company.id },
          selectOptions: ["firstName"],
        });
        if (!foundUser) throw new NotFoundError("User");
        const { id: recipientId, firstName } = foundUser;

        await BankAccountRepo.updateABankAccount({
          queryParams: {
            number: bankPayload.number,
            bankCode: bankPayload.bankCode,
            company: company.id,
          },
          updateFields: { ownerType: "user" },
        });

        payload.recipient = recipientId;
        payload.recipient_type = "user";
        payload.recipientName = firstName;
      }

      // RECIPIENT IS VENDOR CODE
      if (String(payload.recipient).startsWith("vdr_")) {
        const { id: recipientId, name } = await VendorService.getVendor({
          code: payload.recipient,
          company: company.id,
        });

        await VendorService.createVendorCategory(recipientId, payload.category);
        if (payload.recipientEmail)
          await VendorRepo.updateVendor({ queryParams: { id: recipientId }, updateFields: { email: payload.recipientEmail } });

        payload.recipient = recipientId;
        payload.recipient_type = "vendor";
        payload.recipientName = name;
      }
    } else {
      // CREATE NEW VENDOR
      const { id: recipientId, name } = await VendorRepo.findOrCreateVendor({
        name: payload.recipient,
        company: company.id,
        industry: 1,
      });
      await VendorService.createVendorCategory(recipientId, payload.category);
      payload.recipient = recipientId;
      payload.recipient_type = "vendor";
      payload.recipientName = name;
    }

    if (typeof payload.bank_account === "object") {
      payload.bank_account.owner = payload.recipient;
      payload.bank_account.ownerType = payload.recipient_type;
      payload.bank_account.type = "real";
    }

    let {
      id: bankId,
      number: accountNumber,
      bankCode,
      isVerified: isBankVerified = false,
    } = await BankService.findOrCreate({ ...bankPayload, status: STATUSES.ACTIVE, company: company.id });
    if (!isBankVerified) {
      ({
        id: bankId,
        number: accountNumber,
        bankCode,
        isVerified: isBankVerified = false,
      } = await BankService.verifyBank({ bankCode, accountNumber, bankId }));
    }

    if (payload.receipt && !Array.isArray(payload.receipt)) {
      await Paymentservice.callService("getReceiptOrThrowError", payload.receipt);
    }

    const message = "Expenses logged successfully";

    payload.user = user;
    payload.bank_account = bankId;
    payload.company = company.id;
    payload.payer = user.id;
    payload.bujeti_fee = 0;
    payload.processor_fee = fees;
    payload.status = STATUSES.IMPORTED;

    // TODO At this point we should be creating a pending transaction
    const response = await Paymentservice.createPayment(payload);

    if (policyStatus) {
      PolicyService.trackPolicyViolations({
        transaction: response?.policyViolationTransactionId,
        policyViolations: policyStatus,
      });
    }
    await Paymentservice.handleRelevantLedgers(payload);
    ResponseService[response.error ? "failure" : "success"](res, message);
  },

  async viewBatch(req, res) {
    const { code } = req.params;
    const batchTransaction = await Paymentservice.getBatchTransaction({
      company: req.company.id,
      code,
      user: req.user,
      query: req.query,
    });
    const sanitizedTransactions = Sanitizer.sanitizeBatchTransaction(batchTransaction);
    return ResponseService.success(res, "Batch transaction retrieved", sanitizedTransactions);
  },

  async garbageCollector(req, res, next) {
    const originalJSON = res.json.bind(res);
    const { statusCode } = res;
    if ([200, 201].includes(statusCode)) {
      res.json = (body) => {
        if (!body.error) {
          const requiresApproval = body?.requiresApproval;
          if (requiresApproval) return originalJSON(body);

          const code = body?.transaction || body?.data?.transaction;
          const SQSPayload = {
            id: `gc:${code}`,
            company: req.company.code,
            idempotencyKey: `gc:${code}`,
            path: `/transactions/requery`,
            code,
            notify: true,
            key: process.env.INTRA_SERVICE_TOKEN,
          };
          QueueService.addDelayedJob(req, SQSPayload, `Garbage collector(trx:${code})`, 60);
        }
        return originalJSON(body);
      };
    }
    return next();
  },

  /**
   * This controller takes a bulk transfer code and requeries all Transactions in it
   * @param {*} req
   * @param {*} res
   */
  async requeryBulkTransaction(req, res) {
    await Paymentservice.requeryBulkTransaction({ filter: req.params });
    ResponseService.success(res, "Ok");
  },
};

module.exports = Controller;
