const { Sequelize } = require("sequelize");
const ResponseService = require("../services/response");
const PolicyService = require("../services/policy.service");
const Sanitizer = require("../utils/sanitizer");
const { PolicyValidator } = require("../validators");
const { ValidationError, NotFoundError, HttpException, HttpStatus } = require("../utils/error.utils");
const { PolicyDocument, Policy, User } = require("../models");
const { STATUSES } = require("../models/status");
const { Asset } = require("../models");
const CategoryService = require("../services/category.service");

module.exports = {
  /**
   * To list all policy types
   * @method GET /policies/types
   * @param { object } req
   */
  async getPolicyTypes(req, res) {
    const { message, data } = await PolicyService.getPolicyTypes();
    return ResponseService.success(res, message, Sanitizer.sanitizePolicyTypes(data));
  },

  /**
   * To Create a new policy
   * @method POST /policies
   * @param { object } req
   * @param { number } req.company.id company id
   * @param { string } req.company.code company code
   * @param { string } req.body.frequency frequency of the policy used only with calender limit policy type
   * @param { string[] } req.body.period period of the policy used only with calender limit policy type
   * @param { number } req.body.amount amount of the policy used only with individual Allocation policy type
   * @param { number } req.body.amountRange.maxAmount maxAmount of the policy used only with Spend limit policy type
   * @param { number } req.body.amountRange.minAmount minAmount of the policy used only with Spend limit policy type
   * @param { string } req.body.currency currency of the amount in the policy not used only in calender limit policy type
   * @param { string[] } req.body.budget array of budget code
   * @param { string } req.body.type policy type code
   * @param { string } req.body.name policy name
   * @param { string } req.body.description policy description
   */
  async createPolicy(req, res) {
    const payload = { company: req.company.id, companyCode: req.company.code, ...req.body };
    const { budget } = req.body;

    if (req.body.requiresReceipt) {
      payload.receiptAmount = {
        condition: "all",
      };
      delete req.body.requiresReceipt;
    }

    const { error } = PolicyValidator.createPolicy.validate(req.body);
    if (error) throw new ValidationError(error.message);

    let budgetPolicy;

    if (!Array.isArray(payload.type)) {
      payload.type = [payload.type];
    }

    payload.lastModifiedBy = req.user.id;

    const { message, data } = await PolicyService.createPolicy(payload);

    let responseMessage = message;

    if (budget) {
      const {
        success,
        message,
        data: newBudgetPolicy,
      } = await PolicyService.createBudgetPolicy({ company: payload.company, policy: data.id, budget });
      if (!success) throw new HttpException("Error assigning policy to budget", HttpStatus.PARTIAL_CONTENT);
      responseMessage = message;
      budgetPolicy = newBudgetPolicy;
    }

    return ResponseService.success(res, responseMessage, Sanitizer.sanitizePolicy(data));
  },

  /**
   * To link a budget to a policy
   * @method POST /policies/:code/budget/:budgetCode
   * @param { object } req
   * @param { number } req.company.id company id
   * @param { string } req.params.code policy code
   * @param { string } req.params.budgetCode budget code
   */
  async linkPolicyToBudget(req, res) {
    const { budgetCode, code } = req.params;

    const { error } = PolicyValidator.linkBudgetPolicy.validate({ budgetCode, code });
    if (error) throw new ValidationError(error.message);

    // Checking if policy with same name already exists
    const { success: status, message: checkPolicyMessage, data: checkPolicy } = await PolicyService.getPolicy({ code, company: req.company.id });
    if (!status) throw new HttpException(checkPolicyMessage);

    const { message, data } = await PolicyService.createBudgetPolicy({ company: req.company.id, policy: checkPolicy.id, budget: budgetCode });
    return ResponseService.success(res, message, Sanitizer.sanitizePolicy(checkPolicy));
  },

  /**
   * To list all policies
   * @method GET /policies
   * @param { object } req
   * @param { number } req.company.id company id
   * @param { string } req.query.type policy type code
   * @param { string } req.query.search strings to user in a filter search
   * @param { string } req.query.budget budget code
   */
  async getPolicies(req, res) {
    const query = { company: req.company.id, ...req.query };

    const { error } = PolicyValidator.getPolicies.validate(req.query);
    if (error) throw new ValidationError(error.message);

    if (query.category) {
      const foundCategory = await CategoryService.getCategory({
        code: query.category,
      });
      if (!foundCategory) throw new NotFoundError("Category");
      query.category = foundCategory.id;
    }

    const { message, data } = await PolicyService.getPolicies(query);
    return ResponseService.success(res, message, {
      policies: Sanitizer.sanitizePolicies(data.policies),
      meta: data.meta,
    });
  },

  /**
   * To view a policy
   * @method GET /policies/:code
   * @param { object } req
   * @param { number } req.company.id company id
   * @param { string } req.company.code company code
   * @param { string } req.params.code policy code
   */
  async getPolicy(req, res) {
    const query = { company: req.company.id, ...req.params };

    const { error } = PolicyValidator.getPolicy.validate(req.params);
    if (error) throw new ValidationError(error.message);

    const { message, data } = await PolicyService.getPolicy(query);
    return ResponseService.success(res, message, Sanitizer.sanitizePolicy(data));
  },

  /**
   * To pause a budget policy
   * @method PUT /policies/:code
   * @param { object } req
   * @param { number } req.company.id company id
   * @param { string } req.company.code company code
   * @param { string } req.params.code policy code
   * @param { string } req.body.frequency frequency of the policy used only with calender limit policy type
   * @param { string[] } req.body.period period of the policy used only with calender limit policy type
   * @param { number } req.body.amount amount of the policy used only with individual Allocation policy type
   * @param { number } req.body.amountRange.maxAmount maxAmount of the policy used only with Spend limit policy type
   * @param { number } req.body.amountRange.minAmount minAmount of the policy used only with Spend limit policy type
   * @param { string } req.body.currency currency of the amount in the policy not used only in calender limit policy type
   * @param { string } req.body.type policy type code
   * @param { string } req.body.name policy name
   * @param { string } req.body.description policy description
   */

  async updatePolicy(req, res) {
    const query = { company: req.company.id, lastModifiedBy: req.user.id, companyCode: req.company.code, code: req.params.code, ...req.body };

    if (req.body.requiresReceipt) {
      query.receiptAmount = {
        condition: "all",
      };
      delete req.body.requiresReceipt;
    }

    const { error } = PolicyValidator.updatePolicy.validate(req.body);
    if (error) throw new ValidationError(error.message);

    if (query.type && !Array.isArray(query.type)) {
      query.type = [query.type];
    }

    const { message, data } = await PolicyService.updatePolicy(query);

    return ResponseService.success(res, message, Sanitizer.sanitizePolicy(data));
  },

  /**
   * To pause a budget policy
   * @method PUT /policies/:code/budget/:budgetCode
   * @param { object } req
   * @param { number } req.company.id company id
   * @param { string } req.params.code policy code
   * @param { string } req.params.budgetCode budget code
   */
  async pauseBudgetPolicy(req, res) {
    const query = { company: req.company.id, status: req.query.status, ...req.params };

    const { error } = PolicyValidator.pauseBudgetPolicy.validate(req.params);
    if (error) throw new ValidationError(error.message);

    const { message, data } = await PolicyService.pauseBudgetPolicy(query);
    return ResponseService.success(res, message);
  },

  /**
   * To delete a budget policy
   * @method DELETE /policies/:code/budget/:budgetCode
   * @param { object } req
   * @param { number } req.company.id company id
   * @param { string } req.params.code policy code
   * @param { string } req.params.budgetCode budget code
   */
  async deleteBudgetPolicy(req, res) {
    const query = { company: req.company.id, ...req.params };

    const { error } = PolicyValidator.deleteBudgetPolicy.validate(req.params);
    if (error) throw new ValidationError(error.message);

    const { message, data } = await PolicyService.removeBudgetPolicy(query);
    return ResponseService.success(res, message);
  },

  /**
   * To delete a policy
   * @method DELETE /policies/:code
   * @param { object } req
   * @param { number } req.company.id company id
   * @param { string } req.params.code policy code
   */
  async deletePolicy(req, res) {
    const query = { company: req.company.id, ...req.params };

    const { error } = PolicyValidator.getPolicy.validate(req.params);
    if (error) throw new ValidationError(error.message);

    const { message, data } = await PolicyService.removePolicy(query);
    return ResponseService.success(res, message);
  },

  /**
   * To add a document to a policy
   * @method PUT /policies/:code/documents/:asset
   * @param { object } req
   * @param { number } req.company.id company id
   * @param { string } req.params.code policy code
   * @param { string } req.params.asset document code
   */
  async addDocument(req, res) {
    const query = { company: req.company.id, ...req.params, ...(req.body || {}) };

    const { error } = PolicyValidator.addDocument.validate(req.params);
    if (error) throw new ValidationError(error.message);

    const addedDocument = await PolicyService.addDocumentToPolicy(query);
    if (!addedDocument) return ResponseService.failure(res, "Document could not be added, please retry");
    return ResponseService.success(res, "Document successfully added");
  },
  /**
   * To add a document to a policy
   * @method DELETE /policies/:code/documents/:asset
   * @param { object } req
   * @param { number } req.company.id company id
   * @param { string } req.params.code policy code
   * @param { string } req.params.asset document code
   */
  async removeDocument(req, res) {
    const query = { company: req.company.id, ...req.params };

    const { error } = PolicyValidator.addDocument.validate(req.params);
    if (error) throw new ValidationError(error.message);

    const removedDocument = await PolicyService.removeDocumentFromPolicy(query);
    if (!removedDocument) return ResponseService.success(res, "Document could not be removed, please retry");
    return ResponseService.success(res, "Document successfully removed");
  },
  /**
   * To remove an exceptionnal user
   * @method DELETE /policies/:code/exceptions/:user
   * @param { object } req
   * @param { number } req.company.id company id
   * @param { string } req.params.code policy code
   * @param { string } req.params.user document code
   */
  async removeException(req, res) {
    const foundUser = await User.findOne({ where: { code: req.params.user } });
    if (!foundUser) throw new NotFoundError("User a " + req.params.user);

    const foundPolicy = await Policy.findOne({ where: { code: req.params.code } });
    if (!foundPolicy) throw new NotFoundError("Policy");

    const userRemoved = await PolicyService.removeException(foundUser.id, foundPolicy.id);
    if (!userRemoved) return ResponseService.success(res, "User could not be removed from exception list");
    return ResponseService.success(res, "User removed from exception list");
  },
  /**
   * To get policies documents
   * @method GET /policies/documents
   * @param { object } req
   */
  async getPolicyDocuments(req, res) {
    const policiesAssets = await PolicyDocument.findAll({
      where: {
        company: req.company.id,
        status: STATUSES.ACTIVE,
      },
      group: ["asset"],
      attributes: ["asset", [Sequelize.fn("max", Sequelize.col("PolicyDocument.created_at")), "created_at"]],
      include: Asset,
      order: [["created_at", "DESC"]],
    });
    return ResponseService.success(res, "Documents retrieved successfully", Sanitizer.sanitizeEntityAssets(policiesAssets.map(({ Asset }) => Asset)));
  },
};
