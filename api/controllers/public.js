/* eslint-disable no-prototype-builtins */
const { differenceInDays, parseISO } = require("date-fns");
const ResponseService = require("../services/response");
const TransactionService = require("../services/transaction");
const CategoryService = require("../services/category.service");
const { NotFoundError, ValidationError } = require("../utils/error.utils");
const SanitizerService = require("../utils/sanitizer");
const { TransactionValidator } = require("../validators");

module.exports = {
  async list(req, res) {
    const {
      company: { id: company },
    } = req;

    const { error } = TransactionValidator.listTransactions.validate(req.query);
    if (error) throw new ValidationError(error.message);

    const criteria = { company, perPage: 1000, ...req.query };

    if (criteria.category) {
      const foundCategory = await CategoryService.getCategory({
        code: criteria.category,
      });
      if (!foundCategory) throw new NotFoundError("Category");
      criteria.category = foundCategory.id;
    }
    const { transactions, meta } = await TransactionService.listTransactions({ ...criteria });
    const sanitizedTransactions = SanitizerService.sanitizePublicTransactions(transactions);
    return ResponseService.success(res, "Transactions retrieved", {
      transactions: sanitizedTransactions,
      meta,
    });
  },
  async view(req, res) {
    const { code } = req.params;
    const transactions = await TransactionService.getTransaction(code, {
      company: req.company.id,
    });
    const sanitizedTransactions = SanitizerService.sanitizePublicTransaction(transactions);
    return ResponseService.success(res, "Transaction retrieved", sanitizedTransactions);
  },
};
