const { Op } = require("sequelize");
const { Referral, ReferralCode, Campaign } = require("../models");
const Response = require("../services/response");
const { ValidationError, NotFoundError } = require("../utils/error.utils");
const { addDays, getDashboardURL } = require("../utils");
const { notifyUser } = require("../services/notification");
const { ReferralRepo } = require("../repositories/index.repo");
const Sanitizer = require("../utils/sanitizer");
const ReferralService = require("../services/referral.service");

module.exports = {
  async sendReferral(req, res) {
    const { email } = req.body;
    const existingReferral = await Referral.findOne({ where: { email, referrer: req.user.id } });

    if (existingReferral) return Response.success(res, "You have already referred this friend");
    const foundReferralCode = await ReferralCode.findOne({ where: { owner: req.user.id, ownerType: "user" } });
    if (!foundReferralCode) throw new ValidationError("You do not have a referral code to share");
    const payload = {
      referrer: req.user.id,
      referralCode: foundReferralCode.id,
      email,
      validUntil: addDays(new Date(), 90),
    };

    await Referral.create(payload);
    notifyUser({ email }, "referral-invite", {
      referralLink: `${getDashboardURL()}/register?referralCode=${foundReferralCode.code.replace("rfc_", "").substr(0, 8)}`,
      senderName: req.user.firstName,
      companyName: req.company.name,
    });
    return Response.success(res, "Invitation sent");
  },

  async createReferral(req, res) {
    const { type, code, campaign, referrer = 1 } = req.body;
    const existingReferralCode = await ReferralCode.findOne({ where: { code, type } });

    if (existingReferralCode) return Response.success(res, "You have already created this referral");
    const payload = {
      referrer,
      code: `rfc_${code}`,
      validUntil: addDays(new Date(), 90),
    };
    const referralCode = await ReferralCode.create(payload);
    if (campaign && typeof campaign === "object") {
      await Campaign.create({
        referralCode: referralCode.id,
        name: campaign.name,
        platform: campaign.platform,
        startDate: campaign.startDate || new Date(),
        endDate: campaign.endDate,
      });
    }
    return Response.success(res, "Referral code created");
  },

  async listReferrals(req, res) {
    const { page = 1, perPage: requestedPerPage = 20 } = req.query;
    const perPage = Math.min(parseInt(requestedPerPage, 10), 50);

    const { code } = req.params;
    const { id: referrer } = req.user;
    const criteria = {};
    if (code) {
      const referralCode = await ReferralCode.findOne({ where: { code: { [Op.like]: `rfc_${code}` } } });
      if (!referralCode) throw new NotFoundError("Referral code");
      criteria.referralCode = referralCode.id;
    }
    if (referrer) criteria.referrer = referrer;
    const referrals = await Referral.findAll({
      where: criteria,
      limit: perPage,
      ...(page && { skip: perPage * (parseInt(page, 10) - 1) }),
    });
    const count = await Referral.count({ where: criteria });
    return Response.success(res, "Your Referrals", {
      referrals: Sanitizer.sanitizeReferrals(referrals),
      meta: {
        total: count,
        page,
        nextPage: parseInt(page, 10) + 1,
        hasMore: count > 50 * parseInt(page, 10),
        perPage,
      },
    });
  },

  async getReferralCode(req, res) {
    const { id: user } = req.user;
    const { code } = req.params;

    let foundReferralCode = await (code ? ReferralRepo.getReferralCode(code) : ReferralRepo.getUserReferrals(user));

    if (!foundReferralCode) {
      await ReferralRepo.generateUserReferralCode(user, ReferralRepo.ReferralTypes.REWARDS);
      foundReferralCode = await ReferralRepo.getUserReferrals(user);
    }
    const { referralCode, referrals } = Sanitizer.sanitizeReferralCode(foundReferralCode);
    return Response.success(res, "Your referral code", {
      referralCode,
      referrals,
    });
  },

  async processRewards(req, res) {
    const result = await ReferralService.processRewards(req.body);
    return Response.success(res, "OK", result);
  },
};
