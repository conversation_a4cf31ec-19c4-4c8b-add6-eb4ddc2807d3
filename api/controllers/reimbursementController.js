const ResponseService = require("../services/response");
const ReimbursementService = require("../services/reimbursementService");
const CategoryService = require("../services/category.service");
const VendorService = require("../services/vendorService");
const SanitizerService = require("../utils/sanitizer");
const BudgetService = require("../services/budget");
const { STATUSES } = require("../models/status");
const TeamService = require("../services/teams.service");
const TeamMemberService = require("../services/teamMember.service");
const PaymentService = require("../services/paymentservice");
const { NotFoundError, ValidationError } = require("../utils/error.utils");
const BalanceRepo = require("../repositories/balance.repo");
const PolicyService = require("../services/policy.service");
const ReimbursementValidator = require("../validators/reimbursement");
const BalanceService = require("../services/balance");
const BillingService = require("../services/billing");
const SettingsService = require("../services/settings");
const HelperService = require("../services/helper.service");
const BankService = require("../services/bank");
const { CategorizationRuleRepo, ReimbursementRepo } = require("../repositories/index.repo");
const Utils = require("../utils");
const { ONBOARDING_LEVEL } = require("../models/company");
const { Transaction } = require("../models");
const { sendSlackNotification } = require("../utils/slack.utils");
const { NOTIFICATION_TYPE } = require("../constants/notifications");

module.exports = {
  async create(req, res) {
    ReimbursementService.validateCreationPayload(req.body);
    const { id: company } = req.company;
    const { id: user } = req.user;
    const payload = {
      company,
      user,
      ...req.body,
    };
    const { currency, amount, receipt } = req.body || {};
    const notificationPayload = {
      company: req.company.id,
      user: {
        firstName: req.user.firstName,
        lastName: req.user.lastName,
        code: req.user.code,
      },
    };

    const isBudget = payload.source?.startsWith("bdg_");
    const isBalance = payload.source?.startsWith("blc_");

    if (req.body.budget || isBudget) {
      const { id: budget, name: budgetName } =
        (await BudgetService.getBudget({
          code: req.body.budget || payload.source,
          company,
        })) || {};
      if (!budget) throw new NotFoundError("Budget");
      payload.budget = budget;
      notificationPayload.budget = {
        name: budgetName,
        code: req.body.budget,
      };

      const userTeamIds = await TeamMemberService.getUserTeamIds({ user });
      if (userTeamIds && userTeamIds.length) {
        const teamsBudgets = await TeamService.getTeamsBudgets(userTeamIds);
        const foundBudget = teamsBudgets && teamsBudgets.find((teamBudget) => teamBudget.Budget.code === req.body.budget);
        if (foundBudget) payload.team = foundBudget.team;
      }
    }

    if (req.body.receipt) {
      if (!Array.isArray(req.body.receipt)) req.body.receipt = [req.body.receipt];
      await PaymentService.callService("getReceiptOrThrowError", req.body.receipt);
    }

    if (req.body.vendor) {
      const foundVendor = await VendorService.getVendor({
        code: req.body.vendor,
      });
      payload.vendor = foundVendor.id;
      notificationPayload.vendor = {
        name: foundVendor.name,
        email: foundVendor.email,
        code: foundVendor.code,
      };
    }

    if (req.body.category) {
      const foundCategory = await CategoryService.getCategory({
        code: req.body.category,
      });
      if (!foundCategory) throw new NotFoundError("Category");
      payload.category = foundCategory.id;
      notificationPayload.category = {
        name: foundCategory.name,
        code: foundCategory.code,
      };
    } else {
      const autoCategory = await CategorizationRuleRepo.autoCategorizer(payload.description, req.company.id);
      if (autoCategory) {
        payload.category = autoCategory.id;

        notificationPayload.category = {
          name: autoCategory.name,
          code: autoCategory.code,
        };
      }
    }

    if (req.body.balance || isBalance) {
      const foundBalance = await BalanceRepo.getBalance({ filter: { code: req.body.balance || payload.source, company } });
      if (!foundBalance) throw new NotFoundError("Balance");
      payload.balance = foundBalance.id;
      notificationPayload.source = {
        name: foundBalance.name,
        code: foundBalance.code,
      };
    }

    await PolicyService.policyEnforcer({
      company,
      entity: {
        receipt,
        description: payload.description,
        category: payload.category,
        budget: payload.budget,
        amount,
        user,
        team: payload.team,
        account: payload.balance,
        currency,
        vendor: payload.vendor,
        type: "reimbursement",
      },
    });

    await ReimbursementService.createReimbursement({ ...payload, foundCompany: req.company });
    ResponseService.success(res, "Reimbursement requested successfully");
    sendSlackNotification(NOTIFICATION_TYPE.REIMBURSEMENT, { ...payload, ...notificationPayload, type: NOTIFICATION_TYPE.REIMBURSEMENT });
  },
  async list(req, res) {
    const { id: company } = req.company;
    const criteria = { company, ...req.query };

    if (req.isEmployeeRole || !req.hasReimbursementEditPermission) criteria.user = req.user.id;

    const { meta, reimbursements } = await ReimbursementService.listReimbursements({ ...criteria, loggedInUser: req.user.id });

    const summary = await ReimbursementRepo.reimbursementSummary({ filter: criteria });

    ResponseService.success(res, "Reimbursements fetched successfully", {
      reimbursements: SanitizerService.sanitizeReimbursements(reimbursements),
      meta,
      summary: HelperService.groupSummaryByCurrency(summary),
    });
  },
  async view(req, res) {
    const { code } = req.params;
    const { id: company } = req.company;
    const criteria = { code, company };
    if (req.isEmployeeRole) criteria.user = req.user.id;
    const reimbursement = await ReimbursementService.getReimbursement(criteria);
    ResponseService.success(res, "Reimbursement fetched successfully", {
      reimbursement: SanitizerService.sanitizeReimbursement(reimbursement),
    });
  },
  async update(req, res) {
    ReimbursementService.validateUpdatePayload(req.body);
    if (req.isEmployee) delete req.body.status;

    const { id: company } = req.company;
    const payload = {
      ...req.body,
      company,
    };
    const isBudget = payload.source?.startsWith("bdg_");
    const isBalance = payload.source?.startsWith("blc_");

    if (!(req.hasReimbursementEditPermission || req.isManager)) {
      payload.user = req.user.id;
    }

    if (req.body.receipt) {
      if (!Array.isArray(req.body.receipt)) req.body.receipt = [req.body.receipt];
      await PaymentService.callService("getReceiptOrThrowError", req.body.receipt);
    }
    if (req.body.budget || isBudget) {
      const { id: budget } = await BudgetService.getBudget({
        code: req.body.budget || payload.source,
      });
      payload.budget = budget;
      payload.balance = null;
    }

    if (req.body.category) {
      const category = await CategoryService.getCategory({
        code: req.body.category,
      });
      if (!category) throw new NotFoundError("Category");
      payload.category = category.id;
    }

    if (req.body.balance || isBalance) {
      const foundBalance = await BalanceRepo.getBalance({ filter: { code: req.body.balance || payload.source, company } });
      if (!foundBalance) throw new NotFoundError("Balance");
      payload.balance = foundBalance.id;
      payload.budget = null;
    }

    if (payload.directDebit) {
      const { directDebit } = payload;
      const {
        data: { balance },
      } = await BankService.canAccountProcessPayment({ directDebit });
      payload.balance = balance;
      payload.budget = null;
    }

    const reimbursement = await ReimbursementService.updateReimbursement(req.params.code, payload);
    ResponseService.success(res, "Reimbursement updated successfully", {
      reimbursement: SanitizerService.sanitizeReimbursement(reimbursement),
    });
  },

  async disable(req, res) {
    const payload = {
      status: STATUSES.DELETED,
      company: req.company.id,
    };

    if (!req.isAdmin) payload.user = req.user.id;

    await ReimbursementService.updateReimbursement(req.params.code, payload);
    ResponseService.success(res, "Reimbursement deleted successfully");
  },
  async approve(req, res) {
    const { actionLater, schedule } = req.body;
    const {
      params: { code },
      company: { id: company, code: companyCode, paymentPlan, onboardingLevel },
      user: { id: reviewer },
    } = req;

    const { reimbursement } = await ReimbursementService.validateReimbursement({
      ...req.body,
      code,
      company,
      decision: "approve",
    });

    // CHECK BALANCE AND BUDGET

    const { data: { isDirectDebit, bankAccount } = {} } = reimbursement.balance
      ? await HelperService.isDirectDebitAccount({ balance: reimbursement.balance })
      : {};

    const planConfiguration = Utils.parseJSON(paymentPlan?.configuration || SettingsService.get("pricing_config"));
    const { cbnFee, bujetiFee } = BillingService.computeFees({
      amount: reimbursement.amount,
      companyCode,
      currency: reimbursement.currency,
      plan: planConfiguration,
      isDirectDebit,
    });
    const totalAmount = Number(reimbursement.amount) + cbnFee + bujetiFee;
    let canDoDirectDebit = false;
    let mandate = null;

    // check plan payout limit
    await Utils.checkPlanPayoutLimit({ company: { id: company }, amount: reimbursement.amount, currency: reimbursement.currency, Transaction });

    if (!actionLater && onboardingLevel === ONBOARDING_LEVEL.LEVEL_1) {
      const willReachLimit = Utils.checkTransactionLimit({ company: req.company, amount: totalAmount });
      if (willReachLimit) throw new ValidationError("You have reached your transaction limit. Please complete your onboarding.");
    }

    if (!actionLater && !schedule) {
      if (reimbursement.budget) {
        await BudgetService.canBudgetHandleTransaction({ budget: reimbursement.budget, amount: reimbursement.amount, totalAmount });
      } else if (isDirectDebit) {
        const {
          data: { existingMandate },
        } = await BankService.canAccountProcessPayment({ directDebit: { bankAccount: bankAccount.code }, amount: totalAmount });
        mandate = existingMandate;
        canDoDirectDebit = true;
      } else {
        await BalanceService.canBalanceHandleTransaction({ balance: reimbursement.balance, amount: reimbursement.amount, totalAmount, company });
      }
    }

    await ReimbursementService.approveReimbursement({
      ...req.body,
      code,
      company,
      reviewer,
      companyCode,
      recipient: reimbursement.User,
      amount: reimbursement.amount,
      currency: reimbursement.currency,
      budget: reimbursement.budget,
      category: reimbursement.category,
      plan: Utils.parseJSON(req.company.paymentPlan.configuration),
      canDoDirectDebit,
      existingMandate: mandate,
    });
    return ResponseService.success(res, "Reimbursement approved successfully");
  },

  async decline(req, res) {
    const { code } = req.params;
    const { note } = req.body;
    const { id: company } = req.company;
    const { id: reviewer } = req.user;

    const { reimbursement } = await ReimbursementService.validateReimbursement({
      code,
      company,
      decision: "decline",
      note,
    });
    await ReimbursementService.declineReimbursement({
      code,
      company,
      reviewer,
      note,
      recipient: reimbursement.User,
      amount: reimbursement.amount,
      currency: reimbursement.currency,
    });
    return ResponseService.success(res, "Reimbursement declined successfully");
  },
  async finalize(req, res) {
    const { code, transaction, company, transaction_code: transactionCode, status } = req.body;
    await ReimbursementService.finalizeReimbursement({
      code,
      company,
      transaction,
      transaction_code: transactionCode,
      ...(status && { status }),
    });
    return ResponseService.success(res, "OK");
  },

  async viewReimbursementInternally(req, res) {
    const reimbursement = await ReimbursementService.getReimbursementInternally({ code: req.params.code });
    ResponseService.success(res, "Reimbursement fetched successfully", SanitizerService.sanitizeReimbursement(reimbursement));
  },

  async initiateTransaction(req, res) {
    const { code } = req.params;
    const { error } = ReimbursementValidator.validateCode.validate({ code });
    if (error) throw new ValidationError(error.message);

    await ReimbursementService.initiateReimbursementTransaction(code);
    ResponseService.success(res, "Reimbursement Transaction initiated");
  },

  async requestForMoreInfo(req, res) {
    ReimbursementService.validateMoreInfoPayload(req.body);
    const { id: company } = req.company;
    const { note } = req.body;
    const { code } = req.params;
    const { id: reviewer } = req.user;
    await ReimbursementService.requestForMoreInfo({ code, company, note, reviewer });
    return ResponseService.success(res, "More info requested successfully");
  },

  async multipleReimbursementAction(req, res) {
    const { error } = ReimbursementValidator.multipleReimbursementApproval.validate({ ...req.body });
    if (error) throw new ValidationError(error.message);
    const { decision } = req.body;
    const response = await ReimbursementService.multipleReimbursementAction({ ...req.body, reviewer: req.user, company: req.company });
    return ResponseService.success(res, `Reimbursements have been ${decision}d`, response);
  },

  async bulkTransactionInitiation(req, res) {
    const { error: validationError } = ReimbursementValidator.bulkTransactionInitiation.validate({ ...req.body });
    if (validationError) throw new ValidationError(validationError.message);
    const { message, data, error } = await ReimbursementService.initiateBulkReimbursementTransactions({ ...req.body });
    return ResponseService[error ? "error" : "success"](res, message || error, data);
  },
};
