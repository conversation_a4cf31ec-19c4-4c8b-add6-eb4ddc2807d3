const { subMonths, subDays, format } = require("date-fns");
const ReportsService = require("../services/reports.service");
const ResponseService = require("../services/response");
const { BalanceRepo } = require("../repositories/index.repo");

module.exports = {
  async cards(req, res) {
    const { id: company } = req.company;
    const { query } = req;

    let data = await ReportsService.cardsBreakdown(company, query);
    data = data.map(({ card, ...rest }) => rest);
    const message = "Cards report";
    const to = new Date(req.query.to || new Date());
    const from = new Date(req.query.from || subMonths(to, 1));
    const start = subMonths(from, 1);
    const period = `${format(from, "MMM do, yy")} to ${format(to, "MMM do, yy")}`;
    const previousPeriod = `${format(start, "MMM do, yy")} to ${format(subDays(from, 1), "MMM do, yy")}`;

    return ResponseService.success(res, message, { cards: data, period, previousPeriod });
  },
  async vendors(req, res) {
    const { id: company } = req.company;
    const { query } = req;

    let data = await ReportsService.vendorsBreakdown(company, query);
    data = data.map(({ recipient, ...rest }) => rest);
    const message = "Vendors report";
    return ResponseService.success(res, message, { vendors: data, ...ReportsService.getPeriodBreakdown(req.query) });
  },
  async budgets(req, res) {
    const { id: company } = req.company;
    const { query } = req;

    let data = await ReportsService.budgetsBreakdown(company, query);
    data = data.map(({ budget, ...rest }) => rest);
    const message = "Budgets report";
    return ResponseService.success(res, message, { budgets: data, ...ReportsService.getPeriodBreakdown(req.query) });
  },
  async teams(req, res) {
    const { id: company } = req.company;
    const { query } = req;

    let data = await ReportsService.teamsBreakdown(company, query);
    data = data.map(({ team, ...rest }) => rest);
    const message = "Teams breadown";
    return ResponseService.success(res, message, { teams: data, ...ReportsService.getPeriodBreakdown(req.query) });
  },
  async categories(req, res) {
    const { id: company } = req.company;
    const { query } = req;

    let data = await ReportsService.categoriesBreakdown(company, query);
    data = data.map(({ category, ...rest }) => rest);
    const message = "Categories report";
    return ResponseService.success(res, message, { categories: data, ...ReportsService.getPeriodBreakdown(req.query) });
  },
  async spenders(req, res) {
    const { id: company } = req.company;
    const { query } = req;

    const data = await ReportsService.spendersBreakdown(company, query);
    const message = "Spenders report";
    return ResponseService.success(res, message, { spenders: data, ...ReportsService.getPeriodBreakdown(req.query) });
  },
  async cashFlow(req, res) {
    const { id: company } = req.company;
    const { query } = req;

    const { debit = { amount: 0, previousPeriod: 0 }, credit = { amount: 0, previousPeriod: 0 } } = await ReportsService.getCashSummary(
      company,
      query
    );
    const operations = await ReportsService.getOperations(company, query);
    const message = "Cash flow report";

    const operationCounts = await ReportsService.getOperationCounts(company, query);

    const balance = await BalanceRepo.getBalance({ filter: { company, currency: "NGN" } }); // default currency NGN

    const balances = balance
      ? await ReportsService.getBalancesKeyFigures(balance.id, query)
      : { openingBalance: 0, closingBalance: 0, currency: "NGN" };

    return ResponseService.success(res, message, {
      ...ReportsService.getPeriodBreakdown(req.query),
      operationCounts,
      balances,
      summary: {
        debit,
        credit,
        ...balances,
      },
      operations,
    });
  },
};
