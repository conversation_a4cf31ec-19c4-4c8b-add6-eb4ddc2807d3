const ResponseService = require("../services/response");
const RolesService = require("../services/roles.service");
const Sanitizer = require("../utils/sanitizer");
const { RoleValidator } = require("../validators");
const { ValidationError } = require("../utils/error.utils");

module.exports = {
  /**
   * To view a role using the roles code
   * @method GET /roles/:code
   * @param { object } req.params
   * @param { string } req.params.code role code
   */
  async getRole(req, res) {
    const { params } = req;
    const { error } = RoleValidator.getRole.validate(params);
    if (error) throw new ValidationError(error.message);

    const { message, data } = await RolesService.getRoleWithPermissions(req.params.code);
    return ResponseService.success(res, message, Sanitizer.sanitizeRole(data));
  },

  /**
   * To list all system and company roles
   * @method GET /roles
   * @param { object } req.company
   * @param { number } req.company.id company id
   */
  async getRoles(req, res) {
    const criteria = {
      company: req.company.id,
      ...req.params,
    };
    const { message, data } = await RolesService.getAllRoles(criteria);
    return ResponseService.success(res, message, Sanitizer.sanitizeRoles(data));
  },

  /**
   * To list all system permissions
   * @method GET /permissions
   * @param { object } req.query
   */
  async getPermissions(req, res) {
    const { message, data } = await RolesService.getAllPermission();
    return ResponseService.success(res, message, Sanitizer.sanitizeLightPermissions(data));
  },

  /**
   * To cre company roles
   * @method POST /roles
   * @param { object } req
   * @param { number } req.company.id company id
   * @param { string } req.body.name role name
   * @param { string[] } req.body.permissions array of permissions code
   */
  async createRole(req, res) {
    const payload = {
      company: req.company.id,
      ...req.body,
    };

    const { error } = RoleValidator.createRole.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const { message, data } = await RolesService.createRole(payload);
    return ResponseService.success(res, message, Sanitizer.sanitizeRole(data, true));
  },

  /**
   * To update the name of a company's role
   * @method POST /roles/:code
   * @param { object } req
   * @param { number } req.company.id company id
   * @param { string } req.params.code role code
   * @param { string } req.body.name role name
   */
  async updateRole(req, res) {
    const payload = {
      company: req.company.id,
      code: req.params.code,
      ...req.body,
    };
    const { error } = RoleValidator.updateRole.validate({
      code: req.params.code,
      ...req.body,
    });
    if (error) throw new ValidationError(error.message);

    const { message } = await RolesService.updateRole(payload);
    return ResponseService.success(res, message);
  },

  /**
   * Add permissions to an existing company role
   * @method PUT /roles/:code
   * @param { object } req
   * @param { number } req.company.id company id
   * @param { string } req.params.code role code
   * @param { string } req.params.permissionCode permission code
   */
  async addPermissionsToRole(req, res) {
    const { code, permissionCode } = req.params;
    const payload = {
      company: req.company.id,
      code,
      permissionCode,
    };
    const { error } = RoleValidator.addPermissionsToRole.validate({ code, permission: permissionCode });
    if (error) throw new ValidationError(error.message);

    const { message } = await RolesService.addPermissionsToRole(payload);
    return ResponseService.success(res, message);
  },

  /**
   * Remove permissions from an existing company role
   * @method DELETE /roles/:code/permissions/:permissionCode
   * @param { object } req
   * @param { number } req.company.id company id
   * @param { string } req.params.code role code
   * @param { string } req.params.permissionCode permissions code
   */
  async removePermissionFromRole(req, res) {
    const { code, permissionCode } = req.params;
    const payload = {
      company: req.company.id,
      code,
      permissionCode,
    };
    const { error } = RoleValidator.addPermissionsToRole.validate({ code, permission: permissionCode });
    if (error) throw new ValidationError(error.message);

    const { message } = await RolesService.removePermissionFromRole(payload);
    return ResponseService.success(res, message);
  },

  /**
   * Delete an existing company role
   * @method DELETE /roles/:code
   * @param { object } req
   * @param { number } req.company.id company id
   * @param { string } req.params.code role code
   */
  async removeRole(req, res) {
    const { code } = req.params;
    const payload = {
      company: req.company.id,
      code,
    };
    const { error } = RoleValidator.getRole.validate({ code: req.params.code });
    if (error) throw new ValidationError(error.message);

    const { message } = await RolesService.removeRole(payload);
    return ResponseService.success(res, message);
  },
};
