const ResponseService = require("../services/response");
const ScheduleService = require("../services/schedule.service");

module.exports = {
  async createSchedule(req, res) {
    const { company, user } = req;
    const payload = { company: company.id, user: user.id, ...req.body };

    const { message, data } = await ScheduleService.createScheduledRecord(payload);

    return ResponseService.success(res, message, data);
  },
};
