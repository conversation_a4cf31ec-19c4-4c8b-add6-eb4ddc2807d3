const { ValidationError } = require("../utils/error.utils");
const { ScheduledBillValidator } = require("../validators");
const { ScheduledBillService, Response: ResponseService } = require("../services");
const Sanitizer = require("../utils/sanitizer");

module.exports = {
  async listScheduleBills(req, res) {
    const {
      company: { id: company },
    } = req;
    const filter = { ...req.query, company };
    const { error } = ScheduledBillValidator.listScheduledBill.validate({ ...req.query });
    if (error) throw new ValidationError(error.message);

    const { meta, scheduledBills } = await ScheduledBillService.listScheduledBills(filter);
    ResponseService.success(res, "Scheduled Bill listed", { scheduledBills: scheduledBills.map(Sanitizer.sanitizeScheduledBill), meta });
  },

  async getScheduledBill(req, res) {
    const {
      company: { id: company },
    } = req;
    const { error } = ScheduledBillValidator.getScheduledBill.validate({ code: req.params.code });
    if (error) throw new ValidationError(error.message);

    const filter = { code: req.params.code, company };
    const scheduledBill = await ScheduledBillService.getScheduledBill(filter);
    ResponseService.success(res, "Scheduled Bill fetched", { ...Sanitizer.sanitizeScheduledBill(scheduledBill) });
  },

  async updateScheduledBill(req, res) {
    const {
      company: { id: company },
    } = req;
    const { error } = ScheduledBillValidator.updateScheduledBill.validate({ code: req.params.code, ...req.body });
    if (error) throw new ValidationError(error.message);

    const filter = { code: req.params.code, company };
    await ScheduledBillService.updateScheduledBill({ queryParams: filter, payload: req.body, user: req.user });
    ResponseService.success(res, "Scheduled Bill Updated successfully");
  },

  async deleteScheduledBill(req, res) {
    const {
      company: { id: company },
    } = req;
    const { error } = ScheduledBillValidator.deleteScheduledBill.validate({ code: req.params.code });
    if (error) throw new ValidationError(error.message);

    const filter = { code: req.params.code, company };
    await ScheduledBillService.deleteScheduledBill({ filter });
    ResponseService.success(res, "Scheduled Bill deleted successfully");
  },
};
