const { ValidationError } = require("../utils/error.utils");
const { ScheduledInvoiceValidator } = require("../validators");
const { ScheduledInvoice: ScheduledInvoiceService, Response: ResponseService } = require("../services");
const Sanitizer = require("../utils/sanitizer");

module.exports = {
  async listScheduleInvoices(req, res) {
    const {
      company: { id: company },
    } = req;
    const filter = { ...req.query, company };
    const { error } = ScheduledInvoiceValidator.listScheduledInvoice.validate({ ...req.query });
    if (error) throw new ValidationError(error.message);

    const { meta, scheduledInvoices } = await ScheduledInvoiceService.listScheduledInvoices(filter);
    ResponseService.success(res, "Scheduled Invoice listed", { scheduledInvoices: scheduledInvoices.map(Sanitizer.sanitizeScheduledInvoice), meta });
  },

  async getScheduledInvoice(req, res) {
    const {
      company: { id: company },
    } = req;
    const { error } = ScheduledInvoiceValidator.getScheduledInvoice.validate({ code: req.params.code });
    if (error) throw new ValidationError(error.message);

    const filter = { code: req.params.code, company };
    const scheduledInvoice = await ScheduledInvoiceService.getScheduledInvoice(filter);
    ResponseService.success(res, "Scheduled Invoice fetched", { ...Sanitizer.sanitizeScheduledInvoice(scheduledInvoice) });
  },

  async updateScheduledInvoice(req, res) {
    const {
      company: { id: company },
    } = req;
    const { error } = ScheduledInvoiceValidator.updateScheduledInvoice.validate({ code: req.params.code, ...req.body });
    if (error) throw new ValidationError(error.message);

    const filter = { code: req.params.code, company };
    await ScheduledInvoiceService.updateScheduledInvoice({ queryParams: filter, payload: req.body, user: req.user });
    ResponseService.success(res, "Scheduled Invoice Updated successfully");
  },

  async deleteScheduledInvoice(req, res) {
    const {
      company: { id: company },
    } = req;
    const { error } = ScheduledInvoiceValidator.deleteScheduledInvoice.validate({ code: req.params.code });
    if (error) throw new ValidationError(error.message);

    const filter = { code: req.params.code, company };
    await ScheduledInvoiceService.deleteScheduledInvoice({ filter });
    ResponseService.success(res, "Scheduled Invoice deleted successfully");
  },
};
