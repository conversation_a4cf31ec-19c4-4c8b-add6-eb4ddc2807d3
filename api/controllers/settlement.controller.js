/* eslint-disable no-prototype-builtins */
const ResponseService = require("../services/response");
const { ValidationError } = require("../utils/error.utils");
const { SettlementValidator } = require("../validators");
const SettlementService = require("../services/settlement.service");

module.exports = {
  async requestSettlement(req, res) {
    const {
      user: { id: user },
      company: { id: company },
    } = req;

    const { error } = SettlementValidator.create.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const response = await SettlementService.requestSettlement({ ...req.body, company, user });

    return ResponseService.success(res, "An OTP has been sent to the admin of this company to authorize this action", response);
  },

  async verifyWithdrawalOTP(req, res) {
    const { code = null, hash = null } = req.body;
    if (!(code && hash)) throw new ValidationError("Please specify code and hash");

    const response = await SettlementService.verifyOTP({ code, hash });

    ResponseService.success(res, "Withdrawal request approved", response);
  },

  async finalizeWithdrawal(req, res) {
    await SettlementService.finalize(req.body);
    ResponseService.success(res, "Ok");
  },

  async list(req, res) {
    const {
      company: { id: company },
    } = req;
    const response = await SettlementService.list({ ...req.query, company });

    ResponseService.success(res, "Withdrawal request approved", response);
  },

  async processPendingPaystackSettlement(req, res) {
    ResponseService.success(res, "Ok");
    await SettlementService.processPaystackPendingSettlement({ filter: { code: req.params.code }, payload: req.body });
  },
};
