const { ValidationError } = require('../utils/error.utils');
const { BankValidator, SettlementAccountValidator } = require('../validators');
const SettlementService = require('../services/settlementAccount.service');
const ResponseService = require('../services/response');
const { STATUSES } = require('../models/status');
const Sanitizer = require('../utils/sanitizer');

module.exports = {
    async createSettlementAccount(req, res) {
        const { company: { id: company }, user: { id: user } } = req;

        const { error } = BankValidator.create.validate(req.body);
        if(error) throw new ValidationError(error.message);

        const response = await SettlementService.createSettlementAccount({ 
            ...req.body,
            owner: company,
            addedBy: user
        })
        ResponseService.success(res, 'An OTP has been sent to the admin of this company to authorize this action', response);
    },

    async verifySettlementOTP(req, res) {
        const { code = null, hash = null} = req.body;
        if(!code || !hash) throw new ValidationError('Please specify code and hash');

        const response = await SettlementService.verifyOTP({ code, hash });

        ResponseService.success(res, 'Settlement account added');
    },

    async update(req, res) {
        const { id: company} = req.company;

        const { error } = SettlementAccountValidator.update.validate(req.body);
        if(error) throw new ValidationError(error.message);

        const bankAccount = await SettlementService.update({ company, code: req.params.settlementAccount }, req.body);
        ResponseService.success(res, 'Settlement account updated', Sanitizer.sanitizeSettlementAccount(bankAccount))
    },

    async list(req, res) {
        const { id: company} = req.company;

        const settlementAccount = await SettlementService.list({ company });
        ResponseService.success(res, 'Settlement accounts fetched', Sanitizer.sanitizeModelInstances(settlementAccount, 'SettlementAccount'))
    },

    async remove(req, res) {
        const { id: company } = req.company;
        const settlementAccount = await SettlementService.update({ company, code: req.params.settlementAccount }, { status: "inactive" });
        ResponseService.success(res, 'Settlement account deleted', Sanitizer.sanitizeSettlementAccount(settlementAccount))
    }
}