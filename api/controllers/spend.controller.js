const { differenceInDays } = require("date-fns");
const NotFoundError = require("../utils/not-found-error");

const Sanitizer = require("../utils/sanitizer");
const ResponseService = require("../services/response");
const { SpendRepo, CardRepo } = require("../repositories/index.repo");
const VirtualCardService = require("../services/virtualcards.service");
const { STATUSES } = require("../models/status");

module.exports = {
  async statistics(req, res) {
    const data = await SpendRepo.getStatistics(req.company.id);
    return ResponseService.success(res, "Your subscriptions statistics", data);
  },
  async listSubscriptions(req, res) {
    const { meta, spends } = await SpendRepo.listSubscriptions({
      company: req.company.id,
      ...req.query,
    });
    return ResponseService.success(res, "Your subscriptions", Sanitizer.sanitizeSpends(spends), meta);
  },

  async viewSubscription(req, res) {
    const spend = await SpendRepo.viewSubscription(req.params.code);
    if (!spend) throw new NotFoundError("Subscription");
    const totalPayments = await SpendRepo.countTransactions({ spend: spend.id, ...req.query });
    const nextBillingDate = spend.renewalDate ? differenceInDays(new Date(spend.renewalDate), new Date()) : -1;
    const meta = {
      totalPayments,
      totalAmount: totalPayments * spend.amount,
      // eslint-disable-next-line no-nested-ternary
      nextBillingDate: nextBillingDate === 0 ? "Today" : nextBillingDate < 0 ? "-" : `in ${nextBillingDate} days`,
    };
    return ResponseService.success(res, "Your subscription is successfully retrieved", Sanitizer.sanitizeSpend(spend), meta);
  },
  async viewSubscriptionReceipts(req, res) {
    const spend = await SpendRepo.fetchOrCreateSpendForBujetiFetch({ code: req.params.code });
    if (!spend) return ResponseService.success(res, "Your subscriptions transactions are successfully retrieved", []);
    const receipts = await SpendRepo.viewSubscriptionReceipts(spend.id);
    return ResponseService.success(res, "Your subscriptions receipts are successfully retrieved", Sanitizer.sanitizeSpendReceipts(receipts));
  },
  async viewSubscriptionTransactions(req, res) {
    const spend = await SpendRepo.fetchOrCreateSpendForBujetiFetch({ code: req.params.code });
    if (!spend) return ResponseService.success(res, "Your subscriptions transactions are successfully retrieved", []);
    const transactions = await SpendRepo.viewSubscriptionTransactions(spend.id);
    return ResponseService.success(
      res,
      "Your subscriptions transactions are successfully retrieved",
      Sanitizer.sanitizeSpendTransactions(transactions)
    );
  },
  async cancelSubscription(req, res) {
    const spend = await SpendRepo.fetchOrCreateSpendForBujetiFetch({ code: req.params.code });
    const linkedCard = await CardRepo.getCard({
      id: spend.card,
    });
    if (!req.query.overwrite) {
      const { spends: multipleSubs } = await SpendRepo.listSubscriptions({ card: spend.card });
      if (multipleSubs.length >= 2)
        return ResponseService.failure(res, "The card used is tied to multiple subscriptions, do you really want to terminate it?");
    }
    if (linkedCard)
      await VirtualCardService.terminateCard({
        initiator: req.user.id,
        filter: {
          code: linkedCard?.code,
        },
      });
    await SpendRepo.update(
      {
        code: req.params.code,
      },
      {
        status: STATUSES.CANCELLED,
      }
    );
    return ResponseService.success(res, "Subscription cancelled, you will not be charged for the next bill");
  },
  async deleteSubscription(req, res) {
    if (!String(req.params.code).startsWith("spd_")) return ResponseService.failure(res, "Invalid subscription");

    await SpendRepo.update(
      {
        code: req.params.code,
      },
      {
        status: STATUSES.DELETED,
      }
    );
    return ResponseService.success(res, "Subscription successfully deleted");
  },
};
