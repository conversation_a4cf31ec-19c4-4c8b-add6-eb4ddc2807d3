const { ValidationError } = require("../utils/error.utils");
const { TaxValidator } = require("../validators");
const { TaxService, Response: ResponseService } = require("../services");
const Sanitizer = require("../utils/sanitizer");
const Service = require("../services/tax.service");
const { validateTaxGroup } = require("../validators/tax.validator");

const Controller = {
  async create(req, res) {
    const payload = {
      company: req.company.id,
      user: req.user.id,
      ...req.body,
    };

    const { error } = TaxValidator.create.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const response = await TaxService.createTax(payload);
    return ResponseService.success(res, "Tax created successfully", Sanitizer.sanitizeTax(response.data.tax));
  },

  async createVersion(req, res) {
    const filter = {
      code: req.params.code,
      company: req.company.id,
    };

    const payload = {
      user: req.user.id,
      ...req.body,
    };

    const { error } = TaxValidator.createVersion.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const response = await TaxService.createVersion(filter, payload);
    return ResponseService.success(res, "Tax version created successfully", {
      tax: Sanitizer.sanitizeTax(response.data.tax),
      versions: response.data.versions.map((version) => Sanitizer.sanitizeTaxVersion(version)),
    });
  },

  async list(req, res) {
    const criteria = {
      company: req.company.id,
      ...req.query,
    };

    const { error } = TaxValidator.list.validate(req.query);
    if (error) throw new ValidationError(error.message);

    const response = await TaxService.list(criteria);
    return ResponseService.success(res, "Taxes retrieved successfully", {
      taxes: response.data.taxes?.map((tax) => Sanitizer.sanitizeTax(tax)) || [],
      meta: response.data.meta,
    });
  },

  async view(req, res) {
    const filter = {
      code: req.params.code,
      company: req.company.id,
    };

    const response = await TaxService.view(filter);
    return ResponseService.success(res, "Tax retrieved successfully", Sanitizer.sanitizeTax(response.data.tax));
  },

  async listVersions(req, res) {
    const filter = {
      code: req.params.code,
      company: req.company.id,
    };

    const response = await TaxService.listVersions(filter);
    return ResponseService.success(res, "Tax versions retrieved successfully", {
      tax: Sanitizer.sanitizeTax(response.data.tax),
      versions: response.data.versions.map((version) => Sanitizer.sanitizeTaxVersion(version)),
    });
  },

  async update(req, res) {
    const filter = {
      code: req.params.code,
      company: req.company.id,
    };

    const payload = {
      user: req.user.id,
      ...req.body,
    };

    const { error } = TaxValidator.update.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const response = await TaxService.updateTax({ filter, payload });
    return ResponseService.success(res, "Tax updated successfully", Sanitizer.sanitizeTax(response.data.tax));
  },

  async deactivate(req, res) {
    const filter = {
      code: req.params.code,
      company: req.company.id,
    };

    await TaxService.deactivateTax(filter);
    return ResponseService.success(res, "Tax deactivated successfully");
  },

  async activate(req, res) {
    const filter = {
      code: req.params.code,
      company: req.company.id,
    };

    await TaxService.activateTax(filter);
    return ResponseService.success(res, "Tax activated successfully");
  },

  async createTaxGroup(req, res) {
    const { error, value } = TaxValidator.taxGroup.create.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const response = await Service.createTaxGroup(value, req.user.id, req.company.id);
    return ResponseService.success(res, "Tax group created successfully", Sanitizer.sanitizeTaxGroup(response.data.taxGroup));
  },

  async getTaxGroup(req, res) {
    const response = await Service.getTaxGroup({
      filter: {
        code: req.params.code,
        company: req.company.id
      }
    });
    return ResponseService.success(res, "Tax group retrieved successfully", Sanitizer.sanitizeTaxGroup(response.data.taxGroup));
  },

  async getTaxGroups(req, res) {
    const { error, value } = TaxValidator.taxGroup.list.validate(req.query);
    if (error) throw new ValidationError(error.message);

    const response = await Service.getTaxGroups({
      ...value,
      company: req.company.id,
    });
    return ResponseService.success(res, "Tax groups retrieved successfully", {
      taxGroups: Sanitizer.sanitizeTaxGroups(response.data.taxGroups),
      meta: response.data.meta
    });
  },

  async updateTaxGroup(req, res) {
    const { error, value } = validateTaxGroup(req.body);
    if (error) throw new ValidationError(error.message);

    const response = await Service.updateTaxGroup({
      filter: {
        code: req.params.code,
        company: req.company.id
      },
      payload: value
    });
    
    return ResponseService.success(res, "Tax group updated successfully", Sanitizer.sanitizeTaxGroup(response.data.taxGroup));
  },

  async addTaxToGroup(req, res) {
    const { error } = TaxValidator.taxGroup.addTax.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const payload = {
      code: req.params.code,
      tax: req.body.tax,
      user: req.user.id,
      company: req.company.id,
      sequenceNumber: req.body.sequenceNumber
    };

    const response = await Service.addTaxToGroup(payload);
    return ResponseService.success(res, "Tax added to group successfully", {
      taxGroup: Sanitizer.sanitizeTaxGroup(response.data.taxGroup),
      taxGroupTax: response.data.taxGroupTax
    });
  },

  async removeTaxFromGroup(req, res) {
    const filter = {
      code: req.params.code,
      tax: req.params.taxCode,
      company: req.company.id
    };

    const response = await Service.removeTaxFromGroup(filter);
    return ResponseService.success(res, "Tax removed from group successfully", response.data);
  },

  async createTaxApplication(req, res) {
    const { error } = TaxValidator.taxApplication.create.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const payload = {
      company: req.company.id,
      user: req.user.id,
      ...req.body,
    };

    const response = await TaxService.createTaxApplication(payload);
    return ResponseService.success(res, "Tax application created successfully", response.data);
  },

  async listTaxApplications(req, res) {
    const { error } = TaxValidator.taxApplication.list.validate(req.query);
    if (error) throw new ValidationError(error.message);

    const criteria = {
      company: req.company.id,
      ...req.query,
    };

    const response = await TaxService.listTaxApplications(criteria);
    return ResponseService.success(res, "Tax applications retrieved successfully", {
      applications: response.data.applications || [],
      meta: response.data.meta,
    });
  },

  async getTaxApplication(req, res) {
    const filter = {
      code: req.params.code,
      company: req.company.id,
    };

    const response = await TaxService.getTaxApplication(filter);
    return ResponseService.success(res, "Tax application retrieved successfully", response.data);
  },

  async listTaxApplicationsByApplicable(req, res) {
    const { error } = TaxValidator.taxApplication.list.validate({
      applicableType: req.params.type,
      applicableId: req.params.id,
      ...req.query,
    });
    if (error) throw new ValidationError(error.message);

    const filter = {
      applicable_type: req.params.type,
      applicable_id: req.params.id,
      company: req.company.id,
      ...req.query,
    };

    const response = await TaxService.listTaxApplicationsByApplicable(filter);
    return ResponseService.success(res, "Tax applications retrieved successfully", {
      applications: response.data.applications || [],
      meta: response.data.meta,
    });
  },
};

module.exports = Controller;
