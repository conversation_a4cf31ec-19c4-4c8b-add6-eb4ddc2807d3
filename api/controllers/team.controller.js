const { TeamValidator } = require("../validators");
const ResponseService = require("../services/response");
const { NotFoundError, ValidationError } = require("../utils/error.utils");
const {
  Teams: TeamService,
  TeamMember: TeamMemberService,
  Budget: BudgetService,
  TeamBudget: TeamBudgetService,
  Role: RolesService,
  Helper: HelperService,
  User: UserService,
} = require("../services");
const Sanitizer = require("../utils/sanitizer");
const UserRepo = require("../repositories/user.repo");
const { STATUSES } = require("../models/status");
const { SYSTEM_ROLES } = require("../models/role");
const { sequelize } = require("../models");

module.exports = {
  /**
   * Creates a Team for a company
   * @method POST /teams
   * @param { object } req.body
   * @param { String } req.body.name Name of the comany
   * @param { String } req.body.description Description of the company
   * @param { Array } req.body.managers Managers of the team
   * @param { Array } req.body.members Members of the team
   * @returns
   */
  async create(req, res) {
    const transaction = await sequelize.transaction();
    const { name, description = null, managers = [], members = [], budgets: budgetCodes = [] } = req.body;
    const { id: company } = req.company;
    const { error } = TeamValidator.create.validate(req.body);
    if (error) throw new ValidationError(error.message);

    // CHECK TEAM MANAGERS
    let teamMembersAndManagers = [];
    if (managers && managers.length > 0) {
      teamMembersAndManagers = await HelperService.formatTeamMembers({ users: managers, role: "Manager", company });
    }

    // CHECK TEAM MEMBERS
    if (members && members.length > 0) {
      const teamMembers = await HelperService.formatTeamMembers({ users: members, role: "Employee", company });
      teamMembersAndManagers = Array.from(teamMembersAndManagers).concat(teamMembers);
    }

    // CREATE TEAM
    const teamPayload = { name, description, company };
    let team;
    try {
      team = await TeamService.createTeam(teamPayload, transaction);
      // ADD TEAM MEMBERS AND MANAGER TO TEAM
      await TeamMemberService.createTeamMembers(team.id, teamMembersAndManagers, transaction);
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }

    if (budgetCodes && budgetCodes.length) {
      const budgetBeneficiaries = Array.from(managers).concat(members);
      await TeamBudgetService.createTeamBudgets(team, company, budgetCodes, budgetBeneficiaries);
    }

    return ResponseService.success(res, "Team created successfully", Sanitizer.sanitizeTeam(team));
  },

  /**
   * List all teams for a company
   * @method GET /teams
   * @param { object } req.query
   * @param { String } req.query.search search query to search with
   * @param { String } req.query.from from created date
   * @param { String } req.query.to to created date for sorting
   * @param { String | Array } req.query.status status of team to fetch
   * @returns
   */
  async list(req, res) {
    const { id: company } = req.company;
    const criteria = {
      company,
      ...req.query,
    };

    if (req.isEmployeeRole) criteria.user = req.user.id;

    let { teams, meta } = await TeamService.list(criteria);
    teams = Sanitizer.sanitizeTeams(teams);
    teams =
      (teams.length &&
        Array.from(teams).map((team) => {
          const { members, managers } = HelperService.formatMembersAndManagers(team.members);
          team.managers = managers;
          team.members = members;
          return team;
        })) ||
      [];

    return ResponseService.success(res, "Teams fetched successfully", {
      teams,
      meta,
    });
  },

  /**
   * Add a list of Managers to a team
   * @method PATCH /teams/:teamId/add-manager
   * @param { object } req.body
   * @param { String } req.body.managers Array of Manager's to add
   * @returns
   */
  async addMember(req, res) {
    const { id: company } = req.company;

    const { error } = TeamValidator.addMember.validate({ ...req.body, team: req.params.team });
    if (error) throw new ValidationError(error.message);

    const { members = [], role } = req.body;
    const { team: teamCode } = req.params;

    // GET SINGLE TEAM
    const {
      data: { id: roleId },
    } = await RolesService.getRoleWithPermissions(role);
    const { id: teamId, TeamMembers: teamMembers = [] } = await TeamService.getTeam({ code: teamCode });

    const previousTeamManagers =
      teamMembers.length &&
      teamMembers.map((manager) => {
        return { user: manager.user, status: manager.status };
      });

    const newTeamManagers = await UserRepo.getAllUsers({ queryParams: { company, code: members } });
    if (newTeamManagers.length !== members.length) throw new ValidationError(`Invalid user sent`);
    let teamManagers = newTeamManagers.filter(
      (user) => !Array.from(previousTeamManagers).find((manager) => manager.user === user.id && manager.status === STATUSES.ACTIVE)
    );
    if (teamManagers) {
      teamManagers = teamManagers.map((user) => {
        return {
          company,
          user: user.id,
          role: roleId,
        };
      });
      await TeamMemberService.createTeamMembers(teamId, teamManagers);
    }

    return ResponseService.success(res, "Team members added successfully");
  },

  async update(req, res) {
    const { team: code } = req.params;
    const { id: company } = req.company;

    const { error } = TeamValidator.update.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const team = await TeamService.getTeam({ code });

    const { name, description, status, managers = [], members, budgets: budgetCodes } = req.body;
    // UPDATE TEAM DETAILS
    if (name || description || status) {
      const payload = {
        ...(name && { name }),
        ...(description && { description }),
        ...(status && { status: STATUSES[String(status).toUpperCase()] }),
      };
      await TeamService.update({ company, code }, payload);
    }

    let newMembersToAdd = [];
    let oldMembersToRemove = [];

    if (managers && managers.length) {
      // GET CURRENT MANAGERS
      const { membersToAdd: managersToAdd, membersToRemove: managersToRemove } = await TeamMemberService.filterMembersToAddAndRemove({
        company,
        team: team.id,
        members: managers,
        role: SYSTEM_ROLES.MANAGER,
      });
      if (managersToAdd && managersToAdd.length) {
        newMembersToAdd = await HelperService.formatTeamMembers({ users: managersToAdd, role: "Manager", company });
      }

      if (managersToRemove && managersToRemove.length) {
        oldMembersToRemove = await HelperService.formatTeamMembersToRemove({ company, members: managersToRemove });
      }
    }

    if (members) {
      const { membersToAdd, membersToRemove } = await TeamMemberService.filterMembersToAddAndRemove({
        company,
        team: team.id,
        members,
        role: SYSTEM_ROLES.EMPLOYEE,
      });
      // ADD NEWLY ADDED TEAM MANAGERS
      if (membersToAdd && membersToAdd.length) {
        const teamMembers = await HelperService.formatTeamMembers({ users: membersToAdd, role: "Employee", company });
        newMembersToAdd = Array.from(newMembersToAdd).concat(teamMembers);
      }

      if (membersToRemove && membersToRemove.length) {
        const membersToRemovIds = await HelperService.formatTeamMembersToRemove({ company, members: membersToRemove });
        oldMembersToRemove = Array.from(oldMembersToRemove).concat(membersToRemovIds);
      }
    }

    if (oldMembersToRemove && oldMembersToRemove.length) await TeamMemberService.removeTeamMembers(team.id, oldMembersToRemove);
    if (newMembersToAdd && newMembersToAdd.length) await TeamMemberService.createTeamMembers(team.id, newMembersToAdd);

    if (budgetCodes) {
      const previousBudgets =
        (team.TeamBudgets && team.TeamBudgets.length && Array.from(team.TeamBudgets).map((teamBudget) => teamBudget.Budget)) || [];
      const previousBudgetCodes = Array.from(previousBudgets).map((budget) => budget.code);
      let budgetsToRemove = Array.from(previousBudgetCodes).filter((previousBudget) => !Array.from(budgetCodes).includes(previousBudget));
      let budgetsToAdd = Array.from(budgetCodes).filter((budgetCode) => !previousBudgetCodes.includes(budgetCode));

      budgetsToAdd = await BudgetService.fetchBudgetsOnly({ code: budgetsToAdd, status: STATUSES.ACTIVE });
      budgetsToRemove = Array.from(budgetsToRemove).map((budget) => previousBudgets.find((previousBudget) => previousBudget.code === budget));
      await Promise.all([
        budgetsToAdd && budgetsToAdd.length && (await TeamBudgetService.addBudgetsToTeam({ team: team.id, budgets: budgetsToAdd })),
        budgetsToRemove && budgetsToRemove.length && (await TeamBudgetService.removeBudgetsFromTeam({ team: team.id, budgets: budgetsToRemove })),
      ]);
    }

    return ResponseService.success(res, "Team updated successfully");
  },

  async listTeamMembers(req, res) {
    const { team: code } = req.params;
    const { id: company } = req.company;

    const { id: team } = await TeamService.getTeam({ code });
    const { rows: teamMembers, count } = await TeamMemberService.getTeamMembers({ team, company, ...req.query });

    return ResponseService.success(res, "Team members fetched successfully", {
      members: Sanitizer.sanitizeTeamMembers(teamMembers),
    });
  },

  async removeTeamMember(req, res) {
    const { team, member } = req.params;

    const { error } = TeamValidator.deleteTeamMember.validate({ team, member });
    if (error) throw new ValidationError(error.message);

    const foundTeam = await TeamService.getTeam({ code: team });
    const teamMember = await TeamMemberService.getTeamMember({ code: member });
    await TeamMemberService.removeTeamMembers(foundTeam.id, [teamMember.user]);

    return ResponseService.success(res, "Team member deleted successfully");
  },

  async overview(req, res) {
    const { team: code } = req.params;
    const { id: company } = req.company;

    const { id: team } = await TeamService.getTeam({ code });
    const criteria = { ...req.query, team, company };
    const statistics = await TeamService.overview(criteria);

    return ResponseService.success(res, "Team overview fetched", statistics);
  },

  async remove(req, res) {
    const { id: company } = req.company;
    const { team: code } = req.params;
    const payload = { status: STATUSES.DELETED };

    const { id } = await TeamService.getTeam({ company, code });
    await TeamService.update({ id }, payload);

    return ResponseService.success(res, "Team deleted successfully");
  },

  async updateTeamMember(req, res) {
    const { team, member } = req.params;
    const { id: company } = req.company;

    const { error } = TeamValidator.updateTeamMember.validate({ team, member, ...req.body });
    if (error) throw new ValidationError(error.message);

    const foundTeam = await TeamService.getTeam({ code: team });
    const teamMember = await TeamMemberService.getTeamMember({ code: member });

    const status = STATUSES[String(req.body.status).toUpperCase()];

    const payload = { status };

    await TeamMemberService.updateTeamMembers(foundTeam.id, [teamMember.user], payload, company);

    return ResponseService.success(res, "Team member updated successfully");
  },
};
