const ThirdPartyLogService = require('../services/thirdPartyLog');
const ResponseService = require('../services/response');
const Sanitizer = require('../utils/sanitizer');

module.exports = {
    async list(req, res) {
        const logs = await ThirdPartyLogService.list(req.query);
        ResponseService.success(res, 'Logs fetched successfully', Sanitizer.sanitizeModelInstances(logs, 'ThirdPartyLogs'))
    },
}