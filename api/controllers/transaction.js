/* eslint-disable no-prototype-builtins */
const { differenceInDays, parseISO } = require("date-fns");
const ResponseService = require("../services/response");
const TransactionService = require("../services/transaction");
const CategoryService = require("../services/category.service");
const AssetService = require("../services/asset");
const { NotFoundError, ValidationError, ExistsError } = require("../utils/error.utils");
const SanitizerService = require("../utils/sanitizer");
const { STATUSES } = require("../models/status");
const { TransferRepo, TransactionRepo, CompanyRepo, TransactionAttemptRepo, CategoryRepo, BalanceLedgerRepo } = require("../repositories/index.repo");
const ChargeService = require("../services/charge");
const BudgetService = require("../services/budget");
const SettingsService = require("../services/settings");
const BudgetLedgerRepo = require("../repositories/budgetLedger.repo");
const { TransactionValidator } = require("../validators");
const ApprovalService = require("../services/approval.service");
const Providers = require("../services/providers");
const QueueService = require("../services/queue.service");
const UserRepo = require("../repositories/user.repo");
const PolicyService = require("../services/policy.service");
const BankService = require("../services/bank");
const BalanceService = require("../services/balance");
const HelperService = require("../services/helper.service");
const Utils = require("../utils/utils");
const NotificationService = require("../services/notification");
const RedisService = require("../services/redis");
const { TRANSACTION_TYPES } = require("../models/transaction");
const { syncTransactionToZohoBooks } = require("../utils/zoho.utils");

module.exports = {
  async list(req, res) {
    const {
      company: { id: company },
      user: { id: user, role },
    } = req;

    const { error } = TransactionValidator.listTransactions.validate(req.query);
    if (error) throw new ValidationError(error.message);

    const criteria = { company, role, ...req.query };

    if (req.isEmployeeRole) criteria.user = user;
    if (criteria.category) {
      const foundCategory = await CategoryService.getCategory({
        code: criteria.category,
      });
      if (!foundCategory) throw new NotFoundError("Category");
      criteria.category = foundCategory.id;
    }
    const { transactions, meta } = await TransactionService.listTransactions({ ...criteria, loggedInUser: user });
    const sanitizedTransactions = SanitizerService.sanitizeTransactions(transactions);
    return ResponseService.success(res, "Transactions retrieved", {
      transactions: sanitizedTransactions,
      meta,
    });
  },
  async view(req, res) {
    const { code } = req.params;
    const transactions = await TransactionService.getTransaction(code, {
      company: req.company.id,
    });
    const sanitizedTransactions = SanitizerService.sanitizeTransaction(transactions);
    return ResponseService.success(res, "Transaction retrieved", sanitizedTransactions);
  },

  async export(req, res) {
    const {
      company: { id: company },
      user: { id: user },
    } = req;
    const criteria = { perPage: 1000, company, ...req.query };

    if (req.isEmployeeRole) criteria.user = user;

    if (criteria.category) {
      const foundCategory = await CategoryService.getCategory({
        code: criteria.category,
      });
      if (!foundCategory) throw new NotFoundError("Category");
      criteria.category = foundCategory.id;
    }

    const { transactions, meta } = await TransactionService.listTransactions(criteria);
    const sanitizedTransactions = SanitizerService.sanitizeTransactions(transactions);
    return ResponseService.success(res, "Transactions retrieved", {
      transactions: sanitizedTransactions,
      meta,
    });
  },

  async profileStats(req, res) {
    const {
      company: { id: company },
      user: { id: user },
    } = req;

    const criteria = { ...req.query };

    if (req.isEmployeeRole) {
      criteria.user = user;
    } else {
      if (criteria.payer) {
        const foundUser = await UserRepo.fetchUser(criteria.payer);
        if (!foundUser) throw new NotFoundError("Payer");
        criteria.payer = foundUser.id;
      }
    }
    const spent = await TransactionService.getSpentOverDuration(company, criteria);
    return ResponseService.success(res, "Profile stats retrieved", {
      spent: spent.reduce((collection, element) => {
        const { month, year } = element;
        element.totalAmount = parseInt(element.totalAmount, 10);
        collection[`${month}-${year}`] = element;
        return collection;
      }, {}),
    });
  },

  async update(req, res) {
    const { amount, category, receipt, payer, recipient, description, budget, balance, source } = req.body;

    const { code } = req.params;
    const { company, user } = req;
    const payload = {};
    const isBudget = source?.startsWith("bdg_");
    const isBalance = source?.startsWith("blc_");

    const { isInitiator, isAdminOrManager, transaction } = await TransactionService.canUpdateTransactionPayer({
      code,
      company,
      user,
    });

    if (isInitiator || isAdminOrManager) {
      if (category) {
        if (![STATUSES.PENDING, STATUSES.SUCCESS, STATUSES.FAILED].includes(transaction.status))
          throw new ValidationError("Only pending, successful or failed transactions can have their category updated");
        const foundCategory = await CategoryService.getCategory({
          code: category,
        });
        if (!foundCategory) throw new NotFoundError("Category");
        payload.category = foundCategory.id;
      }
      if (receipt) {
        const foundAsset = await AssetService.getAsset(receipt);
        if (!foundAsset) throw new NotFoundError("File");
        payload.receipt = foundAsset.id;
      }
      if ("description" in req.body)
        await TransactionService.editTransactionDescription({
          criteria: { code },
          description,
        });

      if (amount)
        await TransactionService.updateAmountByCode({
          code,
          amount,
        });

      if (budget || isBudget)
        await TransactionService.addBudgetToATransaction({
          criteria: { company, code },
          budget: budget || source,
        });
      if (balance || isBalance)
        await TransactionService.addBalanceToATransaction({
          criteria: { company, code },
          balance: balance || source,
        });
    }

    if ((payer || recipient) && isAdminOrManager) {
      await TransactionService.addPayerAndRecipientToATransaction({
        criteria: { company, code },
        payload: { payer, recipient },
      });
    }

    if (receipt || category) {
      if (category && ![STATUSES.PENDING, STATUSES.SUCCESS, STATUSES.FAILED].includes(transaction.status))
        throw new ValidationError("Only pending, successful or failed transactions can have their category updated");
      await TransactionService.updateTransaction({
        criteria: { code, company: company.id },
        payload,
      });

      const isSuccessfulTransfer = transaction.status === STATUSES.SUCCESS;

      if (category && isSuccessfulTransfer) {
        const previousCategory = transaction.category;
        const currentCategory = payload.category;

        if (previousCategory) {
          await CategoryRepo.decreaseCategorySpent({
            filter: {
              id: previousCategory,
            },
            amount: transaction.amount,
          });
        }

        if (currentCategory) {
          await CategoryRepo.increaseCategorySpent({
            filter: {
              id: currentCategory,
            },
            amount: transaction.amount,
          });
        }
      }

      if (receipt)
        await AssetService.updateAsset({
          updateFields: {
            entityType: "Transaction",
            entityId: transaction.id,
          },
          queryParams: { code: receipt },
        });
    }

    const hasViolations = await PolicyService.getConditionsViolated(transaction.id);

    const days = differenceInDays(new Date(), parseISO(transaction?.created_at));

    if (!hasViolations.length && days <= 3) {
      await TransactionService.overridePolicy({ id: transaction.id });
    }
    return ResponseService.success(res, "Transaction successfully updated");
  },

  async cancelTransaction(req, res) {
    const { code } = req.params;
    const { id: company } = req.company;
    const { id: user } = req.user;
    const foundTransaction = await TransactionRepo.getTransaction({
      queryParams: { code, company },
    });
    if (!foundTransaction) throw new NotFoundError("Transaction");
    if ([STATUSES.PROCESSING].includes(foundTransaction.status)) {
      throw new ValidationError("This transaction cannot be cancelled");
    }
    await TransactionService.cancelTransaction(foundTransaction);
    // create budget ledger when transation is cancelled
    if (foundTransaction.budget) {
      const budgetLedger = await BudgetLedgerRepo.getBudgetLedger({
        criteria: {
          transaction: foundTransaction.id,
          budget: foundTransaction.budget,
        },
      });
      if (budgetLedger) {
        const ledgerPayload = {
          budget: foundTransaction.budget,
          user,
          currency: foundTransaction.currency,
          transaction: foundTransaction.id,
          amount: foundTransaction.amount,
          description: `[Reversal] ${foundTransaction.description}`,
        };
        await BudgetService.createBudgetLedger(ledgerPayload);
      }
    }
    // call the charge endpoint with this information
    return ResponseService.success(res, "Transaction cancelled");
  },

  // eslint-disable-next-line consistent-return
  async retryTransaction(req, res) {
    const { code } = req.params;

    const company = req.company?.id || req.body.company;

    let foundTransaction;
    const isTransfer = code.startsWith("trf");
    if (isTransfer) foundTransaction = await TransferRepo.getTransfer({ filter: { code, company }, includeCompany: true });
    else foundTransaction = await TransactionRepo.getTransaction({ queryParams: { code, company }, company: true });

    if (!foundTransaction) throw new NotFoundError(`${isTransfer ? "Transfer" : "Transaction"}`);
    if (!HelperService.checkTransactionStatusForRetrial(foundTransaction))
      throw new ValidationError(`This ${isTransfer ? "transfer" : "transaction"} cannot be retried`);

    const transactionAttemptFilter = {};
    if (isTransfer) transactionAttemptFilter.transfer = foundTransaction.id;
    else transactionAttemptFilter.transaction = foundTransaction.id;

    // Check processing transaction attempt
    const existingTransactionAttempt = await TransactionAttemptRepo.getTransactionAttempt({
      filter: { ...transactionAttemptFilter, status: STATUSES.PENDING },
    });

    if (existingTransactionAttempt) throw new ValidationError("This transaction is currently processing");

    const isDirectDebitTransaction = foundTransaction.type === TRANSACTION_TYPES.DIRECT_DEBIT;

    if (isDirectDebitTransaction) {
      throw new ValidationError(`This transaction cannot be retried via direct debit, update source and retry`);
    }

    const { budget = null, balance = null, amount, processor_fee: processorFee, bujeti_fee: bujetiFee } = foundTransaction;

    let pendingLedger;
    if (isTransfer) {
      pendingLedger = await BudgetLedgerRepo.getBudgetLedger({
        criteria: { budget, transaction: foundTransaction.id, status: STATUSES.PENDING },
      });
      if (!pendingLedger) {
        pendingLedger = await BalanceLedgerRepo.getBalanceLedger({
          filter: { balance, transaction: foundTransaction.id, status: STATUSES.PENDING },
        });
      }
    } else if (budget) {
      pendingLedger = await BudgetLedgerRepo.getBudgetLedger({
        criteria: { budget, transaction: foundTransaction.id, status: STATUSES.PENDING },
      });
    } else if (balance) {
      pendingLedger = await BalanceLedgerRepo.getBalanceLedger({
        filter: { balance, transaction: foundTransaction.id, status: STATUSES.PENDING },
      });
    }

    if (pendingLedger) throw new ValidationError("This transaction is currently processing");

    const totalAmount = parseInt(amount, 10) + parseInt(processorFee, 10) + parseInt(bujetiFee, 10);
    if (budget)
      await BudgetService.canBudgetHandleTransaction({
        budget,
        amount,
        totalAmount,
      });
    else {
      const { data: isDirectDebitResponse } = await HelperService.isDirectDebitAccount({ balance });
      const { bankAccount = null, isDirectDebit = false } = isDirectDebitResponse || {};

      if (isDirectDebit) {
        if (!bankAccount) throw new NotFoundError("Bank Account");

        await BankService.canAccountProcessPayment({
          directDebit: { bankAccount: bankAccount.code },
          amount,
        });
      } else {
        await BalanceService.canBalanceHandleTransaction({
          balance,
          amount,
          totalAmount,
          company: foundTransaction.company,
        });
      }
    }

    if (isTransfer) {
      await TransferRepo.update({
        filter: { id: foundTransaction.id },
        payload: { status: STATUSES.PENDING },
      });
    } else {
      await TransactionRepo.updateTransaction({
        queryParams: { id: foundTransaction.id },
        updateFields: { status: STATUSES.PROCESSING },
      });
    }
    ResponseService.success(res, `${isTransfer ? "Transfer" : "Transaction"} is being retried`);

    const redisKey = `transaction_retrials:${code}`;

    const hasAlreadyBeenSeen = await RedisService.get(redisKey);

    if (hasAlreadyBeenSeen) return;

    RedisService.setex(redisKey, 1, 60 * 60 * 24);
    // Add to queue
    const SQSPayload = {
      id: code,
      idempotencyKey: code,
      path: `/transactions/${code}/auto-retry`,
      key: process.env.INTRA_SERVICE_TOKEN,
      trial: 0,
      company: foundTransaction.company,
    };

    // eslint-disable-next-line consistent-return
    return QueueService.addDelayedJob({ req }, SQSPayload, `payment:${code}:retry`, 60 * 2);
  },

  async addPayer(req, res) {
    const { code } = req.params;
    const { company } = req;
    const response = await TransactionService.addPayerAndRecipientToATransaction({
      criteria: { company, code },
      payload: { payer: req.body.payer },
    });

    return ResponseService.success(res, response.message);
  },

  async addRecipient(req, res) {
    const { code } = req.params;
    const { company } = req;
    const response = await TransactionService.addPayerAndRecipientToATransaction({
      criteria: { company, code },
      payload: { recipient: req.body.recipient },
    });

    return ResponseService.success(res, response.message);
  },

  async editDescription(req, res) {
    const { code } = req.params;
    const { company, user } = req;

    const { error } = TransactionValidator.editTransactionDescription.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const foundTransaction = await TransactionService.findOne({
      code,
      company: company.id,
    });
    if (!foundTransaction) throw new NotFoundError("Transaction");

    const systemRole = SettingsService.get("SYSTEM_ROLES");
    const { Admin, Manager } = systemRole;
    if (!(foundTransaction.payer === user.id || user.role_id === Admin || user.role_id === Manager))
      throw new ValidationError("You do not have the permission to edit the description of this transaction");

    const response = await TransactionService.editTransactionDescription({
      criteria: { company, code, user },
      description: req.body.description,
    });

    ResponseService.success(res, response.message);
  },

  async bulkRetrial(req, res) {
    const { transactions = [] } = req.body;
    const { error } = TransactionValidator.retryBulkTransaction.validate({
      transactions,
    });
    if (error) throw new ValidationError(error.message);
    let company = req.company && req.company.id;
    if (!company && transactions.length) {
      const foundTransaction = await TransactionRepo.getTransaction({
        queryParams: { code: transactions[0] },
        selectOptions: ["company"],
      });
      company = foundTransaction.company;
    }

    const foundTransactions = await TransactionRepo.getTransactions({
      queryParams: {
        code: transactions,
        status: [STATUSES.CANCELLED, STATUSES.FAILED],
      },
      selectOptions: ["company", "balance", "budget", "amount", "processor_fee", "bujeti_fee"],
    });

    const balanceCheckResponse = await Promise.allSettled([
      ...foundTransactions.map(async (transaction) => {
        const { budget, balance, amount, processor_fee: processorFee, bujeti_fee: bujetiFee } = transaction;
        const totalAmount = parseInt(amount, 10) + parseInt(processorFee, 10) + parseInt(bujetiFee, 10);

        if (budget)
          await BudgetService.canBudgetHandleTransaction({
            budget,
            amount,
            totalAmount,
          });
        else {
          const { data: isDirectDebitResponse } = await HelperService.isDirectDebitAccount({ balance });
          const { bankAccount = null, isDirectDebit = false } = isDirectDebitResponse || {};

          if (isDirectDebit) {
            if (!bankAccount) throw new NotFoundError("Bank Account");

            await BankService.canAccountProcessPayment({
              directDebit: { bankAccount: bankAccount.code },
              amount,
            });
          } else {
            await BalanceService.canBalanceHandleTransaction({
              balance,
              amount,
              totalAmount,
              company: transaction.company,
            });
          }
        }
      }),
    ]);

    const foundFailedCall = balanceCheckResponse.find(({ status }) => status === "rejected");
    if (foundFailedCall) throw new ValidationError(foundFailedCall.reason?.message || "Error occured while trying to retry your payment");

    const bulkRetrialPayload = await TransactionService.prepareBulkRetrial(company, req.body.transactions);

    const { payment } = SettingsService.get("providers");
    const provider = payment[company] || payment.defaultProvider;

    ResponseService.success(res, "Transaction is being retried");

    Array.from(bulkRetrialPayload).forEach((payload, index) =>
      setTimeout(async () => {
        await ChargeService.chargeCompany({ ...payload, provider });
      }, 2000 * index)
    );
  },

  // eslint-disable-next-line consistent-return
  async requerySingleTransaction(req, res) {
    const { code } = req.params;
    const {
      company: { id: company },
      user: { id: user },
    } = req;
    const filter = { code, company };
    if (req.isEmployeeRole) filter.user = user;
    const processingTransaction = await TransactionService.getProcessingTransaction(filter);
    if (!processingTransaction || !processingTransaction.length) return ResponseService.success(res, "Transaction already finalized");

    const foundTransaction = processingTransaction[0];
    const existingAttempt = await TransactionAttemptRepo.getTransactionAttempt({
      filter: { transaction: foundTransaction.id },
    });
    if (existingAttempt && [STATUSES.SUCCESS, STATUSES.FAILED].includes(existingAttempt.status))
      return ResponseService.success(res, "Transaction already finalized");
    const { payment } = SettingsService.get("providers");

    ResponseService.success(res, "Transaction is being queried");

    const provider = payment[company] || payment.defaultProvider;

    if ((existingAttempt && existingAttempt.status === STATUSES.PENDING) || foundTransaction.reference)
      return ChargeService.autoQuerySingleTransaction({
        key: existingAttempt?.externalIdentifier,
        reference: foundTransaction.reference,
        provider,
        notify: true,
      });

    // Check if there's a pending ledger and prevent retrial if it exists
    const foundLedger = await TransactionService.checkTransactionHasLedger(foundTransaction);
    if (foundLedger) throw new ValidationError("Transaction has a pending ledger");

    const isBatch = !!processingTransaction.batch_id;

    // prevent batch ID from being requeued
    if (isBatch) return isBatch;
  },

  async requeryProcessingTransactions(req, res) {
    const payload = { ...req.query, ...req.body };
    const { company, idempotencyKey, notify = false, ...filters } = payload;
    if (company) {
      const { id } = (await CompanyRepo.getOneCompany({ queryParams: { code: company } })) || {};
      if (!id) throw new NotFoundError("Company");
      filters.company = id;
    }
    const processingTransaction = await TransactionService.getProcessingTransaction({ ...filters });

    const { payment } = SettingsService.get("providers");

    ResponseService.success(res, "Transaction are being queried");

    if (processingTransaction.length === 0) return;

    Array.from(processingTransaction).forEach((transaction, index) => {
      // if (!event || !event.response) return;
      if (!transaction.externalIdentifier)
        return TransactionRepo.updateTransaction({
          queryParams: { code: transaction.code },
          updateFields: { status: STATUSES.FAILED, failure_reason: "Error initiating this transaction" },
        });
      setTimeout(async () => {
        const provider = req.body.preferredProvider || payment[company] || payment.defaultProvider;
        await ChargeService.autoQuerySingleTransaction({
          code: transaction.code,
          provider,
          notify: /^true$/i.test(notify),
        });
      }, 2000 * index);
    });
  },

  async reQueryTransactions(req, res) {
    const { company, ...filters } = req.query;
    const { transactions } = req.body;

    if (company) {
      const { id } = (await CompanyRepo.getOneCompany({ queryParams: { code: company } })) || {};
      if (!id) throw new NotFoundError("Company");
      filters.company = id;
    }

    filters.code = transactions;
    const { transactions: transactionsToReQuery } = await TransactionService.listTransactions(filters);

    const { payment } = SettingsService.get("providers");

    ResponseService.success(res, "Transaction are being queried");

    Array.from(transactionsToReQuery).forEach((transaction, index) => {
      setTimeout(async () => {
        const provider = payment[transaction.company] || payment.defaultProvider;
        await ChargeService.autoQuerySingleTransaction({
          code: transaction.code,
          provider,
          notify: false,
        });
      }, 2000 * index);
    });
  },

  /**
   * Automatically Requeries pending transactions and gets status
   * @param {*} req
   * @param {*} res
   */
  // async autoRequeryTransaction(req, res) {
  //   const processingTransactions = await TransactionService.getRedisProcessingTransactions();

  //   const processingTransactionsCount = (processingTransactions && Array.from(processingTransactions).length) || 0;
  //   ResponseService.success(res, "Transaction are being requeried", {
  //     count: processingTransactionsCount,
  //   });

  //   if (!processingTransactionsCount) {
  //     // if there are no transactions to requery disable the cron
  //     const jobId = SettingsService.get("requeryJobId");
  //     await CronService.updateCron(jobId, { activate: false });

  //     const redisKey = "requeryJob:running";
  //     const noProcessingTransactionsCount = await RedisService.get(redisKey);
  //     if (parseInt(noProcessingTransactionsCount, 10) > 3) {
  //       return RedisService.delete(redisKey);
  //     }
  //     return RedisService.incr(redisKey, 1);
  //   }

  //   return Array.from(processingTransactions).forEach((transaction, index) => {
  //     setTimeout(async () => {
  //       const { reference: key, provider, trial } = transaction;
  //       await ChargeService.autoQuerySingleTransaction({
  //         key,
  //         provider,
  //         notify: true,
  //         trial,
  //       });
  //     }, 2000 * index);
  //   });
  // },

  /**
   * Automatically retries failed transactions
   * @param {*} req
   * @param {*} res
   */
  async autoRetryTransaction(req, res) {
    const failedTransactions = await TransactionService.getRedisFailedTransactions();

    ResponseService.success(res, "Transaction are being retried");

    const { payment } = SettingsService.get("providers");
    if (failedTransactions)
      Array.from(failedTransactions).forEach(({ company, recipient: { transaction: transactionCode } }, index) => {
        setTimeout(async () => {
          const provider = payment[company] || payment.defaultProvider;
          await ChargeService.retryTransaction({ transaction: transactionCode, provider });
        }, 2000 * index);
      });
  },

  async massAssignCategories(req, res) {
    const { id } = req.company;

    const { error } = TransactionValidator.massAssignCategories.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const payload = req.body;

    const { message } = await TransactionService.massAssignCategories(payload, id);

    return ResponseService.success(res, message);
  },

  /**
   * Automatically retries failed single transaction
   * @param {*} req
   * @param {*} res
   */
  async autoRetrySingleTransaction(req, res) {
    const { code } = req.params;
    TransactionService.retrySingleTransfer({ ...req.body, code, Providers }); // Passed charge service for Circular dependency
    return ResponseService.success(res, "Transaction is being retried");
  },

  async massAssignRecipients(req, res) {
    const {
      company: { id: company },
    } = req;

    const { error } = TransactionValidator.massAssignRecipients.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const { message } = await TransactionService.massAssignRecipients({ payload: req.body, company });
    return ResponseService.success(res, message);
  },

  async massAssignPayers(req, res) {
    const { id } = req.company;

    const { error } = TransactionValidator.massAssignPayers.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const payload = req.body;

    const { message } = await TransactionService.massAssignPayers(payload, id);

    return ResponseService.success(res, message);
  },

  async overridePolicy(req, res) {
    const { error } = TransactionValidator.overridePolicy.validate(req.params);
    if (error) throw new ValidationError(error.message);

    const criteria = {
      ...req.params,
      company: req.company.id,
    };

    await TransactionService.overridePolicy(criteria, req.user);

    return ResponseService.success(res, "Policy overridden successfully");
  },

  async requestDetails(req, res) {
    const { error } = TransactionValidator.requestDetails.validate(req.params);
    if (error) throw new ValidationError(error.message);

    const criteria = {
      ...req.params,
      company: req.company.id,
    };

    await TransactionService.requestDetails(criteria, PolicyService);

    return ResponseService.success(res, "Details requested successfully");
  },

  async payNow(req, res) {
    const { error } = TransactionValidator.payNow.validate({ ...req.params, ...req.body });
    if (error) throw new ValidationError(error.message);

    await TransactionService.payNow({ ...req.params, ...req.body, company: req.company.id });

    return ResponseService.success(res, "Transaction initiated successfully");
  },

  async bulkPay(req, res) {
    const { error } = TransactionValidator.bulkPay.validate({ ...req.params, ...req.body });
    if (error) throw new ValidationError(error.message);

    const result = await TransactionService.bulkPayNow({ ...req.body, company: req.company.id, user: req.user });

    return ResponseService.success(res, "Transactions initiated successfully", result);
  },

  async syncZoho(req, res) {
    const { codes } = req.body;
    const [code = null] = codes;
    if (!code) return ResponseService.failure(res, "Specify the transactions codes");
    const foundTransaction = await TransactionRepo.getTransaction({
      queryParams: {
        code,
        company: req.company.id,
      },
      selectOptions: ["zohoIdentifier"],
    });
    if (!foundTransaction) return ResponseService.failure(res, "Transaction already synce");
    await syncTransactionToZohoBooks(code, req.company.id);
    return ResponseService.success(res, "Sync in progress");
  },
};
