/* eslint-disable no-prototype-builtins */
const ResponseService = require("../services/response");
const TransactionService = require("../services/transaction");
const CategoryService = require("../services/category.service");
const AssetService = require("../services/asset");
const { NotFoundError, ValidationError } = require("../utils/error.utils");
const SanitizerService = require("../utils/sanitizer");
const { STATUSES } = require("../models/status");
const { TransactionRepo, CompanyRepo } = require("../repositories/index.repo");
const ChargeService = require("../services/charge");
const BudgetService = require("../services/budget");
const SettingsService = require("../services/settings");
const CronService = require("../services/cron.service");
const BudgetLedgerRepo = require("../repositories/budgetLedger.repo");
const { TransactionValidator } = require("../validators");
const RedisService = require("../services/redis");
const ProviderService = require("../services/providers");
const TransferService = require("../services/transfer");
const BankService = require("../services/bank");
const BalanceService = require("../services/balance");
const HelperService = require("../services/helper.service");

module.exports = {
  async list(req, res) {
    const {
      company: { id: company },
      user: { id: user, role },
    } = req;

    const { error } = TransactionValidator.listTransactions.validate(req.query);
    if (error) throw new ValidationError(error.message);

    const criteria = { company, role, ...req.query };

    if (req.isEmployeeRole) criteria.payer = user;
    if (criteria.category) {
      const foundCategory = await CategoryService.getCategory({
        code: criteria.category,
      });
      if (!foundCategory) throw new NotFoundError("Category");
      criteria.category = foundCategory.id;
    }
    const { transactions, meta } = await TransactionService.listTransactions(criteria);
    const sanitizedTransactions = SanitizerService.sanitizeTransactions(transactions);
    return ResponseService.success(res, "Transactions retrieved", {
      transactions: sanitizedTransactions,
      meta,
    });
  },
  async view(req, res) {
    const { code } = req.params;
    const transactions = await TransactionService.getTransaction(code, {
      company: req.company.id,
    });
    const sanitizedTransactions = SanitizerService.sanitizeTransaction(transactions);
    return ResponseService.success(res, "Transaction retrieved", sanitizedTransactions);
  },

  async export(req, res) {
    const {
      company: { id: company },
      user: { id: user },
    } = req;
    const criteria = { perPage: 1000, company, ...req.query };

    if (req.isEmployeeRole) criteria.payer = user;
    const { transactions, meta } = await TransactionService.listTransactions(criteria);
    const sanitizedTransactions = SanitizerService.sanitizeTransactions(transactions);
    return ResponseService.success(res, "Transactions retrieved", {
      transactions: sanitizedTransactions,
      meta,
    });
  },

  async update(req, res) {
    const { category, receipt, payer, recipient, description, budget } = req.body;

    const { code } = req.params;
    const { company, user } = req;
    const payload = {};

    const { isInitiator, isAdminOrManager, transaction } = await TransactionService.canUpdateTransactionPayer({
      code,
      company,
      user,
    });

    if (isInitiator || isAdminOrManager) {
      if (category) {
        if (![STATUSES.SUCCESS, STATUSES.FAILED].includes(transaction.status))
          throw new ValidationError("Only successful or failed transactions can have their category updated");
        const foundCategory = await CategoryService.getCategory({
          code: category,
        });
        if (!foundCategory) throw new NotFoundError("Category");
        payload.category = foundCategory.id;
      }
      if (receipt) {
        const foundAsset = await AssetService.getAsset(receipt);
        if (!foundAsset) throw new NotFoundError("File");
        payload.receipt = foundAsset.id;
      }
      if ("description" in req.body)
        await TransactionService.editTransactionDescription({
          criteria: { code },
          description,
        });
      if (budget)
        await TransactionService.addBudgetToATransaction({
          criteria: { company, code },
          budget,
        });
    }

    if ((payer || recipient) && isAdminOrManager) {
      await TransactionService.addPayerAndRecipientToATransaction({
        criteria: { company, code },
        payload: { payer, recipient },
      });
    }

    if (receipt || category) {
      if (category && ![STATUSES.SUCCESS, STATUSES.FAILED].includes(transaction.status))
        throw new ValidationError("Only successful or failed transactions can have their category updated");
      await TransactionService.updateTransaction({
        criteria: { code, company: company.id },
        payload,
      });

      if (receipt)
        await AssetService.updateAsset({
          updateFields: {
            entityType: "Transaction",
            entityId: transaction.id,
          },
          queryParams: { code: receipt },
        });
    }
    return ResponseService.success(res, "Transaction successfully updated");
  },

  async cancelTransaction(req, res) {
    const { code } = req.params;
    const { id: company } = req.company;
    const { id: user } = req.user;
    const foundTransaction = await TransactionRepo.getTransaction({
      queryParams: { code, company },
    });
    if (!foundTransaction) throw new NotFoundError("Transaction");
    if ([STATUSES.PROCESSING].includes(foundTransaction.status)) {
      throw new ValidationError("This transaction cannot be cancelled");
    }
    await TransactionService.cancelTransaction(foundTransaction);
    // create budget ledger when transation is cancelled
    if (foundTransaction.budget) {
      const budgetLedger = await BudgetLedgerRepo.getBudgetLedger({
        criteria: {
          transaction: foundTransaction.id,
          budget: foundTransaction.budget,
        },
      });
      if (budgetLedger) {
        const ledgerPayload = {
          budget: foundTransaction.budget,
          user,
          currency: foundTransaction.currency,
          transaction: foundTransaction.id,
          amount: foundTransaction.amount,
          description: `[Reversal] ${foundTransaction.description}`,
        };
        await BudgetService.createBudgetLedger(ledgerPayload);
      }
    }
    // call the charge endpoint with this information
    return ResponseService.success(res, "Transaction cancelled");
  },

  async retryTransaction(req, res) {
    const { code } = req.params;
    const { id: company } = req.company;
    const foundTransaction = await TransactionRepo.getTransaction({
      queryParams: { code, company },
    });
    if (!foundTransaction) throw new NotFoundError("Transaction");
    if (![STATUSES.CANCELLED, STATUSES.FAILED].includes(foundTransaction.status)) {
      throw new ValidationError("This transaction cannot be retried");
    }
    const payload = await TransactionService.buildPayloadForRetrial(foundTransaction);
    // call the charge endpoint with this information

    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[req.company.code] || payment.defaultProvider;
    payload.provider = providerToUse;

    await TransactionRepo.updateTransaction({
      queryParams: { id: foundTransaction.id },
      updateFields: { status: STATUSES.PROCESSING },
    });
    ResponseService.success(res, "Transaction is being retried");

    const response = await ChargeService.retryTransaction({ transaction: code, provider: providerToUse });
    if (response.error)
      await TransactionRepo.updateTransaction({
        queryParams: { id: foundTransaction.id },
        updateFields: { status: STATUSES.FAILED },
      });
  },

  async addPayer(req, res) {
    const { code } = req.params;
    const { company } = req;
    const response = await TransactionService.addPayerAndRecipientToATransaction({
      criteria: { company, code },
      payload: { payer: req.body.payer },
    });

    return ResponseService.success(res, response.message);
  },

  async addRecipient(req, res) {
    const { code } = req.params;
    const { company } = req;
    const response = await TransactionService.addPayerAndRecipientToATransaction({
      criteria: { company, code },
      payload: { recipient: req.body.recipient },
    });

    return ResponseService.success(res, response.message);
  },

  async editDescription(req, res) {
    const { code } = req.params;
    const { company, user } = req;

    const { error } = TransactionValidator.editTransactionDescription.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const foundTransaction = await TransactionService.findOne({
      code,
      company: company.id,
    });
    if (!foundTransaction) throw new NotFoundError("Transaction");

    const systemRole = SettingsService.get("SYSTEM_ROLES");
    const { Admin, Manager } = systemRole;
    if (!(foundTransaction.payer === user.id || user.role_id === Admin || user.role_id === Manager))
      throw new ValidationError("You do not have the permission to edit the description of this transaction");

    const response = await TransactionService.editTransactionDescription({
      criteria: { company, code, user },
      description: req.body.description,
    });

    ResponseService.success(res, response.message);
  },

  async bulkRetrial(req, res) {
    const { transactions = [] } = req.body;
    const { error } = TransactionValidator.retryBulkTransaction.validate({
      transactions,
    });
    if (error) throw new ValidationError(error.message);
    let company = req.company && req.company.id;
    if (!company && transactions.length) {
      const foundTransaction = await TransactionRepo.getTransaction({
        queryParams: { code: transactions[0] },
        selectOptions: ["company"],
      });
      company = foundTransaction.company;
    }

    const bulkRetrialPayload = await TransactionService.prepareBulkRetrial(company, req.body.transactions);

    const { payment } = SettingsService.get("providers");
    const provider = payment[company] || payment.defaultProvider;

    ResponseService.success(res, "Transaction is being retried");

    Array.from(bulkRetrialPayload).forEach((payload, index) =>
      setTimeout(async () => {
        await ChargeService.chargeCompany({ ...payload, provider });
      }, 2000 * index)
    );
  },

  async requeryProcessingTransactions(req, res) {
    const { company, notify = false, ...filters } = req.query;
    if (company) {
      const { id } = (await CompanyRepo.getOneCompany({ queryParams: { code: company } })) || {};
      if (!id) throw new NotFoundError("Company");
      filters.company = id;
    }
    const processingTransaction = await TransactionService.getProcessingTransaction(filters);

    const { payment } = SettingsService.get("providers");

    ResponseService.success(res, "Transaction are being queried");

    Array.from(processingTransaction).forEach((transaction, index) => {
      // if (!event || !event.response) return;
      setTimeout(async () => {
        // const eventResponse = JSON.parse(event.response);
        // const {
        //   data: { id: key }
        // } = eventResponse;
        const provider = payment[transaction.company] || payment.defaultProvider;
        await ChargeService.autoQuerySingleTransaction({
          key: transaction.externalIdentifier,
          provider,
          notify: /^true$/i.test(notify),
        });
      }, 2000 * index);
    });
  },

  async reQueryTransactions(req, res) {
    const { company, ...filters } = req.query;
    const { transactions } = req.body;

    if (company) {
      const { id } = (await CompanyRepo.getOneCompany({ queryParams: { code: company } })) || {};
      if (!id) throw new NotFoundError("Company");
      filters.company = id;
    }

    filters.code = transactions;
    const { transactions: transactionsToReQuery } = await TransactionService.listTransactions(filters);

    const { payment } = SettingsService.get("providers");

    ResponseService.success(res, "Transaction are being queried");

    Array.from(transactionsToReQuery).forEach((transaction, index) => {
      setTimeout(async () => {
        const provider = payment[transaction.company] || payment.defaultProvider;
        await ChargeService.autoQuerySingleTransaction({
          key: transaction.externalIdentifier,
          provider,
          notify: false,
        });
      }, 2000 * index);
    });
  },

  /**
   * Automatically Requeries pending transactions and gets status
   * @param {*} req
   * @param {*} res
   */
  async autoRequeryTransaction(req, res) {
    const processingTransactions = await TransactionService.getRedisProcessingTransactions();

    const processingTransactionsCount = (processingTransactions && Array.from(processingTransactions).length) || 0;
    ResponseService.success(res, "Transaction are being requeried", {
      count: processingTransactionsCount,
    });

    if (!processingTransactionsCount) {
      // if there are no transactions to requery disable the cron
      const jobId = SettingsService.get("requeryJobId");
      await CronService.updateCron(jobId, { activate: false });

      const redisKey = "requeryJob:running";
      const noProcessingTransactionsCount = await RedisService.get(redisKey);
      if (parseInt(noProcessingTransactionsCount, 10) > 3) {
        return RedisService.delete(redisKey);
      }
      return RedisService.incr(redisKey, 1);
    }

    return Array.from(processingTransactions).forEach((transaction, index) => {
      setTimeout(async () => {
        const { reference: key, provider, trial } = transaction;
        await ChargeService.autoQuerySingleTransaction({
          key,
          provider,
          notify: true,
          trial,
        });
      }, 2000 * index);
    });
  },

  /**
   * Automatically retries failed transactions
   * @param {*} req
   * @param {*} res
   */
  async autoRetryTransaction(req, res) {
    const failedTransactions = await TransactionService.getRedisFailedTransactions();

    ResponseService.success(res, "Transaction are being retried");

    const { payment } = SettingsService.get("providers");
    if (failedTransactions)
      Array.from(failedTransactions).forEach((transaction, index) => {
        setTimeout(async () => {
          const provider = payment[transaction.company] || payment.defaultProvider;
          await ChargeService.retryTransaction({ transaction: transaction.recipient.transaction, provider });
        }, 2000 * index);
      });
  },

  /**
   * Handle single Book transfer from SQS
   */
  async handleSQSBookTransfer(req, res) {
    const { data } = req.body;
    await TransferService.handleSQSBookTransfer({ data, ProviderService });
    ResponseService.success(res, "OK");
  },
};
