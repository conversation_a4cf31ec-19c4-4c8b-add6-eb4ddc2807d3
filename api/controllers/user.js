const Sanitizer = require("../utils/sanitizer");
const AuthorizationService = require("../services/authentication");
const RedisService = require("../services/redis");
const { STATUSES } = require("../models/status");
const { User, BankAccount, TeamMember, Team, UserBudget, Budget, Role } = require("../models/index");
const OTPService = require("../services/otp");
const BeneficiaryService = require("../services/beneficiary.service");
const BudgetService = require("../services/budget");
const Utils = require("../utils/utils");
const NotificationService = require("../services/notification");
const UserService = require("../services/user");
const RolesService = require("../services/roles.service");
const ResponseService = require("../services/response");
const AnalyticService = require("../services/analytics");
const CompanyService = require("../services/company");
const SettingsService = require("../services/settings");
const CardService = require("../services/card");
const ReferralService = require("../services/referral.service");
const ValidationError = require("../utils/validation-error");
const NotFoundError = require("../utils/not-found-error");
const DemoRequestUtils = require("../utils/demoRequest");
const UserValidator = require("../validators/user.validator");
const { isProd } = require("../utils");
const { ForbiddenError } = require("../utils/error.utils");
const { CompanyRepo, UserRepo, PhoneNumberRepo, ReferralRepo, RoleRepo, BeneficiaryRepo, TeamMemberRepo } = require("../repositories/index.repo");
const AuditLogsService = require("../services/auditLogs");

module.exports = {
  async register(req, res) {
    const { error } = UserValidator.create.validate(req.body);
    if (error) throw new ValidationError(error.message);

    const rejectedDomains = ["bujeti.com"];

    if (!isProd() && !rejectedDomains.includes(String(req.body.email).split("@")[1])) {
      return ResponseService.failure(res, "Please use your work email");
    }

    const { referral: referralCode, inviteCode, ...rest } = req.body;

    await UserService.validateRegistrationPayload(req.body);

    let companyData = null;
    if (inviteCode) {
      companyData = await CompanyService.verifyInviteCode(inviteCode);
    }

    const {
      message,
      data: { newUser, hash },
    } = await UserService.registerUser({
      ...rest,
    });

    if (referralCode) RedisService.set(`${newUser.code}:referralCode`, referralCode);

    if (companyData)
      await RedisService.setex(
        `invite:${newUser.code}`,
        JSON.stringify({
          companyName: companyData.name,
          companyCode: companyData.code,
        }),
        86400
      );

    return ResponseService.success(res, message, { hash });
  },

  async verifyRegistrationToken(req, res) {
    const demoCode = await RedisService.get(`registration:${req.query.token}`);
    if (!demoCode) {
      return ResponseService.failure(res, "Your invitation has already expired");
    }
    const { id, ...company } = await DemoRequestUtils.getDemoRequest(demoCode);
    return ResponseService.success(res, "Valid token", { ...company });
  },

  async getInvitedUser(req, res) {
    const { hash: emailhash = undefined } = req.query;
    if (!emailhash) throw new ValidationError("Invalid request, hash is not specified");
    const redisData = Utils.parseJSON(await RedisService.get(emailhash));
    if (typeof redisData !== "object" || !redisData) throw new ValidationError("Invalid hash");
    const { userCode = undefined } = redisData;
    if (!userCode) throw new ValidationError("Invalid hash submitted");
    const user = await UserService.fetchUser(userCode);
    if (!user) throw new NotFoundError("User");
    const { firstName, lastName, email } = user;
    const hash = Utils.generateUniqueId(Utils.alphabet.alphanumeric, 30);
    await RedisService.setex(emailhash, JSON.stringify({ hash, userCode }), 86400);
    return ResponseService.success(res, "User details fetched successfully", {
      firstName,
      lastName,
      email,
      hash,
    });
  },

  async setNewBeneficiaryPassword(req, res) {
    const { hash: emailhash } = req.body;
    const redisData = Utils.parseJSON(await RedisService.get(emailhash));
    if (typeof redisData !== "object" || !redisData) throw new ValidationError(`Invalid hash`);
    const { userCode: code = undefined } = redisData;
    if (!code) throw new ValidationError("Invalid hash sent");
    await UserService.validateNewBeneficiaryPasswordPayload(req.body);
    const body = { code, password: req.body.password };
    const user = await UserService.setNewBeneficiaryPassword(body);
    const { data: userRoles } = await RolesService.getRoleWithPermissions(user.Role.code);
    const { token, refreshToken } = await AuthorizationService.generateJwtToken(user);
    const sanitizedUser = Sanitizer.sanitizeUser(user);
    await RedisService.delete(emailhash);
    return ResponseService.success(res, "Password updated successfully", {
      user: sanitizedUser,
      token,
      refreshToken,
      role: Sanitizer.sanitizeRole(userRoles),
    });
  },

  async view(req, res) {
    const { code } = req.params;
    if (req.isInternalCall) {
      const foundCompany = await CompanyRepo.getOneCompany({ code: req.query.company });
      if (!foundCompany) throw new NotFoundError("Company");
      req.company = foundCompany;
    }

    let { id } = req.user || {};

    if (req.isManager || req.isInternalCall) {
      id = req.query.user || id;
    }

    let user;
    if (code) user = await UserService.fetchUserByCodeOrId(code);
    else user = await UserService.fetchUser(id);
    if (!user) throw new NotFoundError("User");
    if (req.isMobile) {
      const [stats, { cards }] = await Promise.all([
        UserService.getUserStats(id),
        CardService.fetchCards({ company: req.company.id, user: user.id }),
      ]);
      user.stats = stats;
      user.cards = cards;
    }
    const userRole = await RoleRepo.getRoleWithPermissions(user.role_id);
    user.role = userRole.name;
    if (!user.referralCode) {
      ReferralRepo.generateUserReferralCode(user.id, ReferralRepo.ReferralTypes.REWARDS);
    }
    const bankAccounts = await BankAccount.findAll({
      where: {
        owner: user.id,
        ownerType: "user",
      },
    });

    const { Team: foundTeam } =
      (await TeamMember.findOne({
        where: {
          user: user.id,
          status: STATUSES.ACTIVE,
        },
        include: [Team],
      })) || {};

    const userBudgets = await UserBudget.findAll({
      where: {
        user: user.id,
        status: STATUSES.ACTIVE,
      },
      include: [{ model: Budget, attributes: ["name"] }],
    });

    return ResponseService.success(res, "User retrieved", {
      user: Sanitizer.sanitizeUser(user),
      role: Sanitizer.sanitizeRole(userRole),
      bankAccounts: Sanitizer.sanitizeModelInstances(bankAccounts, "BankAccount"),
      team: Sanitizer.sanitizeTeam(foundTeam),
      budgets: userBudgets.map((userBudget) => userBudget?.Budget),
    });
  },

  async update(req, res) {
    const { id, company } = req.user;
    const { code: companyCode } = company || {};
    await UserService.validateUpdatePayload({ ...req.body });
    const { lookedUpCompany, company: companyName, pin, newPin, ...userData } = req.body;
    let message = "User successfully updated";
    let method = "success";
    if (!companyCode && companyName) {
      const { error, message: companyMessage, company: createdCompany } = await CompanyService.createCompany(companyName, id);
      if (error) {
        message = companyMessage;
        method = "failure";
        CompanyService.sendExistingCompanyMail(req.user, companyName);
      } else userData.company = createdCompany.id;
    }

    const { phoneNumber = null } = req.body;
    if (phoneNumber) {
      if (req.user.phoneNumber || req.user.PhoneNumber) {
        const phoneId = req.user.phonenumber || req.user.PhoneNumber?.id;
        await PhoneNumberRepo.updateAPhoneNumber({
          queryParams: {
            id: phoneId,
          },
          updateFields: { ...phoneNumber, internationalFormat: Utils.formatPhoneNumber(phoneNumber.localFormat, phoneNumber.countryCode) },
        });
        userData.phoneNumber = phoneId;
      } else {
        const newPhoneNumber = await PhoneNumberRepo.createAPhoneNumber({
          queryParams: {
            ...phoneNumber,
            internationalFormat: Utils.formatPhoneNumber(phoneNumber.localFormat, phoneNumber.countryCode),
            user: req.user.id,
          },
        });
        userData.phoneNumber = newPhoneNumber.id;
      }
    }

    if (newPin) {
      const blursedPin = newPin.length === 4 ? Utils.md5(String(newPin)) : newPin.substring(6, 38);
      userData.pin = await AuthorizationService.encryptPassword(blursedPin);
    }

    const updatedUser = await UserService.updateUser(id, userData);
    ResponseService[method](res, message, {
      user: Sanitizer.sanitizeUser(updatedUser),
    });

    if (lookedUpCompany) await CompanyService.updateCompanyDataFromLookUp({ lookupCode: lookedUpCompany, company: userData.company });
    UserService.updateUserProviderDetails({ id, payload: userData });

    // TODO make it  a Slack Notification and integrate with emailer to follow up
    if (companyName) {
      NotificationService.sendEmail(
        [SettingsService.get("customer_experience_email")],
        "new-company-registration",
        {
          company: companyName,
          name: updatedUser.firstName,
          contact: updatedUser.email,
        },
        { subject: "New Registration 🚀🚀" }
      );
    }
    const redisKey = `${req.user.code}:referralCode`;
    const referralCode = await RedisService.get(redisKey);
    if (referralCode) {
      if (!userData.company) {
        userData.company = req.user.company;
      }
      await ReferralService.linkCompanyToReferrer(referralCode, userData.company);

      ReferralService.claimReferral({
        email: updatedUser.email,
        referralCode,
        firstName: updatedUser.firstName,
        companyName,
        company: userData.company,
      });
    }
    RedisService.delete(redisKey);
  },

  async verifyOTP(req, res) {
    const { code: statusCode, message, user } = await OTPService.verifyRedis({ ...req.body });
    if (statusCode === 200) {
      const payload = {
        firstName: user.firstName || "there",
        login: `${Utils.getDashboardURL()}/login`,
      };

      const inviteData = await RedisService.get(`invite:${user.code}`);

      if (inviteData) {
        const companyData = Utils.parseJSON(inviteData);
        // Associate user with company and set role as employee
        const employeeRole = await RoleRepo.getRole({
          queryParams: {
            name: "Employee",
            status: STATUSES.ACTIVE,
          },
        });

        if (!employeeRole) {
          throw new ValidationError("Employee role not found");
        }

        const { id: companyId = null } = (await CompanyRepo.getOneCompany({ queryParams: { code: companyData.companyCode } })) || {};
        if (!companyId) {
          throw new NotFoundError("Company");
        }

        const [companyAdmins, teamMembership] = await Promise.all([
          CompanyService.getCompanyWithAdmins({ code: companyData.companyCode }),
          TeamMemberRepo.getTeamMember({
            user: user.id,
            company: companyId,
            status: STATUSES.ACTIVE,
          }),
        ]);
        // Get the first admin if available, otherwise leave manager as null
        const adminUser = companyAdmins?.Users?.[0] || null;

        await UserRepo.updateUser({
          queryParams: { id: user.id },
          updateFields: {
            company: companyId,
            role_id: employeeRole.id,
            role: employeeRole.name,
            manager: adminUser?.id || null,
          },
        });

        await user.reload();
        await RedisService.delete(`invite:${user.code}`);

        const teamName = teamMembership ? teamMembership?.Team?.name : companyData.companyName;

        // If there are admins, notify them and create audit logs
        if (companyAdmins?.Users?.length > 0) {
          await Promise.all(
            companyAdmins.Users.map((admin) => {
              const notificationOptions = {
                subject: `New team member added: ${user.firstName} ${user.lastName}`,
                from_name: "Bujeti",
              };

              const notificationPayload = {
                firstName: admin.firstName,
                memberName: `${user.firstName} ${user.lastName}`,
                teamName,
                year: new Date().getFullYear(),
                teamLink: `${Utils.getDashboardURL()}/team/people`,
              };
              return NotificationService.notifyUser(admin, "new-team-member", notificationPayload, notificationOptions);
            })
          );
        }

        await BeneficiaryService.addBeneficiary(user.id, user.id, companyId, employeeRole.id, STATUSES.PENDING);

        await AuditLogsService.createLog({
          event: "new-team-member-added",
          user: user.id,
          table_type: "User",
          table_id: user.id,
          initial_state: null,
          delta: Sanitizer.sanitizeLightUser(user),
        });
      }

      const isNonVerifiedUser = [STATUSES.PENDING, STATUSES.UNVERIFIED].includes(user.status);

      if (isNonVerifiedUser) {
        if (!user.company) {
          NotificationService.notifyUser(user, "welcome", payload);
        }

        BeneficiaryService.updateBeneficiary({ user: user.id }, STATUSES.ACTIVE);
        BudgetService.activateBeneficiaryBudgets(user.id);
        await User.update({ status: STATUSES.ACTIVE }, { where: { id: user.id } });
      }

      if (req.isMobile) {
        const [stats, { cards }] = await Promise.all([
          UserService.getUserStats(user.id),
          CardService.fetchCards({ company: user.company, user: user.id }),
        ]);
        user.stats = stats;
        user.cards = cards;
      }

      const { data: userRoles } = await RolesService.getRoleWithPermissions(user.Role.code);
      const beneficiary = await BeneficiaryService.getSingleBeneficiary({
        user: user.id,
        company: user.company,
      });

      const { token, refreshToken } = await AuthorizationService.generateJwtToken(user);
      const serializedUser = Sanitizer.sanitizeUser(user);

      if (beneficiary) serializedUser.beneficiary = beneficiary.code;
      if (!user.referralCode) {
        ReferralRepo.generateUserReferralCode(user.id, ReferralRepo.ReferralTypes.REWARDS);
      }

      return ResponseService.success(res, "User authenticated", {
        user: serializedUser,
        token,
        ...(req.body.rememberMe && { refreshToken }),
        role: Sanitizer.sanitizeRole(userRoles),
        hasPinEnabled: !!user.pin,
      });
    }
    return ResponseService.json(res, { statusCode, data: { message } });
  },

  async verifyPin(req, res) {
    if (!req.isMobile) {
      throw new ForbiddenError("Action not allowed");
    }

    if (!req.body.pin) {
      throw new ValidationError("Please provide your pin");
    }

    const foundRefreshToken = Utils.parseJSON(await RedisService.get(`refreshToken:${req.body.refreshToken}`));

    if (!foundRefreshToken) {
      throw new ForbiddenError("Invalid refresh token");
    }

    const foundUser = await UserService.fetchUser(foundRefreshToken.user);
    if (!foundUser?.pin) {
      throw new ForbiddenError("Invalid pin");
    }

    const cleanedPin = req.body.pin.length > 4 ? req.body.pin.substring(6, 38) : Utils.md5(req.body.pin);
    const isPinCorrect = await AuthorizationService.comparePassword(cleanedPin, foundUser.pin);
    if (!isPinCorrect) {
      throw new ValidationError("Invalid pin");
    }

    const { token, refreshToken } = AuthorizationService.generateJwtToken(foundUser);
    const { data: userRoles } = await RolesService.getRoleWithPermissions(foundUser.Role.code);
    const serializedUser = Sanitizer.sanitizeUser(foundUser);

    // delete old token
    RedisService.delete(`refreshToken:${req.body.refreshToken}`);

    return ResponseService.success(res, "User authenticated", {
      user: serializedUser,
      token,
      refreshToken,
      role: Sanitizer.sanitizeRole(userRoles),
    });
  },

  async login(req, res) {
    UserService.validateLoginPayload(req.body);
    const user = await UserService.authenticate(req.body);

    if ([STATUSES.BLOCKED, STATUSES.INACTIVE].includes(user.status)) {
      return ResponseService.failure(res, "Account blocked or deactivated");
    }

    if (req.body.rememberMe && req.body.refreshToken) {
      const foundRefreshToken = Utils.parseJSON(await RedisService.get(`refreshToken:${req.body.refreshToken}`));
      if (foundRefreshToken && foundRefreshToken.user === user.id) {
        const { token, refreshToken } = AuthorizationService.generateJwtToken(user);
        const { data: userRoles } = await RolesService.getRoleWithPermissions(user.Role.code);
        const serializedUser = Sanitizer.sanitizeUser(user);

        // delete old token
        RedisService.delete(`refreshToken:${req.body.refreshToken}`);

        return ResponseService.success(res, "User authenticated", {
          user: serializedUser,
          token,
          refreshToken,
          role: Sanitizer.sanitizeRole(userRoles),
        });
      }
    }

    const otp = await OTPService.createOTPRedisCode(user);
    // SEND OTP TO EMAIL
    const { first_name: firstName, email, phoneNumber, status } = user;
    const { hash, code } = otp;

    NotificationService.notifyUser(
      { email, phoneNumber },
      "otp-verification-login",
      { firstName: firstName || "there", code: code.split("") },
      {
        from: "Bujeti Security Team",
        subject: `Your OTP to sign in`,
      }
    );

    return ResponseService.json(res, {
      statusCode: status === STATUSES.UNVERIFIED ? 400 : 200,
      data: {
        message: status === STATUSES.UNVERIFIED ? "Please verify your email/phone number" : "An OTP has been sent to your email address",
        status: true,
        error: false,
        data: { hash },
      },
    });
  },

  async logout(req, res) {
    UserService.killSession(req.key.sessionId, req.key.exp);
    return ResponseService.success(res, "Logout successfully");
  },

  async forgotPassword(req, res) {
    UserService.validatePasswordResetPayload(req.body);
    ResponseService.success(res, "A reset link will be sent to your email/phone number");
    const user = await UserService.fetchUserByEmailOrPhoneNumber(null, req.body.email, req.body.phoneNumber);
    if (!user || user.status === STATUSES.BLOCKED) return;
    return UserService.sendPasswordResetLink(user);
  },

  async resetPassword(req, res) {
    const { passwordUpdated, ...user } = await UserService.resetPassword(req.body);
    if (passwordUpdated) UserService.sendPasswordChangedEmail(user);
    return ResponseService.success(res, "Password reset. Now you can log in");
  },

  async internalResetPassword(req, res) {
    const { id } = req.user;
    const { passwordUpdated, ...user } = await UserService.internalResetPassword({ id, ...req.body });
    if (passwordUpdated) UserService.sendPasswordChangedEmail(user);
    return ResponseService.success(res, "Password reset. Now you can log in");
  },

  // eslint-disable-next-line consistent-return
  async resendVerificationEmail(req, res) {
    const { email, phoneNumber } = req.body;
    if (email) {
      const existinRecord = Utils.parseJSON(await RedisService.get(`otp:${email}`));
      if (existinRecord) return ResponseService.success(res, "A verification code has already been sent to your email");
    }
    const existingUser = await UserService.fetchUserByEmailOrPhoneNumber(null, email, phoneNumber);
    if (!existingUser) return ResponseService.failure(res, "User not found");
    await OTPService.expireOldOTPRedis(existingUser);
    const otp = await OTPService.createOTPRedisCode(existingUser);
    ResponseService.success(res, "A verification code will be sent to your email", { hash: otp.hash });
    await OTPService.sendVerificationCode(existingUser, otp, "otp-verification-login");
  },

  async analytics(req, res) {
    const analytics = await AnalyticService.getAnalytics(req.user.id, req.query);
    return ResponseService.success(res, "Your insights have been retrieved successfully", analytics);
  },

  /**
   * Refreshes a user's access token
   * @method POST /refresh-token
   * @param {*} req.body
   * @param { String } req.body.token The refresh token to expire
   */
  async refreshToken(req, res) {
    const { id: user } = req.user;
    const { token: previousToken } = req.body;

    const { token, refreshToken } = await AuthorizationService.refreshToken({
      user,
      token: previousToken,
    });

    return ResponseService.success(res, "Access token generated", {
      token,
      refreshToken,
    });
  },

  /**
   * Authenticate a user's
   * @method POST /companies/:code/authenticate
   */
  async authenticate(req, res) {
    const { id: user, code } = req.user;
    if (req.company?.code !== req.params.code) throw new ForbiddenError("You are not authorized here");

    const { token, refreshToken } = AuthorizationService.generateJwtToken({
      id: user,
      code,
      company: req.company.code,
    });

    const serializedUser = Sanitizer.sanitizeUser(req.user);
    const { data: userRoles } = await RolesService.getRoleWithPermissions(req.user.role_id);

    return ResponseService.success(res, "Access token generated", {
      token,
      refreshToken,
      user: serializedUser,
      role: Sanitizer.sanitizeRole(userRoles),
    });
  },

  async switchAccount(req, res) {
    const { destination } = req.params;

    const foundCompany = await CompanyRepo.getOneCompany({ queryParams: { code: destination } });

    if (!foundCompany) return ResponseService.failure(res, "Company not found");
    if ([STATUSES.ACTIVE, STATUSES.APPROVED, STATUSES.VERIFYING, STATUSES.VERIFIED, STATUSES.UNVERIFIED].includes(foundCompany.status)) {
      return ResponseService.failure(res, "Company in invalid state(" + foundCompany.code + ")");
    }

    const userCompany = await BeneficiaryRepo.getOneBeneficiary({
      queryParams: {
        user: req.user.id,
        company: foundCompany.id,
        status: STATUSES.ACTIVE,
      },
    });
    if (!userCompany) return ResponseService.failure(res, "You are not authorized to access this account");
    await UserRepo.updateUser({
      queryParams: {
        id: req.user.id,
      },
      updateFields: {
        company: foundCompany.id,
        role_id: userCompany.role,
      },
    });
    setTimeout(() => UserService.killSession(req.key.sessionId, req.key.exp), 3000);
    return ResponseService.success(res, "Account successfully switched");
  },
};
