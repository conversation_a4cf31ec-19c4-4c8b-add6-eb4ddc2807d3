const ResponseService = require("../services/response");
const UserBudgetService = require("../services/userBudget.service");

module.exports = {
  async getUserBudgets(req, res) {
    const { id: employee } = req.user;
    const { message, data } = await UserBudgetService.getUserBudget({
      employee,
      ...req.query,
    });
    return ResponseService.success(res, message, data);
  },
  async getUserBudget(req, res) {
    const payload = {
      id: req.user.id,
      country_code: req.query.country_code,
    };
    const { message, data } = await UserBudgetService.getAUserBudget(payload);
    return ResponseService.success(res, message, data);
  },
};
