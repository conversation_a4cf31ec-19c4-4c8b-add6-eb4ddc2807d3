const axios = require("axios");
const { Status } = require("../models");
const ResponseService = require("../services/response");
const { Utils, Budget } = require("../services");
const DemoRequestUtils = require("../utils/demoRequest");
const FileService = require("../services/FileService");
const RedisService = require("../services/redis");
const BankService = require("../services/bank");
const NotificationService = require("../services/notification");
const { ValidationError, NotFoundError } = require("../utils/error.utils");
const BalanceValidator = require("../validators/balance.validator");
const Provider = require("../services/providers");
const SettingsService = require("../services/settings");
const { CompanyRepo } = require("../repositories/index.repo");
const VirtualCardService = require("../services/virtualcards.service");
const mockBanks = require("../mocks/banks.mock");
const UtilsService = require("../services/utils.service");
const Sanitizer = require("../utils/sanitizer");
const UtilsValidator = require("../validators/util.validator");
const { formatEmployeeData } = require("../utils/zoho.utils");
const { BankValidator } = require("../validators");
const { getAccountIssuers, sampleBanks } = require("../utils/banks.utils");
const { isProd } = require("../utils");

module.exports = {
  async statuses(req, res) {
    const statuses = await Status.findAll();
    return ResponseService.success(
      res,
      "Statuses retrieved",
      statuses.map((status) => status.toJSON())
    );
  },
  uploadUrl(req, res) {
    FileService.validateUploadUrlPayload(req.body);
    const response = FileService.getUploadUrl({
      ...req.body,
      companyName: req.company.name,
      companyCode: req.company.code,
    });
    return ResponseService.success(res, "Upload URL retrieved", response);
  },
  async requestDemo(req, res) {
    DemoRequestUtils.validateDemoRequest(req.body);
    const { code } = await DemoRequestUtils.createRequest(req.body);
    DemoRequestUtils.sendCalendlyLinkToRequester(req.body);
    DemoRequestUtils.notifyCustomerSuccessTeam({ ...req.body, code });
    return ResponseService.success(res, "Request received");
  },
  async sendRegistrationInvitation(req, res) {
    const { email, firstName, lastName } = req.body;
    const randomString = Utils.generateRandomString(20);
    const { code } = await DemoRequestUtils.createRequest(req.body);
    const key = `registration:${randomString}`;
    await RedisService.set(key, code);
    NotificationService.notifyUser({ email }, "demo-registration-invite", {
      firstName,
      lastName,
      registration_link: `${Utils.getDashboardURL()}/register?token=${randomString}`,
    });
    return ResponseService.success(res, "Mail sent");
  },

  /**
   * Get's the current exchange rate and returns the equivalent in the target currency
   * @param {*} req //params: { targetCurrency, baseCurrency } // body: { amount }
   * @param {*} res
   * @returns { rate, currency, amount  }
   */
  async getExchangeRateAndAmount(req, res) {
    const payload = { ...req.body, ...req.params };
    const { targetCurrency } = req.params;
    const { error } = BalanceValidator.exchangeRate.validate(payload);
    if (error) throw new ValidationError(error.message);
    const rate = await VirtualCardService.getExchangeRate(targetCurrency, req.company);
    const response = { rate, currency: targetCurrency };
    // Rate has to be converted to it's lowest currency since that payload is in it's lowest currency
    if (targetCurrency === "usd") {
      // If target curency is usd, divide naira by the cent value
      response.amount = (parseInt(payload.amount, 10) / (parseInt(rate, 10) * 100)) * 100;
    } else if (targetCurrency === "ngn") {
      // If target curency is ngn, multiply naira by the cent value
      response.amount = parseInt(payload.amount, 10) * parseInt(rate, 10);
    }
    return ResponseService.success(res, "Exchange rate fetched successfully", response);
  },

  /**
   * Endpoint to list all banks from the current supporting provider
   * @param {*} req
   * @param {*} res
   */
  async listBanks(req, res) {
    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[req.company.code] || payment.defaultProvider;
    const { error, data } = await Provider[providerToUse].listBanks();
    if (error) throw new ValidationError(`Provider error: ${data.errors.map(({ detail }) => detail || "").join("\n")}`);
    const formattedBanks = Array.from(data).map(({ attributes }) => ({
      code: attributes.nipCode,
      name: attributes.name,
    }));
    return ResponseService.success(res, "Banks fetched successfully", formattedBanks);
  },
  async reconcileBudgetLedgers(req, res) {
    ResponseService.success(res, "Budget ledgers reconciliation in progress...");
    await Budget.reconcileBudgetLedgersWithTransactions(req.query);
  },

  async resolveAccountIssuers(req, res) {
    const { error } = BankValidator.resolveAccountNumber.validate(req.body);
    if (error) throw new ValidationError(error.message);
    const { accountNumber } = req.body;
    // MOCK FAILURE
    if (accountNumber === "**********") {
      return Response.failure(res, "Something went wrong, please try again later.");
    }
    // RETURN DUMMY DATA FOR ALL CALLS ON STAGING AND DEMO
    let formattedBanks = [];
    if (!isProd()) {
      formattedBanks = sampleBanks();
    } else {
      const { payment } = SettingsService.get("providers");
      const providerToUse = payment[req.company.code] || payment.defaultProvider;
      let { data } = await Provider[providerToUse].listBanks();
      formattedBanks = Array.from(data).map(({ attributes }) => ({
        code: attributes.nipCode,
        name: attributes.name,
      }));
    }
    const detectedBanks = getAccountIssuers(accountNumber, formattedBanks);
    return ResponseService.success(res, "Banks fetched successfully", detectedBanks.length ? detectedBanks : formattedBanks);
  },

  async generateVirtualAccount(req, res) {
    const { companyCode } = req.params;
    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[companyCode] || payment.defaultProvider;
    const companyDetails = await CompanyRepo.getCompanyWithAdmins({ code: companyCode }, true);

    if (!companyDetails) throw new NotFoundError("Company");
    const admins = companyDetails?.Users;
    const {
      error: providerError,
      message,
      account: virtualAccount,
    } = await BankService.generateVirtualAccount({
      company: companyDetails.id,
      companyCode: companyDetails.code,
      provider: providerToUse,
      user: admins[0].id,
    });
    if (providerError) throw new ValidationError(message);
    return ResponseService.success(res, "Account created", { virtualAccount });
  },

  /**
   * Paginate any request
   * @param {*} req
   */
  paginate(req) {
    const page = req?.query.page || 1;
    const perPage = req?.query.perPage || 20;
    return {
      limit: Number(perPage),
      offset: (page - 1) * perPage,
      subQuery: false,
    };
  },

  async getPendingTransactions(req, res) {
    const pendingTransactions = await RedisService.getSetMembers("pending:transactions");

    const transactions = Array.from(pendingTransactions).map(JSON.parse);
    return ResponseService.success(res, "Pending transactions", {
      transactions,
    });
  },

  async deletePendingTransactions(req, res) {
    await RedisService.delete("pending:transactions");

    return ResponseService.success(res, "Pending transactions deleted");
  },

  async listBanksFromMock(req, res) {
    const { search } = req.query;
    const { error } = UtilsValidator.searchBanks.validate({ search });
    if (error) throw new ValidationError(error.message);

    let banks = await BankService.listBanks({ filter: req.query });
    banks = Sanitizer.sanitizeBanks(banks);
    return ResponseService.success(res, "Banks fetched successfully", banks);
  },

  async clearCache(req, res) {
    const { key } = req.body;
    ResponseService.success(res, "Ok");

    if (!key || typeof key !== "string") return;

    await RedisService.delete(key);
  },

  async directDebitBanks(req, res) {
    const banks = mockBanks.Banks.filter((bank) => !!bank.supportsDirectDebit).map((bank) => ({
      ...bank,
      logo: `https://cdn.jsdelivr.net/gh/wovenfinance/cdn@main/logos/${bank.nipCode}.png`,
    }));

    return ResponseService.success(res, "Direct banks fetched successfully", banks);
  },

  async companyLookup(req, res) {
    const { error } = UtilsValidator.companyLookup.validate(req.query);
    if (error) throw new ValidationError(error.message);
    const { search } = req.query;
    const { data = [] } = await UtilsService.getCompanyLookup(search);
    const sanitizedList = Array.from(data).map(Sanitizer.sanitizeCompanyLookup);
    return ResponseService.success(res, "Company lookup successful", sanitizedList);
  },

  async getLookedUpCompany(req, res) {
    const { error } = UtilsValidator.getLookedUpCompany.validate({ company: req.params.code });
    if (error) throw new ValidationError(error.message);
    const { data } = await UtilsService.getLookedUpCompany({ code: req.params.code });
    return ResponseService.success(res, "Company retrieved successfully", Sanitizer.sanitizeCompanyLookup(data));
  },

  async proxy(req, res) {
    const { method, url, params, payload, headers } = req.body;
    try {
      const response = await axios[method](url, {
        headers,
        params,
        data: payload,
      });

      const language = payload.location === "eu" ? "fr" : "en";

      const { message, data } = response;
      return ResponseService.success(res, message || "Response from remote", formatEmployeeData(data, payload.location, language));
    } catch (error) {
      return ResponseService.failure(res, error.message || "Error occurred", { error });
    }
  },
};
