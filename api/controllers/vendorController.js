const { Op } = require("sequelize");
const VendorService = require("../services/vendorService");
const ResponseService = require("../services/response");
const IndustryService = require("../services/industryService");
const SanitizeService = require("../utils/sanitizer");
const AddressService = require("../services/addressService");
const Sanitizer = require("../utils/sanitizer");
const { ValidationError } = require("../utils/error.utils");
const Notification = require("../services/notification");
const { Redis, Utils } = require("../services");
const { generateJwtToken } = require("../services/authentication");
const { getDashboardURL, generateUniqueId } = require("../utils");
const { STATUSES } = require("../models/status");
const HelperService = require("../services/helper.service");
const { VENDORS } = require("../mocks/constants.mock");
const { getSaasFaviconUrl } = require("../utils/saas.utils");

const Controller = {
  async create(req, res) {
    const { value = {} } = VendorService.validateVendorCreationPayload(req.body);
    const { address, categories, taxIdentificationNumber: tin, industry: industryCode, isAnUpdate = false } = req.body;

    const payload = {
      ...value,
      company: req.company.id,
      tin,
    };
    if (industryCode) {
      const foundIndustry = await IndustryService.getIndustry({
        code: industryCode,
      });

      if (!foundIndustry) throw new ValidationError("Invalid industry code");

      payload.industry = foundIndustry.id;
    }
    if (address) {
      const { id: addressId } = await AddressService.createAddress(address);
      payload.address = addressId;
    }
    const categoriesIds = await VendorService.getVendorCategoriesIds(categories);
    const vendor = await VendorService.createVendor(payload);
    if (categoriesIds.length) await VendorService.createVendorCategories(vendor, categoriesIds);
    let message = `Vendor ${!isAnUpdate ? "created" : "updated"} successfully`;
    if (req.body.status) {
      message = "Vendor successfully invited";
      await VendorService.inviteVendor(vendor, req.company);
    }
    ResponseService.success(res, message);
    if (!payload.website) return;
    const logoURL = await getSaasFaviconUrl(payload.website);
    if (!logoURL) return;
    VendorService.updateVendor(
      {
        code: vendor.code,
      },
      {
        logo: logoURL,
      }
    );
  },

  async list(req, res) {
    const criteria = {
      company: req.company.id,
      ...req.query,
    };
    const { meta, vendors } = await VendorService.listVendors(criteria);
    return ResponseService.success(res, "Vendor retrieved successfully", {
      meta,
      vendors: SanitizeService.sanitizeVendors(vendors),
    });
  },
  async get(req, res) {
    const criteria = {
      company: { [Op.or]: [null, req.company.id] },
      code: req.params.code,
    };
    const vendor = await VendorService.getVendor(criteria, { includeBankAccount: true });
    return ResponseService.success(res, "Vendor retrieved successfully", {
      vendor: SanitizeService.sanitizeVendor(vendor),
    });
  },

  async verify(req, res) {
    const criteria = {
      hash: req.query.hash,
    };
    try {
      const vendor = await VendorService.getVendor(criteria);
      const otp = generateUniqueId(Utils.alphabet.digits, 6);
      await Redis.set(`vendor:otp:${otp}`, vendor.hash);
      Notification.notifyUser(
        { email: vendor.email },
        "otp-verification-login",
        { firstName: vendor.name || "there", code: otp.split("") },
        {
          from: "Bujeti Security Team",
          subject: `Your OTP is ${otp}`,
        }
      );
    } catch (errorToDismiss) {
      return ResponseService.failure(res, "Invalid link");
    }
    return ResponseService.success(res, "OTP sent to your email if it exists");
  },

  async verifyOTP(req, res) {
    const hash = await Redis.get(`vendor:otp:${req.body.otp}`);

    if (!hash) return ResponseService.failure(res, "Invalid code");
    if (req.body.hash !== hash) return ResponseService.failure(res, "Invalid link");

    const criteria = {
      hash,
    };
    const vendor = await VendorService.getVendor(criteria);
    Redis.delete(`vendor:otp:${req.body.otp}`);
    return ResponseService.success(res, "Welcome to Bujeti", {
      vendor: SanitizeService.sanitizeVendor(vendor),
      authentication: generateJwtToken({ code: vendor.code, company: vendor.company, id: vendor.id }),
    });
  },

  async requestInfo(req, res) {
    const { fields } = req.body;
    const { code } = req.params;
    const vendor = await VendorService.getVendor({ code });

    if (!vendor.hash) {
      const hash = Utils.generateUniqueId(Utils.alphabet.alphanumeric, 32);
      await VendorService.updateVendor({ code }, { hash });
      vendor.hash = hash;
    }

    Notification.notifyUser(
      { email: vendor.email },
      "vendor-request",
      {
        fields,
        name: vendor.name,
        companyName: req.company.name,
        url: `${getDashboardURL().replace("dashboard", "vendor")}/${vendor.hash}`,
      },
      {
        subject: `[Request] ${req.company.name} needs additional informations from you`,
      }
    );
    return ResponseService.success(res, `Request successfully sent to ${vendor.name}`);
  },
  async update(req, res) {
    VendorService.validateVendorUpdatePayload(req.body);
    const { code } = req.params;
    const { categories: categoriesCodes, phoneNumber, address, ...payload } = req.body;
    const criteria = {
      code,
    };
    const vendor = await VendorService.getVendor(criteria);
    if (vendor.company && vendor.company !== req.company.id) throw new ValidationError("Vendor cannot be edited");
    if (!vendor.company) {
      req.body = {
        ...vendor.toJSON(),
        ...req.body,
        id: undefined,
        code: undefined,
        created_at: undefined,
        updated_at: undefined,
        isAnUpdate: true,
      };
      return Controller.create(req, res);
    }
    if (categoriesCodes && categoriesCodes.length > 0) {
      const categoriesIds = await VendorService.getVendorCategoriesIds(categoriesCodes);
      if (categoriesIds.length) await VendorService.updateVendorCategories(vendor, categoriesIds);
    }

    if (address) {
      const { id: addressId } = await AddressService.findOrCreate(address);
      payload.address = addressId;
    }

    if (phoneNumber) {
      const { id: phoneNumberId } = await HelperService.findOrCreatePhoneNumber(phoneNumber);
      payload.phoneNumber = phoneNumberId;
    }

    payload.isVendorPortalRequest = !!req.vendor;

    const data = await VendorService.updateVendor(criteria, payload);
    ResponseService.success(res, "Vendor updated successfully", data);

    if (!payload.website) return null;
    const logoURL = await getSaasFaviconUrl(payload.website);
    if (!logoURL) return null;
    return VendorService.updateVendor(
      {
        code,
      },
      {
        logo: logoURL,
      }
    );
  },
  async disable(req, res) {
    const { code } = req.params;
    const criteria = {
      company: req.company.id,
      code,
    };
    await VendorService.disableVendor(criteria);
    return ResponseService.success(res, "Vendor disabled successfully");
  },

  async activate(req, res) {
    const { code } = req.params;
    const criteria = {
      company: req.company.id,
      code,
      status: [STATUSES.BLOCKED, STATUSES.DELETED],
    };
    await VendorService.activateVendor(criteria);
    return ResponseService.success(res, "Vendor activated successfully");
  },

  async vendorTransactionAnalytics(req, res) {
    const criteria = {
      company: req.company.id,
      from: req.query.from,
      to: req.query.to,
    };
    const { message, data } = await VendorService.vendorTransactionAnalytics(criteria);
    const { vendorTransactionAnalytics, highestTransactionVolume, highestTransactionCount, ...response } = data;
    return ResponseService.success(res, message, {
      ...response,
      highestTransactionCount: Sanitizer.sanitizeVendorTransactionForAnalytics(highestTransactionCount),
      highestTransactionVolume: Sanitizer.sanitizeVendorTransactionForAnalytics(highestTransactionVolume),
      analytics: Sanitizer.sanitizeVendorTransactionsForAnalytics(vendorTransactionAnalytics),
    });
  },
  async bulkCreate(req, res) {
    // { name, email, taxIdentificationNumber, registrationNumber, phoneNumber, bankName, accountNumber }
    VendorService.validateVendorsBulkCreationPayload(req.body.vendors);
    const newVendors = await VendorService.createVendorsBulk(req.company.id, req.body.vendors);
    // create vendors
    return ResponseService.success(res, `${newVendors.length} Vendors successfully created`);
  },
  async exportVendors(req, res) {
    const criteria = {
      company: req.company.id,
      ...req.query,
    };
    const { vendors } = await VendorService.listVendors(criteria);
    const vendorsToExport = SanitizeService.sanitizeVendors(vendors);

    return ResponseService.success(res, "Export ready", { vendors: vendorsToExport });
  },
  async resendInvite(req, res) {
    const criteria = {
      company: req.company.id,
      ...(!req.params.code && req.body.email && { email: req.body.email }),
      ...(req.params.code && { code: req.params.code }),
    };
    const foundVendor = await VendorService.getVendor(criteria);

    const message = "Vendor successfully invited";
    if (!foundVendor.hash) {
      foundVendor.hash = Utils.generateUniqueId(Utils.alphabet.alphanumeric, 32);
    }
    if (!foundVendor.email) {
      foundVendor.email = req.body.email;
      await foundVendor.save();
    }
    await VendorService.inviteVendor(foundVendor, req.company);
    return ResponseService.success(res, message);
  },

  async massUpdate(req, res) {
    const { action } = req.body;
    const vendorActions = {
      [VENDORS.ACTIONS.DELETE]: "deleted",
      [VENDORS.ACTIONS.BLOCK]: "blocked",
    };
    VendorService.validateVendorMassUpdatePayload(req.body);

    await VendorService.massUpdateVendors({ ...req.body, company: req.company });

    return ResponseService.success(res, `Vendors ${vendorActions[action]} successfully`);
  },

  async syncZoho(req, res) {
    const { codes = [] } = req.body;

    if (codes.length > 0) {
      const { error } = VendorService.validateSyncZoho({ codes });
      if (error) throw new ValidationError(error.message);

      await VendorService.syncZoho({ codes, company: req.company.id });
      ResponseService.success(res, "Sync in progress");
    }
    ResponseService.success(res, "Please selecte one or multiple vendors to sync");
  },
};

module.exports = Controller;
