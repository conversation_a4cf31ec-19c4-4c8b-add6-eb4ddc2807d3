const { STATUSES } = require("../models/status");
const { VirtualCardRepo } = require("../repositories/index.repo");
const virtualCardService = require("../services/virtualcards.service");
const ResponseService = require("../services/response");
const NotificationService = require("../services/notification");
const { sanitizeLightCreditCard, sanitizeCreditCardsLight } = require("../utils/sanitizer");
const { paginate } = require("./utils");
const { getDashboardURL, parseJSON } = require("../utils");
const { ValidationError, NotFoundError } = require("../utils/error.utils");
const VirtualCardValidator = require("../validators/virtual-card.validator");
const SettingsService = require("../services/settings");

const createCardController = async (req, res) => {
  const { error } = VirtualCardValidator.dashboard.create.validate(req.body);
  if (error) throw new ValidationError(error.message);

  const {
    user: { id: user },
    company: { id: company, paymentPlan },
  } = req;

  try {
    const { message } = await virtualCardService.createCard({
      ...req.body,
      company,
      user,
      issuer: req.cardIssuer.toLowerCase(),
      paymentPlan: parseJSON((paymentPlan && paymentPlan.configuration) || SettingsService.get("pricing_config").models.start),
    });
    ResponseService.success(res, message);
  } catch (cardCreationError) {
    const notificationPayload = {
      company,
      user_id: req.user.id,
      type: `alert`,
      badge: `card`,
      title: `Your ${req.body.currency}${req.body.amount && (req.body.amount / 100).toLocaleString()} ${req.body.type} card couldn't be created`,
      message: `Reason: ${cardCreationError.message}`,
      table: { entity: "", code: null },
      event: "cardRequest",
      body: null,
      reference_code: null,
    };
    NotificationService.saveNotification(notificationPayload);
    NotificationService.notifyUser({ email: req.user.email }, "card-creation-failed", {
      ...req.body,
      reason: cardCreationError.message,
      firstName: req.user.firstName,
      linkToDashboard: `${getDashboardURL()}/cards`,
      dashboardUrl: `${getDashboardURL()}/cards`,
    });
    virtualCardService.markProcessingCardAsFailed({
      ...req.body,
      user,
      company,
      reason: cardCreationError.message,
    });
    ResponseService.failure(res, cardCreationError.message);
  }
};

const updateCardController = async (req, res) => {
  const { error } = VirtualCardValidator.dashboard.update.validate(req.body);
  if (error) throw new ValidationError(error.message);
  const { code: cardCode } = req.params;
  const {
    user: { id: user },
    company: { id: company },
  } = req;
  try {
    const { message } = await virtualCardService.updateCard({
      filter: { code: cardCode, company },
      payload: { ...req.body },
    });
    ResponseService.success(res, message);
  } catch (cardUpdateError) {
    const notificationPayload = {
      company,
      user_id: req.user.id,
      type: `alert`,
      badge: `card`,
      title: `Your ${req.body.currency}${req.body.amount && (req.body.amount / 100).toLocaleString()} ${req.body.type} card couldn't be updated`,
      message: `Reason: ${cardUpdateError.message}`,
      table: { entity: "", code: null },
      event: "cardRequest",
      body: null,
      reference_code: null,
    };

    NotificationService.saveNotification(notificationPayload);

    // NotificationService.notifyUser(
    //   { email: req.user.email },
    //   "card-update-failed",
    //   {
    //     ...req.body,
    //     reason: cardUpdateError.message,
    //     firstName: req.user.firstName,
    //     linkToDashboard: `${getDashboardURL()}/cards`,
    //   }
    // );
    ResponseService.failure(res, cardUpdateError.message);
  }
};

const fundCardController = async (req, res) => {
  const { code: cardCode } = req.params;
  const { id: user } = req.user;
  const { id: company, paymentPlan } = req.company;
  const { amount, budget, balance } = req.body;

  await virtualCardService.fundCard({
    cardCode,
    user,
    company,
    amount,
    budget,
    balance,
    paymentPlan: parseJSON((paymentPlan && paymentPlan.configuration) || SettingsService.get("pricing_config").models.start),
  });
  ResponseService.success(res, `Processing Card Funding`);
};

const getAllCardsController = async (req, res) => {
  const paginateOptions = paginate(req);
  const {
    company: { code: company },
    user: { code: user },
  } = req;
  const cards = await virtualCardService.getAllCards({
    query: { ...req.query, company, user },
    paginateOptions,
  });

  return ResponseService.success(res, "Cards Fetched", {
    cards: sanitizeCreditCardsLight(cards.rows),
    meta: {
      total: cards.count,
      hasMore: (req.page || 1) < cards.number_of_pages,
      page: req.page || 1,
      perPage: req.perPage || 20,
      nextPage: cards.count > (req.page || 1) * (req.perPage || 20) ? (req.page || 1) + 1 : 1,
    },
  });
};

const liquidateCardController = async (req, res) => {
  const { code } = req.params;
  const {
    user: { id: user },
    company: { id: company },
  } = req;

  const criteria = { code, company };
  if (req.isEmployeeRole) criteria.user = user;
  const { message, error } = await virtualCardService.liquidateCard({ user, criteria, payload: { ...req.body } });

  ResponseService[error ? "failure" : "success"](res, message || error);
};

const blockOrUnblockCardController = async (req, res) => {
  const { type } = req.query;
  if (!["block", "unblock"].includes(type)) {
    throw new ValidationError("Invalid type sent");
  }
  const { code } = req.params;
  const { id: user, role } = req.user;
  const cardConditions = { code };
  if (role !== "admin") {
    cardConditions.user = user;
  }
  const card = await VirtualCardRepo.find(cardConditions);
  if (!card) throw new NotFoundError("Card");
  if (type === "block" && card.status === STATUSES.BLOCKED) {
    throw new ValidationError("Card does not exist or is already blocked");
  }
  if (type === "unblock" && card.status !== STATUSES.BLOCKED) {
    throw new ValidationError("Card does not exist or is already unblocked");
  }
  ResponseService.success(res, `Processing ${type}ing of Card`);
  const updateStatus = await virtualCardService.updateStatusOfCard({
    type,
    code,
    user,
    card,
  });
};

const terminateCardController = async (req, res) => {
  const {
    params: { code },
    user: { id: user },
    company: { id: company },
    isAdmin = false,
  } = req;

  const foundCard = await VirtualCardRepo.find({
    code,
    company,
    ...(!isAdmin && { user }),
  });

  if (!foundCard) throw new NotFoundError("Card");

  if (foundCard.status === STATUSES.DELETED) {
    throw new ValidationError("Card does not exist or is already terminated");
  }
  ResponseService.success(res, `Processing Card Termination`);
  await virtualCardService.terminateCard({
    initiator: user,
    filter: { code },
  });
};

const getOneCardController = async (req, res) => {
  const { code: cardCode } = req.params;
  const { id: user } = req.user;
  const foundCard = await virtualCardService.getOneCard({
    cardCode,
    user,
    isAdmin: req.isAdmin,
  });
  if (foundCard.status === STATUSES.PROCESSING) return ResponseService.failure(res, "We're still processing your card creation request");
  if (foundCard.status === STATUSES.FAILED && foundCard.failureReason) return ResponseService.failure(res, foundCard.failureReason);
  if (foundCard && !foundCard.externalIdentifier) {
    return ResponseService.failure(res, "We cannot retrieve this card details at the moment.");
  }
  ResponseService.success(res, `Card successfully retrieved`, {
    card: sanitizeLightCreditCard(foundCard),
  });
};

const listCards = async (req, res) => {
  const {
    company: { id: company },
    user: { id: user },
  } = req;

  const criteria = { ...req.query, company };

  if (req.isEmployeeRole) criteria.user = user;

  const { cards, meta } = await virtualCardService.listCards(criteria);

  ResponseService.success(res, "Cards fetched successfully", {
    cards: sanitizeCreditCardsLight(cards),
    meta,
  });
};

const registerCardHolder = async (req, res) => {
  const { redisKey } = req.params;
  const { bvn, dob, phoneNumber } = req.body;
  const cardHolder = await virtualCardService.createCardHolder({
    bvn,
    redisKey,
    dob,
    phoneNumber,
  });
  ResponseService.success(res, cardHolder.message, {
    responseCode: cardHolder.responseCode,
  });
};

const verifyCardHolderOtp = async (req, res) => {
  const { bvn, otp } = req.body;
  const verification = await virtualCardService.otpVerification(bvn, otp);
  ResponseService.success(res, `Card creation processing.`);
};

const deactivateCardHolder = async (req, res) => {
  const { externalIdentifier } = req.params;
  await virtualCardService.deactivateCardHolder(externalIdentifier);
  ResponseService.success(res, `Card deactivated successfully`);
};

const getAllCardHolders = async (req, res) => {
  const cardholders = await virtualCardService.getAllCardHolders();
  ResponseService.success(res, `Card holders gotten successfully.`, {
    cardholders,
  });
};

const resendOtp = async (req, res) => {
  const { redisKey } = req.params;
  await virtualCardService.resendOtp({ redisKey });
  ResponseService.success(res, `Otp resent please check your email.`);
};

const simulateCardAuthorization = async (req, res) => {
  const { error } = VirtualCardValidator.simulateCharge.validate(req.body);
  if (error) throw new ValidationError(error.message);
  await virtualCardService.simulateChargeCard(req.body);
  ResponseService.success(res, `Charge simulated successfully.`);
};

const creditCard = async (req, res) => {
  ResponseService.success(res, "OK");
  await virtualCardService.topUpCard(req.body);
};

const completeCardCreation = async (req, res) => {
  ResponseService.success(res, "Ok");
  await virtualCardService.completeCardCreation(req.body);
};

const changeCardPin = async (req, res) => {
  const { oldPin, newPin } = req.body;
  if (!(oldPin && newPin)) throw new ValidationError("Enter your old and new pin");
  const { code } = req.params;
  const foundCard = await virtualCardService.getOneCard({ cardCode: code, user: req.user.id });
  if (!foundCard) throw new NotFoundError("Card");
  await virtualCardService.changeCardPin({
    id: foundCard.externalIdentifier,
    oldPin,
    newPin,
    issuer: foundCard.issuer,
    company: foundCard.company,
  });
  await VirtualCardRepo.update({ conditions: { id: foundCard.id }, changes: { requiresPinChange: false, status: STATUSES.ACTIVE } });
  return ResponseService.success(res, "Card's pin successfully updated");
};

const processCardFunding = async (req, res) => {
  ResponseService.success(res, "Ok");
  await virtualCardService.processCardProviderFunding(req.body);
};

const getMccs = async (req, res) => {
  const result = await virtualCardService.getMccs(req.query);
  ResponseService.success(res, `Mccs fetched successfully`, result);
};

const terminateCardRetrial = async (req, res) => {
  const {
    params: { code },
  } = req;

  const foundCard = await VirtualCardRepo.getCard({
    filter: { code },
  });

  if (!foundCard || foundCard?.status === STATUSES.DELETED) return ResponseService.success(res, "Ok");

  const { initiator, trial } = req.body;

  if (trial + 1 >= 4) return ResponseService.success(res, "Ok");

  ResponseService.success(res, `Processing Card Termination`);

  await virtualCardService.terminateCard({
    initiator,
    filter: { code },
    extras: { trial },
  });
};

const reassignCard = async (req, res) => {
  const {
    params: { code },
    company: { id: company },
    user: { id: user },
  } = req;
  const { error } = VirtualCardValidator.dashboard.cardReassignment.validate(req.body);
  if (error) throw new ValidationError(error.message);

  const { message } = await virtualCardService.reassignCard({ filter: { code, company }, payload: { ...req.body, assignedBy: user } });
  ResponseService.success(res, message);
};

const initiateLiquidation = async (req, res) => {
  ResponseService.success(res, "Ok");
  await virtualCardService.initiateLiquidation({ payload: { ...req.body } });
};
const confirmLiquidationTransaction = async (req, res) => {
  ResponseService.success(res, "Ok");
  await virtualCardService.getAccountTransaction({ payload: { ...req.body } });
};

const finalizeLiquidation = async (req, res) => {
  ResponseService.success(res, "Ok");
  await virtualCardService.finalizeLiquidation({ payload: { ...req.body } });
};

const sudoAuthorizePaymentCheck = async (req, res) => {
  const { success } = await virtualCardService.sudoAuthorizePaymentCheck({ payload: req.body });

  res.status(success ? 200 : 400);
  res.json({
    statusCode: success ? 200 : 400,
    responseCode: success ? "00" : "01",
    data: {},
  });
};

module.exports = {
  createCardController,
  updateCardController,
  fundCardController,
  getAllCardsController,
  liquidateCardController,
  blockOrUnblockCardController,
  terminateCardController,
  getOneCardController,
  registerCardHolder,
  verifyCardHolderOtp,
  deactivateCardHolder,
  getAllCardHolders,
  listCards,
  resendOtp,
  simulateCardAuthorization,
  creditCard,
  changeCardPin,
  processCardFunding,
  completeCardCreation,
  getMccs,
  terminateCardRetrial,
  reassignCard,
  initiateLiquidation,
  confirmLiquidationTransaction,
  finalizeLiquidation,
  sudoAuthorizePaymentCheck,
};
