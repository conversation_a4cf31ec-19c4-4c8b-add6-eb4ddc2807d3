const ResponseService = require("../services/response");
const MonoService = require("../services/mono");
const AnchorService = require("../services/anchor");
const { smileIdWebhook } = require("../webhooks");
const { <PERSON>usherIntegrator } = require("../integrations");
const FlutterwaveService = require("../services/flutterwave");
const BridgeCardService = require("../services/bridgecard.service");
const SudoService = require("../services/sudo.service");
const QueueService = require("../services/queue.service");
const Utils = require("../utils/utils");
const ThirdPartyLogService = require("../services/thirdPartyLog");
const PolicyService = require("../services/policy.service");
const PaystackService = require("../services/paystack.service");
const GraphService = require("../services/graph.service");
const BankService = require("../services/bank");
const Providers = require("../services/providers");
const { ANCHOR_WEBHOOK_EVENTS } = require("../mocks/constants.mock");

module.exports = {
  async flutterwave(req, res) {
    ResponseService.success(res, "OK");
    FlutterwaveService.webhook(req.body);
  },
  async stripe(req, res) {
    ResponseService.success(res, "OK");
  },
  async union54(req, res) {
    ResponseService.success(res, "OK");
  },

  async mono(req, res) {
    ResponseService.success(res, "OK");
    MonoService.handleWebhook(req, { BankService });
  },

  async smileId(req, res) {
    // TODO security check secret key and all
    ResponseService.success(res, "OK");
    smileIdWebhook.basicKycResponse(req.body);
  },

  async pusher(req, res) {
    // TODO security check secret key and all
    ResponseService.success(res, "OK");
    const { channel, event, data } = req.body;
    PusherIntegrator.channel_trigger(channel, event, data);
  },

  async beam(req, res) {
    // TODO security check secret key and all
    ResponseService.success(res, "OK");
    const { interests, data } = req.body;
    PusherIntegrator.beam(interests, data);
  },

  async anchor(req, res) {
    if (!req.body.data) req.body = { data: req.body };
    // const processedEvent = await ThirdPartyLogService.eventProcessed(req.body.data.id);
    // if (processedEvent) return ResponseService.success(res, "OK");

    const event = req.body.data.type;

    const payload = {
      ...req.body,
      id: req.body.data.id || Utils.generateRandomString(22),
      path: "/webhook/anchor/processor",
      key: process.env.INTRA_SERVICE_TOKEN,
    };

    if (event === ANCHOR_WEBHOOK_EVENTS.NIP_TRANSFER_INITIATED) {
      // we use the balance or budget as the message group id to ensure a FIFO on same account. We essentially create a partition.
      const sourceOfFunds = payload.attributes?.metadata?.budget || payload.attributes?.metadata?.balance;
      const messageGroupId = `Anchor:${ANCHOR_WEBHOOK_EVENTS.NIP_TRANSFER_INITIATED}:${sourceOfFunds}`;

      await QueueService.addJob(req, payload, messageGroupId);
    } else {
      const delayedTime = [
        ANCHOR_WEBHOOK_EVENTS.NIP_TRANSFER_FAILED,
        ANCHOR_WEBHOOK_EVENTS.NIP_TRANSFER_SUCCESSFUL,
        ANCHOR_WEBHOOK_EVENTS.BOOK_TRANSFER_FAILED,
        ANCHOR_WEBHOOK_EVENTS.NIP_TRANSFER_SUCCESSFUL,
      ].includes(req.body.data.type)
        ? 5
        : 2;

      await QueueService.addDelayedJob(req, payload, `Anchor:${payload.data.id}`, delayedTime);
    }

    ResponseService.success(res, "OK");
  },

  async anchorProcessor(req, res) {
    req.body = Utils.parseJSON(req.headers["x-aws-sqsd-attr-payload"] || req.body) || req.body;
    await AnchorService.handleWebhook(req);
    ResponseService.success(res, "OK");
  },

  async bridgecard(req, res) {
    ResponseService.success(res, "OK");
    // sleep 2s and add to the queue
    await Utils.sleep(10000);
    const payload = {
      ...req.body,
      id: `bridge:${Utils.generateRandomString(26)}`,
      path: "/webhook/bridgecard/processor",
      key: process.env.INTRA_SERVICE_TOKEN,
    };
    QueueService.addJob(req, payload, `BridgeCard:${payload.data.id}`);
  },

  async bridgecardProcessor(req, res) {
    await BridgeCardService.webhook(req.body);
    ResponseService.success(res, "OK");
  },

  async sudo(req, res) {
    const payload = {
      ...req.body,
      // eslint-disable-next-line no-underscore-dangle
      id: req.body._id || Utils.generateRandomString(22),
      path: "/webhook/sudo/processor",
      key: process.env.INTRA_SERVICE_TOKEN,
    };

    await QueueService.addDelayedJob(req, payload, `Sudo:${payload.id}`, 5);
    ResponseService.success(res, "OK");
  },

  async sudoProcessor(req, res) {
    req.body = Utils.parseJSON(req.headers["x-aws-sqsd-attr-payload"] || req.body);
    await SudoService.webhook(req.body, { PolicyService });
    ResponseService.success(res, "OK");
  },

  async paystack(req, res) {
    const payload = {
      ...req.body,
      id: req.body.data.id || Utils.generateRandomString(22),
      path: "/webhook/paystack/processor",
      key: process.env.INTRA_SERVICE_TOKEN,
    };

    await QueueService.addDelayedJob(req, payload, `Paystack:${payload.data.id}`, 5);
    ResponseService.success(res, "OK");
  },

  async paystackProcessor(req, res) {
    req.body = Utils.parseJSON(req.headers["x-aws-sqsd-attr-payload"] || req.body);
    await PaystackService.handleWebhook(req, Providers);
    ResponseService.success(res, "OK");
  },

  async graph(req, res) {
    const payload = {
      ...req.body,
      id: req.body.data.id || Utils.generateRandomString(22),
      path: "/webhook/graph/processor",
      key: process.env.INTRA_SERVICE_TOKEN,
    };

    await QueueService.addDelayedJob(req, payload, `Graph:${payload.data.id}`, 5);
    ResponseService.success(res, "OK");
  },

  async graphProcessor(req, res) {
    req.body = Utils.parseJSON(req.headers["x-aws-sqsd-attr-payload"] || req.body);
    await GraphService.handleWebhook(req, Providers);
    ResponseService.success(res, "OK");
  },

  async choiceBank(req, res) {
    ResponseService.success(res, "OK");
  },
};
