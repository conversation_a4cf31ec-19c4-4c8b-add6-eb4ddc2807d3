/* eslint-disable consistent-return */
/* eslint-disable no-useless-catch */
/* eslint-disable no-restricted-syntax */
/* eslint-disable no-unused-expressions */
const axios = require("axios");
require("dotenv").config();
const ValidationError = require("../utils/validation-error");
const NotFoundError = require("../utils/not-found-error");
const Utils = require("../utils/utils");

const Service = {
  weekDay: {
    monday: 1,
    tuesday: 2,
    wednesday: 3,
    thursday: 4,
    friday: 5,
    saturday: 6,
    sunday: 7,
  },

  month: {
    january: 1,
    february: 2,
    march: 3,
    april: 4,
    may: 5,
    june: 6,
    july: 7,
    august: 8,
    september: 9,
    october: 10,
    november: 11,
    december: 12,
  },

  METHODS: {
    GET: 0,
    POST: 1,
    PUT: 4,
    DELETE: 5,
    PATCH: 8,
  },

  cronConfig: {
    enabled: true,
    saveResponses: false,
    requestTimeout: 30,
    redirectSuccess: false,
    auth: {
      enable: false,
      user: "",
      password: "",
    },
    notification: {
      onSuccess: true,
      onDisable: true,
      onFailure: true,
    },
    requestMethod: 0,
    extendedData: {
      body: "",
      headers: {},
    },
  },

  axiosInstance: axios.create({
    baseURL: `${process.env.CRONJOB_URI}`,
    headers: {
      Authorization: `Bearer ${process.env.CRONJOB_KEY}`,
    },
  }),

  async createAJob(data) {
    const { body, method = 0, title, url, schedule, generate = true } = data;

    const generateSchedule = generate ? await Service.generateSchedule(schedule) : schedule;
    try {
      const result = await Service.axiosInstance.put("/jobs", {
        job: {
          title,
          url: `${Utils.getApiURL()}/jobs/${url}`,
          ...Service.cronConfig,
          schedule: generateSchedule,
          requestMethod: method,
          enabled: generateSchedule.enabled,
          extendedData: {
            headers: {
              Authorization: `Bearer ${process.env.INTRA_SERVICE_TOKEN}`,
            },
            body: body && `data=${JSON.stringify(body)}`,
          },
        },
      });
      return result;
    } catch (error) {
      console.log({ "error.message": error.message });
    }
  },

  async generateSchedule(schedule) {
    const { day, weekDay, month, hour, minute, expiresAt = 0 } = schedule;
    const generateSchedule = {
      mdays: [],
      wdays: [],
      months: [],
      hours: [],
      minutes: [],
      timezone: "Africa/Lagos",
      expiresAt,
    };

    day === "every" ? (generateSchedule.mdays = [-1]) : generateSchedule.mdays.concat(day);
    weekDay === "every" ? (generateSchedule.wdays = [-1]) : generateSchedule.wdays.concat(Service.getSequence("weekDay", weekDay, "asc"));
    month === "every" ? (generateSchedule.months = [-1]) : generateSchedule.months.concat(Service.getSequence("month", month, "asc"));
    hour === "every" ? (generateSchedule.hours = [-1]) : generateSchedule.hours.concat(hour);

    if (minute.every) {
      const everyMinuteDuration = [];
      if (60 % minute.duration === 0) {
        let num = 0;

        while (num <= 60 - minute.duration) {
          everyMinuteDuration.push(num);
          num += minute.duration;
        }
        generateSchedule.minutes = everyMinuteDuration;
      } else throw new ValidationError(`every ${minute.duration} is not possible`);
    } else generateSchedule.minutes = minute.duration;

    return generateSchedule;
  },

  async listJobs() {
    try {
      return Service.axiosInstance.get("/jobs");
    } catch (error) {
      throw error;
    }
  },

  async fetchJob(id) {
    try {
      return Service.axiosInstance.get(`/jobs/${id}`);
    } catch (error) {
      if (error.response.status === 404) throw new NotFoundError("Cron job");
      throw error.message;
    }
  },

  async deleteJob(id) {
    try {
      return Service.axiosInstance.delete(`/jobs/${id}`);
    } catch (error) {
      throw error;
    }
  },

  async updateJob(id, data) {
    try {
      return Service.axiosInstance.patch(`/jobs/${id}`, data);
    } catch (error) {
      throw error;
    }
  },

  async fetchJobHistory(id) {
    try {
      return Service.axiosInstance.get(`/jobs/${id}/history`);
    } catch (error) {
      throw error;
    }
  },

  async fetchJobHistoryDetails(id, identifier) {
    try {
      return Service.axiosInstance.get(`/jobs/${id}/history/${identifier}`);
    } catch (error) {
      throw error;
    }
  },

  getSequence(sequence, sequenceNames, sortOrder = "asc") {
    const sequenceNumbers = [];
    for (const sequenceName of sequenceNames) {
      sequenceNumbers.push(Service[sequence][sequenceName]);
    }
    if (sortOrder === "asc") {
      sequenceNumbers.sort((a, b) => a - b);
    } else if (sortOrder === "desc") {
      sequenceNumbers.sort((a, b) => b - a);
    }
    return sequenceNumbers;
  },
};

module.exports = Service;
