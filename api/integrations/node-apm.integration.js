const tracker = require("@middleware.io/node-apm");
const Utils = require("../utils");

class Tracker {
  // private properties
  #isTracking = false;

  #projectName = "";

  #serviceName = "";

  #accessToken = "";

  constructor() {
    this.#projectName = process.env.MIDDLEWARE_PROJECT_NAME;
    this.#serviceName = process.env.MIDDLEWARE_SERVICE_NAME;
    this.#accessToken = process.env.MIDDLEWARE_ACCESS_TOKEN;
    // singleton pattern
    if (!this.#isTracking) {
      this.#track();
      this.#isTracking = true;
    }
  }

  #track() {
    return tracker.track({
      projectName: this.#projectName,
      serviceName: this.#serviceName,
      accessToken: this.#accessToken,
      customResourceAttributes: {
        env: Utils.currentEnvContext(),
      },
    });
  }

  errorRecord(error) {
    return tracker.errorRecord(error);
  }

  error(error) {
    return tracker.error(error);
  }
  getTracer() {
    return tracker.getTracer();
  }

  setAttribute(key, value) {
    tracker.setAttribute(key, value);
    return tracker;
  }
}

module.exports = Tracker;
