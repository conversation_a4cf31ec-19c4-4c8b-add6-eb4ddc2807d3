const Pusher = require("pusher");
const axios = require('axios');
require('dotenv').config();
const SettingsService = require('../services/settings');


const Service = {
    pusher: new Pusher({
        appId: process.env.PUSHER_APPID,
        key: process.env.PUSHER_KEY,
        secret: process.env.PUSHER_SECRET,
        cluster: process.env.PUSHER_CLUSTER,
        useTLS: true
    }),

    axiosInstance: axios.create({
        baseURL: `https://${process.env.PUSHER_INSTANCE}.pushnotifications.pusher.com/publish_api/v1/instances/${process.env.PUSHER_INSTANCE}/publishes`,
        headers: {
          Authorization: `Bearer ${process.env.PUSHER_BEARER}`,
        },
    }),

    channel_trigger(user_id, event, data){
        Service.pusher.trigger(user_id, event, data);
    },

    beam(interests, data){
        Service.axiosInstance.post('', {
            interests: [interests],
            web: {
                notification: data
            }
        });
    }
}

module.exports = Service;