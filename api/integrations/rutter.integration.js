const axios = require("axios");
const { requestHandler } = require("../utils");
const { ValidationError } = require("../utils/error.utils");
require("dotenv").config();

axios.defaults.timeout = 300000;
const httpClient = {};
const Service = {
  httpClient: () => {
    const headers = {
      Authorization: `Basic ${Buffer.from(`${process.env.RUTTER_CLIENT_ID}:${process.env.RUTTER_CLIENT_SECRET}`).toString("base64")}`,
      "Content-Type": "application/json",
    };
    if (!httpClient.client)
      httpClient.client = axios.create({
        headers,
        baseURL: process.env.RUTTER_BASEURL,
      });
    return httpClient.client;
  },
  async createAccessTokenIntegration(token) {
    try {
      const connection = Service.httpClient().post(`/item/public_token/exchange`, {
        client_id: `${process.env.RUTTER_CLIENT_ID}`,
        secret: `${process.env.RUTTER_CLIENT_SECRET}`,
        public_token: token,
      });
      return requestHandler(connection);
    } catch (error) {
      throw new ValidationError(error.response.data.error_message || `Error from provider`);
    }
  },

  async getAllCategory(token) {
    try {
      const categories = Service.httpClient().get(`/accounting/classes?access_token=${token}&limit=500&force_fetch=true`);
      return requestHandler(categories);
    } catch (error) {
      throw new ValidationError(error.response.data.error_message || `Error from provider`);
    }
  },

  async getAllAccounts(token, nextCursor = null) {
    try {
      const accounts = Service.httpClient().get(
        `/accounting/accounts?access_token=${token}&limit=500&force_fetch=true${nextCursor ? `&cursor=${nextCursor}` : ''}`
      );
      return requestHandler(accounts);
    } catch (error) {
      throw new ValidationError(error.response.data.error_message || "Oops, something went wrong.");
    }
  },

  async getAllVendors(token, nextCursor = null) {
    try {
      const vendors = Service.httpClient().get(
        `/accounting/vendors?access_token=${token}&limit=500&force_fetch=true${nextCursor ? `&cursor=${nextCursor}` : ''}`
      );
      return requestHandler(vendors);
    } catch (error) {
      throw new ValidationError(error.response.data.error_message || `Oops, something went wrong`);
    }
  },

  async createAccount(token, payload) {
    try {
      const account = Service.httpClient().post(`/accounting/accounts?access_token=${token}`, payload);
      return requestHandler(account);
    } catch (error) {
      throw new ValidationError(error.response.data.error_message || `Error from provider`);
    }
  },

  async createVendor(token, payload) {
    try {
      const vendor = Service.httpClient().post(`/accounting/vendors?access_token=${token}`, payload);
      return requestHandler(vendor);
    } catch (error) {
      throw new ValidationError(error.response.data.error_message || `Error from provider`);
    }
  },

  async createTransaction(token, payload) {
    try {
      const transaction = Service.httpClient().post(`/accounting/expenses?access_token=${token}`, payload);
      return requestHandler(transaction);
    } catch (error) {
      throw new ValidationError(error.response.data.error_message || `Error from provider`);
    }
  },

  async deleteConnection(token, id) {
    try {
      const quickBookConnection = Service.httpClient().delete(`/connections/${id}?access_token=${token}`);
      return requestHandler(quickBookConnection);
    } catch (error) {
      throw new ValidationError(error.response.data.error_message || "Oops, something went wrong.");
    }
  },
};
module.exports = Service;
