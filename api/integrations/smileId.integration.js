const { UserRepo, CompanyRepo, AddressRepo } = require("../repositories/index.repo");
const responseUtils = require("../utils/response.utils");
const { ValidationError, HttpException, HttpStatus } = require("../utils/error.utils");
const { log, Log } = require("../utils/logger.utils");
const axios = require("axios");
const smileIdentityCore = require("smile-identity-core");
const { SmileIdValidator } = require("../validators");

const WebApi = smileIdentityCore.WebApi;

require("dotenv").config();
let crypto = require("crypto");

const Service = {
  async smmileIdSignature() {
    let timestamp = new Date().toISOString();
    let api_key = process.env.SMILEID_API_KEY;
    let partner_id = process.env.SMILEID_PARTNER_ID;
    let hmac = crypto.createHmac("sha256", api_key);

    hmac.update(timestamp, "utf8");
    hmac.update(partner_id, "utf8");
    hmac.update("sid_request", "utf8");

    let signature = hmac.digest().toString("base64");
    return { signature, timestamp };
  },

  async smmileIdWebApiConnection() {
    const connectionPayload = {
      api_key: process.env.SMILEID_API_KEY,
      partner_id: process.env.SMILEID_PARTNER_ID,
      default_callback: process.env.SMILEID_CALLBACK_URL,
      ...(process.env.SMILEID_ENV === "SANDBOX" && { sid_server: 0 }),
      ...(process.env.SMILEID_ENV === "PRODUCTION" && { sid_server: 1 }),
    };

    const { partner_id, default_callback, api_key, sid_server } = connectionPayload;
    const connection = new WebApi(partner_id, default_callback, api_key, sid_server);
    return connection;
  },

  axiosInstance: axios.create({
    baseURL: process.env.SMILEID_URL,
    // headers: {
    //   Authorization: `Bearer ${FLUTTERWAVE_SECRET_KEY}`,
    // },
  }),

  smileIDEnv: {
    source_sdk: "rest_api",
    partner_id: process.env.SMILEID_PARTNER_ID,
    country: "NG",
    callback_url: process.env.SMILEID_CALLBACK_URL,
  },

  smileIDType: {
    BVN: "BVN",
    NIN: "NIN",
    NIN2: "NIN_SLIP",
    DRIVERS: "DRIVERS_LICENSE",
    PHONE: "PHONE_NUMBER",
    VOTER: "VOTER_ID",
    BANK: "BANK_ACCOUNT",
  },

  smileIDTypeData: {
    BVN: "***********",
    NIN: "***********",
    NIN2: "***********",
    DRIVERS: "ABC********0",
    PHONE: "***********",
    VOTER: "*******************",
    TIN: "********-0000",
    CAC: "0000000",
  },

  smileBusinessIDType: {
    TIN: "TIN",
    CAC: "CAC",
  },

  smileDocumentIDType: {
    PASSPORT: "PASSPORT",
    DRIVERS_LICENSE: "DRIVERS_LICENSE",
    NATIONAL_ID: "NATIONAL_ID",
    VOTER_ID: "VOTER_ID",
  },

  smileIDBanks: {
    "Access Bank": 044,
    "Access Bank (Diamond Bank)": 063,
    Ecobank: 050,
    "Enterprise Bank": 084,
    "Fidelity Bank": 070,
    "First Bank": 011,
    "First City Monument Bank": 214,
    "Guaranty Trust Bank": Number("058"),
    "Heritage Bank": 030,
    "Jaiz Bank": 301,
    "Keystone Bank": 082,
    "Mainstreet Bank": 014,
    "Polaris Bank": 076,
    "Stanbic IBTC": Number("039"),
    "Skye Bank": 076,
    "Sterling Bank": 232,
    "Union Bank": 032,
    UBA: 033,
    "Unity Bank": 215,
    "Wema Bank": 035,
    "Zenith Bank": 057,
  },

  async basicKyc(payload) {
    log(Log.fg.cyan, `smile-Id basicKyc`);
    const { error } = SmileIdValidator.basicKyc.validate(payload);
    if (error) throw new ValidationError(error.message);

    const { first_name, last_name, phone_number, dob, bank_code, id_type, id_number, gender, partner_params } = payload;
    const { table_id, table_type, table_code } = partner_params;
    const { signature, timestamp } = await Service.smmileIdSignature();

    try {
      const payload = {
        ...Service.smileIDEnv,
        signature,
        source_sdk_version: "2.0.0",
        timestamp,
        ...(id_type === Service.smileIDType.DRIVERS && { phone_number, dob }),
        ...(id_type === Service.smileIDType.BANK && { bank_code }),
        ...(gender && { gender }),
        ...(process.env.SMILEID_ENV === "SANDBOX" && {
          id_number: Service.smileIDTypeData[id_type],
        }),
        ...(process.env.SMILEID_ENV === "PRODUCTION" && { id_number }),
        id_type,
        first_name,
        last_name,
        partner_params: { job_id: table_code, user_id: table_id, table_type },
      };
      const { data } = await Service.axiosInstance.post("/v2/verify_async", payload);
      return responseUtils.sendObjectResponse("smileID response", data);
    } catch (error) {
      // throw new HttpException(error.response.data.error, HttpStatus.NOT_ACCEPTABLE);
    }
  },

  async businessKyb(payload) {
    log(Log.fg.cyan, `smile-Id businessKyb`);
    const { error } = SmileIdValidator.businessKyc.validate(payload);
    if (error) throw new ValidationError(error.message);

    const { id_type, id_number, company, partner_params } = payload;
    const { table_id, table_type, table_code } = partner_params;
    const { signature, timestamp } = await Service.smmileIdSignature();
    try {
      const payload = {
        ...Service.smileIDEnv,
        signature,
        source_sdk_version: "1.0.0",
        timestamp,
        smile_client_id: process.env.SMILEID_PARTNER_ID,
        ...(id_type === Service.smileBusinessIDType.CAC && { company }),
        ...(process.env.SMILEID_ENV === "SANDBOX" && {
          id_number: Service.smileIDTypeData[id_type],
        }),
        ...(process.env.SMILEID_ENV === "PRODUCTION" && { id_number }),
        id_type,
        partner_params: {
          job_type: 5,
          job_id: table_code,
          user_id: table_id,
          table_type,
        },
      };
      const data = await Service.axiosInstance.post("/v1/async_id_verification", payload);
      return responseUtils.sendObjectResponse("smileID response", data);
    } catch (error) {
      log(Log.fg.red, error.response.data);
      // throw new HttpException(error.response.data.message, HttpStatus.NOT_ACCEPTABLE);
    }
  },

  async documentVerification(payload) {
    log(Log.fg.cyan, `smile-Id documentVerification`);
    let idTypeChange = {
      ip: "PASSPORT",
      dl: "DRIVERS_LICENSE",
      nin: "NATIONAL_ID",
      vi: "VOTER_ID",
    };

    payload.id_type = idTypeChange[payload.id_type];

    const { error } = SmileIdValidator.documentVerification.validate(payload);
    if (error) throw new ValidationError(error.message);

    const { id_type, is_image_base64 = false, image, image_selfie, partner_params } = payload;
    const { table_id, table_type, table_code } = partner_params;

    try {
      const connection = await Service.smmileIdWebApiConnection();

      const createdPayload = {
        options: {
          return_job_status: true,
          return_history: true,
          return_image_links: false,
          signature: true,
        },
        image_details: [
          {
            image_type_id: 0,
            image: image_selfie || "/Users/<USER>/Desktop/Bujeti/api/public/images/Copenhagen.jpg",
          },
          {
            image_type_id: is_image_base64 ? 3 : 1,
            image: "/Users/<USER>/Desktop/Bujeti/api/public/images/Copenhagen.jpg",
          },
        ],
        id_info: {
          country: "NG",
          id_type,
        },
        partner_params: {
          job_type: 6,
          job_id: table_code,
          user_id: table_id,
          table_type,
        },
      };

      const { partner_params, image_details, id_info, options } = createdPayload;

      response = await connection.submit_job(partner_params, image_details, id_info, options);
      return responseUtils.sendObjectResponse("smileID response", response);
    } catch (error) {
      log(Log.fg.red, error.message);
      // throw new HttpException(error.message, HttpStatus.NOT_ACCEPTABLE);
    }
  },
};

module.exports = Service;
