const AuthenticationError = require("../utils/authentication-error");
const { Authentication: AuthenticationService, User: UserService, Company: CompanyService, Utils, Vendor: VendorService } = require("../services");
const RedisService = require("../services/redis");
const IndividualRepo = require("../repositories/individual.repo");

const NotFoundError = require("../utils/not-found-error");
const { STATUSES } = require("../models/status");
const { ApiKey } = require("../models");
const { parseJSON, md5 } = require("../utils");
const { ForbiddenError } = require("../utils/error.utils");

const UNAUTHENTICATED_ENDPOINTS = [
  "/",
  "/demo/invite",
  "/users/verify-user-invite",
  "/users/resend-verification",
  "/users/register",
  "/users/login",
  "/users/verify-pin",
  "/users/forgot-password",
  "/users/reset-password",
  "/users/get-invited-user",
  "/users/beneficiary-password",
  "/users/verify-otp",
  "/webhook/stripe",
  "/webhook/flw",
  "/webhook/mono",
  "/webhook/union54",
  "/webhook/smile-id",
  "/webhook/pusher",
  "/webhook/beam",
  "/testing/test",
  "/statuses",
  "/demo/request",
  "/companies/review",
  "/webhook/anchor",
  "/webhook/anchor/processor",
  "/invoices/get-invoice-account",
  "/webhook/bridgecard",
  "/webhook/bridgecard/processor",
  "/webhook/sudo",
  "/referrals/rewards/process",
  "/integrations/quickbooks/sync/transactions",
  "/webhook/paystack",
  "/webhook/paystack/processor",
  "/vendors/invite/verify",
  "/vendors/verify-otp",
  "/countries",
  "/companies/verify-invite",
];

const publicPath = [
  {
    path: "/public/invoices/",
    prefix: "inv_",
  },
  {
    path: "/public/directors/",
    prefix: "idv_",
  },
  {
    path: "/assets",
    prefix: "idv_",
  },
  {
    path: "/files/url/upload",
    prefix: "idv_",
  },
];

const isIpBanned = async (req) => {
  const IP = req.headers["x-forwarded-for"] || req.connection.remoteAddress || req.ip || req.headers["cf-connecting-ip"];
  if (IP) {
    const realIP = IP.split(", ");
    return !!(realIP && realIP.length && (await RedisService.get(`auth:${realIP[0]}`)));
  }
  return false;
};
// We can add method here to make it more secure before it goes public public
const PUBLIC_SCOPES = {
  "/v1/transactions": "transactions.read",
  "/v1/transactions/trx_[a-zA-Z](17)": "transactions.read",
};
const isInScope = (path, scope) => {
  const directScope = PUBLIC_SCOPES[path];
  if (directScope) return scope.includes(directScope);
  const keys = Object.keys(PUBLIC_SCOPES);
  return keys.some((key) => {
    const regex = new RegExp(key);
    const foundScope = String(path).match(regex);
    if (foundScope) {
      return scope.includes(foundScope);
    }
    return false;
  });
};

module.exports = async (req, res, next) => {
  if (await isIpBanned(req)) return res.status(301);
  const { authorization, appPlatform } = req.headers;

  req.isMobile = ["ios", "android"].includes(appPlatform || req.headers["app-platform"]);

  if (UNAUTHENTICATED_ENDPOINTS.includes(req.path)) {
    return next();
  }
  if (!authorization) {
    throw new AuthenticationError("Missing authentication header");
  }
  if (!authorization.trim().startsWith("Bearer ")) {
    throw new AuthenticationError("Wrong Authentication header format");
  }
  const submittedKey = authorization.trim().replace("Bearer ", "");
  if (submittedKey === process.env.INTRA_SERVICE_TOKEN) return next();
  if (submittedKey.length === 32) {
    const apiKey = await ApiKey.findOne({
      where: {
        key: submittedKey,
        status: STATUSES.ACTIVE,
      },
    });
    if (!apiKey) return res.status(401);
    const scope = parseJSON(apiKey.scope);
    if (!isInScope(req.path, scope)) throw new AuthenticationError("You are not allowed to access that data");
    req.company = await CompanyService.getCompanyByCriteria({
      criteria: { id: apiKey.company },
    });
    return next();
  }

  const decodedKey = AuthenticationService.decryptJwtToken(submittedKey);

  if (!decodedKey) {
    const foundPath = publicPath.find((singleObject) => String(req.path).includes(singleObject.path));

    if (foundPath) {
      const code = String(submittedKey).replace(String(submittedKey).substring(0, 6), foundPath.prefix);
      // Check if user is valid
      if (code.startsWith("idv_")) {
        const foundIndividual = await IndividualRepo.getOneIndividual({
          queryParams: { code },
          includeCompany: true,
        });
        if (!foundIndividual) throw new NotFoundError("Director");
        req.company = foundIndividual.Company;
      }
      req.code = code;
      return next();
    }

    const redisAuthenticatedPath = ["/assets", "/files/url/upload"];
    const foundRedisPath = redisAuthenticatedPath.find((path) => String(req.path).includes(path));
    if (foundRedisPath) {
      const hash = String(submittedKey).replace(String(submittedKey).substring(0, 7), "");
      const foundHashData = Utils.parseJSON(await RedisService.get(hash));
      if (foundHashData) {
        const { prefix, company } = foundHashData;
        const code = String(hash).padStart(21, prefix);
        req.code = code;
        req.company = await CompanyService.getCompanyByCriteria({
          id: company,
        });
        return next();
      }
    }

    // tries to get data from redis
    const foundData = Utils.parseJSON(await RedisService.get(submittedKey));
    if (!foundData) throw new AuthenticationError("Invalid authentication token");

    req.company = await CompanyService.getCompanyByCriteria({
      criteria: { id: foundData.company },
    });
    return next();
  }

  const { sessionId, exp, code: userCode } = decodedKey;

  if (!sessionId) throw new AuthenticationError("Invalid authentication token");

  if (await RedisService.get(sessionId)) throw new AuthenticationError("Invalid authenticaation token");

  const currentTime = new Date().getTime() / 1000;
  if (currentTime > exp) throw new AuthenticationError("Invalid authentication token");

  if (userCode.startsWith("usr_")) {
    const user = await UserService.fetchUser(userCode);
    const key = `blocked:account:${user.code}`;

    try {
      if (parseInt(await RedisService.get(key), 10) >= 3) {
        throw new AuthenticationError("Your account is locked for security reasons, please contact the support team");
      }
      // eslint-disable-next-line no-empty
    } catch (blockageError) {}
    if (!user) throw new NotFoundError("User");
    req.user = user;

    if (user.pin && req.body.pin) {
      const cleanedPin = req.body.pin.length > 4 ? req.body.pin.substring(6, 38) : md5(req.body.pin);
      const isPinCorrect = await AuthenticationService.comparePassword(cleanedPin, user.pin);

      if (!isPinCorrect) {
        if (!(await RedisService.get(key))) RedisService.set(key, 1);
        else RedisService.incr(key, 1);

        throw new ForbiddenError("Invalid code");
      } else RedisService.delete(key);
    }

    if (!user.Company && user.company) {
      req.company = await CompanyService.getCompanyByCriteria({
        id: user.company,
      });
    } else req.company = user.Company && user.Company.toJSON();
  }
  if (userCode.startsWith("vdr_")) {
    const vendor = await VendorService.getVendor({
      code: userCode,
    });
    if (!vendor) throw new NotFoundError("Vendor");
    req.vendor = vendor;
    req.company = await CompanyService.getCompanyByCriteria({
      id: vendor.company,
    });
  }

  if (req.company && req.company.status && [STATUSES.BLOCKED, STATUSES.INACTIVE].includes(req.company.status)) {
    throw new AuthenticationError("Account deactivated");
  }

  req.key = decodedKey;
  return next();
};
