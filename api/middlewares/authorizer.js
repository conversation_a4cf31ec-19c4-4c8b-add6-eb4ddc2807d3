const { createHmac } = require("crypto");
const ForbiddenError = require("../utils/forbidden-error");
const { STATUSES } = require("../models/status");
const { ValidationError, NotFoundError } = require("../utils/error.utils");
const CompanyRepo = require("../repositories/company.repo");
const { ONBOARDING_LEVEL } = require("../models/company");

module.exports = {
  isEmployee: (req, res, next) => {
    if (["manager", "admin", "employee"].includes(req.user.role.toLowerCase())) {
      req.isEmployee = true;
    }
    next();
  },
  isAdmin: (req, res, next) => {
    if (["admin"].includes(req.user.role.toLowerCase())) {
      req.isAdmin = true;
    }
    next();
  },
  isManager: (req, res, next) => {
    if (req.headers.authorization === `Bearer ${process.env.INTRA_SERVICE_TOKEN}` || ["manager", "admin"].includes(req.user.role?.toLowerCase())) {
      req.isManager = true;
    }
    next();
  },
  isInternalCall: (req, res, next) => {
    if (req.headers.authorization === `Bearer ${process.env.INTRA_SERVICE_TOKEN}`) {
      req.isInternalCall = true;
      return next();
    }
    throw new ForbiddenError("You cannot carry out this action");
  },
  isCompanyApproved: (req, res, next) => {
    const { company } = req;
    const { id: statusId } = company.Status;

    if (company.onboardingStatus === STATUSES.APPROVED) return next();

    if (statusId === STATUSES.UNVERIFIED) throw new ForbiddenError(`Your account hasn't been verified`);
    if (statusId === STATUSES.PENDING) throw new ForbiddenError(`Your account is pending review`);
    if (statusId === STATUSES.REJECTED) throw new ForbiddenError(`Your account failed verification, Please check your mails for instructions`);

    throw new ForbiddenError("Your account is pending verification");
  },
  isEmployeeRole: (req, res, next) => {
    req.isEmployeeRole =
      ["employee"].includes(req.user.role.toLowerCase()) || (req.user.permissions && !req.user.permissions.includes("access-full"));
    next();
  },

  monoMiddleware: (req, res, next) => {
    // allow mono webhook replays
    if (req.headers.authorization === `Bearer ${process.env.INTRA_SERVICE_TOKEN}`) return next();
    const monoSecret = req.headers["mono-webhook-secret"];
    if (monoSecret !== process.env.MONO_SECURITY_KEY) throw new ForbiddenError("You cannot carry out this action");
    next();
  },

  anchorMiddleware: (req, res, next) => {
    if (
      req.headers.authorization === `Bearer ${process.env.INTRA_SERVICE_TOKEN}` ||
      (req.headers["x-aws-sqsd-attr-sqsjob"] && req.headers["x-aws-sqsd-attr-key"] !== process.env.INTRA_SERVICE_TOKEN)
    )
      return next();
    const hash = req.headers["x-anchor-signature"];
    if (!hash) throw new ForbiddenError("You cannot carry out this action");
    const body = JSON.stringify(req.body);
    const calculatedHash = Buffer.from(createHmac("sha1", process.env.ANCHOR_WEBHOOK_KEY).update(body).digest("hex")).toString("base64");
    if (hash !== calculatedHash) throw new ValidationError("Invalid hash sent");
    return next();
  },

  sudoMiddleware: (req, res, next) => {
    if (
      req.headers.authorization === process.env.SUDO_WEBHOOK_KEY ||
      req.headers.authorization === `Bearer ${process.env.INTRA_SERVICE_TOKEN}` ||
      (req.headers["x-aws-sqsd-attr-sqsjob"] && req.headers["x-aws-sqsd-attr-key"] !== process.env.INTRA_SERVICE_TOKEN)
    )
      return next();
    throw new ForbiddenError("You cannot carry out this action");
  },

  cronMiddleware: (req, res, next) => {
    if (req.headers.authorization === `Bearer ${process.env.INTRA_SERVICE_TOKEN}`) return next();
    throw new ForbiddenError("You cannot carry out this action");
  },
  isCompanyEmployee: async (req, res, next) => {
    const companyCode = req.params.code;
    const foundCompany = await CompanyRepo.getOneCompany({
      queryParams: { code: companyCode },
    });
    if (!foundCompany) throw new NotFoundError("Company");
    if (req.user.company !== foundCompany.id) throw new ValidationError("You are not an employee of this company");
    return next();
  },

  paystackMiddleware: (req, res, next) => {
    if (
      req.headers.authorization === `Bearer ${process.env.INTRA_SERVICE_TOKEN}` ||
      (req.headers["x-aws-sqsd-attr-sqsjob"] && req.headers["x-aws-sqsd-attr-key"] !== process.env.INTRA_SERVICE_TOKEN)
    )
      return next();
    const hash = req.headers["x-paystack-signature"];
    if (!hash) throw new ForbiddenError("You cannot carry out this action");
    const body = JSON.stringify(req.body);
    const calculatedHash = createHmac("sha512", process.env.PAYSTACK_SECRET_KEY).update(body).digest("hex");
    if (hash !== calculatedHash) throw new ValidationError("Invalid hash sent");
    return next();
  },

  choiceBankMiddleware: (req, res, next) => {
    if (
      req.headers.authorization === `Bearer ${process.env.INTRA_SERVICE_TOKEN}` ||
      (req.headers["x-aws-sqsd-attr-sqsjob"] && req.headers["x-aws-sqsd-attr-key"] !== process.env.INTRA_SERVICE_TOKEN)
    )
      return next();
  },

  hasBasicOnboarding: async (req, res, next) => {
    const { company: { onboardingLevel = null } = {} } = req;
    if (!onboardingLevel) throw new ValidationError("Please complete your onboarding to carry out this action");
    return next();
  },
  hasFullyOnboarded: async (req, res, next) => {
    const { company: { onboardingLevel = null } = {} } = req;
    if (!onboardingLevel || onboardingLevel !== ONBOARDING_LEVEL.LEVEL_3)
      throw new ValidationError("Please complete your onboarding to carry out this action");
    return next();
  },
  hasReimbursementEditPermission: async (req, res, next) => {
    if (
      req.user.permissions &&
      req.user.permissions.includesAnyOf("org-*", "org-edit", "transaction-edit", "reimbursement-edit", "reimbursement-*")
    ) {
      req.hasReimbursementEditPermission = true;
    }
    return next();
  },
  hasFundRequestEditPermission: async (req, res, next) => {
    if (
      req.user.permissions &&
      req.user.permissions.includesAnyOf("org-*", "org-edit", "transaction-edit", "reimbursement-edit", "reimbursement-*")
    ) {
      req.hasFundRequestEditPermission = true;
    }
    return next();
  },
};
