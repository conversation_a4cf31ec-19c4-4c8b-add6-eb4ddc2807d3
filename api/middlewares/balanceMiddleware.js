const { ForbiddenError } = require("../utils/error.utils");

module.exports = {
  canViewBalances: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("org-*", "org-edit", "balance-*", "balance-view", "budget-create"))
      req.isManager = true;
    else req.isManager = false;
    return next();
  },
  canViewBalance: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("org-*", "org-edit", "balance-*", "balance-view", "budget-create")) return next();
    throw new ForbiddenError("You cannot view this balance");
  },
  canEditBalance: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("org-*", "org-edit", "balance-*")) return next();
    throw new ForbiddenError("You cannot edit this balance");
  },
};
