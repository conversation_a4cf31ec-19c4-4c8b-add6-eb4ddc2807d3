const ForbiddenError = require("../utils/forbidden-error");

module.exports = {
  canCreateBatchTransaction: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("batch-transaction-*", "batch-transaction-create")) return next();
    throw new ForbiddenError("You cannot make a payment, kindly request the right from your administrator");
  },

  canViewBatchTransaction: (req, res, next) => {
    const { permissions } = req.user;
    if (req.user.permissions && req.user.permissions.includesAnyOf("batch-transaction-*", "batch-transaction-view")) {
      if (!permissions.includesAnyOf("batch-transaction-*"))
        req.query = {
          ...req.query,
          user: req.user.id,
        };
      return next();
    }
    throw new ForbiddenError("You cannot list or view a payment, kindly request the right from your administrator");
  },

  canEditBatchTransaction: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("batch-transaction-*", "batch-transaction-edit")) return next();
    throw new ForbiddenError("You cannot edit a transaction, kindly request the right from your administrator");
  },
};
