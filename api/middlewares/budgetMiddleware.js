const ForbiddenError = require("../utils/forbidden-error");

module.exports = {
  canViewBudgetHistory: (req, res, next) => {
    if (["employee"].includes(req.user.role)) {
      req.query.user = req.user.id;
    }
    next();
  },
  canCreateBudget: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("budget-create", "budget-*")) {
      return next();
    }
    throw new ForbiddenError("You cannot create a budget");
  },
  canViewBudgets: (req, res, next) => {
    const { permissions } = req.user;
    if (permissions && permissions.includesAnyOf("budget-view", "budget-*")) {
      return next();
    }
    throw new ForbiddenError("You cannot view budgets");
  },
  canDeleteBudget: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("budget-delete", "budget-*")) {
      return next();
    }
    throw new ForbiddenError("You cannot delete a budget");
  },
  canEditBudget: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("budget-edit", "budget-*")) {
      return next();
    }
    throw new ForbiddenError("You cannot edit a budget");
  },
};
