const ForbiddenError = require("../utils/forbidden-error");

module.exports = {
  canCreateCards: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("card-create", "card-*")) {
      return next();
    }
    throw new ForbiddenError("You cannot add or create a beneficiary/employee");
  },
  canViewCards: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("card-view", "card-*")) {
      return next();
    }
    throw new ForbiddenError("You cannot view the list of cards");
  },
  canEditCards: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("card-edit", "card-*")) {
      return next();
    }
    throw new ForbiddenError("You cannot edit a card details");
  },
  canDeleteCards: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("card-delete", "card-*")) {
      return next();
    }
    throw new ForbiddenError("You cannot delete a card");
  },
};
