const { CARD_ISSUER } = require("../models/cardissuer");
const { getIssuserById } = require("../services/virtualcards.service");

class CardsMiddleware {
  static cardPicker(req, res, next) {
    if (req.body.currency?.toLowerCase() === "usd") {
      req.cardIssuer = getIssuserById(CARD_ISSUER, 5);
    } else {
      req.cardIssuer = getIssuserById(CARD_ISSUER, 5);
    }
    next();
  }
}

module.exports = CardsMiddleware;
