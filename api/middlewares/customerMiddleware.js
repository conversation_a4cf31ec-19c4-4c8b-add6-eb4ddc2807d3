const ForbiddenError = require("../utils/forbidden-error");

module.exports = {
  canCreateCustomer: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("customer-*", "customer-create")) return next();
    throw new ForbiddenError("You cannot create a customer, kindly request the right from your administrator");
  },

  canViewCustomer: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("customer-*", "customer-view")) return next();
    throw new ForbiddenError("You cannot list or view a customer, kindly request the right from your administrator");
  },

  canEditCustomer: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("customer-*", "customer-edit")) return next();
    throw new ForbiddenError("You cannot edit a customer, kindly request the right from your administrator");
  },

  canDeleteCustomer: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("customer-*", "customer-delete")) return next();
    throw new ForbiddenError("You cannot delete a customer, kindly request the right from your administrator");
  },
};
