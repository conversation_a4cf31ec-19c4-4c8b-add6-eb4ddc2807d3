const ForbiddenError = require("../utils/forbidden-error");

module.exports = {
  canViewStatistics: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("org-*", "org-edit", "dash-view", "dash-*")) {
      return next();
    }
    throw new ForbiddenError("You cannot view the company dashboard");
  },
  canManageDashboard: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("org-*", "dash-*")) {
      return next();
    }
    throw new ForbiddenError("You cannot view the company funding details");
  },
  canFundViaDashboard: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("org-*", "org-edit", "dash-edit", "dash-*")) {
      return next();
    }
    throw new ForbiddenError("You cannot view the company funding details");
  },
};
