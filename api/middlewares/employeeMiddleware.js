const ForbiddenError = require("../utils/forbidden-error");

module.exports = {
  canCreateEmployees: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("employee-create", "employee-*")) {
      return next();
    }
    throw new ForbiddenError("You cannot add or create a beneficiary/employee");
  },
  canViewEmployees: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("employee-view", "employee-*", "employee-create")) {
      return next();
    }
    throw new ForbiddenError("You cannot view the list of employees/beneficiaries");
  },
  canEditEmployees: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("employee-edit", "employee-*")) {
      return next();
    }
    throw new ForbiddenError("You cannot edit an employee/beneficiary details");
  },
  canDeleteEmployees: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("employee-*", "employee-create")) {
      return next();
    }
    throw new ForbiddenError("You cannot delete an employee/beneficiary");
  },
};
