const { TrackerIntegrator } = require("../integrations");

const tracker = new TrackerIntegrator();

module.exports = (error, req, res, next) => {
  if (error) {
    let status = error.status || 500;
    let message = status === 500 ? "Server error" : error.message;
    tracker.errorRecord(error);
    if (status === 500) {
      console.error(error);
    }
    try {
      return res.status(status).json({
        message,
        error: true,
        status: false,
      });
    } catch (e) {}
    throw error;
  }
  return next();
};
