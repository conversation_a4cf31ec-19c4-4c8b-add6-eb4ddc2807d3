const ForbiddenError = require("../utils/forbidden-error");

module.exports = {
  canCreateFundRequests: (req, res, next) => {
    if (
      req.user.permissions &&
      req.user.permissions.includesAnyOf("org-*", "org-edit", "transaction-create", "reimbursement-create", "reimbursement-*")
    ) {
      return next();
    }
    throw new ForbiddenError("You cannot claim a fundRequest");
  },
  canViewFundRequests: (req, res, next) => {
    if (
      req.user.permissions &&
      req.user.permissions.includesAnyOf("org-*", "org-edit", "transaction-create", "reimbursement-create", "reimbursement-view", "reimbursement-*")
    ) {
      return next();
    }
    throw new ForbiddenError("You cannot view the list of fundRequests");
  },
  canEditFundRequests: (req, res, next) => {
    if (
      req.user.permissions &&
      req.user.permissions.includesAnyOf("org-*", "org-edit", "transaction-edit", "reimbursement-edit", "reimbursement-*")
    ) {
      req.canEditFundRequests = true;
      return next();
    }
    throw new ForbiddenError("You cannot edit a fundRequest");
  },
  canDeleteFundRequests: (req, res, next) => {
    if (
      req.user.permissions &&
      req.user.permissions.includesAnyOf("org-*", "org-edit", "transaction-create", "reimbursement-delete", "reimbursement-*")
    ) {
      return next();
    }
    throw new ForbiddenError("You cannot delete a fundRequest");
  },
  canApproveFundRequests: (req, res, next) => {
    if (
      req.user.permissions &&
      req.user.permissions.includesAnyOf("org-*", "org-edit", "transaction-edit", "transaction-approve", "reimbursement-approve", "reimbursement-*")
    ) {
      return next();
    }
    throw new ForbiddenError("You cannot approve a fundRequest");
  },
  canDeclineFundRequests: (req, res, next) => {
    if (
      req.user.permissions &&
      req.user.permissions.includesAnyOf("org-*", "org-edit", "transaction-edit", "transaction-approve", "reimbursement-decline", "reimbursement-*")
    ) {
      return next();
    }
    throw new ForbiddenError("You cannot decline a fundRequest");
  },
};
