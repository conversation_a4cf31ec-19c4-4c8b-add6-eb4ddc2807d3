/* eslint-disable no-case-declarations */
const { Op } = require("sequelize");
const { STATUSES } = require("../models/status");
const { BudgetRepo, CardRepo, UserRepo, BankAccountRepo, PolicyRepo } = require("../repositories/index.repo");
const Utils = require("../utils");
const { Subscription, BillingHistory } = require("../models");

const MESSAGES = {
  MAXIMUM_BUDGETS: "You have reached the maximum number of budgets you can create under this plan",
  MAXIMUM_CARDS: "You have reached the maximum number of cards you can create under this plan",
  MAXIMUM_USERS: "You have reached the maximum number of users you can add under this plan",
  START_APPROVAL_LEVEL:
    "You cannot add more than 1 approval level under this plan. If you need more please upgrade to Growth or Scale for unlimited levels",
  GROWTH_APPROVAL_LEVEL: "You cannot add more than 3 approval level under this plan. If you need more please upgrade to Scale plan",
  NO_VIRTUAL_ACCOUNT: "You cannot generate a virtual account under this plan. You need to upgrade to either Growth or Scale plan",
  MAXIMUM_VIRTUAL_ACCOUNT: "You have reached the maximum number of virtual account you can create under this plan",
  MAXIMUM_APPROVAL_LEVEL: "You have reached the maximum number of approval plans you can create under this plan",
  TEAM_MANAGEMENT: "You cannot manage teams under this plan",
  SUBSIDIARY_MANAGEMENT: "You cannot manage subsidiaries under this plan",
  HR_TOOLS_INTEGRATION: "You cannot integrate HR tools under this plan",
  API_INTEGRATION: "You cannot integrate API under this plan",
  MAXIMUM_SUBACCOUNTS: "You have reached the maximum number of subaccounts you can create under this plan",
  MAXIMUM_POLICIES: "You have reached the maximum number of policies you can create under this plan",
  SPECIFY_MORE_SEATS: "Please specify more seats to add",
  NO_ACTIVE_PLAN: "You do not have an active plan. Please upgrade to continue using this feature",
};

const MODULES = {
  BENEFICIARIES: "beneficiaries",
  BUDGETS: "budgets",
  VIRTUAL_ACCOUNTS: "virtual_accounts",
  CARDS: "cards",
  APPROVALS: "approvals",
  TEAMS: "teams",
  ROLES: "roles",
  POLICIES: "policies",
  CATEGORIES: "categories",
  SUBACCOUNTS: "subaccounts",
  BILLS: "bills",
  INVOICES: "invoices",
  VENDORS: "vendors",
  CUSTOMERS: "customers",
  SUBSIDIARIES: "subsidiaries",
  MULTI_COMPANIES: "multi_companies",
  ACCOUNTING: "accounting",
  HR_TOOLS_INTEGRATION: "hr_tools_integration",
  API_INTEGRATION: "api_integration",
};

module.exports = async (req, res, next, service, serviceMetaData = {}) => {
  if (!req.company) return next();
  const { paymentPlan } = req.company;

  const serviceConfig = typeof paymentPlan.configuration === "string" ? JSON.parse(paymentPlan.configuration) : paymentPlan.configuration;
  const isCreate = req.method === "POST";
  const isUpdate = req.method === "PUT";

  switch (service) {
    case MODULES.BENEFICIARIES:
      if (!isCreate) return next();

      const userCount = await UserRepo.count({
        company: req.company.id,
        status: {
          [Op.ne]: STATUSES.DELETED,
        },
      });

      const freeUsers = serviceConfig.userManagement?.freeUsers || 0;
      const additionalUsersCount = serviceMetaData?.count || 1;
      const totalCount = userCount + additionalUsersCount;
      const userCountWillReachLimit = freeUsers < totalCount;
      const additionalSeats = req.body.additionalSeats || 0;

      if (!userCountWillReachLimit) {
        return next();
      }

      const isLegacyPlanUser = Utils.isNotOnNewPlan(paymentPlan);

      if (isLegacyPlanUser) {
        const maxBeneficiaries = serviceConfig.beneficiaries?.max_users || 0;

        if (maxBeneficiaries >= totalCount) {
          return next();
        }

        return res.status(400).json({ message: MESSAGES.MAXIMUM_USERS });
      }

      const activeSubscription = await Subscription.findOne({
        where: {
          plan: paymentPlan.id,
          status: STATUSES.ACTIVE,
          company: req.company.id,
        },
        attributes: ["id", "additionalSeats", "isFreeTrial"],
        include: [
          {
            model: BillingHistory,
            required: true,
            where: {
              status: STATUSES.PAID,
              company: req.company.id,
            },
            order: [["created_at", "DESC"]],
            limit: 1,
          },
        ],
      });

      if (!activeSubscription || activeSubscription.isFreeTrial) return res.status(400).json({ message: MESSAGES.NO_ACTIVE_PLAN });

      const reservedSeats = activeSubscription?.additionalSeats || 0;

      if (additionalSeats < additionalUsersCount) {
        const allSeatsAccountedFor = Utils.money(reservedSeats).plus(additionalSeats).toNumber() >= additionalUsersCount;
        if (!allSeatsAccountedFor) {
          return res.status(400).json({ message: MESSAGES.SPECIFY_MORE_SEATS });
        }
      }

      const defaultCurrency = "NGN";
      const basicExtraBeneficiaryCost = serviceConfig.userManagement?.additionalUsers?.[defaultCurrency];

      if (!basicExtraBeneficiaryCost) return res.status(400).json({ message: MESSAGES.MAXIMUM_USERS });

      const [billingHistory] = activeSubscription?.BillingHistories || [];
      const dueDate = billingHistory?.dueDate;

      const basicProratedCost = Utils.calculateProratedCost(basicExtraBeneficiaryCost, dueDate);

      if (basicProratedCost === false) return res.status(400).json({ message: MESSAGES.MAXIMUM_USERS });

      if (basicProratedCost === 0) return next();

      req.isNotFreeUser = true;

      const totalCost = Utils.money(basicProratedCost).times(additionalSeats).toNumber();

      req.additionalUsersCost = totalCost;
      req.additionalUsersCount = additionalSeats;
      break;
    case MODULES.BUDGETS:
      const budgetCount = await BudgetRepo.count({
        company: req.company.id,
        status: STATUSES.ACTIVE,
      });
      const budgetCountWillReachLimit = serviceConfig?.budgets?.max_budgets < budgetCount + 1;
      if (isCreate && budgetCountWillReachLimit) return res.status(400).json({ message: MESSAGES.MAXIMUM_BUDGETS });
      const changingToActive = [STATUSES.ACTIVE, "active"].includes(req.body.status);
      if (isUpdate && changingToActive && budgetCountWillReachLimit) {
        return res.status(400).json({ message: MESSAGES.MAXIMUM_BUDGETS });
      }
      break;
    case MODULES.SUBACCOUNTS:
      const subAccountCurrency = req.body?.currency?.toUpperCase() || "NGN";
      const isSubAccount = Boolean(req.body?.parent);
      if (isSubAccount) {
        const totalSubAccounts = await BankAccountRepo.count({
          filter: {
            owner: req.company.id,
            ownerType: "company",
            status: STATUSES.ACTIVE,
            currency: subAccountCurrency,
            parent: {
              [Op.not]: null,
            },
          },
        });
        const subAccountWillReachLimit = Number(serviceConfig?.treasury?.sub_accounts?.[subAccountCurrency].max_number) < totalSubAccounts + 1;
        if (isCreate && subAccountWillReachLimit) return res.status(400).json({ message: MESSAGES.MAXIMUM_SUBACCOUNTS });
      }
      break;
    case MODULES.VIRTUAL_ACCOUNTS:
      // check accounts limits
      const currency = req.body?.currency?.toUpperCase() || "NGN";
      if (!serviceConfig?.virtual_accounts?.NGN?.allowed || !serviceConfig?.virtual_accounts?.USD?.allowed) {
        return res.status(400).json({ message: MESSAGES.NO_VIRTUAL_ACCOUNT });
      }
      const totalVirtualAccount = await BankAccountRepo.count({
        filter: {
          type: { [Op.like]: `%virtual%` },
          owner: req.company.id,
          ownerType: "company",
          status: STATUSES.ACTIVE,
          currency,
        },
      });
      const virtualAccountWillReachLimit = Number(serviceConfig?.virtual_accounts?.[currency].max_number) < totalVirtualAccount + 1;
      if (isCreate && virtualAccountWillReachLimit) return res.status(400).json({ message: MESSAGES.MAXIMUM_VIRTUAL_ACCOUNT });
      break;
    case MODULES.CARDS:
      // check cards limits
      const cardsCount = await CardRepo.count({
        company: req.company.id,
        status: STATUSES.ACTIVE,
      });
      const cardCountWillReachLimit = serviceConfig?.cards.max_number < cardsCount + 1;
      if (isCreate && cardCountWillReachLimit) return res.status(400).json({ message: MESSAGES.MAXIMUM_CARDS });
      const activateCard = [STATUSES.ACTIVE, "active"].includes(req.body.status);
      if (isUpdate && activateCard && cardCountWillReachLimit) {
        res.status(400).json({ message: MESSAGES.MAXIMUM_CARDS });
      }
      break;
    case MODULES.APPROVALS:
      // check approvals level limits
      if (isCreate) {
        if (req.body.reviews.length > serviceConfig?.approval_levels) return res.status(400).json({ message: MESSAGES.MAXIMUM_APPROVAL_LEVEL });
      }
      break;
    case MODULES.POLICIES:
      const totalPolicies = await PolicyRepo.count({ queryParams: { company: req.company.id, status: STATUSES.ACTIVE } });
      const policyCountWillReachLimit = serviceConfig?.policyManagement?.policies < totalPolicies + 1;
      if (isCreate && policyCountWillReachLimit) return res.status(400).json({ message: MESSAGES.MAXIMUM_POLICIES });
      break;
    case MODULES.TEAMS:
      const canManageTeams = serviceConfig?.userManagement?.teamManagement;
      if (!canManageTeams) return res.status(400).json({ message: MESSAGES.TEAM_MANAGEMENT });
      break;
    case MODULES.SUBSIDIARIES:
      const canManageSubsidiaries = serviceConfig?.multiEntity?.subsidiaries;
      if (!canManageSubsidiaries) return res.status(400).json({ message: MESSAGES.SUBSIDIARY_MANAGEMENT });
      break;
    case MODULES.HR_TOOLS_INTEGRATION:
      const canIntegrateHRTools = serviceConfig?.integrations?.hr;
      if (!canIntegrateHRTools) return res.status(400).json({ message: MESSAGES.HR_TOOLS_INTEGRATION });
      break;
    case MODULES.API_INTEGRATION:
      const canIntegrateAPI = serviceConfig?.integrations?.api;
      if (!canIntegrateAPI) return res.status(400).json({ message: MESSAGES.API_INTEGRATION });
      break;
    default:
      break;
  }
  return next();
};

module.exports.MODULES = MODULES;
