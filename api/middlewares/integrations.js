const { addHours } = require("date-fns");
const { STATUSES } = require("../models/utils");
const { ExternalIntegrationRepo } = require("../repositories/index.repo");
const { Response } = require("../services");
const { toTitle, firstDateIsBeforeSecondDate } = require("../utils");
const { refreshZohoToken, fetchAdditionalAccountDetails, fetchAccountDetails, fetchChartOfAccounts } = require("../utils/zoho.utils");
const { ExternalIntegration } = require("../models");
const { getSlackChannels } = require("../utils/slack.utils");
const { PLATFORM } = require("../constants/platforms");

const Middlewares = {
  async isIntegrationActive(req, res, next) {
    const { platform: retrievedPlatform } = req.params;
    const platform = retrievedPlatform.toLowerCase();
    const [foundIntegration] = await ExternalIntegration.findAll({
      where: {
        company: req.company.id,
        platform: String(platform).toUpperCase(),
      },
      order: [["created_at", "DESC"]],
      limit: 1,
    });
    if (platform === PLATFORM.SLACK.toLocaleLowerCase()) {
      const channels = await getSlackChannels(foundIntegration.access_token);
      ExternalIntegrationRepo.updateIntegration({
        queryParams: {
          code: foundIntegration.code,
        },
        updateFields: {
          metadata: {
            ...foundIntegration.metadata,
            channels,
          },
        },
      });
    }
    if (foundIntegration?.status === STATUSES.PROCESSING) {
      Response.failure(res, `We are still configuring your integration, please wait a few minutes...`, {
        configurationInProgress: true,
      });
      if (platform === PLATFORM.ZOHO.toLocaleLowerCase()) {
        try {
          return Middlewares.checkForFurtherDetailsNeeded(req, res, next);
        } catch (error) {
          /* empty */
        }
      }
    }
    if (![STATUSES.ACTIVE, STATUSES.PROCESSING].includes(foundIntegration?.status) || !foundIntegration.access_token)
      return Response.failure(res, `${toTitle(platform)} integration not active. Reconnect the app.`, {
        openIntegrationPage: true,
      });
    req.integration = foundIntegration;
    if (platform.toLowerCase() === PLATFORM.ZOHO.toLocaleLowerCase()) return Middlewares.checkForFurtherDetailsNeeded(req, res, next);
    return next();
  },
  async checkForFurtherDetailsNeeded(req, res, next) {
    let needConfiguration = 0;
    const foundIntegration = await Middlewares.runTokenRefreshing(req.integration);

    if (!foundIntegration.metadata?.organizationId) {
      if (foundIntegration.metadata?.organizations) {
        const { code, metadata, platform } = foundIntegration && foundIntegration.toJSON();
        const redirectURL = `${process.env.ZOHO_URL}/auth?client_id=${process.env.ZOHO_CLIENT_ID}&scope=${process.env.ZOHO_SCOPE}&response_type=code&access_type=offline&prompt=consent&redirect_uri=${process.env.ZOHO_REDIRECT_URL}/settings/integrations/zoho`;

        return Response.failure(res, `You need to configure your organization`, {
          configurationInProgress: true,
          openIntegrationPage: true,
          status: "active",
          metadata,
          platform,
          redirectURL,
          code,
        });
      }
      await fetchAccountDetails(foundIntegration.code);
      needConfiguration = 1;
    }
    if (
      !foundIntegration.metadata?.currencies ||
      !Object.keys(foundIntegration.metadata?.currencies || {}).length ||
      !foundIntegration.metadata?.currencies?.NGN
    ) {
      fetchAdditionalAccountDetails(foundIntegration.code);
      needConfiguration += 2;
    }
    if (
      !foundIntegration.metadata?.chartofaccounts ||
      !Object.keys(foundIntegration.metadata?.chartofaccounts || {}).length ||
      !foundIntegration.metadata?.chartofaccounts?.Expenses_from_Bujeti
    ) {
      fetchChartOfAccounts(foundIntegration.code);
      needConfiguration += 4;
    }
    const { code, metadata, platform } = foundIntegration && foundIntegration.toJSON();

    if (foundIntegration.status === STATUSES.PROCESSING || (needConfiguration && foundIntegration.status === STATUSES.ACTIVE)) {
      Response.failure(
        res,
        needConfiguration === 1
          ? `You need to configure your organization`
          : `We are still setting up your integration, please try again in 2 minutes`,
        {
          configurationInProgress: true,
          openIntegrationPage: true,
          metadata,
          platform,
          code,
        }
      );
    }
    return next();
  },
  async runTokenRefreshing(integration) {
    if (!firstDateIsBeforeSecondDate(new Date(integration.expirationDate), new Date()) || !integration.platform.toLowerCase() === "zoho") {
      return integration;
    }
    // eslint-disable-next-line camelcase
    const { access_token } = await refreshZohoToken(integration.code);
    await ExternalIntegrationRepo.updateIntegration({
      queryParams: {
        code: integration.code,
      },
      updateFields: {
        // eslint-disable-next-line camelcase
        access_token,
        expirationDate: addHours(new Date(), 1),
      },
    });
    return ExternalIntegration.findOne({
      where: {
        code: integration.code,
      },
    });
  },
  // eslint-disable-next-line consistent-return
  async refreshToken(req, res, next) {
    try {
      const { platform } = req.params;

      if (!req.integration)
        return Response.failure(res, `Unexpected error occurred accessing your ${toTitle(platform)} integration. Reconnect the app.`, {
          openIntegrationPage: true,
        });
      if (req.integration.refresh_token) {
        await Middlewares.runTokenRefreshing(req.integration);
        return next();
      }
      return Response.failure(res, `Unexpected error occurred accessing your ${toTitle(platform)} integration. Please reconnect the app.`, {
        openIntegrationPage: true,
      });
    } catch (error) {
      /* empty */
    }
  },
};

module.exports = Middlewares;
