const ForbiddenError = require("../utils/forbidden-error");

module.exports = {
  canScanDocument: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("invoice-*", "invoice-create")) {
      const { paymentPlan } = req.company;
      const serviceConfig = typeof paymentPlan.configuration === "string" ? JSON.parse(paymentPlan.configuration) : paymentPlan.configuration;

      const enabledForOcr = serviceConfig?.payables?.billPayment?.ocr;
      if (!enabledForOcr)
        return res.status(200).json({
          data: null,
          message: "Your payment plan does not allow file scanning",
        });
      return next();
    }
    throw new ForbiddenError("You cannot create an invoice, kindly request the right from your administrator");
  },
  canCreateInvoice: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("invoice-*", "invoice-create")) return next();
    throw new ForbiddenError("You cannot create an invoice, kindly request the right from your administrator");
  },

  canViewInvoice: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("invoice-*", "invoice-view")) return next();
    throw new ForbiddenError("You cannot list or view an invoice, kindly request the right from your administrator");
  },

  canEditInvoice: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("invoice-*", "invoice-edit")) return next();
    throw new ForbiddenError("You cannot edit an invoice, kindly request the right from your administrator");
  },

  canDeleteInvoice: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("invoice-*", "invoice-delete")) return next();
    throw new ForbiddenError("You cannot delete an invoice, kindly request the right from your administrator");
  },
};
