const RedisService = require("../services/redis");

module.exports = (req, res, next, error) => {
  if (error) {
    switch (error.name) {
      case "ValidationError":
        return res.status(400).json({ message: error.message });
      case "NotFoundError":
        return res.status(404).json({ message: error.message });
      case "TooManyConnectionError":
        const IP =
          req.headers["x-forwarded-for"] ||
          req.connection.remoteAddress ||
          req.ip ||
          req.headers["cf-connecting-ip"];
        if (IP) {
          const realIP = IP.split(", ");
          realIP &&
            realIP.length &&
            RedisService.setex(`auth:${realIP[0]}`, 3600);
        }
        return res.status(error.status).json({ message: error.message });
    }
  }
  return next();
};
