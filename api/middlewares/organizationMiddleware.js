const ForbiddenError = require("../utils/forbidden-error");

module.exports = {
  canViewOrganization(req, res, next) {
    if (req.user.permissions && req.user.permissions.includesAnyOf("org-view", "org-*")) {
      return next();
    }
    throw new ForbiddenError("You cannot view the organization information");
  },
  canEditOrganization(req, res, next) {
    if (req.user.permissions && req.user.permissions.includesAnyOf("org-edit", "org-*")) {
      return next();
    }
    throw new ForbiddenError("You cannot edit the organization information");
  },
  canManageOrganization(req, res, next) {
    if (req.user.permissions && req.user.permissions.includesAnyOf("org-view", "org-edit", "org-*")) {
      return next();
    }
    throw new ForbiddenError("You cannot edit the organization information");
  },
};
