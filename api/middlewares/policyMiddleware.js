const ForbiddenError = require("../utils/forbidden-error");

module.exports = {
  canCreatePolicy: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("policy-*", "policy-create")) return next();
    throw new ForbiddenError("You cannot create a policy, kindly request the right from your administrator");
  },

  canViewPolicy: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("policy-*", "policy-view")) return next();
    throw new ForbiddenError("You cannot list or view a policy, kindly request the right from your administrator");
  },

  canEditPolicy: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("policy-*", "policy-edit")) return next();
    throw new ForbiddenError("You cannot edit a policy, kindly request the right from your administrator");
  },

  canDeletePolicy: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("policy-*", "policy-delete")) return next();
    throw new ForbiddenError("You cannot delete a policy, kindly request the right from your administrator");
  },
};
