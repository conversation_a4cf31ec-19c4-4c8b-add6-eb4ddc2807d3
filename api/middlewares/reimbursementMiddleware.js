const ForbiddenError = require("../utils/forbidden-error");

module.exports = {
  canCreateReimbursements: (req, res, next) => {
    if (
      req.user.permissions &&
      req.user.permissions.includesAnyOf("org-*", "org-edit", "transaction-create", "reimbursement-create", "reimbursement-*")
    ) {
      return next();
    }
    throw new ForbiddenError("You cannot claim a reimbursement");
  },
  canViewReimbursements: (req, res, next) => {
    if (
      req.user.permissions &&
      req.user.permissions.includesAnyOf("org-*", "org-edit", "transaction-create", "reimbursement-create", "reimbursement-view", "reimbursement-*")
    ) {
      return next();
    }
    throw new ForbiddenError("You cannot view the list of reimbursements");
  },
  canEditReimbursements: (req, res, next) => {
    if (
      req.user.permissions &&
      req.user.permissions.includesAnyOf("org-*", "org-edit", "transaction-edit", "reimbursement-edit", "reimbursement-*")
    ) {
      req.canEditReimbursements = true;
      return next();
    }
    throw new ForbiddenError("You cannot edit a reimbursement details");
  },
  canDeleteReimbursements: (req, res, next) => {
    if (
      req.user.permissions &&
      req.user.permissions.includesAnyOf("org-*", "org-edit", "transaction-create", "reimbursement-delete", "reimbursement-*")
    ) {
      return next();
    }
    throw new ForbiddenError("You cannot delete a reimbursement");
  },
  canApproveReimbursements: (req, res, next) => {
    if (
      req.user.permissions &&
      req.user.permissions.includesAnyOf("org-*", "org-edit", "transaction-edit", "transaction-approve", "reimbursement-approve", "reimbursement-*")
    ) {
      return next();
    }
    throw new ForbiddenError("You cannot approve a reimbursement");
  },
  canDeclineReimbursements: (req, res, next) => {
    if (
      req.user.permissions &&
      req.user.permissions.includesAnyOf("org-*", "org-edit", "transaction-edit", "transaction-approve", "reimbursement-decline", "reimbursement-*")
    ) {
      return next();
    }
    throw new ForbiddenError("You cannot decline a reimbursement");
  },
};
