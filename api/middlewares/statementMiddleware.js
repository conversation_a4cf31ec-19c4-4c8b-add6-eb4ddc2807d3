const ForbiddenError = require("../utils/forbidden-error");

module.exports = {
  canViewStatement: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("statement-*", "statement-view")) return next();
    throw new ForbiddenError("You cannot view statements, kindly request the right from your administrator");
  },

  canExportStatement: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("statement-*", "statement-export")) return next();
    throw new ForbiddenError("You cannot export a statement, kindly request the right from your administrator");
  },
};
