const ForbiddenError = require("../utils/forbidden-error");

module.exports = {
  canCreateTeam: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("team-*", "team-create")) return next();
    throw new ForbiddenError("You cannot perform this action");
  },

  canViewTeam: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("team-*", "team-view")) return next();
    throw new ForbiddenError("You cannot perform this action");
  },

  canEditTeam: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("team-*", "team-edit")) return next();
    throw new ForbiddenError("You cannot perform this action");
  },

  canDeleteTeam: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("team-*", "team-delete", "team-create")) return next();
    throw new ForbiddenError("You cannot perform this action");
  },
};
