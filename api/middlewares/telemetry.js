const Utils = require("../utils");
const { TrackerIntegrator } = require("../integrations");

const tracker = new TrackerIntegrator();
const sendTelemetry = async (company, responseBody, route) => {
  const span = tracker.getTracer().startSpan(`telemetry - ${route}`);
  if (company) {
    span.setAttribute("companyName", company.name);
    span.setAttribute("companyCode", company.code);
  }
  span.setAttribute("response", responseBody);
  span.setAttribute("env", Utils.currentEnvContext());

  span.end();
};

const telemetry = async (req, res, next) => {
  try {
    const originalSend = res.send;

    res.send = function sendResponse(body) {
      res.locals.responseBody = body;
      return originalSend.call(this, body);
    };

    res.on("finish", () => {
      const { responseBody } = res.locals;
      const { company } = req;
      const route = req.originalUrl;

      // Send telemetry data to a third-party service
      sendTelemetry(company, responseBody, route);
    });
  } catch (error) {
    tracker.errorRecord(error);
  }

  next();
};

exports.telemetry = telemetry;
