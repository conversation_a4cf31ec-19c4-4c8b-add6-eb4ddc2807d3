const { ForbiddenError, ValidationError } = require("../utils/error.utils");
const SettingsService = require("../services/settings");
const { ONBOARDING_LEVEL } = require("../models/company");
const { InvoiceRepo, BillRepo, VirtualCardRepo, ApprovalRuleRepo } = require("../repositories/index.repo");

module.exports = {
  checkTierPermission: async (req, res, next, module) => {
    if (!module) throw new ValidationError("Please specify module");
    const { onboardingLevel, id: company } = req.company;
    if (onboardingLevel === ONBOARDING_LEVEL.LEVEL_3) return next(); // Allowed for tier 3 companies
    const permissions = SettingsService.get("TIER_PERMISSIONS");
    const foundModule = permissions[module];
    if (!foundModule) return next(); // There's no restricion on this module

    let featureCount = 0;
    switch (module) {
      case "invoice":
        featureCount = await InvoiceRepo.count({
          filters: { company },
        });
        break;
      case "bills":
        featureCount = await BillRepo.countBills({
          filter: { company },
        });
        break;
      case "cards":
        featureCount = await VirtualCardRepo.count({
          filter: { company },
        });
        break;
      case "approvals":
        featureCount = await ApprovalRuleRepo.countRules({
          queryParams: { company },
        });
        break;

      default:
        break;
    }

    if (featureCount + 1 <= foundModule[onboardingLevel]) return next();
    return res
      .status(400)
      .json({ message: `You have reached the maximum number of ${module} you can create. Complete your onboarding to create more` });
  },
};
