const ForbiddenError = require("../utils/forbidden-error");

module.exports = {
  canCreateTransaction: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("transaction-*", "transaction-create")) return next();
    throw new ForbiddenError("You cannot make a payment, kindly request the right from your administrator");
  },

  canViewTransaction: (req, res, next) => {
    const { permissions } = req.user;
    if (permissions && permissions.includesAnyOf("transaction-*", "transaction-view")) {
      return next();
    }
    throw new ForbiddenError("You cannot list or view a payment, kindly request the right from your administrator");
  },

  canEditTransaction: (req, res, next) => {
    if (req.user.permissions && req.user.permissions.includesAnyOf("transaction-*", "transaction-edit")) return next();
    throw new ForbiddenError("You cannot edit a transaction, kindly request the right from your administrator");
  },
};
