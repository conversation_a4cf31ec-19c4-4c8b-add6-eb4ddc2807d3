const { customAlphabet } = require("nanoid");
const nanoid = customAlphabet('0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp', 17);

module.exports = (connection, Sequelize) => {
    const schema = {
        code: {
            type: Sequelize.STRING,
            defaultValue: () => 'ach_' + nanoid()
        },
        externalIdentifier: Sequelize.STRING,
        type: {
            type: Sequelize.STRING,
            allowNull: false,
        },
        provider: {
            type: Sequelize.INTEGER,
            allowNull: false
        },
        company: {
            type: Sequelize.INTEGER,
            allowNull: true,
        },
        created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        }
    };
    return connection.define('AccountHolder', schema,
        { timestamps: false });
};
