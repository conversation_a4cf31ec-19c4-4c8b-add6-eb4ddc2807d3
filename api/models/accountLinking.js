const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");
const nanoid = customAlphabet("123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `acl_${nanoid()}`,
    },
    company: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    bankAccount: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    meta: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    externalIdentifier: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.ACTIVE,
    },
    syncStatus: {
      type: Sequelize.INTEGER,
      defaultValue: null,
      allowNull: true,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    lastSyncedAt: {
      type: Sequelize.DATE,
      defaultValue: null,
      allowNull: true,
    },
  };
  return connection.define("AccountLinking", schema, { timestamps: false });
};
