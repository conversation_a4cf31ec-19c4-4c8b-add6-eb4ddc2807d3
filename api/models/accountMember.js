const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `acm_${nanoid()}`,
    },
    bankAccount: Sequelize.INTEGER,
    user: Sequelize.INTEGER,
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.ACTIVE,
      allowNull: false,
    },
    company: {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "Companies",
        key: "id",
      },
    },
    removed_at: {
      type: Sequelize.DATE,
    },
    designation: Sequelize.STRING,
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("AccountMember", schema, { timestamps: false });
};
