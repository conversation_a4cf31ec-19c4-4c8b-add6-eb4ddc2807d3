const { customAlphabet } = require("nanoid");
const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `acm_${nanoid()}`,
    },

    tableType: {
      type: Sequelize.STRING,
      allowNull: false,
    },

    status: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: {
          tableName: "Statuses",
        },
        key: "id",
      },
    },

    company: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: {
          tableName: "Companies",
        },
        key: "id",
      },
    },

    oldAccount: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },

    newAccount: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },

    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },

    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("AccountMigrations", schema, { timestamps: false });
};
