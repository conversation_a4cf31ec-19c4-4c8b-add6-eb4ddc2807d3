const { customAlphabet } = require("nanoid");
const { addYears } = require("date-fns");
const { STATUSES } = require("./status");

const nanoid = customAlphabet("123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `apk_${nanoid()}`,
    },

    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.ACTIVE,
    },
    company: {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: null,
    },

    key: {
      type: Sequelize.STRING,
      allowNull: false,
      defaultValue: customAlphabet("123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp", 32)(),
    },
    scope: {
      type: Sequelize.JSON,
      defaultValue: ["transactions.read"],
    },
    expiresOn: {
      type: Sequelize.DATE,
      defaultValue: addYears(new Date(), 1),
    },
  };

  return connection.define("ApiKey", schema, { timestamps: false });
};
