const { customAlphabet } = require('nanoid');

const nanoid = customAlphabet('123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp', 17);

module.exports = (connection, Sequelize) => {
	const schema = {
		code: {
			type: Sequelize.STRING,
			defaultValue: () => `apv_${nanoid()}`,
		},
		approver: {
			type: Sequelize.INTEGER,
			allowNull: false,
		},
		request: {
			type: Sequelize.INTEGER,
			allowNull: false,
		},
		status: {
			type: Sequelize.INTEGER,
		},
		reason: Sequelize.STRING,
		created_at: {
			type: Sequelize.DATE,
			defaultValue: Sequelize.NOW,
		},
		updated_at: {
			type: Sequelize.DATE,
			defaultValue: Sequelize.NOW,
			onUpdate: Sequelize.NOW,
		},
	};
	return connection.define('Approval', schema, { timestamps: false });
};
