const { customAlphabet } = require('nanoid');
const { STATUSES } = require('./status');

const nanoid = customAlphabet('123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp', 17);

module.exports = (connection, Sequelize) => {
	const schema = {
		code: {
			type: Sequelize.STRING,
			defaultValue: () => `apc_${nanoid()}`,
		},
		rank: Sequelize.INTEGER,
		approvers_threshold: Sequelize.INTEGER,
		trigger: Sequelize.STRING,
		operator: Sequelize.STRING,
		rule: {
			type: Sequelize.INTEGER,
			allowNull: false,
		},
		status: {
			type: Sequelize.INTEGER,
			defaultValue: STATUSES.ACTIVE,
		},
		created_at: {
			type: Sequelize.DATE,
			defaultValue: Sequelize.NOW,
		},
		updated_at: {
			type: Sequelize.DATE,
			defaultValue: Sequelize.NOW,
			onUpdate: Sequelize.NOW,
		},
	};
	return connection.define('ApprovalCondition', schema, { timestamps: false });
};
