const { customAlphabet } = require('nanoid');
const { STATUSES } = require('./status');

const nanoid = customAlphabet('123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp', 17);

module.exports = (connection, Sequelize) => {
	const schema = {
		code: {
			type: Sequelize.STRING,
			defaultValue: () => `apr_${nanoid()}`,
		},
		entity: {
			type: Sequelize.STRING,
			defaultValue: 'transaction',
			allowNull: false,
		},
		entity_id: Sequelize.INTEGER,
		rule: {
			type: Sequelize.INTEGER,
			allowNull: false,
		},
		trigger: {
			type: Sequelize.INTEGER,
			allowNull: false,
		},
		company: Sequelize.INTEGER,
		status: {
			type: Sequelize.INTEGER,
			defaultValue: STATUSES.PENDING,
		},
		created_at: {
			type: Sequelize.DATE,
			defaultValue: Sequelize.NOW,
		},
		updated_at: {
			type: Sequelize.DATE,
			defaultValue: Sequelize.NOW,
			onUpdate: Sequelize.NOW,
		},
	};
	return connection.define('ApprovalRequest', schema, { timestamps: false });
};
