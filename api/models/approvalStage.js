const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet(
  "123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp",
  17
);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `aps_${nanoid()}`,
    },
    request: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    approver_level: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    level: {
      type: Sequelize.INTEGER,
      defaultValue: 1,
      allowNull: false,
    },
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.PENDING,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
      onUpdate: Sequelize.NOW,
    },
  };
  return connection.define("ApprovalStage", schema, { timestamps: false });
};
