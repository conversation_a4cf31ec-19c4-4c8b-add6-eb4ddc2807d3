const { customAlphabet } = require("nanoid");
const nanoid = customAlphabet(
  "0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp",
  17
);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => "aul_" + nanoid(),
    },
    event: Sequelize.STRING,
    company: Sequelize.INTEGER,
    user: Sequelize.INTEGER,
    table_type: Sequelize.STRING,
    table_id: Sequelize.INTEGER,
    initial_state: Sequelize.JSON,
    delta: Sequelize.JSON,
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("AuditLogs", schema, { timestamps: false });
};
