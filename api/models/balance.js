const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `blc_${nanoid()}`,
    },
    amount: Sequelize.BIGINT,
    currency: Sequelize.STRING,
    company: Sequelize.INTEGER,
    name: Sequelize.STRING,
    bankAccount: Sequelize.INTEGER,
    purpose: {
      type: Sequelize.STRING,
      allowNull: false,
      defaultValue: "expenses",
    },
    type: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.ACTIVE,
    },
  };
  return connection.define("Balance", schema, { timestamps: false });
};

module.exports.ACCOUNT_TYPES = {
  LINKED: "linked",
  DIRECT_DEBIT: "direct-debit",
  INTERNAL: "internal",
};
