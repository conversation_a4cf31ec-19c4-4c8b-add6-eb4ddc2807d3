const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `bll_${nanoid()}`,
    },
    amount: Sequelize.BIGINT,
    currency: Sequelize.STRING,
    company: Sequelize.INTEGER,
    balance: Sequelize.INTEGER,
    card: Sequelize.INTEGER,
    transfer: Sequelize.INTEGER,
    transaction: Sequelize.INTEGER,
    budgetLedger: Sequelize.INTEGER,
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.PENDING,
    },
    description: Sequelize.STRING,
    balanceBefore: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    balanceAfter: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    scale: {
      type: Sequelize.INTEGER,
      defaultValue: 2,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    invoice: {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: {
          tableName: "Invoices",
        },
        key: "id",
      },
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("BalanceLedger", schema, { timestamps: false });
};
