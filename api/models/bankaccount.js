const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `bnk_${nanoid()}`,
    },
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.ACTIVE,
    },
    owner: {
      type: Sequelize.INTEGER,
    },
    ownerType: {
      type: Sequelize.STRING,
      defaultValue: "company",
    },
    issuer: {
      type: Sequelize.INTEGER,
    },
    externalIdentifier: {
      type: Sequelize.STRING,
    },
    number: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    type: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    currency: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    subType: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    accountName: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    bankName: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    name: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    isVerified: {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
    },
    bankCode: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    requiresReauth: {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: 0,
    },
    balance: {
      type: Sequelize.BIGINT,
      allowNull: true,
      defaultValue: 0,
    },
    parent: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    externalBankAccountId: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    routingNumber: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    company: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    zohoIdentifier: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    meta: {
      type: Sequelize.JSON,
      allowNull: true,
    },
    routingType: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    beneficiaryAddress: {
      type: Sequelize.JSON,
      allowNull: true,
    },
    bankAddress: {
      type: Sequelize.JSON,
      allowNull: true,
    },
    wireType: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    accountType: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("BankAccount", schema, { timestamps: false });
};
