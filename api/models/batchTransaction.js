const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet("123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `btc_${nanoid()}`,
    },
    name: Sequelize.STRING,
    currency: Sequelize.STRING,
    user: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    amount: Sequelize.BIGINT,
    budget: {
      type: Sequelize.INTEGER,
      defaultValue: null,
    },
    company: Sequelize.INTEGER,
    receipt: Sequelize.STRING,
    balance: Sequelize.INTEGER,
    externalIdentifier: Sequelize.STRING,
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.PROCESSING,
    },
    deadLine: {
      type: Sequelize.DATE,
      allowNull: null,
      defaultValue: null,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
      onUpdate: Sequelize.NOW,
    },
  };
  return connection.define("BatchTransaction", schema, { timestamps: false });
};
