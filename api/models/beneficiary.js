const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet(
  "0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp",
  17
);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `bnf_${nanoid()}`,
    },
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.INVITED,
    },
    owner: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    user: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    company: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    role: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    slackId: {
      type: Sequelize.STRING,
      allowNull: true,
    },
  };
  return connection.define("Beneficiary", schema, { timestamps: false });
};
module.exports.STATUSES = ["invited", "pending", "registered", "cancelled"];
