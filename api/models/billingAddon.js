const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `bla_${nanoid()}`,
    },
    company: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    subscription: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    transaction: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    entity: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    entityId: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    user: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    creditsUsed: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.PENDING,
    },
    transfer: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("BillingAddon", schema, { timestamps: false });
};
