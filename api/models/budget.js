const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet("123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp", 17);

// eslint-disable-next-line no-unused-vars
const BUDGET_TYPES = {
  GROUP: 4,
};

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `bdg_${nanoid()}`,
    },
    currency: {
      type: Sequelize.STRING,
    },
    name: Sequelize.STRING,
    amount: Sequelize.BIGINT,
    available: {
      type: Sequelize.BIGINT,
      defaultValue: 0,
    },
    bufferAmount: Sequelize.BIGINT,
    schedule_id: Sequelize.INTEGER,
    owner: Sequelize.INTEGER,
    spent: {
      type: Sequelize.BIGINT,
      defaultValue: 0,
    },
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.ACTIVE,
      allowNull: false,
    },
    type: Sequelize.INTEGER,
    company: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    isNewBudget: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
    },
    isFunded: {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
    },
    balance: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    parent: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    sharingType: {
      type: Sequelize.STRING,
      defaultValue: "even",
      isIn: [["even", "custom"]],
    },
    level: {
      type: Sequelize.INTEGER,
      defaultValue: 1,
    },
    meta: {
      type: Sequelize.JSON,
      defaultValue: null,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("Budget", schema, { timestamps: false });
};
