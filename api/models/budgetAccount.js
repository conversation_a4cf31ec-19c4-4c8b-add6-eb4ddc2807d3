const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `bac_${nanoid()}`,
    },

    budget: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },

    company: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },

    externalIdentifier: {
      type: Sequelize.STRING,
      allowNull: false,
    },

    type: {
      type: Sequelize.STRING,
      allowNull: false,
    },

    accountId: {
      type: Sequelize.STRING,
      allowNull: false,
    },

    number: {
      type: Sequelize.STRING,
      allowNull: false,
    },

    bankName: {
      type: Sequelize.STRING,
      allowNull: false,
    },

    accountName: {
      type: Sequelize.STRING,
      allowNull: false,
    },

    bankCode: {
      type: Sequelize.STRING,
      allowNull: false,
    },

    provider: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },

    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.ACTIVE,
    },

    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
    },

    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
      onUpdate: Sequelize.NOW,
    },
  };
  return connection.define("BudgetAccount", schema, { timestamps: false });
};
