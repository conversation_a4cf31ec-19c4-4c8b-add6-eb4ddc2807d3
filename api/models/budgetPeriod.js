const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet("123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp", 17);

// eslint-disable-next-line no-unused-vars
const PERIODS = {
  1: "One time",
  2: "Monthly",
  3: "Quarterly",
  4: "Yearly",
  "one-time": 1,
  monthly: 2,
  quarterly: 3,
  yearly: 4,
};

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `bgp_${nanoid()}`,
    },
    currency: {
      type: Sequelize.STRING,
    },
    name: Sequelize.STRING,
    amount: Sequelize.BIGINT,
    available: {
      type: Sequelize.BIGINT,
      defaultValue: 0,
    },
    bufferAmount: {
      type: Sequelize.BIGINT,
      defaultValue: 0,
    },
    budget: Sequelize.INTEGER,
    spent: {
      type: Sequelize.BIGINT,
      defaultValue: 0,
    },
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.INACTIVE,
      allowNull: false,
    },
    frequency: {
      type: Sequelize.INTEGER,
      defaultValue: 1,
    },
    startDate: Sequelize.DATE,
    endDate: Sequelize.DATE,
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("BudgetPeriod", schema, { timestamps: false });
};

module.exports.PERIODS = PERIODS;
