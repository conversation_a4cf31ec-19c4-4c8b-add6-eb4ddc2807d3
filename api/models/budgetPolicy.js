const { customAlphabet } = require("nanoid");
const {STATUSES} = require("./status");
const nanoid = customAlphabet('123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp', 17);

module.exports = (connection, Sequelize) => {
    const schema = {
        code: {
            type: Sequelize.STRING,
            defaultValue: () => 'bpc_' + nanoid()
        },
        budget: {
            type: Sequelize.INTEGER,
            allowNull: false,
        },
        policy: {
            type: Sequelize.INTEGER,
            allowNull: false,
        },
        status: {
            type: Sequelize.INTEGER,
            allowNull: false,
            defaultValue: STATUSES.ACTIVE
        },
        created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updated_at: {
            type: Sequelize.DATE,
            onUpdate: Sequelize.literal('CURRENT_TIMESTAMP')
        }
    };
    return connection.define('BudgetPolicy', schema,
        { timestamps: false });
}