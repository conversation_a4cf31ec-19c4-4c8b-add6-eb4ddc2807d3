const { customAlphabet } = require("nanoid");

const nanoid = customAlphabet("123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `blg_${nanoid()}`,
    },
    currency: Sequelize.STRING,
    transaction: Sequelize.INTEGER,
    transfer: Sequelize.INTEGER,
    amount: Sequelize.BIGINT,
    budget: Sequelize.INTEGER,
    user: Sequelize.INTEGER,
    balanceLedger: Sequelize.INTEGER,
    externalIdentifier: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    card: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    status: {
      type: Sequelize.INTEGER,
      defaultsTo: 11, // PENDING
    },
    scale: {
      type: Sequelize.INTEGER,
      defaultValue: 2,
    },
    balanceBefore: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    balanceAfter: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    description: Sequelize.STRING,
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("BudgetLedger", schema, { timestamps: false });
};
