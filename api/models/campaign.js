const { randomCode, STATUSES } = require("./utils");

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `cpg_${randomCode()}`,
    },
    name: Sequelize.STRING,
    platform: Sequelize.ENUM("facebook", "twitter", "event", "offline", "linkedin", "online"),
    referralCode: Sequelize.INTEGER,
    startDate: Sequelize.DATE,
    endDate: Sequelize.DATE,
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.ACTIVE,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("Campaign", schema, { timestamps: false });
};
