const { customAlphabet } = require("nanoid");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `cas_${nanoid()}`,
    },

    card: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: {
          tableName: "CreditCards",
        },
        key: "id",
      },
    },

    company: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: {
          tableName: "Companies",
        },
        key: "id",
      },
    },

    assignedBy: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: {
          tableName: "Users",
        },
        key: "id",
      },
    },

    previousOwner: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: {
          tableName: "Users",
        },
        key: "id",
      },
    },

    newOwner: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: {
          tableName: "Users",
        },
        key: "id",
      },
    },

    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },

    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("CardAssignment", schema, { timestamps: false });
};
