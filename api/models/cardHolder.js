const { customAlphabet } = require("nanoid");
const nanoid = customAlphabet(
  "0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp",
  17
);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => "cah_" + nanoid(),
    },
    externalIdentifier: Sequelize.STRING,
    provider: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    user: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    company: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    status: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("CardHolder", schema, { timestamps: false });
};
