const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `car_${nanoid()}`,
    },
    owner: Sequelize.INTEGER,
    requestedBy: Sequelize.INTEGER,
    policy: Sequelize.INTEGER,
    budget: Sequelize.INTEGER,
    balance: Sequelize.INTEGER,
    firstName: Sequelize.STRING,
    lastName: Sequelize.STRING,
    billingAddress: Sequelize.STRING,
    city: Sequelize.STRING,
    state: Sequelize.STRING,
    postalCode: Sequelize.STRING,
    country: Sequelize.Sequelize.INTEGER,
    company: Sequelize.Sequelize.INTEGER,
    atmWithdrawals: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
    },

    posTransaction: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
    },

    onlineTransaction: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
    },

    contactlessTransaction: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
    },

    expenseCategory: Sequelize.INTEGER,

    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.PROCESSING,
    },
    approval_request_id: {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: {
          tableName: "ApprovalRequests",
        },
        key: "id",
      },
    },

    payment_entity_id: {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: true,
    },

    payment_entity_type: {
      type: Sequelize.STRING,
      allowNull: true,
    },

    transfer: {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: true,
    },

    transaction: {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: true,
    },

    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("CardRequests", schema, { timestamps: false });
};
