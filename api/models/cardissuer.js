const { customAlphabet } = require("nanoid");
const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => "cis_" + nanoid(),
    },
    name: Sequelize.STRING,
    website: Sequelize.STRING,
    active: Sequelize.TINYINT,
    primary_contact_email: Sequelize.STRING,
    primary_contact_phone: Sequelize.STRING,
    fees: Sequelize.JSON,
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("CardIssuer", schema, { timestamps: false });
};

module.exports.CARD_ISSUER = {
  mono: 1,
  Mono: 1,
  flutterwave: 2,
  Flutterwave: 2,
  Anchor: 3,
  anchor: 3,
  bridgecard: 4,
  Bridgecard: 4,
  sudo: 5,
  Sudo: 5,
  Paystack: 6,
  paystack: 6,
  Zoho: 7,
  Blnk: 8,
  blnk: 8,
  Slack: 9,
  graph: 10,
  Graph: 10,
};
