const { custom<PERSON><PERSON>phabet } = require("nanoid");
const { STATUSES } = require("./status");
const { RULES_EVALUATOR } = require("../mocks/constants.mock");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    id: {
      type: Sequelize.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `ctr_${nanoid()}`,
    },
    name: Sequelize.STRING,
    description: Sequelize.STRING,
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.ACTIVE,
    },
    company: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    category: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    operator: {
      type: Sequelize.STRING,
      allowNull: false,
      defaultValue: RULES_EVALUATOR.OR,
    },
    created_by: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("CategorizationRule", schema, { timestamps: false });
};
