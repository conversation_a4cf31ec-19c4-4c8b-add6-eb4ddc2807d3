const { customAlphabet } = require("nanoid");
const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    id: {
      type: Sequelize.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `ctg_${nanoid()}`,
    },
    name: Sequelize.STRING,
    slug: Sequelize.STRING,
    description: Sequelize.STRING,
    status: {
      type: Sequelize.INTEGER,
      defaultValue: 1,
    },
    bufferAmount: {
      type: Sequelize.BIGINT,
      defaultValue: 0,
    },
    limit: {
      type: Sequelize.BIGINT,
      defaultValue: 0,
    },
    spent: {
      type: Sequelize.BIGINT,
      defaultValue: 0,
    },
    company: {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: null,
    },
    parent: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("Category", schema, { timestamps: false });
};
