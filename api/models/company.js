const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);
const inviteNanoid = customAlphabet("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", 6);
const ValidationError = require("../utils/validation-error");

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `cmp_${nanoid()}`,
    },
    inviteCode: {
      type: Sequelize.STRING(50),
      unique: true,
      allowNull: true,
    },
    name: Sequelize.STRING,
    industry: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    website: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    logo: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    businessType: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    description: {
      type: Sequelize.DataTypes.STRING,
      allowNull: true,
    },
    size: {
      type: Sequelize.STRING,
      allowNull: true,
      validate: {
        isIn: [["micro", "small", "medium", "large"]],
      },
    },
    contactEmail: {
      type: Sequelize.STRING,
      field: "contact_email",
    },
    contact_phone: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    bujetiMail: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    paymentPlan: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 1,
    },
    phoneNumber: Sequelize.INTEGER,
    address: Sequelize.INTEGER,
    director: Sequelize.INTEGER,
    incorporation: Sequelize.INTEGER,
    document_reference: Sequelize.STRING,
    onboardingStatus: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.PENDING,
    },
    active: {
      type: Sequelize.TINYINT,
      defaultValue: 1,
    },
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.UNVERIFIED,
    },
    dateOfRegistration: Sequelize.DATE,
    settlementType: {
      type: Sequelize.ENUM("manual", "auto"),
      defaultValue: "manual",
    },
    settlementAccount: Sequelize.INTEGER,
    rewardPoints: {
      type: Sequelize.INTEGER,
      allowNull: 0,
    },
    referrer: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    parent: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },

    approvedDate: Sequelize.DATE,

    accounts: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.INACTIVE,
    },

    cards: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.INACTIVE,
    },

    invoices: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.INACTIVE,
    },

    bills: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.INACTIVE,
    },

    budgets: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.INACTIVE,
    },

    registeredAddress: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },

    onboardingLevel: {
      type: Sequelize.STRING,
      allowNull: true,
    },

    maxTransactionAmount: {
      type: Sequelize.INTEGER,
      defaultValue: 0,
    },

    amountSpent: {
      type: Sequelize.INTEGER,
      defaultValue: 0,
    },

    graphIdentifier: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    credits: {
      type: Sequelize.BIGINT,
      defaultValue: 0,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };

  const Company = connection.define("Company", schema, {
    timestamps: false,
    hooks: {
      beforeCreate: async (company) => {
        if (!company.inviteCode) {
          company.inviteCode = await generateUniqueInviteCode(company.name, Company);
        }
      },
      beforeUpdate: async (company) => {
        if (!company.inviteCode) {
          company.inviteCode = await generateUniqueInviteCode(company.name, Company);
        }
      },
    },
  });

  Company.prototype.regenerateInviteCode = async function () {
    this.inviteCode = await generateUniqueInviteCode(this.name, Company);
    return this.save();
  };

  return Company;
};

async function generateUniqueInviteCode(companyName, Company) {
  if (!companyName) return null;

  const baseName = companyName
    .toLowerCase()
    .replace(/[^a-z0-9]/g, "")
    .slice(0, 4);

  let attempts = 0;
  const maxAttempts = 5;
  const baseDelayMs = 100; // this is for the first retry which is 100ms

  while (attempts < maxAttempts) {
    const inviteCode = `${baseName}${inviteNanoid()}`;

    // just check if the code exists to avoid race condition
    const existing = await Company.findOne({
      where: { inviteCode },
      attributes: ["id"],
    });

    if (!existing) {
      return inviteCode;
    }

    attempts += 1;

    if (attempts < maxAttempts) {
      const delayMs = baseDelayMs * Math.pow(2, attempts) * (0.5 + Math.random());
      // eslint-disable-next-line no-await-in-loop
      await new Promise((resolve) => setTimeout(resolve, delayMs));
    }
  }

  throw new ValidationError("Failed to generate unique invite code after maximum attempts");
}

module.exports.ONBOARDING_LEVEL = {
  LEVEL_1: "level_1", // Has a max of 250, not approved on anchor
  LEVEL_2: "level_2", // Has a max of 1M, approved on anchor
  LEVEL_3: "level_3", // No max, approved on anchor, submitted all docs
};

module.exports.BUSINESS_TYPE = {
  PRIVATE_LIMITED_LIABILITY: "private limited liability",
  PUBLIC_LIMITED_LIABILITY: "public limited liability",
  INCORPORATED_TRUSTEES: "incorporated trustees",
  BUSINESS_NAME: "business name",
};
