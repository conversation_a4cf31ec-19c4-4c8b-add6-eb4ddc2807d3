const { customAlphabet } = require("nanoid");
const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.DataTypes.STRING(22),
      allowNull: false,
      unique: true,
      defaultValue: () => `clk_${nanoid()}`,
    },

    name: {
      type: Sequelize.DataTypes.STRING(250),
      allowNull: false,
      unique: true,
    },

    externalIdentifier: {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: false,
      unique: true,
    },

    state: {
      type: Sequelize.DataTypes.STRING(50),
      allowNull: true,
    },

    city: {
      type: Sequelize.DataTypes.STRING(50),
      allowNull: true,
    },

    address: {
      type: Sequelize.DataTypes.STRING(250),
      allowNull: true,
    },

    branchAddress: {
      type: Sequelize.DataTypes.STRING(250),
      allowNull: true,
    },

    rcNumber: {
      type: Sequelize.DataTypes.STRING(20),
      allowNull: true,
    },

    registrationDate: {
      type: Sequelize.DataTypes.DATE,
      allowNull: true,
    },

    email: {
      type: Sequelize.DataTypes.STRING(50),
      allowNull: true,
    },

    shareholders: {
      type: Sequelize.DataTypes.JSON,
      allowNull: true,
    },

    created_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },

    updated_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("CompanyLookups", schema, { timestamps: false });
};
