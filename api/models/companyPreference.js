const { customAlphabet } = require("nanoid");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `cprf_${nanoid()}`,
    },
    company: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    feature: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    key: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    description: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    value_type: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    value: {
      type: Sequelize.JSON,
      allowNull: false,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
      onUpdate: Sequelize.NOW,
    },
  };
  return connection.define("CompanyPreference", schema, { timestamps: false });
};

module.exports.VALUE_TYPE = {
  INT: 1,
  TXT: 2,
  ARR: 3,
  BOOL: 4,
};

module.exports.FEATURES = {
  ACCOUNTS: 1,
  BILLS: 2,
  BUDGETS: 3,
  CARDS: 4,
  DASHBOARD: 5,
  INVOICES: 6,
  VENDORS: 7,
};

module.exports.DEFAULT_PREFERENCES = [
  {
    feature: 6,
    key: "template",
    value_type: 2,
    value: "ivtmp_5gkN0zbWoreiNWab5", // default template,
    description: "The default template to use while sending invoices to your customers",
  },
  {
    feature: 6,
    key: "cards_payment",
    value_type: 4,
    value: true,
    description: "Allow cards payments for your invoices. There is a transaction fees of <b>2.9%</b>",
  },
  {
    feature: 6,
    key: "colour",
    value_type: 2,
    value: "#fbf5ec",
    description: "The background color for the invoice.",
  },
];
