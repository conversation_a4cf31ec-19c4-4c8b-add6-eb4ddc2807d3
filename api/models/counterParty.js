const { customAlphabet } = require("nanoid");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    id: {
      type: Sequelize.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `cpty_${nanoid()}`,
    },
    status: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 1,
    },
    externalIdentifier: {
      type: Sequelize.STRING(127),
      allowNull: true,
    },
    number: {
      type: Sequelize.STRING(20),
      allowNull: true,
    },
    type: {
      type: Sequelize.STRING(20),
      allowNull: true,
    },
    currency: {
      type: Sequelize.CHAR(3),
      allowNull: false,
      defaultValue: "NGN",
    },
    subType: {
      type: Sequelize.STRING(25),
      allowNull: true,
    },
    name: {
      type: Sequelize.STRING(255),
      allowNull: true,
    },
    accountName: {
      type: Sequelize.STRING(250),
      allowNull: true,
    },
    bankName: {
      type: Sequelize.STRING(75),
      allowNull: true,
    },
    bankCode: {
      type: Sequelize.STRING(10),
      allowNull: true,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("CounterParty", schema, { timestamps: false });
};
