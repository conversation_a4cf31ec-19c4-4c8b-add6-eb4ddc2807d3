const { customAlphabet } = require("nanoid");
const nanoid = customAlphabet(
  "0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp",
  17
);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `ctry_${nanoid()}`,
    },
    name: Sequelize.STRING,
    iso_code: Sequelize.STRING,
    currency: Sequelize.STRING,
    alpha: Sequelize.STRING,
    caller_code: Sequelize.INTEGER,
    activate: {
      type: Sequelize.DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("Country", schema, { timestamps: false });
};
