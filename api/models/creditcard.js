const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `crd_${nanoid()}`,
    },
    hash: Sequelize.STRING,
    externalIdentifier: Sequelize.STRING,
    number: Sequelize.STRING,
    pan: Sequelize.STRING,
    pin: Sequelize.STRING,
    exp_month: Sequelize.STRING,
    exp_year: Sequelize.STRING,
    last_4: Sequelize.STRING,
    cvv: Sequelize.STRING,
    brand: Sequelize.STRING,
    name: Sequelize.STRING,
    currency: Sequelize.STRING,
    user: Sequelize.INTEGER,
    amount: Sequelize.BIGINT,
    spent: Sequelize.BIGINT,
    budget: Sequelize.INTEGER,
    balance: Sequelize.INTEGER,
    issuer: Sequelize.INTEGER,
    type: Sequelize.TINYINT,
    requiresPinChange: {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
    },
    atmWithdrawals: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
    },

    posTransaction: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
    },

    onlineTransaction: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
    },

    isExternal: {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
    },
    contactlessTransaction: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
    },
    expenseCategory: Sequelize.INTEGER,

    team: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },

    impactBudget: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },

    failureReason: {
      type: Sequelize.STRING(500),
      allowNull: true,
    },
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.PROCESSING,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("CreditCard", schema, { timestamps: false });
};

module.exports.DummyCards = [
  {
    code: "crd_fdkfdfdoifdoid",
    last_4: "4242",
    pan: "4242********4242",
    name: "Hitesh Card",
    brand: "mastercard",
    type: "physical",
    cvv: null,
    number: null,
    exp_month: null,
    exp_year: null,
    budget: {
      code: "bdg_P96lAFthlYiVhAnvq",
      spent: 0,
      status: 1,
      sharingType: "even",
      created_at: "2022-02-01T00:00:00Z",
      updated_at: "2022-02-01T00:00:00Z",
      owner: 1,
      name: "Marketing Budget",
      amount: 30000,
      currency: "EUR",
      type: 1,
      users: null,
    },
  },
  {
    code: "crd_op76jopJDI8fd",
    last_4: "4249",
    pan: "4559********4249",
    name: "Freelance Budget",
    brand: "visa",
    type: "virtual",
    cvv: null,
    number: null,
    budget: {
      code: "bdg_P96lAFthlYiVhAnvq",
      spent: 0,
      status: 1,
      sharingType: "even",
      created_at: "2022-02-01T00:00:00Z",
      updated_at: "2022-02-01T00:00:00Z",
      owner: 1,
      name: "Marketing Budget",
      amount: 30000,
      currency: "EUR",
      type: 1,
      users: null,
    },
  },
];
