
module.exports = (connection, Sequelize) => {
    const schema = {
        id: {
            type: Sequelize.DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        code: Sequelize.STRING,
        short_code: {
            type: Sequelize.DataTypes.STRING,
            unique: true,
            allowNull: false
        },
        currency_name: Sequelize.DataTypes.STRING, 
        currency_unit_name: Sequelize.DataTypes.STRING,
        enabled: {
            type: Sequelize.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true,
        },  
        icon: Sequelize.DataTypes.STRING,
        created_at: {
            type: Sequelize.DataTypes.DATE,
            defaultValue: Sequelize.fn('now')
        },
        updated_at: {
            type: Sequelize.DataTypes.DATE,
            defaultValue: null,
          }
    };
    return connection.define('Currencies', schema,
        { timestamps: false });
};
