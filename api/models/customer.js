const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");
const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `cus_${nanoid()}`,
    },

    company: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },

    name: {
      type: Sequelize.STRING,
      allowNull: false,
    },

    email: {
      type: Sequelize.STRING,
      allowNull: true,
    },

    tin: {
      type: Sequelize.STRING,
      allowNull: true,
    },

    type: {
      type: Sequelize.STRING,
      allowNull: true,
    },

    phoneNumber: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },

    category: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },

    address: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },

    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.ACTIVE,
    },

    zohoIdentifier: {
      type: Sequelize.STRING,
      allowNull: true,
    },

    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
      onUpdate: Sequelize.NOW,
    },
  };
  return connection.define("Customer", schema, { timestamps: false });
};
