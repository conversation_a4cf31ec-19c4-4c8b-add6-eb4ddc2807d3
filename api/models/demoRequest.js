const { customAlphabet } = require("nanoid");
const nanoid = customAlphabet('0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp', 17);

module.exports = (connection, Sequelize) => {
    const schema = {
        code: {
            type: Sequelize.STRING,
            defaultValue: () => 'drq_' + nanoid()
        },
        email: Sequelize.STRING,
        firstName: Sequelize.STRING,
        lastName: Sequelize.STRING,
        website: Sequelize.STRING,
        company: Sequelize.STRING,
        companySize: Sequelize.STRING,
        description: Sequelize.STRING,
        phoneNumber: Sequelize.INTEGER,
        reason: {
            type: Sequelize.STRING,
            allowNull: false,
            defaultValue: 'demo',
        },
        message: Sequelize.STRING,
        position: Sequelize.STRING,
        demoCompleted: {
            type: Sequelize.TINYINT,
            defaultValue: 0,
        },
        willSignUp: {
            type: Sequelize.TINYINT,
            defaultValue: 0,
        },
        created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updated_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            onUpdate: Sequelize.literal('CURRENT_TIMESTAMP')
        }
    };
    return connection.define('DemoRequest', schema,
        { timestamps: false });
};
