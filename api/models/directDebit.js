const { customAlphabet } = require("nanoid");
const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `drd_${nanoid()}`,
    },
    mandate: {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: false,
    },
    company: {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: false,
    },
    amount: Sequelize.INTEGER,
    beneficiaryAccountNumber: Sequelize.STRING,
    beneficiaryBankCode: Sequelize.STRING,
    narration: Sequelize.STRING,
    status: Sequelize.INTEGER,
    reference: Sequelize.STRING,
    created_at: {
      type: Sequelize.DATE,
      allowNull: true,
      defaultValue: Sequelize.NOW,
    },
    updated_at: {
      type: Sequelize.DATE,
      allowNull: true,
      onUpdate: Sequelize.NOW,
      defaultValue: Sequelize.NOW,
    },
  };
  return connection.define("DirectDebit", schema, { timestamps: false });
};
