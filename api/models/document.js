const { custom<PERSON><PERSON>phabet } = require("nanoid");
const { BUSINESS_TYPE } = require("./company");
const { STATUSES } = require("./status");
const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => "doc_" + nanoid(),
    },
    type: Sequelize.STRING,
    reference: Sequelize.STRING,
    metadata: {
      type: Sequelize.DataTypes.JSON,
      allowNull: true,
    },
    number: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    url: {
      type: Sequelize.STRING,
      allowNull: true,
      required: false,
    },
    asset: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    expiryDate: { type: Sequelize.DATE, allowNull: true },
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.VERIFYING,
    },
    processor: {
      type: Sequelize.DataTypes.STRING,
      defaultValue: "metamap",
      allowNull: false,
      validate: {
        isIn: [["smileID", "Shuftipro", "metamap", "anchor"]],
      },
    },
    response: {
      type: Sequelize.DataTypes.STRING,
      allowNull: true,
    },
    country: {
      type: Sequelize.DataTypes.STRING,
      defaultValue: "NIGERIA",
      allowNull: false,
    },
    table_type: {
      type: Sequelize.DataTypes.STRING,
      defaultValue: "business",
      allowNull: false,
      validate: {
        isIn: [["business", "user", "individual"]],
      },
    },
    table_id: {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: true,
    },
    issuing_date: Sequelize.DATE,
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("Document", schema, { timestamps: false });
};

module.exports.ID_TYPES = {
  NIN: {
    value: "nin",
    type: "text",
    hasFile: false,
  },
  INTERNATIONAL_PASSPORT: {
    value: "ip",
    type: "text",
    hasFile: true,
  },
  DRIVERS_LICENSE: {
    value: "dl",
    type: "text",
    hasFile: true,
  },
  VOTERS_CARD: {
    value: "vi",
    type: "text",
    hasFile: false,
  },
};

module.exports.DOCUMENTS = [
  {
    name: "Incorporated Trustees Application Form(Form CAC IT/1)",
    value: "cacITForm1",
    type: "file",
    companyType: [BUSINESS_TYPE.INCORPORATED_TRUSTEES],
  },
  {
    name: "Business Name Number",
    value: "bnNumber",
    isBasicDocument: true,
    type: "text",
    companyType: [BUSINESS_TYPE.BUSINESS_NAME],
  },
  {
    name: "Registration number for incorporated trustees",
    value: "cacITNumber",
    type: "text",
    companyType: [BUSINESS_TYPE.INCORPORATED_TRUSTEES],
  },
  {
    name: "Application for registration with the CAC(Form CAC 1)",
    pre2020: true,
    post2020: false,
    pre2017: false,
    value: "cacForm1",
    type: "file",
    companyType: [BUSINESS_TYPE.PUBLIC_LIMITED_LIABILITY, BUSINESS_TYPE.PRIVATE_LIMITED_LIABILITY],
  },
  {
    name: "Statement of share Capital and return of Allotment(Form CAC 2)",
    value: "cacForm2",
    pre2017: true,
    post2020: false,
    pre2020: false,
    type: "file",
    companyType: [BUSINESS_TYPE.PRIVATE_LIMITED_LIABILITY],
  },
  {
    name: "Notification of Registered Address(Form CAC 3)",
    value: "cacForm3",
    pre2017: true,
    post2020: false,
    pre2020: false,
    type: "file",
    companyType: [BUSINESS_TYPE.PRIVATE_LIMITED_LIABILITY],
  },
  {
    name: "Particulars of Directors(Form CAC 7)",
    value: "cacForm7",
    pre2017: true,
    post2020: false,
    pre2020: false,
    type: "file",
    companyType: [BUSINESS_TYPE.PRIVATE_LIMITED_LIABILITY],
  },
  {
    name: "Certificate of Incorporation",
    isBasicDocument: true,
    value: "incorp_C",
    type: "file",
    companyType: [BUSINESS_TYPE.PUBLIC_LIMITED_LIABILITY, BUSINESS_TYPE.PRIVATE_LIMITED_LIABILITY, BUSINESS_TYPE.INCORPORATED_TRUSTEES],
  },
  {
    name: "RC Number",
    isBasicDocument: true,
    value: "rcNumber",
    type: "text",
    companyType: [BUSINESS_TYPE.PUBLIC_LIMITED_LIABILITY, BUSINESS_TYPE.PRIVATE_LIMITED_LIABILITY, BUSINESS_TYPE.INCORPORATED_TRUSTEES],
  },
  {
    name: "Tax Identification Number",
    value: "tin",
    type: "text",
    companyType: [BUSINESS_TYPE.PUBLIC_LIMITED_LIABILITY, BUSINESS_TYPE.PRIVATE_LIMITED_LIABILITY],
  },
  {
    name: "Certificate of Registration of Business Name",
    isBasicDocument: true,
    value: "cac",
    type: "file",
    companyType: [BUSINESS_TYPE.BUSINESS_NAME],
  },
  {
    name: "Status Report",
    isBasicDocument: false,
    value: "cacStatusReport",
    post2020: true,
    pre2017: false,
    pre2020: false,
    type: "file",
    companyType: [BUSINESS_TYPE.PUBLIC_LIMITED_LIABILITY, BUSINESS_TYPE.PRIVATE_LIMITED_LIABILITY, BUSINESS_TYPE.BUSINESS_NAME],
  },
  {
    name: "Memorandum and Article of Association",
    isBasicDocument: false,
    value: "moa",
    type: "file",
    companyType: [BUSINESS_TYPE.PUBLIC_LIMITED_LIABILITY, BUSINESS_TYPE.PRIVATE_LIMITED_LIABILITY],
  },
  {
    name: "Proof of address",
    isBasicDocument: false,
    value: "utilityBill",
    type: "file",
    companyType: [...Object.values(BUSINESS_TYPE)],
  },
  {
    name: "CAC BN 1",
    isBasicDocument: false,
    value: "cacBn1",
    type: "file",
    companyType: [BUSINESS_TYPE.BUSINESS_NAME],
  },
  {
    name: "CAC IT 1",
    isBasicDocument: false,
    value: "cacIT1",
    type: "file",
    companyType: [BUSINESS_TYPE.INCORPORATED_TRUSTEES],
  },
  {
    name: "Constitution or duties of the partners",
    isBasicDocument: false,
    value: "constitution",
    type: "file",
    companyType: [BUSINESS_TYPE.BUSINESS_NAME, BUSINESS_TYPE.INCORPORATED_TRUSTEES],
  },
  {
    name: "SCUML(Special Control Unit against Money Laundering)",
    isBasicDocument: false,
    value: "scumlCertificate",
    type: "file",
    companyType: [BUSINESS_TYPE.INCORPORATED_TRUSTEES],
  },
];

module.exports.DOCUMENT_MAPPER = {
  UTILITY_BILL: "utilityBill",
  BVN: "bvn",
  CAC: "cac",
  BN_NUMBER: "bnNumber",
  NIN: "nin",
  RC_NUMBER: "rcNumber",
  INCORP_C: "incorp_C",
};

module.exports.DOCUMENT_ENTITY_TYPE = {
  INDIVIDUAL: "individual",
  BUSINESS: "business",
};
