const { customAlphabet } = require("nanoid");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);
const { STATUSES } = require("./status");

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `frq_${nanoid()}`,
    },
    user: {
      type: Sequelize.INTEGER,
      required: true,
    },
    type: {
      type: Sequelize.INTEGER,
      required: true,
    },
    budget: {
      // budget to top up
      type: Sequelize.INTEGER,
      required: false,
      allowNull: true,
    },
    budgetPeriod: Sequelize.INTEGER,
    sourceBudget: {
      // budget to pay from
      type: Sequelize.INTEGER,
      required: false,
      allowNull: true,
    },
    vendor: {
      // vendor to pay
      type: Sequelize.INTEGER,
      required: false,
      allowNull: true,
    },
    bankAccount: {
      // bank account to top up
      type: Sequelize.INTEGER,
      required: false,
      allowNull: true,
    },
    card: Sequelize.INTEGER,
    balance: Sequelize.INTEGER,
    currency: Sequelize.STRING,
    amount: Sequelize.INTEGER,
    reviewer: Sequelize.INTEGER,
    transaction: Sequelize.INTEGER,
    transfer: Sequelize.INTEGER,
    description: Sequelize.STRING,
    note: Sequelize.STRING,
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.PENDING,
    },
    needsMoreInfo: {
      type: Sequelize.TINYINT,
      defaultValue: 0,
    },
    moreInfoDescription: Sequelize.STRING,
    category: Sequelize.INTEGER,
    company: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    approval_request_id: {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: {
          tableName: "ApprovalRequests",
        },
        key: "id",
      },
    },
    reviewed_on: {
      type: Sequelize.DATE,
      defaultValue: null,
    },
    meta: {
      type: Sequelize.JSON,
      defaultValue: {},
    },
    deadLine: {
      type: Sequelize.DATE,
      allowNull: true,
      defaultValue: null,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("FundRequest", schema, { timestamps: false });
};

module.exports.FUND_REQUEST_TYPES = {
  BUDGET: 1, // when approved the funds are turned into a budget
  PAYMENT: 2, // when approved the funds are use to pay the linked vendor
  TOP_UP: 3, // when approved the funds are used to top up a budget or an account
};
