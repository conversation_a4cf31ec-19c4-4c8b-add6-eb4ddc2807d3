/* eslint-disable no-unused-expressions */
/* eslint-disable no-underscore-dangle */
const Sequelize = require("sequelize");
const config = require("../../config/db");

const __models = {
  Asset: "asset",
  Address: "address",
  User: "user",
  PlatformIdentity: "platformIdentity",
  Balance: "balance",
  BalanceLedger: "balanceledger",
  BankAccount: "bankaccount",
  Budget: "budget",
  BudgetLedger: "budgetledger",
  Beneficiary: "beneficiary",
  Device: "device",
  DemoRequest: "demoRequest",
  Carrier: "carrier",
  CardIssuer: "cardissuer",
  Company: "company",
  Country: "country",
  Currencies: "currencies",
  CardRequest: "cardRequest",
  CreditCard: "creditcard",
  Document: "document",
  Individual: "individual",
  Industry: "industry",
  MonoAccountHolder: "monoaccountholder",
  PhoneNumber: "phonenumber",
  Setting: "setting",
  Status: "status",
  Transaction: "transaction",
  Transfer: "transfer",
  UserBudget: "userbudget",
  Vendor: "vendor",
  PasswordResetRequest: "passwordresetrequest",
  Reimbursement: "reimbursement",
  Otp: "otp",
  ThirdPartyLog: "thirdpartylog",
  AuditLogs: "auditLogs",
  Notification: "notification",
  Role: "role",
  Permission: "permission",
  Category: "category",
  Policy: "policy",
  PolicyType: "policyType",
  PolicyViolation: "policyViolation",
  BudgetPolicy: "budgetPolicy",
  Team: "Team",
  TeamMember: "teamMember",
  TeamBudget: "teamBudget",
  OnboardingInvite: "onboardingInvite",
  TransactionType: "transactionType",
  ApprovalRule: "rule",
  ApprovalRequest: "approvalRequest",
  ApprovalCondition: "approvalCondition",
  Approver: "approver",
  ApprovalConditionOperand: "approvalConditionOperand",
  Approval: "approval",
  ApprovalStage: "approvalStage",
  ApproverLevel: "approverLevel",
  PaymentPlan: "paymentPlan",
  BillingHistory: "billingHistory",
  Subscription: "subscription",
  AccountLinking: "accountLinking",
  BatchTransaction: "batchTransaction",
  ScheduledTransaction: "scheduledTransaction",
  ScheduledBudget: "scheduledBudget",
  Schedule: "schedule",
  AccountHolder: "accountHolder",
  Invoice: "invoice",
  InvoiceProduct: "invoiceProduct",
  Product: "product",
  Customer: "customer",
  InvoiceAccount: "invoiceAccount",
  VendorCategory: "vendorCategory",
  SettlementAccount: "settlementAccount",
  Settlement: "settlement",
  CardHolder: "cardHolder",
  ExternalIntegration: "integration",
  Synchronization: "synchronization",
  CategoryMapping: "categoryMapping",
  BudgetAccount: "budgetAccount",
  DisabledCategory: "disabledCategory",
  FundRequest: "fundrequest",
  PolicyRestriction: "policyRestriction",

  // Referrals
  ReferralType: "referralType",
  ReferralCode: "referralCode",
  Referral: "referral",
  Partner: "partner",
  Campaign: "campaign",

  BalanceType: "balanceType",
  Mandate: "mandate",
  DirectDebit: "directDebit",
  CompanyLookup: "companyLookup",
  MoreInfoLog: "moreInfoLog",

  InvoiceInstallment: "invoiceInstallment",
  ScheduledInvoice: "scheduledInvoice",
  ScheduledInvoiceProduct: "scheduledInvoiceProduct",
  ScheduledInvoiceInstallment: "scheduledInvoiceInstallment",
  InvoicePayment: "invoicePayment",
  PlanOption: "planoption",
  Mcc: "mcc",
  MccCard: "mccCard",
  MccCardRequest: "mccCardRequest",
  CardPolicy: "cardPolicy",
  AccountMember: "accountMember",

  TransactionAttempt: "transactionAttempt",

  // Policy conditions
  PolicyCondition: "policyCondition",
  PolicyDocument: "policyDocument",
  PolicyConditionOperand: "policyConditionOperand",
  PolicyUserException: "policyUserException",
  AccountMigration: "accountMigration",
  CategorizationRule: "categorizationRule",
  CategorizationRuleCondition: "categorizationRuleCondition",
  CardAssignment: "cardAssignment",
  PendingSettlement: "pendingSettlement",

  // Bills
  Bill: "bill",
  BillInstallment: "billInstallment",
  BillProduct: "billProduct",
  BillPayment: "billPayment",
  ScheduledBill: "scheduledBill",
  ScheduledBillInstallment: "scheduledBillInstallment",
  ScheduledBillProduct: "scheduledBillProduct",

  // Spend management
  Spend: "spend",
  SpendPlan: "spendPlan",
  SpendAsset: "spendAsset",
  SpendTransaction: "spendTransaction",

  // Counterparty
  CounterParty: "counterParty",
  // API KEY
  ApiKey: "apiKey",

  BillingAddon: "billingAddon",
  InvoiceTemplate: "invoiceTemplate",
  CompanyPreference: "companyPreference",

  LedgerIdentity: "ledgerIdentity",
  Bank: "bank",

  Tax: "tax",
  TaxVersion: "taxVersion",
  TaxGroup: "taxgroup",
  TaxGroupTax: "taxgrouptax",
  TaxApplication: "taxapplication",

  BudgetPeriod: "budgetPeriod",
};

const sequelize = new Sequelize(config.DB, config.USER, config.PASSWORD, {
  host: config.HOST,
  port: config.PORT,
  dialect: config.dialect,
  pool: config.pool,
  logging: Boolean(config.logging),
  retry: config.retry,
});

// eslint-disable-next-line import/order
const knex = require("knex")({
  client: "mysql2",
  connection: {
    host: config.HOST,
    port: config.PORT,
    user: config.USER,
    password: config.PASSWORD,
    database: config.DB,
  },
});

const models = {
  Sequelize,
  sequelize,
  knex,
};

const __loadModels = () => {
  for (const key of Object.keys(__models)) {
    models[key] = require(`./${__models[key]}.js`)(sequelize, Sequelize);
  }
};

__loadModels();

// close unused connections
process.on("SIGINT", async () => {
  await sequelize.close();
  console.log("Database connection closed");
  process.exit(0);
});

models.Balance.hasMany(models.BalanceLedger, { foreignKey: "balance" });
models.BalanceType.hasMany(models.Balance, { foreignKey: "type" });
models.Balance.belongsTo(models.BalanceType, { foreignKey: "type" });
models.BalanceLedger.belongsTo(models.Balance, { foreignKey: "balance" });
models.BalanceLedger.belongsTo(models.Status, { foreignKey: "status" });

models.Team.belongsTo(models.Status, { foreignKey: "status" });
models.Team.belongsTo(models.Company, { foreignKey: "company" });
models.Team.hasMany(models.Transaction, { foreignKey: "team" });
models.Team.hasMany(models.TeamBudget, { foreignKey: "team" });

models.TeamMember.belongsTo(models.Team, { foreignKey: "team" });
models.TeamMember.belongsTo(models.User, { foreignKey: "user" });
models.Team.hasMany(models.TeamMember, { foreignKey: "team" });
models.Team.belongsTo(models.Company, { foreignKey: "company" });
models.Team.hasMany(models.Reimbursement, { foreignKey: "team" });
models.Team.hasMany(models.CreditCard, { foreignKey: "team" });
models.TeamMember.belongsTo(models.Role, { foreignKey: "role" });
models.TeamMember.belongsTo(models.Status, { foreignKey: "status" });

models.TeamBudget.belongsTo(models.Budget, { foreignKey: "budget" });
models.TeamBudget.belongsTo(models.Team, { foreignKey: "team" });
models.TeamBudget.belongsTo(models.Status, { foreignKey: "status" });
models.Budget.hasMany(models.TeamBudget, { foreignKey: "budget" });

models.Budget.belongsTo(models.Balance, { foreignKey: "balance" });
models.Budget.belongsTo(models.Status, { foreignKey: "status" });
models.Budget.hasMany(models.CreditCard, { foreignKey: "budget" });
models.CreditCard.belongsTo(models.Budget, { foreignKey: "budget" });
models.Budget.hasMany(models.Transaction, { foreignKey: "budget" });

models.BudgetLedger.belongsTo(models.Budget, { foreignKey: "budget" });
models.Budget.hasMany(models.BudgetLedger, {
  foreignKey: "budget",
  as: "figures",
});

models.BudgetLedger.belongsTo(models.CreditCard, { foreignKey: "card" });
models.CreditCard.hasMany(models.BudgetLedger, { foreignKey: "card" });

models.BalanceLedger.belongsTo(models.CreditCard, { foreignKey: "card" });
models.CreditCard.hasMany(models.BalanceLedger, { foreignKey: "card" });

models.BankAccount.belongsTo(models.Status, { foreignKey: "status" });
models.BankAccount.belongsTo(models.User, { foreignKey: "owner" });
models.BankAccount.belongsTo(models.Vendor, { foreignKey: "owner" });
models.BankAccount.belongsTo(models.Company, { foreignKey: "owner" });
models.BankAccount.belongsTo(models.CardIssuer, { foreignKey: "issuer" });
models.User.hasMany(models.BankAccount, { foreignKey: "owner" });
models.Vendor.hasMany(models.BankAccount, { foreignKey: "owner" });
models.Customer.hasMany(models.BankAccount, {
  foreignKey: "owner",
  constraints: false,
  scope: { ownerType: "customer" },
});

models.Beneficiary.belongsTo(models.Company, { foreignKey: "company" });
models.Beneficiary.belongsTo(models.User, { as: "Owner", foreignKey: "owner" });
models.Beneficiary.belongsTo(models.User, { foreignKey: "user" });
models.Beneficiary.belongsTo(models.Status, { foreignKey: "status" });
models.Beneficiary.belongsTo(models.Role, { foreignKey: "role" });
models.User.hasOne(models.Beneficiary, { foreignKey: "user" });

models.CreditCard.hasMany(models.Transaction, { foreignKey: "card" });
models.CreditCard.hasMany(models.FundRequest, { foreignKey: "card" });
models.CreditCard.belongsTo(models.Category, { foreignKey: "expenseCategory" });
models.Category.hasMany(models.CreditCard, { foreignKey: "expenseCategory" });

// models.User.belongsTo(models.PhoneNumber, { foreignKey: 'phoneNumber' });
models.User.belongsTo(models.Address, { foreignKey: "address" });

models.User.hasMany(models.Budget, { foreignKey: "owner" });
models.Budget.belongsTo(models.User, { foreignKey: "owner" });

models.Budget.belongsTo(models.Company, { foreignKey: "company" });

models.User.hasMany(models.UserBudget, { foreignKey: "user" });
models.Budget.hasMany(models.UserBudget, {
  foreignKey: "budget",
  as: "beneficiaries",
});
models.UserBudget.belongsTo(models.Budget, { foreignKey: "budget" });
models.UserBudget.belongsTo(models.User, { foreignKey: "user" });
models.UserBudget.belongsTo(models.Status, { foreignKey: "status" });

models.CardIssuer.hasMany(models.CreditCard, { foreignKey: "issuer" });
models.CreditCard.belongsTo(models.CardIssuer, { foreignKey: "issuer" });
models.CreditCard.belongsTo(models.Status, { foreignKey: "status" });
models.CreditCard.belongsTo(models.User, { foreignKey: "user" });
models.CreditCard.belongsTo(models.Company, { foreignKey: "company" });
models.CreditCard.belongsTo(models.Team, { foreignKey: "team" });

models.CardRequest.belongsTo(models.User, { foreignKey: "owner", as: "cardOwner" });
models.CardRequest.belongsTo(models.User, { foreignKey: "requestedBy", as: "cardRequester" });
models.CardRequest.belongsTo(models.Budget, { foreignKey: "budget" });
models.CardRequest.belongsTo(models.Country, { foreignKey: "country" });
models.CardRequest.belongsTo(models.Company, { foreignKey: "company" });
models.CardRequest.hasMany(models.ApprovalRequest, { foreignKey: "entity_id", as: "CardRequestApprovalRequests" });
models.CardRequest.belongsTo(models.Status, { foreignKey: "status" });
models.Status.hasMany(models.CardRequest, { foreignKey: "status" });

models.Country.hasOne(models.Currencies, { foreignKey: "currency" });
models.Currencies.belongsTo(models.Country, { foreignKey: "currency" });

models.User.hasMany(models.PasswordResetRequest, { foreignKey: "user" });
models.PasswordResetRequest.belongsTo(models.User, { foreignKey: "user" });

models.User.belongsTo(models.Company, { foreignKey: "company" });
models.User.belongsTo(models.PhoneNumber, { foreignKey: "phoneNumber" });
models.User.belongsTo(models.Status, { foreignKey: "status" });
models.User.belongsTo(models.User, { foreignKey: "manager", as: "Manager" });

models.Address.belongsTo(models.Company, { foreignKey: "company" });

models.Otp.belongsTo(models.User, { foreignKey: "user" });

models.User.hasMany(models.Transaction, { foreignKey: "payer" });
models.Transaction.belongsTo(models.User, { foreignKey: "payer" });
models.User.hasMany(models.Transaction, { foreignKey: "user", as: "Requester" });
models.Transaction.belongsTo(models.User, { foreignKey: "user", as: "Requester" });
models.Transaction.belongsTo(models.Company, { foreignKey: "company" });
models.Transaction.belongsTo(models.Budget, { foreignKey: "budget" });
models.Transaction.belongsTo(models.CreditCard, { foreignKey: "card" });
models.Transaction.belongsTo(models.Status, { foreignKey: "status" });
models.Transaction.belongsTo(models.User, {
  foreignKey: "recipient",
  as: "payment_recipient",
});
models.Transaction.belongsTo(models.Vendor, { foreignKey: "recipient" });
models.Transaction.belongsTo(models.Asset, { foreignKey: "receipt" });
models.Transaction.belongsTo(models.BankAccount, {
  foreignKey: "bank_account",
});
models.ScheduledTransaction.belongsTo(models.BankAccount, {
  foreignKey: "bank_account",
});
models.Transaction.belongsTo(models.Category, { foreignKey: "category" });
models.ScheduledTransaction.belongsTo(models.Category, {
  foreignKey: "category",
});
models.Transaction.belongsTo(models.Team, { foreignKey: "team" });
models.Transaction.belongsTo(models.Balance, { foreignKey: "balance" });

models.BatchTransaction.belongsTo(models.User, { foreignKey: "user" });
models.BatchTransaction.belongsTo(models.Budget, { foreignKey: "budget" });
models.BatchTransaction.hasMany(models.Transaction, { foreignKey: "batch_id" });
models.Transaction.belongsTo(models.BatchTransaction, {
  foreignKey: "batch_id",
});
models.BatchTransaction.belongsTo(models.Company, { foreignKey: "company" });
models.BatchTransaction.belongsTo(models.Balance, { foreignKey: "balance" });
models.Company.hasMany(models.BatchTransaction, { foreignKey: "company" });
models.Transaction.hasMany(models.Asset, {
  foreignKey: "entityId",
  as: "TransactionAssets",
});
models.BatchTransaction.hasMany(models.Asset, {
  foreignKey: "entityId",
  as: "BatchTransactionAssets",
});

models.ScheduledTransaction.hasMany(models.Asset, {
  foreignKey: "entityId",
  as: "ScheduledTransactionAssets",
});

models.ScheduledTransaction.belongsTo(models.Schedule, {
  foreignKey: "schedule_id",
});
models.ScheduledTransaction.belongsTo(models.User, { foreignKey: "user" });
models.ScheduledTransaction.belongsTo(models.User, {
  foreignKey: "recipient",
  as: "payment_recipient",
});
models.ScheduledTransaction.belongsTo(models.Vendor, {
  foreignKey: "recipient",
});
models.ScheduledTransaction.belongsTo(models.Company, {
  foreignKey: "company",
});
models.ScheduledTransaction.belongsTo(models.Budget, { foreignKey: "budget" });
models.ScheduledTransaction.belongsTo(models.Balance, { foreignKey: "balance" });
models.ScheduledTransaction.hasMany(models.Transaction, {
  foreignKey: "schedule_id",
});
models.Transaction.belongsTo(models.ScheduledTransaction, {
  foreignKey: "schedule_id",
});
// Scheduled Budget Relationships
models.ScheduledBudget.belongsTo(models.Schedule, {
  foreignKey: "schedule_id",
});
models.ScheduledBudget.belongsTo(models.Company, { foreignKey: "company" });
models.ScheduledBudget.belongsTo(models.Budget, { foreignKey: "budget" });
models.ScheduledBudget.hasMany(models.Budget, {
  foreignKey: "schedule_id",
  as: "Budgets",
});
models.Budget.hasOne(models.ScheduledBudget, { foreignKey: "budget" });

models.Transfer.belongsTo(models.Company, { foreignKey: "company" });
models.Transfer.belongsTo(models.Status, { foreignKey: "status" });
models.Transfer.belongsTo(models.Balance, { foreignKey: "balance" });
models.Transfer.belongsTo(models.Category, { foreignKey: "category" });
models.Transfer.belongsTo(models.CounterParty, { foreignKey: "counterParty" });

models.Company.hasMany(models.Transaction, { foreignKey: "company" });
models.Company.hasMany(models.User, { foreignKey: "company" });
models.Company.hasMany(models.Beneficiary, { foreignKey: "company" });
models.Company.hasMany(models.CreditCard, { foreignKey: "company" });
models.Company.hasMany(models.Team, { foreignKey: "company" });
models.Company.hasMany(models.TeamMember, { foreignKey: "company" });
models.Company.hasMany(models.Team, { foreignKey: "company" });
models.Company.hasMany(models.TeamMember, { foreignKey: "company" });
models.Company.hasMany(models.Budget, { foreignKey: "company" });
models.Company.hasMany(models.OnboardingInvite, { foreignKey: "company" });
models.Company.belongsTo(models.Address, { foreignKey: "address" });
models.Company.belongsTo(models.Address, { foreignKey: "registeredAddress", as: "RegisteredAddress" });
models.Company.belongsTo(models.Document, { foreignKey: "incorporation" });
models.Company.belongsTo(models.Individual, { foreignKey: "director" });
models.Company.belongsTo(models.Industry, { foreignKey: "industry" });
models.Company.belongsTo(models.Status, { foreignKey: "status" });
models.Company.belongsTo(models.PhoneNumber, { foreignKey: "phoneNumber" });
models.Company.belongsTo(models.PaymentPlan, { foreignKey: "paymentPlan" });
models.PaymentPlan.belongsTo(models.Company, { foreignKey: "company" });
models.Company.belongsTo(models.Company, { foreignKey: "parent" });

models.Individual.belongsTo(models.Address, { foreignKey: "address" });
models.Company.hasMany(models.Document, { foreignKey: "table_id", as: "CompanyDocuments" });
models.Individual.hasMany(models.Document, { foreignKey: "table_id", as: "DirectorDocuments" });
models.Individual.belongsTo(models.Status, { foreignKey: "status" });
models.Individual.belongsTo(models.PhoneNumber, { foreignKey: "phoneNumber" });
models.Individual.belongsTo(models.Company, { foreignKey: "company" });
models.Company.hasMany(models.Individual, { foreignKey: "company" });

models.Document.belongsTo(models.Status, { foreignKey: "status" });
models.Document.belongsTo(models.Asset, { foreignKey: "asset" });
models.Asset.hasOne(models.Document, { foreignKey: "asset" });

models.Vendor.belongsTo(models.Status, { foreignKey: "status" });
models.Vendor.belongsTo(models.Company, { foreignKey: "company" });
models.Vendor.belongsTo(models.Address, { foreignKey: "address" });
models.Vendor.belongsTo(models.PhoneNumber, { foreignKey: "phoneNumber" });

models.Reimbursement.belongsTo(models.Status, { foreignKey: "status" });
models.Reimbursement.belongsTo(models.User, { foreignKey: "user", as: "User" });
models.Reimbursement.belongsTo(models.Budget, { foreignKey: "budget" });
models.Reimbursement.belongsTo(models.Balance, { foreignKey: "balance" });
models.Reimbursement.belongsTo(models.User, {
  foreignKey: "reviewer",
  as: "Reviewer",
});
models.Reimbursement.belongsTo(models.Company, { foreignKey: "company" });
models.Reimbursement.belongsTo(models.Asset, { foreignKey: "receipt" });
models.Reimbursement.hasMany(models.Asset, {
  foreignKey: "entityId",
  as: "ReimbursementAssets",
});

models.Reimbursement.belongsTo(models.Balance, { foreignKey: "balance" });
models.Reimbursement.belongsTo(models.Vendor, { foreignKey: "vendor" });
models.Reimbursement.belongsTo(models.Category, { foreignKey: "category" });
models.Reimbursement.belongsTo(models.Team, { foreignKey: "team" });
models.Reimbursement.belongsTo(models.ApprovalRequest, {
  foreignKey: "approval_request_id",
});
models.Reimbursement.belongsTo(models.Transaction, {
  foreignKey: "transaction",
});
models.Transaction.hasOne(models.Reimbursement, { foreignKey: "transaction" });
models.Budget.hasMany(models.Reimbursement, { foreignKey: "budget" });
models.Company.hasMany(models.Reimbursement, { foreignKey: "company" });
models.User.hasMany(models.Reimbursement, { foreignKey: "user" });
models.User.hasMany(models.Reimbursement, { foreignKey: "reviewer" });
models.Vendor.hasMany(models.Reimbursement, { foreignKey: "vendor" });
models.Role.hasMany(models.Permission);
models.Budget.hasMany(models.BudgetPolicy, { foreignKey: "budget" });
models.Policy.hasMany(models.BudgetPolicy, { foreignKey: "policy" });
models.Policy.belongsTo(models.PolicyType, { foreignKey: "type" });
models.PolicyType.belongsToMany(models.Policy, {
  foreignKey: "policy_type",
  through: models.PolicyRestriction,
  as: "PolicyTypes",
});
models.Policy.belongsToMany(models.PolicyType, {
  foreignKey: "policy",
  through: models.PolicyRestriction,
  as: "PolicyTypes",
});

models.Role.hasMany(models.User, { foreignKey: "role_id" });

// models.ThirdPartyLog.belongsTo(models.Company, { foreignKey: "company" });
models.ThirdPartyLog.belongsTo(models.CardIssuer, { foreignKey: "provider" });
models.AuditLogs.belongsTo(models.User, { foreignKey: "user" });
models.Role.belongsTo(models.Company, { foreignKey: "company" });
models.User.belongsTo(models.Role, { foreignKey: "role_id" });
models.User.belongsTo(models.Role, { foreignKey: "role_id" });
models.Balance.belongsTo(models.BankAccount, { foreignKey: "bankAccount" });
models.BankAccount.hasOne(models.Balance, { foreignKey: "bankAccount" });
models.Balance.belongsTo(models.Status, { foreignKey: "status" });
models.Status.hasMany(models.Balance, { foreignKey: "status" });
models.BudgetPolicy.belongsTo(models.Budget, { foreignKey: "budget" });
models.BudgetPolicy.belongsTo(models.Policy, { foreignKey: "policy" });
models.Budget.hasMany(models.BudgetPolicy, { foreignKey: "budget" });
models.ApprovalRequest.hasMany(models.ApprovalStage, { foreignKey: "request" });
models.ApprovalRequest.belongsTo(models.ApprovalRule, { foreignKey: "rule" });
models.ApprovalRequest.belongsTo(models.Transaction, {
  foreignKey: "entity_id",
});
models.Transaction.hasMany(models.ApprovalRequest, {
  foreignKey: "entity_id",
  as: "TransactionApprovalRequests",
});
models.ApprovalRequest.belongsTo(models.BatchTransaction, {
  foreignKey: "entity_id",
});
models.BatchTransaction.hasMany(models.ApprovalRequest, {
  foreignKey: "entity_id",
  as: "BatchTransactionApprovalRequests",
});
models.ApprovalRequest.belongsTo(models.ScheduledTransaction, {
  foreignKey: "entity_id",
});
models.ApprovalRequest.belongsTo(models.Reimbursement, {
  foreignKey: "entity_id",
});
models.ApprovalRequest.belongsTo(models.FundRequest, {
  foreignKey: "entity_id",
});
models.ApprovalRequest.belongsTo(models.Invoice, {
  foreignKey: "entity_id",
});
models.ApprovalRequest.belongsTo(models.Bill, {
  foreignKey: "entity_id",
});
models.Reimbursement.hasMany(models.ApprovalRequest, {
  foreignKey: "entity_id",
  as: "ReimbursementApprovalRequests",
});
models.ApprovalRequest.belongsTo(models.TransactionType, {
  foreignKey: "trigger",
});
models.Transaction.hasOne(models.ApprovalRequest, { foreignKey: "entity_id" });

models.BatchTransaction.hasOne(models.ApprovalRequest, { foreignKey: "entity_id" });

models.ApprovalRequest.hasMany(models.Approval, { foreignKey: "request" });

models.Approval.belongsTo(models.Approver, { foreignKey: "approver" });
models.Approver.belongsTo(models.User, { foreignKey: "user" });

models.Category.belongsTo(models.Status, { foreignKey: "status" });
models.Category.belongsTo(models.Company, { foreignKey: "company" });

models.OnboardingInvite.belongsTo(models.Company, { foreignKey: "company" });
models.OnboardingInvite.belongsTo(models.Status, { foreignKey: "status" });
models.Status.hasMany(models.OnboardingInvite, { foreignKey: "status" });

models.ApprovalRule.hasMany(models.ApprovalCondition, { foreignKey: "rule" });
models.ApprovalRule.hasMany(models.ApprovalCondition, {
  foreignKey: "rule",
  as: "ApprovedBy",
});
models.ApprovalRule.hasMany(models.ApprovalCondition, {
  foreignKey: "rule",
  as: "RuleConditions",
});
models.ApprovalRule.hasMany(models.Approver, { foreignKey: "rule" });
models.Approver.belongsTo(models.ApprovalCondition, {
  foreignKey: "condition",
});
// * New Approver Level code
models.Approver.belongsTo(models.ApproverLevel, {
  foreignKey: "approver_level",
});
models.ApproverLevel.hasMany(models.Approver, { foreignKey: "approver_level" });
models.ApprovalStage.belongsTo(models.ApproverLevel, {
  foreignKey: "approver_level",
});
models.ApprovalRule.hasMany(models.ApproverLevel, { foreignKey: "rule" });

models.Approver.belongsTo(models.User, { foreignKey: "user" });
models.Approver.belongsTo(models.ApprovalRule, { foreignKey: "rule" });
models.ApprovalCondition.hasMany(models.Approver, { foreignKey: "condition" });
models.ApprovalStage.belongsTo(models.ApprovalCondition, {
  foreignKey: "condition",
});
models.ApprovalCondition.hasMany(models.ApprovalConditionOperand, {
  foreignKey: "condition",
});
models.ApprovalConditionOperand.belongsTo(models.Category, {
  foreignKey: "operand",
  as: "CategoryOperands",
});
models.ApprovalConditionOperand.belongsTo(models.Vendor, {
  foreignKey: "operand",
  as: "VendorOperands",
});
models.ApprovalConditionOperand.belongsTo(models.Budget, {
  foreignKey: "operand",
  as: "BudgetOperands",
});
models.ApprovalConditionOperand.belongsTo(models.TransactionType, {
  foreignKey: "operand",
  as: "TypeOperands",
});
models.ApprovalConditionOperand.belongsTo(models.Balance, {
  foreignKey: "operand",
  as: "AccountOperands",
});
models.Approver.hasMany(models.Approval, { foreignKey: "approver" });

models.AccountLinking.belongsTo(models.Company, { foreignKey: "company" });
models.AccountLinking.belongsTo(models.BankAccount, {
  foreignKey: "bankAccount",
});
models.Company.hasMany(models.AccountLinking, { foreignKey: "company" });

models.AccountLinking.belongsTo(models.Company, { foreignKey: "company" });
models.AccountLinking.belongsTo(models.BankAccount, {
  foreignKey: "bankAccount",
});
models.Company.hasMany(models.AccountLinking, { foreignKey: "company" });
models.Company.belongsTo(models.Asset, { foreignKey: "logo" });

models.AccountHolder.belongsTo(models.Company, { foreignKey: "company" });
models.Company.hasMany(models.AccountHolder, { foreignKey: "company" });
models.AccountHolder.belongsTo(models.CardIssuer, { foreignKey: "provider" });
models.CardIssuer.hasMany(models.AccountHolder, { foreignKey: "provider" });

models.Product.belongsTo(models.Company, { foreignKey: "company" });
models.Company.hasMany(models.Product, { foreignKey: "company" });
models.Product.belongsTo(models.Status, { foreignKey: "status" });
models.Status.hasMany(models.Product, { foreignKey: "status" });

models.InvoiceProduct.belongsTo(models.Company, { foreignKey: "company" });
models.Company.hasMany(models.InvoiceProduct, { foreignKey: "company" });
models.InvoiceProduct.belongsTo(models.Invoice, { foreignKey: "invoice" });
models.Invoice.hasMany(models.InvoiceProduct, { foreignKey: "invoice" });
models.InvoiceProduct.belongsTo(models.Status, { foreignKey: "status" });
models.Status.hasMany(models.InvoiceProduct, { foreignKey: "status" });

models.Invoice.belongsTo(models.Company, { foreignKey: "company" });
models.Company.hasMany(models.Invoice, { foreignKey: "company" });
models.Invoice.belongsTo(models.User, { foreignKey: "user" });
models.User.hasMany(models.Invoice, { foreignKey: "user" });
models.Invoice.belongsTo(models.Status, { foreignKey: "status" });
models.Status.hasMany(models.Invoice, { foreignKey: "status" });
models.InvoiceProduct.belongsTo(models.Product, { foreignKey: "product" });
models.InvoiceProduct.belongsTo(models.Invoice, { foreignKey: "invoice" });
models.Product.hasMany(models.InvoiceProduct, { foreignKey: "product" });
models.Invoice.hasMany(models.InvoiceProduct, { foreignKey: "invoice" });
models.Invoice.hasMany(models.Transfer, { foreignKey: "invoice" });
models.Transfer.belongsTo(models.Invoice, { foreignKey: "invoice" });
models.Invoice.belongsTo(models.ApprovalRequest, {
  foreignKey: "approval_request_id",
});
models.Invoice.belongsTo(models.Balance, { foreignKey: "balance" });
models.Balance.hasMany(models.Invoice, { foreignKey: "balance" });
models.Invoice.belongsTo(models.Budget, { foreignKey: "budget" });
models.Budget.hasMany(models.Invoice, { foreignKey: "budget" });

models.BudgetLedger.belongsTo(models.Status, { foreignKey: "status" });
models.Status.hasMany(models.BudgetLedger, { foreignKey: "status" });
models.Customer.hasMany(models.Invoice, { foreignKey: "customer" });
models.Invoice.belongsTo(models.Customer, { foreignKey: "customer" });
models.Customer.belongsTo(models.Status, { foreignKey: "status" });
models.Status.hasMany(models.Customer, { foreignKey: "status" });
models.Customer.belongsTo(models.PhoneNumber, { foreignKey: "phoneNumber" });
models.Customer.belongsTo(models.Address, { foreignKey: "address" });
models.Customer.belongsTo(models.Category, { foreignKey: "category" });

models.BudgetLedger.belongsTo(models.Transaction, {
  foreignKey: "transaction",
});
models.BudgetLedger.belongsTo(models.Transfer, { foreignKey: "transfer" });
models.Transfer.hasOne(models.BudgetLedger, { foreignKey: "transfer" });
models.Transaction.hasMany(models.BudgetLedger, { foreignKey: "transaction" });
models.Invoice.hasMany(models.Transfer, { foreignKey: "invoice" });
models.Transfer.belongsTo(models.Invoice, { foreignKey: "invoice" });
models.Invoice.hasMany(models.InvoiceAccount, { foreignKey: "invoice" });
models.CardIssuer.hasMany(models.InvoiceAccount, { foreignKey: "issuer" });
models.InvoiceAccount.belongsTo(models.Invoice, { foreignKey: "invoice" });
models.InvoiceAccount.belongsTo(models.Status, { foreignKey: "status" });
models.InvoiceAccount.belongsTo(models.CardIssuer, { foreignKey: "issuer" });

models.Customer.hasMany(models.Invoice, { foreignKey: "customer" });
models.Invoice.belongsTo(models.Customer, { foreignKey: "customer" });
models.Customer.belongsTo(models.Status, { foreignKey: "status" });
models.Status.hasMany(models.Customer, { foreignKey: "status" });
models.Customer.belongsTo(models.PhoneNumber, { foreignKey: "phoneNumber" });

models.Invoice.hasMany(models.InvoiceAccount, { foreignKey: "invoice" });
models.CardIssuer.hasMany(models.InvoiceAccount, { foreignKey: "issuer" });
models.InvoiceAccount.belongsTo(models.Invoice, { foreignKey: "invoice" });
models.InvoiceAccount.belongsTo(models.Status, { foreignKey: "status" });
models.InvoiceAccount.belongsTo(models.CardIssuer, { foreignKey: "issuer" });

models.BalanceLedger.belongsTo(models.Transaction, {
  foreignKey: "transaction",
});
models.BalanceLedger.belongsTo(models.Transfer, {
  foreignKey: "transfer",
});
models.Transaction.hasMany(models.BalanceLedger, { foreignKey: "transaction" });
models.Transfer.hasMany(models.BalanceLedger, { foreignKey: "transfer" });
models.VendorCategory.belongsTo(models.Category, { foreignKey: "category" });
models.VendorCategory.belongsTo(models.Vendor, { foreignKey: "vendor" });
models.VendorCategory.belongsTo(models.Status, { foreignKey: "status" });
models.Vendor.hasMany(models.VendorCategory, {
  foreignKey: "vendor",
  as: "categories",
});
models.Category.hasMany(models.VendorCategory, { foreignKey: "category" });
models.SettlementAccount.belongsTo(models.Company, { foreignKey: "company" });
models.SettlementAccount.belongsTo(models.BankAccount, {
  foreignKey: "bankAccount",
});
models.SettlementAccount.belongsTo(models.User, { foreignKey: "addedBy" });
models.SettlementAccount.belongsTo(models.Status, { foreignKey: "status" });
models.Company.hasMany(models.SettlementAccount, { foreignKey: "company" });
models.Status.hasMany(models.SettlementAccount, { foreignKey: "status" });
models.BankAccount.hasOne(models.SettlementAccount, {
  foreignKey: "bankAccount",
});
models.User.hasMany(models.SettlementAccount, { foreignKey: "addedBy" });
models.User.hasMany(models.ExternalIntegration, { foreignKey: "user" });
models.ExternalIntegration.belongsTo(models.User, { foreignKey: "user" });
models.Company.hasMany(models.ExternalIntegration, { foreignKey: "company" });
models.ExternalIntegration.belongsTo(models.Company, { foreignKey: "company" });
models.Settlement.belongsTo(models.Company, { foreignKey: "company" });
models.Company.hasMany(models.Settlement, { foreignKey: "company" });
models.Settlement.belongsTo(models.User, { foreignKey: "initiatedBy" });
models.User.hasMany(models.Settlement, { foreignKey: "initiatedBy" });
models.Status.hasMany(models.Settlement, { foreignKey: "status" });
models.Settlement.belongsTo(models.Status, { foreignKey: "status" });
models.Settlement.belongsTo(models.CardIssuer, { foreignKey: "provider" });
models.CardIssuer.hasMany(models.Settlement, { foreignKey: "provider" });
models.Settlement.belongsTo(models.SettlementAccount, {
  foreignKey: "settlementAccount",
});
models.SettlementAccount.hasMany(models.Settlement, {
  foreignKey: "settlementAccount",
});
models.BudgetLedger.belongsTo(models.User, { foreignKey: "user" });
models.User.hasMany(models.BudgetLedger, { foreignKey: "user" });

models.CardHolder.belongsTo(models.Company, { foreignKey: "company" });
models.Company.hasMany(models.CardHolder, { foreignKey: "company" });

models.CardHolder.belongsTo(models.User, { foreignKey: "user" });
models.User.hasMany(models.CardHolder, { foreignKey: "user" });

models.CardHolder.belongsTo(models.CardIssuer, { foreignKey: "provider" });
models.CardIssuer.hasMany(models.CardHolder, { foreignKey: "provider" });

models.PlatformIdentity.belongsTo(models.Status, { foreignKey: "status" });
models.PlatformIdentity.hasMany(models.ExternalIntegration, {
  foreignKey: "platform",
  sourceKey: "name",
});
models.ExternalIntegration.belongsTo(models.PlatformIdentity, {
  foreignKey: "platform",
  targetKey: "name",
});

models.CategoryMapping.belongsTo(models.Category, { foreignKey: "category" });
models.Category.hasMany(models.CategoryMapping, { foreignKey: "category" });
models.BudgetAccount.belongsTo(models.Budget, { foreignKey: "budget" });
models.Budget.hasOne(models.BudgetAccount, { foreignKey: "budget" });
models.BudgetAccount.belongsTo(models.Company, { foreignKey: "company" });
models.Company.hasMany(models.BudgetAccount, { foreignKey: "company" });
models.BudgetAccount.belongsTo(models.CardIssuer, { foreignKey: "provider" });
models.CardIssuer.hasMany(models.BudgetAccount, { foreignKey: "provider" });
models.BudgetAccount.belongsTo(models.Status, { foreignKey: "status" });
models.Status.hasMany(models.BudgetAccount, { foreignKey: "status" });

// Referrals
models.User.hasOne(models.ReferralCode, { foreignKey: "owner" });
models.ReferralCode.belongsTo(models.User, { foreignKey: "owner" });

models.User.hasMany(models.Referral, { foreignKey: "referrer" });
models.Referral.belongsTo(models.User, { foreignKey: "referrer" });

models.ReferralCode.hasMany(models.Referral, { foreignKey: "referralCode" });
models.Referral.belongsTo(models.ReferralCode, { foreignKey: "referralCode" });

models.Referral.belongsTo(models.Campaign, { foreignKey: "campaign" });
models.Campaign.hasMany(models.Referral, { foreignKey: "campaign" });

models.Partner.hasMany(models.ReferralCode, { foreignKey: "owner" });

models.ReferralCode.belongsTo(models.ReferralType, { foreignKey: "type" });
models.ReferralType.hasMany(models.ReferralCode, { foreignKey: "type" });

models.DisabledCategory.belongsTo(models.Company, { foreignKey: "company" });
models.Company.hasOne(models.DisabledCategory, { foreignKey: "company" });

models.DisabledCategory.belongsTo(models.Category, { foreignKey: "category" });
models.Category.hasOne(models.DisabledCategory, { foreignKey: "category" });
models.Category.hasMany(models.Transaction, { foreignKey: "category" });

models.FundRequest.belongsTo(models.Status, { foreignKey: "status" });
models.FundRequest.belongsTo(models.User, { foreignKey: "user", as: "User" });
models.FundRequest.belongsTo(models.Budget, { foreignKey: "budget" });
models.FundRequest.belongsTo(models.Budget, { foreignKey: "sourceBudget", as: "SourceBudget" });
models.FundRequest.belongsTo(models.User, {
  foreignKey: "reviewer",
  as: "Reviewer",
});
models.FundRequest.belongsTo(models.Company, { foreignKey: "company" });
models.FundRequest.hasMany(models.Asset, { foreignKey: "entityId", as: "FundRequestAssets" });
models.FundRequest.belongsTo(models.Vendor, { foreignKey: "vendor" });
models.FundRequest.belongsTo(models.BankAccount, { foreignKey: "bankAccount" });
models.Budget.hasMany(models.FundRequest, { foreignKey: "budget" });
models.Company.hasMany(models.FundRequest, { foreignKey: "company" });
models.User.hasMany(models.FundRequest, { foreignKey: "user", as: "User" });
models.User.hasMany(models.FundRequest, { foreignKey: "reviewer", as: "Reviewer" });
models.Vendor.hasMany(models.FundRequest, { foreignKey: "vendor" });
models.BankAccount.hasMany(models.FundRequest, { foreignKey: "bankAccount" });
models.FundRequest.hasMany(models.ApprovalRequest, {
  foreignKey: "entity_id",
  as: "FundRequestApprovalRequests",
});
models.FundRequest.belongsTo(models.Transfer, { foreignKey: "transfer" });
models.FundRequest.belongsTo(models.Transaction, { foreignKey: "transaction" });
models.Transaction.hasOne(models.FundRequest, { foreignKey: "transaction" });

models.FundRequest.belongsTo(models.Category, { foreignKey: "category" });
models.FundRequest.belongsTo(models.ApprovalRequest, {
  foreignKey: "approval_request_id",
});
models.FundRequest.belongsTo(models.CreditCard, { foreignKey: "card" });
models.FundRequest.belongsTo(models.Balance, { foreignKey: "balance" });
models.DirectDebit.belongsTo(models.Mandate, { foreignKey: "mandate" });
models.Mandate.belongsTo(models.BankAccount, { foreignKey: "bankAccount" });

models.MoreInfoLog.belongsTo(models.FundRequest, { foreignKey: "entityId", as: "MoreInfoFundRequests" });
models.FundRequest.hasMany(models.MoreInfoLog, { foreignKey: "entityId" });

models.MoreInfoLog.belongsTo(models.User, { foreignKey: "reviewer" });

models.Category.hasMany(models.Category, { as: "SubCategories", foreignKey: "parent" });
models.Category.belongsTo(models.Category, { as: "ParentCategory", foreignKey: "parent" });

models.Invoice.hasMany(models.InvoiceInstallment, { foreignKey: "invoice" });
models.InvoiceInstallment.belongsTo(models.Invoice, { foreignKey: "invoice" });
models.InvoicePayment.belongsTo(models.Invoice, { foreignKey: "invoice" });
models.Invoice.hasMany(models.InvoicePayment, { foreignKey: "invoice" });
models.InvoiceInstallment.hasOne(models.InvoicePayment, { foreignKey: "installment" });
models.InvoicePayment.belongsTo(models.InvoiceInstallment, { foreignKey: "installment" });
models.InvoicePayment.belongsTo(models.Transfer, { foreignKey: "transfer" });
models.Transfer.hasOne(models.InvoicePayment, { foreignKey: "transfer" });

models.ScheduledInvoice.belongsTo(models.Customer, { foreignKey: "customer" });
models.Customer.hasMany(models.ScheduledInvoice, { foreignKey: "customer" });
models.ScheduledInvoice.hasMany(models.Invoice, { foreignKey: "schedule" });
models.Invoice.belongsTo(models.ScheduledInvoice, { foreignKey: "schedule" });
models.ScheduledInvoice.belongsTo(models.Schedule, { foreignKey: "schedule" });
models.Schedule.hasOne(models.ScheduledInvoice, { foreignKey: "schedule" });
models.ScheduledInvoice.belongsTo(models.Status, { foreignKey: "status" });
models.ScheduledInvoice.belongsTo(models.User, { foreignKey: "user" });
models.User.hasMany(models.ScheduledInvoice, { foreignKey: "user" });
models.ScheduledInvoice.belongsTo(models.Balance, { foreignKey: "balance" });
models.Balance.hasMany(models.ScheduledInvoice, { foreignKey: "balance" });
models.ScheduledInvoice.belongsTo(models.Budget, { foreignKey: "budget" });
models.Budget.hasMany(models.ScheduledInvoice, { foreignKey: "budget" });

models.ScheduledInvoiceProduct.belongsTo(models.ScheduledInvoice, { foreignKey: "scheduledInvoice" });
models.ScheduledInvoice.hasMany(models.ScheduledInvoiceProduct, { foreignKey: "scheduledInvoice" });
models.Status.hasMany(models.ScheduledInvoiceProduct, { foreignKey: "status" });
models.ScheduledInvoiceProduct.belongsTo(models.Status, { foreignKey: "status" });
models.ScheduledInvoiceProduct.belongsTo(models.Product, { foreignKey: "product" });
models.Product.hasMany(models.ScheduledInvoiceProduct, { foreignKey: "product" });
models.ScheduledInvoice.hasMany(models.ScheduledInvoiceInstallment, { foreignKey: "scheduledInvoice" });
models.ScheduledInvoiceInstallment.belongsTo(models.ScheduledInvoice, { foreignKey: "scheduledInvoice" });
models.PaymentPlan.hasMany(models.PlanOption, { foreignKey: "plan" });
models.PaymentPlan.hasMany(models.Subscription, { foreignKey: "plan" });
models.PlanOption.belongsTo(models.PaymentPlan, { foreignKey: "plan" });

models.Subscription.hasMany(models.BillingHistory, { foreignKey: "subscription" });
models.Subscription.belongsTo(models.PaymentPlan, { foreignKey: "plan" });
models.Subscription.belongsTo(models.Company, { foreignKey: "company" });
models.BillingHistory.belongsTo(models.Subscription, { foreignKey: "subscription" });
models.BillingHistory.belongsTo(models.PaymentPlan, { foreignKey: "plan" });
models.CreditCard.hasMany(models.MccCard, { foreignKey: "card" });
models.CardRequest.hasMany(models.MccCardRequest, { foreignKey: "cardRequest" });

models.MccCard.belongsTo(models.CreditCard, { foreignKey: "card" });
models.MccCardRequest.belongsTo(models.CardRequest, { foreignKey: "cardRequest" });

models.MccCardRequest.belongsTo(models.Mcc, { foreignKey: "mcc" });
models.MccCard.belongsTo(models.Mcc, { foreignKey: "mcc" });

models.CardPolicy.belongsTo(models.CreditCard, { foreignKey: "card" });
models.CardPolicy.belongsTo(models.Policy, { foreignKey: "policy" });
models.CreditCard.hasMany(models.CardPolicy, { foreignKey: "card" });

models.CardRequest.belongsTo(models.Policy, { foreignKey: "policy" });

models.CardRequest.belongsTo(models.Balance, { foreignKey: "balance" });
models.CreditCard.belongsTo(models.Balance, { foreignKey: "balance" });
models.AccountMember.belongsTo(models.User, { foreignKey: "user" });

models.BalanceLedger.belongsTo(models.User, { foreignKey: "user" });
models.Transaction.hasMany(models.TransactionAttempt, { foreignKey: "transaction" });
models.TransactionAttempt.belongsTo(models.Transaction, { foreignKey: "transaction" });

models.PolicyCondition.belongsTo(models.Policy, { foreignKey: "policy" });
models.PolicyCondition.hasMany(models.PolicyConditionOperand, {
  foreignKey: "condition",
});
models.PolicyConditionOperand.belongsTo(models.Category, {
  foreignKey: "operand",
  as: "CategoryOperands",
});
models.PolicyConditionOperand.belongsTo(models.Vendor, {
  foreignKey: "operand",
  as: "VendorOperands",
});
models.PolicyConditionOperand.belongsTo(models.Budget, {
  foreignKey: "operand",
  as: "BudgetOperands",
});
models.PolicyConditionOperand.belongsTo(models.TransactionType, {
  foreignKey: "operand",
  as: "TypeOperands",
});
models.PolicyConditionOperand.belongsTo(models.Balance, {
  foreignKey: "operand",
  as: "AccountOperands",
});
models.PolicyConditionOperand.belongsTo(models.Team, {
  foreignKey: "operand",
  as: "TeamOperands",
});
models.PolicyConditionOperand.belongsTo(models.Company, {
  foreignKey: "operand",
  as: "CompanyOperands",
});
models.Policy.belongsTo(models.User, {
  foreignKey: "lastModifiedBy",
});
models.Policy.hasMany(models.PolicyDocument, {
  foreignKey: "policy",
  as: "documents",
});
models.Policy.hasMany(models.PolicyCondition, {
  foreignKey: "policy",
  as: "conditions",
});
models.PolicyDocument.belongsTo(models.Policy, {
  foreignKey: "policy",
});
models.PolicyDocument.belongsTo(models.Asset, {
  foreignKey: "asset",
});

models.User.hasMany(models.PolicyUserException, {
  foreignKey: "user",
  as: "exceptions",
});
models.Policy.hasMany(models.PolicyUserException, {
  foreignKey: "policy",
  as: "exceptions",
});
models.PolicyUserException.belongsTo(models.Policy, {
  foreignKey: "policy",
});
models.PolicyUserException.belongsTo(models.User, {
  foreignKey: "user",
});
models.PolicyUserException.belongsTo(models.Status, {
  foreignKey: "status",
});

models.AccountMigration.belongsTo(models.Company, { foreignKey: "company" });
models.Company.hasMany(models.AccountMigration, { foreignKey: "company" });
models.AccountMigration.belongsTo(models.Status, { foreignKey: "status" });
models.Status.hasMany(models.AccountMigration, { foreignKey: "status" });

models.CategorizationRule.belongsTo(models.Category, { foreignKey: "category" });
models.Category.hasMany(models.CategorizationRule, { foreignKey: "category" });

models.CategorizationRuleCondition.belongsTo(models.CategorizationRule, { foreignKey: "rule" });
models.CategorizationRule.hasMany(models.CategorizationRuleCondition, { foreignKey: "rule" });

models.CategorizationRule.belongsTo(models.Company, { foreignKey: "company" });
models.Company.hasMany(models.CategorizationRule, { foreignKey: "company" });

models.CategorizationRuleCondition.belongsTo(models.Company, { foreignKey: "company" });
models.Company.hasMany(models.CategorizationRuleCondition, { foreignKey: "company" });

models.CategorizationRule.belongsTo(models.User, { foreignKey: "created_by" });
models.User.hasMany(models.CategorizationRule, { foreignKey: "created_by" });

models.Transaction.hasMany(models.PolicyViolation, { foreignKey: "transaction" });
models.PolicyViolation.belongsTo(models.Transaction, { foreignKey: "transaction" });

models.Policy.hasMany(models.PolicyViolation, { foreignKey: "policy" });
models.PolicyViolation.belongsTo(models.Policy, { foreignKey: "policy" });

models.CardAssignment.belongsTo(models.CreditCard, { foreignKey: "card" });
models.CreditCard.hasMany(models.CardAssignment, { foreignKey: "card" });
models.User.hasMany(models.CardAssignment, { foreignKey: "assignedBy", as: "AssignedBy" });
models.CardAssignment.belongsTo(models.User, { foreignKey: "assignedBy", as: "AssignedBy" });
models.User.hasMany(models.CardAssignment, { foreignKey: "previousOwner", as: "PreviousOwner" });
models.CardAssignment.belongsTo(models.User, { foreignKey: "previousOwner", as: "PreviousOwner" });
models.User.hasMany(models.CardAssignment, { foreignKey: "newOwner", as: "NewOwner" });
models.CardAssignment.belongsTo(models.User, { foreignKey: "newOwner", as: "NewOwner" });
models.CardAssignment.belongsTo(models.Company, { foreignKey: "company" });
models.Company.hasMany(models.CardAssignment, { foreignKey: "company" });

// Pending Settlement
models.PendingSettlement.belongsTo(models.Status, { foreignKey: "status" });
models.Status.hasMany(models.PendingSettlement, { foreignKey: "status" });

// Add hooks

// Update Budget Available after ledger is created
models.BudgetLedger.afterCreate(async (createdLedger, options) => {
  if (!createdLedger.card) {
    const available = createdLedger.balanceAfter < 0 ? 0 : createdLedger.balanceAfter;
    await models.Budget.update(
      { available },
      { where: { id: createdLedger.budget }, ...(options.transaction && { transaction: options.transaction }) }
    );
  }
});

models.BalanceLedger.afterCreate(async (createdLedger, options) => {
  if (!createdLedger.card) {
    const amount = createdLedger.balanceAfter < 0 ? 0 : createdLedger.balanceAfter;
    await models.Balance.update(
      { amount },
      { where: { id: createdLedger.balance }, ...(options.transaction && { transaction: options.transaction }) }
    );
  }
});

models.BalanceLedger.beforeCreate((ledgerPayload) => {
  // eslint-disable-next-line no-param-reassign
  if (ledgerPayload.description) {
    try {
      ledgerPayload.description = decodeURIComponent(String(ledgerPayload.description));
    } catch (error) {
      /* empty */
    }
  }
});

models.BudgetLedger.beforeCreate((ledgerPayload) => {
  // eslint-disable-next-line no-param-reassign
  if (ledgerPayload.description) {
    try {
      ledgerPayload.description = decodeURIComponent(String(ledgerPayload.description));
    } catch (error) {
      /* empty */
    }
  }
});

models.Transaction.beforeCreate(async (transaction) => {
  // set user, if not already aspecified

  if (transaction.payer && typeof transaction.user !== "number") {
    // eslint-disable-next-line no-param-reassign
    transaction.user = transaction.payer;
  }
  if (transaction.budget) {
    const budgetPeriod = await models.BudgetPeriod.findOne({
      where: {
        status: 1, // active
        budget: transaction.budget,
      },
      attributes: ["id"],
    });
    if (budgetPeriod) {
      // eslint-disable-next-line no-param-reassign
      transaction.budgetPeriod = budgetPeriod.id;
    }
  }
});
models.Reimbursement.beforeCreate(async (reimbursement) => {
  // set user, if not already aspecified

  if (reimbursement.budget) {
    const budgetPeriod = await models.BudgetPeriod.findOne({
      where: {
        status: 1, // active
        budget: reimbursement.budget,
      },
      attributes: ["id"],
    });
    if (budgetPeriod) {
      // eslint-disable-next-line no-param-reassign
      reimbursement.budgetPeriod = budgetPeriod.id;
    }
  }
});
models.FundRequest.beforeCreate(async (request) => {
  // set user, if not already aspecified

  if (request.budget) {
    const budgetPeriod = await models.BudgetPeriod.findOne({
      where: {
        status: 1, // active
        budget: request.budget,
      },
      attributes: ["id"],
    });
    if (budgetPeriod) {
      // eslint-disable-next-line no-param-reassign
      request.budgetPeriod = budgetPeriod.id;
    }
  }
});

models.Bill.belongsTo(models.Company, { foreignKey: "company" });
models.Company.hasMany(models.Bill, { foreignKey: "company" });

models.Bill.belongsTo(models.User, { foreignKey: "user" });
models.User.hasMany(models.Bill, { foreignKey: "user" });

models.Bill.belongsTo(models.ApprovalRequest, {
  foreignKey: "approvalRequestId",
});

models.Bill.belongsTo(models.Balance, { foreignKey: "paymentMethodId", as: "balance" });
models.Bill.belongsTo(models.Budget, { foreignKey: "paymentMethodId", as: "budget" });
models.Bill.belongsTo(models.BankAccount, { foreignKey: "paymentMethodId", as: "directDebitAccount" });
models.Bill.belongsTo(models.Status, { foreignKey: "status" });
models.Bill.belongsTo(models.Category, { foreignKey: "category" });

models.Bill.hasMany(models.BillInstallment, { foreignKey: "bill" });
models.BillInstallment.belongsTo(models.Bill, { foreignKey: "bill" });

models.Bill.hasMany(models.BillProduct, { foreignKey: "bill" });
models.BillProduct.belongsTo(models.Bill, { foreignKey: "bill" });
models.BillProduct.belongsTo(models.Company, { foreignKey: "company" });

models.BillProduct.belongsTo(models.Product, { foreignKey: "product" });
models.Product.hasMany(models.BillProduct, { foreignKey: "product" });

models.Bill.hasMany(models.BillPayment, { foreignKey: "bill" });
models.BillPayment.belongsTo(models.Bill, { foreignKey: "bill" });

models.Bill.belongsTo(models.Vendor, { foreignKey: "vendor" });
models.Vendor.hasMany(models.Bill, { foreignKey: "vendor" });

models.BillInstallment.hasOne(models.BillPayment, { foreignKey: "installment" });
models.BillPayment.belongsTo(models.BillInstallment, { foreignKey: "installment" });
models.BillPayment.belongsTo(models.Transaction, { foreignKey: "transaction" });
models.Transaction.hasOne(models.BillPayment, { foreignKey: "transaction" });

models.ScheduledBill.belongsTo(models.Company, { foreignKey: "company" });
models.Company.hasMany(models.ScheduledBill, { foreignKey: "company" });

models.Bill.belongsTo(models.ScheduledBill, { foreignKey: "scheduledBill" });
models.ScheduledBill.hasMany(models.Bill, { foreignKey: "scheduledBill" });

models.ScheduledBill.belongsTo(models.Schedule, { foreignKey: "schedule" });
models.Schedule.hasOne(models.ScheduledBill, { foreignKey: "schedule" });

models.ScheduledBill.belongsTo(models.User, { foreignKey: "user" });
models.User.hasMany(models.ScheduledBill, { foreignKey: "user" });

models.ScheduledBill.belongsTo(models.Vendor, { foreignKey: "vendor" });
models.Vendor.hasMany(models.ScheduledBill, { foreignKey: "vendor" });

models.ScheduledBillProduct.belongsTo(models.ScheduledBill, { foreignKey: "scheduledBill" });
models.ScheduledBill.hasMany(models.ScheduledBillProduct, { foreignKey: "scheduledBill" });

models.ScheduledBillProduct.belongsTo(models.Product, { foreignKey: "product" });
models.Product.hasMany(models.ScheduledBillProduct, { foreignKey: "product" });

models.ScheduledBill.hasMany(models.ScheduledBillInstallment, { foreignKey: "scheduledBill" });
models.ScheduledBillInstallment.belongsTo(models.ScheduledBill, { foreignKey: "scheduledBill" });

models.ScheduledBill.belongsTo(models.Balance, { foreignKey: "paymentMethodId", as: "balance" });
models.ScheduledBill.belongsTo(models.Budget, { foreignKey: "paymentMethodId", as: "budget" });
models.ScheduledBill.belongsTo(models.BankAccount, { foreignKey: "paymentMethodId", as: "directDebitAccount" });

models.ScheduledBill.belongsTo(models.Status, { foreignKey: "status" });
models.ScheduledBill.belongsTo(models.Category, { foreignKey: "category" });

models.Bill.hasMany(models.Asset, {
  foreignKey: "entityId",
  as: "BillAssets",
});

models.ScheduledBill.hasMany(models.Asset, {
  foreignKey: "entityId",
  as: "ScheduledBillAssets",
});

models.Spend.hasMany(models.SpendTransaction, {
  foreignKey: "spend",
});
models.Spend.belongsTo(models.Vendor, {
  foreignKey: "vendor",
});
models.Spend.belongsTo(models.CreditCard, {
  foreignKey: "card",
});
models.Spend.belongsTo(models.SpendPlan, {
  foreignKey: "plan",
});
models.SpendTransaction.belongsTo(models.Transaction, {
  foreignKey: "transaction",
});
models.SpendTransaction.belongsTo(models.Spend, {
  foreignKey: "spend",
});
models.SpendAsset.belongsTo(models.Spend, {
  foreignKey: "spend",
});

models.SpendAsset.belongsTo(models.Asset, {
  foreignKey: "asset",
});

models.Spend.hasMany(models.SpendAsset, {
  foreignKey: "spend",
});

models.ApiKey.belongsTo(models.Company, {
  foreignKey: "company",
});

models.BillingAddon.belongsTo(models.Company, {
  foreignKey: "company",
});

models.BillingAddon.belongsTo(models.User, {
  foreignKey: "user",
  as: "inviter",
});

models.Invoice.belongsTo(models.InvoiceTemplate, {
  foreignKey: "template",
});

models.ScheduledInvoice.belongsTo(models.InvoiceTemplate, {
  foreignKey: "template",
});

models.CompanyPreference.belongsTo(models.Company, {
  foreignKey: "company",
});

models.LedgerIdentity.belongsTo(models.Company, {
  foreignKey: "company",
});

models.Bank.belongsTo(models.Status, { foreignKey: "status" });

models.Budget.hasMany(models.BudgetPeriod, { as: "periods" });
models.Budget.hasMany(models.Budget, { as: "subBudgets", foreignKey: "parent" });

models.Tax.hasMany(models.TaxVersion, { foreignKey: "tax", as: "versions" });
models.TaxVersion.belongsTo(models.Tax, { foreignKey: "tax" });
models.TaxVersion.belongsTo(models.User, { foreignKey: "created_by", as: "creator" });
models.Tax.belongsTo(models.User, { foreignKey: "created_by", as: "creator" });
models.Tax.belongsTo(models.Company, { foreignKey: "company", as: "companyDetails" });

// Tax Group Associations
models.TaxGroup.belongsTo(models.Company, { foreignKey: "company", as: "companyDetails" });
models.TaxGroup.belongsTo(models.User, { foreignKey: "user", as: "creator" });
models.TaxGroup.hasMany(models.TaxGroupTax, { foreignKey: "tax_group", as: "taxes" });

models.TaxGroupTax.belongsTo(models.TaxGroup, { foreignKey: "tax_group" });
models.TaxGroupTax.belongsTo(models.Tax, { foreignKey: "tax" });
models.TaxGroupTax.belongsTo(models.User, { foreignKey: "user", as: "creator" });

// Tax Application Associations
models.TaxApplication.belongsTo(models.TaxVersion, { foreignKey: "tax_version", as: "taxVersion" });
models.TaxApplication.belongsTo(models.User, { foreignKey: "user", as: "creator" });
models.TaxVersion.hasMany(models.TaxApplication, { foreignKey: "tax_version", as: "applications" });

module.exports = models;
