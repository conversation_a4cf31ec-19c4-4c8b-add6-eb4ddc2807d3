const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");
const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => "idv_" + nanoid(),
    },
    type: Sequelize.STRING,
    firstName: Sequelize.STRING,
    lastName: Sequelize.STRING,
    dob: Sequelize.DATE,
    percentageOwned: Sequelize.DOUBLE,
    email: Sequelize.STRING,
    company: Sequelize.INTEGER,
    metadata: Sequelize.JSON,
    address: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    phoneNumber: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    document: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.UNVERIFIED,
    },
    nextReminder: {
      type: Sequelize.DATE,
      allowNull: true,
    },
    graphIdentifier: {
      type: Sequelize.STRING,
      allowNull: true,
    },

    externalIdentifier: {
      type: Sequelize.STRING,
      allowNull: true,
    },

    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("Individual", schema, { timestamps: false });
};
