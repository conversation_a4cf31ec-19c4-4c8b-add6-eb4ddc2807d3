// const { customAlphabet } = require("nanoid");
const {STATUSES} = require("./status");
// const nanoid = customAlphabet('0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp', 17);
const randomstring = require("randomstring");


module.exports = (connection, Sequelize) => {
    const schema = {
        code: {
            type: Sequelize.STRING,
            defaultValue: 'idt_' + randomstring.generate({ length: 17, capitalization: 'lowercase', charset: 'alphanumeric' }),
        },
        name: Sequelize.STRING
    };
    return connection.define('Industry', schema,
        { timestamps: false });
};
