const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `inv_${nanoid()}`,
    },
    company: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },

    user: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },

    customer: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },

    invoiceId: {
      type: Sequelize.STRING,
      allowNull: true,
    },

    currency: {
      type: Sequelize.STRING,
      defaultValue: "NGN",
      allowNull: false,
    },

    terms: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },

    approval_request_id: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },

    description: Sequelize.STRING,

    amount: Sequelize.BIGINT,

    recurring: Sequelize.INTEGER,

    due_date: Sequelize.DATE,

    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.PENDING,
    },

    title: {
      type: Sequelize.STRING,
      allowNull: true,
    },

    processing_fee: Sequelize.INTEGER,

    vat: Sequelize.DOUBLE,

    vatAmount: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },

    shouldSplitTax: {
      type: Sequelize.TINYINT,
      allowNull: true,
    },

    discount: Sequelize.INTEGER,

    discount_type: Sequelize.STRING,

    paidOn: Sequelize.DATE,

    balance: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    template: {
      type: Sequelize.INTEGER,
      defaultValue: 1, // default template on the system
    },

    budget: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: {
          tableName: "Budgets",
        },
        key: "id",
      },
    },

    zohoIdentifier: {
      type: Sequelize.STRING,
      allowNull: true,
    },

    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
      onUpdate: Sequelize.NOW,
    },
  };
  return connection.define("Invoice", schema, { timestamps: false });
};
