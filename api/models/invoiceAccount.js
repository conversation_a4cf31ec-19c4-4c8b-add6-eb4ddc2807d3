const { customAlphabet } = require("nanoid");
const {STATUSES} = require("./status");
const nanoid = customAlphabet('0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp', 17);


module.exports = (connection, Sequelize) => {
    const schema = {
        code: {
            type: Sequelize.STRING,
            defaultValue: () => 'ina_' + nanoid()
        },

        invoice: {
            type: Sequelize.DataTypes.INTEGER,
            allowNull: false
        },

        externalIdentifier: {
            type: Sequelize.STRING,
            allowNull: false
        },

        accountNumber: {
            type: Sequelize.STRING,
            allowNull: false
        },

        accountName: {
            type: Sequelize.STRING,
            allowNull: false
        },

        currency: {
            type: Sequelize.STRING,
            defaultValue: 'NGN',
            allowNull: true
        },

        bankName: Sequelize.STRING,

        nipCode: Sequelize.STRING,

        issuer: Sequelize.INTEGER,

        status: {
            type: Sequelize.INTEGER,
            defaultValue: STATUSES.ACTIVE
        },

        created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.NOW,
          },

          updated_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.NOW,
            onUpdate: Sequelize.NOW,
          },
    };
    return connection.define('InvoiceAccount', schema,
        { timestamps: false });
};