const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");
const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `iin_${nanoid()}`,
    },

    invoice: {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: false,
    },

    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.PENDING,
    },

    amount: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },

    percentage: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },

    type: {
      type: Sequelize.STRING,
      allowNull: false,
    },

    due_date: {
      type: Sequelize.DATE,
      allowNull: true,
    },

    paid_on: {
      type: Sequelize.DATE,
      allowNull: true,
    },

    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
    },

    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
      onUpdate: Sequelize.NOW,
    },
  };
  return connection.define("InvoiceInstallment", schema, { timestamps: false });
};
