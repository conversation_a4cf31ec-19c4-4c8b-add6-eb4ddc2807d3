const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");
const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `inp_${nanoid()}`,
    },

    invoice: {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: false,
    },

    installment: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },

    company: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },

    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.PENDING,
    },

    zohoIdentifier: {
      type: Sequelize.STRING,
      allowNull: true,
    },

    transfer: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },

    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
    },

    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
      onUpdate: Sequelize.NOW,
    },
  };
  return connection.define("InvoicePayment", schema, { timestamps: false });
};
