const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");
const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => "ivp_" + nanoid(),
    },

    company: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },

    amount: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },

    currency: {
      type: Sequelize.STRING,
      allowNull: false,
      defaultValue: "NGN",
    },

    invoice: {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: false,
    },

    product: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },

    quantity: Sequelize.INTEGER,

    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.ACTIVE,
    },

    discount: Sequelize.INTEGER,

    discount_type: Sequelize.STRING,

    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },

    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("InvoiceProduct", schema, { timestamps: false });
};
