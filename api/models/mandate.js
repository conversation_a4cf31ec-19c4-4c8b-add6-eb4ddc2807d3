const { customAlphabet } = require("nanoid");
const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `mnd_${nanoid()}`,
    },
    externalIdentifier: Sequelize.STRING,
    externalCustomerId: Sequelize.STRING,
    company: {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: false,
    },
    amount: Sequelize.INTEGER,
    bankAccount: Sequelize.INTEGER,
    issuer: Sequelize.INTEGER,
    signature: Sequelize.STRING,
    status: Sequelize.STRING,
    mandateType: Sequelize.STRING,
    debitType: Sequelize.STRING,
    isReadyForDebit: { type: Sequelize.TINYINT, defaultValue: false },
    reference: {
      type: Sequelize.STRING,
      defaultValue: () => nanoid(),
    },
    startDate: {
      type: Sequelize.DATE,
      allowNull: false,
    },
    endDate: {
      type: Sequelize.DATE,
      allowNull: false,
    },
    created_at: {
      type: Sequelize.DATE,
      allowNull: true,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      allowNull: true,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("Mandate", schema, { timestamps: false });
};

module.exports.MANDATE_TYPES = {
  E_MANDATE: "emandate",
  SIGNED: "signed",
};

module.exports.MANDATE_STATUS = {
  PENDING: "pending",
  GRANTED: "granted",
  FAILED: "failed",
};
