const { STATUSES } = require("./status");

module.exports = (sequelize, DataTypes) => {
  const MccCardRequest = sequelize.define(
    "MccCard",
    {
      mcc: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      card: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      status: {
        type: DataTypes.INTEGER,
        defaultValue: STATUSES.ACTIVE,
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize.literal("CURRENT_TIMESTAMP"),
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize.literal("CURRENT_TIMESTAMP"),
        onUpdate: sequelize.literal("CURRENT_TIMESTAMP"),
      },
    },
    {
      tableName: "MccCreditCards",
      timestamps: false, // Disable timestamps
    }
  );

  return MccCardRequest;
};
