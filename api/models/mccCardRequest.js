const { STATUSES } = require("./status");

module.exports = (sequelize, DataTypes) => {
  const MccCardRequest = sequelize.define(
    "MccCardRequest",
    {
      mcc: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      cardRequest: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      status: {
        type: DataTypes.INTEGER,
        defaultValue: STATUSES.ACTIVE,
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize.literal("CURRENT_TIMESTAMP"),
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize.literal("CURRENT_TIMESTAMP"),
        onUpdate: sequelize.literal("CURRENT_TIMESTAMP"),
      },
    },
    {
      tableName: "MccCardRequests",
      timestamps: false, // Disable timestamps
    }
  );

  return MccCardRequest;
};
