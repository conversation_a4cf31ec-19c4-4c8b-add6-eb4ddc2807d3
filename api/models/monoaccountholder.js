const { customAlphabet } = require("nanoid");
const {STATUSES} = require("./status");
const nanoid = customAlphabet('0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp', 17);

module.exports = (connection, Sequelize) => {
    const schema = {
        type: Sequelize.STRING,
        holderId: Sequelize.STRING,
        main: {
            type: Sequelize.INTEGER,
            allowNull: true,
        },
        user: {
            type: Sequelize.INTEGER,
            allowNull: true,
        },
        company: {
            type: Sequelize.INTEGER,
            allowNull: true,
        },
        created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        }
    };
    return connection.define('MonoAccountHolder', schema,
        { timestamps: false });
};
