const { customAlphabet } = require("nanoid");
const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `inf_${nanoid()}`,
    },
    entityType: Sequelize.STRING,
    entityId: Sequelize.INTEGER,
    company: {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: false,
    },
    reviewer: {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: false,
    },
    info: Sequelize.STRING,
    created_at: {
      type: Sequelize.DATE,
      allowNull: true,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      allowNull: true,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("MoreInfoLog", schema, { timestamps: false });
};
