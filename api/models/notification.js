const { customAlphabet } = require("nanoid");
const nanoid = customAlphabet('0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp', 17);

module.exports = (connection, Sequelize) => {
    const schema = {
        code: {
            type: Sequelize.STRING,
            allowNull: false,
            defaultValue: () => 'ntf_' + nanoid()
        },
        type: Sequelize.STRING, // 'alert', 'request', 'info',
        badge: Sequelize.STRING,
        title: { 
            type: Sequelize.STRING,
            allowNull: false
        },
        body: { 
            type: Sequelize.STRING,
            allowNull: false
        },
        reference_code: {
            type: Sequelize.STRING,
            allowNull: true
        },
        seen: {
            type: Sequelize.TINYINT,
            defaultValue: 0,
        },
        sent: {
            type: Sequelize.TINYINT,
            defaultValue: 0,
        },
        toAdmins: {
            type: Sequelize.TINYINT,
            defaultValue: 0,
        },
        user: Sequelize.INTEGER,
        table: Sequelize.STRING,
        table_id: Sequelize.INTEGER,
        company: {
            type: Sequelize.INTEGER,
            allowNull: true
        },
        created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updated_at: {
            type: Sequelize.DATE,
            allowNull: true,
            onUpdate: Sequelize.literal('CURRENT_TIMESTAMP')
        }
    };
    return connection.define('Notification', schema, { timestamps: false });

};
