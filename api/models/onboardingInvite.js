const { custom<PERSON>lphabet } = require("nanoid");
const {STATUSES} = require("./status");
const nanoid = customAlphabet('0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp', 17);


module.exports = (connection, Sequelize) => {
    const schema = {
        code: {
            type: Sequelize.STRING,
            defaultValue: () => 'oiv_' + nanoid()
        },
        name: {
            type: Sequelize.STRING,
            allowNull: false
        },
        email: {
            type: Sequelize.STRING,
            allowNull: false
        },
        company: Sequelize.INTEGER,
        status: {
            type: Sequelize.INTEGER,
            defaultValue: STATUSES.INVITED,
            allowNull: false,
        },
        hash: {
            type: Sequelize.STRING,
            allowNull: false
        },
        note: {
            type: Sequelize.STRING,
            allowNull: true
        },
        fields: {
            type: Sequelize.STRING,
            allowNull: true
        },
        created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updated_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            onUpdate: Sequelize.literal('CURRENT_TIMESTAMP')
        }
    };
    return connection.define('OnboardingInvites', schema,
        { timestamps: false });
};
