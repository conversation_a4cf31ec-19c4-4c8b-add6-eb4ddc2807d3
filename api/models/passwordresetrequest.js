const addMinutes = require('date-fns/addMinutes');

module.exports = (connection, Sequelize) => {
    const schema = {
        hash: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV4,
            primaryKey: true,
        },
        expirationDate: {
            type: Sequelize.DATE,
            defaultValue: () => {
                return addMinutes(new Date(), 15);
            }
        },
        created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updated_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            onUpdate: Sequelize.literal('CURRENT_TIMESTAMP')
        }
    };
    return connection.define('PasswordResetRequest', schema,
        { timestamps: false });
};
