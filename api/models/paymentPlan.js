const { STATUSES } = require("./status");
const { customAlphabet } = require("nanoid");
const { DataTypes } = require("sequelize");
const nanoid = customAlphabet("123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => "pln_" + nanoid(),
    },
    name: Sequelize.STRING,
    description: Sequelize.STRING,
    configuration: DataTypes.JSON,
    externalIdentifier: Sequelize.STRING,
    currency: Sequelize.STRING,
    amount: Sequelize.INTEGER,
    company: Sequelize.INTEGER,
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.ACTIVE,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("PaymentPlan", schema, { timestamps: false });
};
