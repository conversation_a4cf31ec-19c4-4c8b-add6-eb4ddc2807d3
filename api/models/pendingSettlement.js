const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");
const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `pst_${nanoid()}`,
    },

    amount: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },

    reference: {
      type: Sequelize.STRING,
      allowNull: false,
    },

    company: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: {
          tableName: "Companies",
        },
        key: "id",
      },
    },

    entity: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },

    entityType: {
      type: Sequelize.STRING,
      allowNull: false,
    },

    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.PENDING,
      allowNull: false,
    },

    externalIdentifier: {
      type: Sequelize.STRING,
      allowNull: true,
    },

    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },

    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("PendingSettlement", schema, { timestamps: false });
};
