const { customAlphabet } = require("nanoid");
const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => "phn_" + nanoid(),
    },
    countryCode: Sequelize.INTEGER,
    localFormat: Sequelize.STRING,
    internationalFormat: Sequelize.STRING,
    isVerified: {
      type: Sequelize.TINYINT,
      defaultValue: 0,
    },
    isMobileWallet: {
      type: Sequelize.TINYINT,
      defaultValue: 0,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("PhoneNumber", schema, { timestamps: false });
};
