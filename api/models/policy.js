const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");
const nanoid = customAlphabet("123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => "pol_" + nanoid(),
    },
    company: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    name: Sequelize.STRING,
    strict: Sequelize.TINYINT,
    status: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: STATUSES.ACTIVE,
    },
    description: Sequelize.STRING,
    type: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    currency: Sequelize.STRING,
    minAmount: Sequelize.INTEGER,
    maxAmount: Sequelize.INTEGER,
    receiptPolicyCondition: Sequelize.STRING(20),
    receiptPolicyValue: Sequelize.INTEGER,
    requiresBudget: Sequelize.TINYINT,
    requiresDescription: Sequelize.TINYINT,
    requiresCategory: Sequelize.TINYINT,
    lastModifiedBy: Sequelize.INTEGER,
    frequency: {
      type: Sequelize.ENUM,
      values: ["monthly", "daily", "weekly", "yearly", "weekends"],
    },
    period: Sequelize.STRING,
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("Policy", schema, { timestamps: false });
};
