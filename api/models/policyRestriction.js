const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");
const nanoid = customAlphabet("123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => "ppt_" + nanoid(),
    },
    policy: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    policy_type: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    company: Sequelize.INTEGER,
    status: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: STATUSES.ACTIVE,
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("PolicyRestriction", schema, { timestamps: false });
};
