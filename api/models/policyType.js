const { customAlphabet } = require("nanoid");
const {STATUSES} = require("./status");
const nanoid = customAlphabet('123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp', 17);

module.exports = (connection, Sequelize) => {
    const schema = {
        code: {
            type: Sequelize.STRING,
            defaultValue: () => 'pty_' + nanoid()
        },
        name: Sequelize.STRING,
        description: Sequelize.STRING,
        strict: Sequelize.TINYINT,
        created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updated_at: {
            type: Sequelize.DATE,
            onUpdate: Sequelize.literal('CURRENT_TIMESTAMP')
        }
    };
    return connection.define('PolicyType', schema,
        { timestamps: false });
};
