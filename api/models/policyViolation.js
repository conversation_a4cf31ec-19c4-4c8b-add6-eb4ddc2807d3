const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");
const nanoid = customAlphabet("123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => "pvo_" + nanoid(),
    },
    policy: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    transaction: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    resolved: {
      type: Sequelize.TINYINT,
      allowNull: false,
      defaultValue: false,
    },
    resolvedBy: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("PolicyViolation", schema, { timestamps: false });
};
