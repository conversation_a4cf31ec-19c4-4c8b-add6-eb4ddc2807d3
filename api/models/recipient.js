const { customAlphabet } = require("nanoid");
const nanoid = customAlphabet('0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp', 17);
const { STATUSES } = require('./status');

module.exports = (connection, Sequelize) => {
    const schema = {
        code: {
            type: Sequelize.STRING,
            defaultValue: () => 'rcp_' + nanoid()
        },
        firstName: {
            type: Sequelize.STRING,
            required: true,
        },
        lastName: Sequelize.STRING,
        middleName: {
            type: Sequelize.STRING,
        },
        email: Sequelize.STRING,
        account: Sequelize.STRING,
        bankName: {
            type: Sequelize.TINYINT,
            defaultValue: 0,
        },
        bankCode: {
            type: Sequelize.STRING,
        },
        status: {
            type: Sequelize.INTEGER,
            defaultValue: STATUSES.ACTIVE,
        },
        requireApproval: {
            type: Sequelize.TINYINT,
            defaultValue: 0,
        },
        company: {
            type: Sequelize.INTEGER,
            allowNull: true
        },
        created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updated_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            onUpdate: Sequelize.literal('CURRENT_TIMESTAMP')
        }
    };
    return connection.define('Recipient', schema,
        { timestamps: false });
};
