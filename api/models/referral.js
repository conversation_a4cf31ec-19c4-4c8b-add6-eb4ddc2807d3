const { randomCode, STATUSES } = require("./utils");

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `rfl_${randomCode()}`,
    },
    referrer: Sequelize.INTEGER,
    referralCode: Sequelize.INTEGER,
    campaign: Sequelize.INTEGER,
    email: Sequelize.STRING,
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.PENDING,
    },
    claimedOn: Sequelize.DATE,
    validUntil: Sequelize.DATE,
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("Referral", schema, { timestamps: false });
};
