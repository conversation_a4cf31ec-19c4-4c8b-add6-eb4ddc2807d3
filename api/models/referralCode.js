const { randomCode } = require("./utils");

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `rfc_${randomCode(8)}`,
    },
    type: Sequelize.INTEGER,
    owner: Sequelize.INTEGER,
    ownerType: Sequelize.ENUM("user", "partner", "campaign"),
    campaign: Sequelize.INTEGER,
    plan: Sequelize.INTEGER,
    validUntil: Sequelize.DATE,
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("ReferralCode", schema, { timestamps: false });
};
