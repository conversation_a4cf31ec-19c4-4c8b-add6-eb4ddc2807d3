const { customAlphabet } = require("nanoid");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);
const { STATUSES } = require("./status");

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `rbs_${nanoid()}`,
    },
    user: {
      type: Sequelize.INTEGER,
      required: true,
    },
    budget: Sequelize.INTEGER,
    budgetPeriod: Sequelize.INTEGER,
    currency: Sequelize.STRING,
    amount: Sequelize.BIGINT,
    receipt: Sequelize.INTEGER,
    reviewer: Sequelize.INTEGER,
    transaction: Sequelize.INTEGER,
    category: Sequelize.INTEGER,
    vendor: Sequelize.INTEGER,
    description: Sequelize.STRING,
    note: Sequelize.STRING,
    expense_date: Sequelize.DATE,
    balance: Sequelize.INTEGER,
    team: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.PENDING,
    },
    company: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    reviewed_on: {
      type: Sequelize.DATE,
      defaultValue: null,
    },
    needsMoreInfo: {
      type: Sequelize.TINYINT,
      defaultValue: 0,
    },
    moreInfoDescription: Sequelize.STRING,
    directDebitId: Sequelize.INTEGER,
    deadLine: {
      type: Sequelize.DATE,
      allowNull: true,
      defaultValue: null,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("Reimbursement", schema, { timestamps: false });
};
