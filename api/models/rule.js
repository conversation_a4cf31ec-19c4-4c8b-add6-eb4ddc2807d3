const { customAlphabet } = require('nanoid');
const { STATUSES } = require('./status');

const nanoid = customAlphabet('123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp', 17);

module.exports = (connection, Sequelize) => {
	const schema = {
		code: {
			type: Sequelize.STRING,
			defaultValue: () => `rul_${nanoid()}`,
		},
		name: Sequelize.STRING,
		status: {
			type: Sequelize.INTEGER,
			defaultValue: STATUSES.ACTIVE,
		},
		created_by: {
			type: Sequelize.INTEGER,
			allowNull: false,
		},
		company: Sequelize.INTEGER,
		created_at: {
			type: Sequelize.DATE,
			defaultValue: Sequelize.NOW,
		},
		updated_at: {
			type: Sequelize.DATE,
			defaultValue: Sequelize.NOW,
			onUpdate: Sequelize.NOW,
		},
	};
	return connection.define('ApprovalRule', schema, { timestamps: false });
};
