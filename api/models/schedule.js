const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");
const CronParser = require("cron-parser");

const nanoid = customAlphabet("123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `sch_${nanoid()}`,
    },
    cron_id: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    cron_expression: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.ACTIVE,
    },
    nextExecutionDate: {
      type: Sequelize.DATE,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
      onUpdate: Sequelize.NOW,
    },
  };
  return connection.define("Schedules", schema, { timestamps: false });
};
