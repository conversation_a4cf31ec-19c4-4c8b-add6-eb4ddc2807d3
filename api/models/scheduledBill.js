const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false,
    },
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `sbl_${nanoid()}`,
    },
    schedule: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    user: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    vendor: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    balanceDue: {
      type: Sequelize.BIGINT,
      allowNull: true,
    },
    currency: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    amount: {
      type: Sequelize.BIGINT,
      allowNull: true,
    },
    minAmountDue: {
      type: Sequelize.BIGINT,
      allowNull: true,
    },
    dueDate: {
      type: Sequelize.DATE,
      allowNull: true,
    },
    lastPaymentDate: {
      type: Sequelize.DATE,
      allowNull: true,
    },
    company: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    reference: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    status: {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: STATUSES.ACTIVE,
    },
    category: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    withholdTaxes: {
      type: Sequelize.TINYINT,
      allowNull: true,
    },
    withHeldTax: {
      type: Sequelize.BIGINT,
      allowNull: true,
    },
    taxAccount: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    vat: {
      type: Sequelize.DOUBLE,
      allowNull: true,
    },
    discount: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    discount_type: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    shouldNotify: {
      type: Sequelize.BOOLEAN,
      allowNull: true,
    },
    approvalRequestId: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    paymentMethod: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    paymentMethodId: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    reason: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    terms: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    sendForApproval: {
      type: Sequelize.TINYINT,
      allowNull: true,
    },
    vatAmount: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    type: {
      type: Sequelize.STRING,
      defaultValue: "recurring",
    },
    expiryDate: {
      type: Sequelize.DATE,
      allowNull: true,
    },
    startDate: {
      type: Sequelize.DATE,
      allowNull: true,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };

  return connection.define("ScheduledBill", schema, { timestamps: false });
};
