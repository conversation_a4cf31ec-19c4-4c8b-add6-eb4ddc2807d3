const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false,
    },
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `sbp_${nanoid()}`,
    },
    company: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    scheduledBill: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    discount: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    discount_type: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    product: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    quantity: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    unitPrice: {
      type: Sequelize.BIGINT,
      allowNull: true,
    },
    currency: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    status: {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: STATUSES.ACTIVE,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };

  return connection.define("ScheduledBillProduct", schema, { timestamps: false });
};
