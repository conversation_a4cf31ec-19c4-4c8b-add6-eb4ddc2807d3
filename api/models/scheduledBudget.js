const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet("123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `scb_${nanoid()}`,
    },
    budget: {
      type: Sequelize.INTEGER,
      defaultValue: null,
    },
    company: Sequelize.INTEGER,
    schedule_id: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    reason: Sequelize.STRING,
    type: Sequelize.STRING,
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.ACTIVE,
    },
    start_date: {
      type: Sequelize.DATE,
      allowNull: true,
    },
    expiry_date: {
      type: Sequelize.DATE,
      allowNull: true,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
      onUpdate: Sequelize.NOW,
    },
  };
  return connection.define("ScheduledBudgets", schema, { timestamps: false });
};
