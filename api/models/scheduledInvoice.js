const { literal } = require("sequelize");
const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet("123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `sci_${nanoid()}`,
    },

    company: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },

    user: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },

    currency: {
      type: Sequelize.STRING,
      allowNull: false,
    },

    invoiceId: {
      type: Sequelize.STRING,
      allowNull: true,
    },

    description: {
      type: Sequelize.STRING,
      allowNull: true,
    },

    amount: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },

    vat: {
      type: Sequelize.DOUBLE,
      allowNull: true,
    },

    discount: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },

    balance: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },

    budget: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: {
          tableName: "Budgets",
        },
        key: "id",
      },
    },

    discountType: {
      type: Sequelize.STRING,
      allowNull: true,
    },

    vatAmount: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },

    customer: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },

    title: {
      type: Sequelize.STRING,
      allowNull: true,
    },

    expiryDate: {
      type: Sequelize.DATE,
      allowNull: true,
    },

    startDate: {
      type: Sequelize.DATE,
      allowNull: true,
    },

    terms: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },

    schedule: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },

    status: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: STATUSES.ACTIVE,
    },

    type: {
      type: Sequelize.STRING,
      defaultValue: "recurring",
    },

    shouldSplitTax: {
      type: Sequelize.TINYINT,
      allowNull: true,
    },

    meta: {
      type: Sequelize.DataTypes.JSON,
      allowNull: true,
    },

    template: {
      type: Sequelize.INTEGER,
      defaultValue: 1, // default template on the system
    },
    created_at: {
      type: Sequelize.DATE,
      allowNull: true,
      defaultValue: literal("CURRENT_TIMESTAMP"),
    },

    updated_at: {
      type: Sequelize.DATE,
      allowNull: true,
      onUpdate: Sequelize.NOW,
      defaultValue: literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("ScheduledInvoice", schema, { timestamps: false });
};
