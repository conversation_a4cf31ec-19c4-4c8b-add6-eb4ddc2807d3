const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `sip_${nanoid()}`,
    },

    scheduledInvoice: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },

    product: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },

    unitPrice: {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: true,
    },

    quantity: Sequelize.INTEGER,

    discount: Sequelize.INTEGER,

    discountType: Sequelize.STRING,

    status: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: STATUSES.ACTIVE,
    },

    currency: {
      type: Sequelize.STRING,
      allowNull: false,
      defaultValue: "NGN",
    },

    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },

    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("ScheduledInvoiceProduct", schema, { timestamps: false });
};
