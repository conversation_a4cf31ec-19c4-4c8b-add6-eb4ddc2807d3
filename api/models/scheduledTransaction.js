const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet("123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `stc_${nanoid()}`,
    },
    currency: Sequelize.STRING,
    user: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    amount: Sequelize.BIGINT,
    budget: {
      type: Sequelize.INTEGER,
      defaultValue: null,
    },
    company: Sequelize.INTEGER,
    schedule_id: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    recipient: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    recipient_type: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    reason: Sequelize.STRING,
    type: Sequelize.STRING,
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.ACTIVE,
    },
    balance: Sequelize.INTEGER,
    category: Sequelize.INTEGER,
    processor_fee: Sequelize.INTEGER,
    bujeti_fee: Sequelize.INTEGER,
    bank_account: Sequelize.INTEGER,
    narration: Sequelize.STRING,
    description: Sequelize.STRING,
    frequency: Sequelize.STRING,
    start_date: {
      type: Sequelize.DATE,
    },
    expiry_date: {
      type: Sequelize.DATE,
      allowNull: true,
    },
    directDebitId: Sequelize.INTEGER,
    type: Sequelize.STRING,
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
      onUpdate: Sequelize.NOW,
    },
  };
  return connection.define("ScheduledTransactions", schema, { timestamps: false });
};
