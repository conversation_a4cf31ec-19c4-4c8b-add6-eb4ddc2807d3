
module.exports = (connection, Sequelize) => {
    const schema = {
        key: Sequelize.STRING,
        value: Sequelize.JSON,
        created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updated_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            onUpdate: Sequelize.literal('CURRENT_TIMESTAMP')
        }
    };
    return connection.define('Setting', schema,
        { timestamps: false });
};
