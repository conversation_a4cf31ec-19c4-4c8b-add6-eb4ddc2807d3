const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");
const nanoid = customAlphabet(
  "0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp",
  17
);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => "stm_" + nanoid(),
    },

    initiatedBy: {
        type: Sequelize.INTEGER,
        allowNull: false
    },

    company: {
        type: Sequelize.INTEGER,
        allowNull: false
    },

    amount: {
        type: Sequelize.INTEGER,
        allowNull: false
    },

    currency: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: "NGN"
    },

    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.PENDING,
    },

    reference: {
        type: Sequelize.STRING,
        allowNull: true,
    },

    provider: {
        type: Sequelize.INTEGER,
        allowNull: false
    },

    settlementType: {
        type: Sequelize.ENUM("manual", "auto"),
        defaultValue: "manual",
    },

    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("Settlement", schema, { timestamps: false });
};
