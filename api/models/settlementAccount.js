const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");
const nanoid = customAlphabet(
  "0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp",
  17
);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => "sac_" + nanoid(),
    },

    company: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },

    addedBy: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },

    bankAccount: {
        type: Sequelize.INTEGER,
        allowNull: false,
    },

    status: {
        type: Sequelize.INTEGER,
        allowNull: false,
    },

    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },

    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("SettlementAccount", schema, { timestamps: false });
};
