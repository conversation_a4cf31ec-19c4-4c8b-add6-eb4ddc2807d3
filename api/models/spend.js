const { custom<PERSON><PERSON>phabet } = require("nanoid");
const { STATUSES } = require("./status");
const { METHODS } = require("../mocks/constants.mock");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `spd_${nanoid()}`,
    },
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.ACTIVE,
    },
    company: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    plan: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    method_id: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    method: {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: METHODS.CARD,
    },
    currency: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    amount: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    billingPeriod: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    contactEmail: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    vendor: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    card: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    seats: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    isFreeTrial: {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
    },
    renewalDate: {
      type: Sequelize.DATE,
      allowNull: true,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("Spend", schema, { timestamps: false });
};
