module.exports = (connection, Sequelize) => {
  const schema = {
    spend: Sequelize.INTEGER,
    asset: Sequelize.INTEGER,
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("SpendAsset", schema, { timestamps: false });
};
