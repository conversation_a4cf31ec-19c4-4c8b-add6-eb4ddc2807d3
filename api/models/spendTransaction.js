const { custom<PERSON><PERSON>phabet } = require("nanoid");
const { STATUSES } = require("./status");
const { METHODS } = require("../mocks/constants.mock");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `sptrx_${nanoid()}`,
    },
    amount: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    currency: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    transaction: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    spend: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("SpendTransaction", schema, { timestamps: false });
};
