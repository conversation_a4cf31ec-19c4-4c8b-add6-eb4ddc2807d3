const { custom<PERSON><PERSON>phabet } = require("nanoid");
const { STATUSES } = require("./status");
const { METHODS } = require("../mocks/constants.mock");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `sub_${nanoid()}`,
    },
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.ACTIVE,
    },
    company: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    plan: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    method_id: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    method: {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: METHODS.BUDGET,
    },
    currency: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    amount: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    billingPeriod: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    contactEmail: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    reason: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    isFreeTrial: {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
    },
    address: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    state: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    country: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    companyName: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    city: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    firstName: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    lastName: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    additionalSeats: {
      type: Sequelize.BIGINT,
      allowNull: true,
      defaultValue: 0,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("Subscription", schema, { tableName: "Subscriptions", timestamps: false });
};
