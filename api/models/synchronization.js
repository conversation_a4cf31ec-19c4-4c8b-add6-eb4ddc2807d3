const { customAlphabet } = require("nanoid");

const nanoid = customAlphabet("123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `syn_${nanoid()}`,
    },
    company: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    entity: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    entity_id: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    platform_id: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    platform: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
      onUpdate: Sequelize.NOW,
    },
  };
  return connection.define("Synchronization", schema, { timestamps: false });
};
