const { Model } = require("sequelize");
const { customAlphabet } = require("nanoid");
const { TAX_APPLICABLE_TYPES } = require("../constants/taxTypes");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (sequelize, Sequelize) => {
  class TaxApplication extends Model {}

  TaxApplication.init(
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      code: {
        type: Sequelize.STRING(21),
        allowNull: false,
        unique: true,
        defaultValue: () => `tap_${nanoid()}`,
      },
      applicable_type: {
        type: Sequelize.ENUM(Object.values(TAX_APPLICABLE_TYPES)),
        allowNull: false,
      },
      applicable_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      tax_version: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: {
            tableName: "TaxVersions",
          },
          key: "id",
        },
      },
      base_amount: {
        type: Sequelize.BIGINT,
        allowNull: false,
      },
      tax_amount: {
        type: Sequelize.BIGINT,
        allowNull: false,
      },
      final_amount: {
        type: Sequelize.BIGINT,
        allowNull: false,
      },
      sequence_number: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      user: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: {
            tableName: "Users",
          },
          key: "id",
        },
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      metadata: {
        type: Sequelize.JSON,
        allowNull: true,
      },
    },
    {
      sequelize,
      modelName: "TaxApplication",
      tableName: "TaxApplications",
      timestamps: false,
      indexes: [
        {
          fields: ["code"],
        },
        {
          fields: ["applicable_type", "applicable_id"],
        },
        {
          unique: true,
          fields: ["applicable_type", "applicable_id", "tax_version"],
        },
        {
          fields: ["applicable_type", "applicable_id", "sequence_number"],
        },
      ],
    }
  );

  return TaxApplication;
};
