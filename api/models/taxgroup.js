const { Model } = require("sequelize");
const { customAlphabet } = require("nanoid");
const { TAX_CALCULATION_TYPES } = require("../constants/taxTypes");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (sequelize, Sequelize) => {
  class TaxGroup extends Model {}

  TaxGroup.init(
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      code: {
        type: Sequelize.STRING(21),
        allowNull: false,
        unique: true,
        defaultValue: () => `tgp_${nanoid()}`,
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      calculation_type: {
        type: Sequelize.ENUM(Object.values(TAX_CALCULATION_TYPES)),
        allowNull: false,
        defaultValue: TAX_CALCULATION_TYPES.SEQUENTIAL,
      },
      company: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: {
            tableName: "Companies",
          },
          key: "id",
        },
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
      },
      user: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: {
            tableName: "Users",
          },
          key: "id",
        },
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      }
    },
    {
      sequelize,
      modelName: "TaxGroup",
      tableName: "TaxGroups",
      timestamps: false
    }
  );

  return TaxGroup;
};
