const { Model } = require("sequelize");

module.exports = (sequelize, Sequelize) => {
  class TaxGroupTax extends Model {}

  TaxGroupTax.init(
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      tax_group: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: {
            tableName: "TaxGroups",
          },
          key: "id",
        },
      },
      tax: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: {
            tableName: "Taxes",
          },
          key: "id",
        },
      },
      sequence_number: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      user: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: {
            tableName: "Users",
          },
          key: "id",
        },
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      }
    },
    {
      sequelize,
      modelName: "TaxGroupTax",
      tableName: "TaxGroupTaxes",
      timestamps: false,
      indexes: [
        {
          unique: true,
          fields: ["tax_group", "tax"],
        },
        {
          fields: ["tax_group", "sequence_number"],
        },
      ],
    }
  );

  return TaxGroupTax;
};
