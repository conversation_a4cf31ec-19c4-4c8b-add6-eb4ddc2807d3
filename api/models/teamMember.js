const { custom<PERSON>lphabet } = require("nanoid");
const {STATUSES} = require("./status");
const nanoid = customAlphabet('0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp', 17);


module.exports = (connection, Sequelize) => {
    const schema = {
        code: {
            type: Sequelize.STRING,
            defaultValue: () => 'tmb_' + nanoid()
        },
        team: {
            type: Sequelize.INTEGER,
            allowNull: false,
        },

        user: {
            type: Sequelize.INTEGER,
            allowNull: false,
        },

        role: {
            type: Sequelize.INTEGER,
            allowNull: false,
        },

        company: {
            type: Sequelize.INTEGER,
            allowNull: false,
        },

        status: {
            type: Sequelize.INTEGER,
            defaultValue: STATUSES.ACTIVE,
            allowNull: false,
        },

        created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updated_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            onUpdate: Sequelize.literal('CURRENT_TIMESTAMP')
        }
    };
    return connection.define('TeamMember', schema,
        { timestamps: false });
};
