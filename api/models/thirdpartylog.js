const { customAlphabet } = require("nanoid");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `evt_${nanoid()}`,
    },
    event: Sequelize.STRING,
    externalIdentifier: Sequelize.STRING,
    endpoint: Sequelize.STRING,
    method: Sequelize.STRING,
    message: Sequelize.STRING,
    statusCode: Sequelize.INTEGER,
    payload: Sequelize.TEXT,
    response: Sequelize.TEXT,
    providerType: Sequelize.STRING,
    provider: Sequelize.INTEGER,
    company: Sequelize.INTEGER,
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("ThirdPartyLog", schema, { timestamps: false });
};
