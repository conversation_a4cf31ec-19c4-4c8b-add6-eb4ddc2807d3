const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `trx_${nanoid()}`,
    },
    amount: Sequelize.BIGINT,
    company: Sequelize.INTEGER,
    budget: {
      type: Sequelize.INTEGER,
      defaultValue: null,
    },
    budgetPeriod: {
      type: Sequelize.INTEGER,
      defaultValue: null,
    },
    reference: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUID4,
    },
    narration: Sequelize.STRING,
    receipt: Sequelize.INTEGER,
    currency: Sequelize.STRING,
    recipient: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    recipient_type: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    bank_account: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    payer: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    user: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    externalIdentifier: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    balance: Sequelize.INTEGER,
    processor_fee: Sequelize.INTEGER,
    card: Sequelize.INTEGER,
    team: Sequelize.INTEGER,
    batch_id: Sequelize.INTEGER,
    schedule_id: Sequelize.INTEGER,
    category: Sequelize.INTEGER,
    bujeti_fee: Sequelize.INTEGER,
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.PROCESSING,
    },
    failure_reason: Sequelize.STRING,
    decline_reason: Sequelize.STRING,
    description: Sequelize.STRING,
    sessionId: Sequelize.STRING,
    policiesViolated: {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: true,
    },
    paidOn: {
      type: Sequelize.DATE,
      allowNull: true,
    },
    scale: {
      type: Sequelize.INTEGER,
      defaultValue: 2,
    },
    notifyRecipient: {
      type: Sequelize.DataTypes.TINYINT,
      allowNull: true,
    },
    directDebitId: Sequelize.INTEGER,
    type: Sequelize.STRING,
    deadLine: {
      type: Sequelize.DATE,
      allowNull: true,
      defaultValue: null,
    },
    zohoIdentifier: Sequelize.STRING,
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("Transaction", schema, {
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
  });
};

module.exports.TRANSACTION_TYPES = {
  PAYMENT: "payment",
  DIRECT_DEBIT: "directDebit",
  SUBSCRIPTION: "subscription",
  BILL: "bill",
  SUBSCRIPTION_ADDONS: "subscriptionAddons",
  ARCHIVED_ACCOUNT_LIQUIDATION: "archivedAccountLiquidation",
  CARDREQUEST_CHARGE: "cardRequestCharge",
};
