const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet("123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `tat_${nanoid()}`,
    },

    reference: {
      type: Sequelize.STRING,
      allowNull: false,
    },

    transaction: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },

    transfer: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },

    card: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },

    status: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: STATUSES.PENDING,
    },

    externalIdentifier: {
      type: Sequelize.STRING,
      allowNull: false,
    },

    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },

    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("TransactionAttempt", schema, { timestamps: false });
};
