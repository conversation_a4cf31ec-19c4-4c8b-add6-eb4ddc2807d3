const { customAlphabet } = require('nanoid');

const nanoid = customAlphabet('123456789AQWXSCZEDCVFRTGBHYNMJUIKLOP0aqwxszedcvfrtgbnhyujmkiolp', 17);

module.exports = (connection, Sequelize) => {
	const schema = {
		code: {
			type: Sequelize.STRING,
			defaultValue: () => `tty_${nanoid()}`,
		},
		name: Sequelize.STRING,
		feature_name: Sequelize.STRING,
		created_at: {
			type: Sequelize.DATE,
			defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
		},
		updated_at: {
			type: Sequelize.DATE,
			defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
			onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
		},
	};
	return connection.define('TransactionType', schema, { timestamps: false });
};
