const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `trf_${nanoid()}`,
    },
    amount: Sequelize.BIGINT,
    company: Sequelize.INTEGER,
    reference: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUID4,
    },
    externalIdentifier: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    narration: Sequelize.STRING,
    receipt: Sequelize.STRING,
    currency: Sequelize.STRING,
    processor_fee: Sequelize.INTEGER,
    bujeti_fee: Sequelize.INTEGER,
    invoice: {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: true,
    },
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.PENDING,
    },
    scale: {
      type: Sequelize.INTEGER,
      defaultValue: 2,
    },
    failure_reason: Sequelize.STRING,
    description: Sequelize.STRING,
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    balance: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    category: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    counterParty: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    budget: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("Transfer", schema, { timestamps: false });
};
