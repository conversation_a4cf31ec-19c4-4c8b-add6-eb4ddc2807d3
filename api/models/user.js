const { customAlphabet } = require("nanoid");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);
const { STATUSES } = require("./status");
const { SYSTEM_ROLES } = require("./role");

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `usr_${nanoid()}`,
    },
    firstName: {
      type: Sequelize.STRING,
      required: true,
    },
    lastName: Sequelize.STRING,
    middleName: {
      type: Sequelize.STRING,
    },
    email: Sequelize.STRING,
    password: Sequelize.STRING,
    "2fa": {
      type: Sequelize.TINYINT,
      defaultValue: 0,
    },
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.UNVERIFIED,
    },
    role_id: {
      type: Sequelize.INTEGER,
      defaultValue: SYSTEM_ROLES.ADMIN,
    },
    role: {
      type: Sequelize.STRING,
      defaultValue: "admin",
    },
    company: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    manager: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    dob: {
      type: Sequelize.DATE,
      allowNull: true,
    },
    address: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    phoneNumber: Sequelize.INTEGER,
    rewardPoints: {
      type: Sequelize.INTEGER,
      defaultValue: 0,
    },
    hiredOn: {
      type: Sequelize.DATEONLY,
      allowNull: true,
    },
    pin: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    leftOn: {
      type: Sequelize.DATEONLY,
      allowNull: true,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("User", schema, { timestamps: false });
};
