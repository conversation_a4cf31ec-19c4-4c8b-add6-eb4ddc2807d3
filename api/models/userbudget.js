const { STATUSES } = require("./status");
const { customAlphabet } = require("nanoid");

const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `ubg_${nanoid()}`,
    },
    budget: Sequelize.INTEGER,
    user: Sequelize.INTEGER,
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.ACTIVE,
      allowNull: false,
    },
    amount: Sequelize.BIGINT,
    spent: {
      type: Sequelize.BIGINT,
      defaultValue: 0,
    },
    removed_at: {
      type: Sequelize.DATE,
    },
    limit: Sequelize.BIGINT,
    isBudgetOwner: Sequelize.BOOLEAN,
    duration: Sequelize.STRING,
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("UserBudget", schema, { timestamps: false });
};
