const { customAlphabet } = require("nanoid");
const { STATUSES } = require("./status");
const nanoid = customAlphabet("0123456789AQWXSCZEDCVFRTGBHYNJUIKLOPaqwxszedcvfrtgbnhyujmkiolp", 17);

module.exports = (connection, Sequelize) => {
  const schema = {
    code: {
      type: Sequelize.STRING,
      defaultValue: () => `vdr_${nanoid()}`,
    },
    status: {
      type: Sequelize.INTEGER,
      defaultValue: STATUSES.ACTIVE,
    },
    company: {
      type: Sequelize.INTEGER,
    },
    name: Sequelize.STRING,
    hash: Sequelize.STRING,
    description: Sequelize.STRING,
    email: Sequelize.STRING,
    website: Sequelize.STRING,
    logo: Sequelize.STRING,
    phoneNumber: Sequelize.INTEGER,
    industry: Sequelize.INTEGER,
    address: Sequelize.INTEGER,
    tin: Sequelize.STRING,
    registrationNumber: Sequelize.STRING,
    taxWithHolding: Sequelize.INTEGER,
    ignoreable: {
      type: Sequelize.TINYINT,
      defaultValue: 0,
    },
    zohoIdentifier: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      onUpdate: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  };
  return connection.define("Vendor", schema, { timestamps: false });
};
