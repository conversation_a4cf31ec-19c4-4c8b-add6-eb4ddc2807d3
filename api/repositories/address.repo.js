const { Address } = require("../models");

module.exports = {
  async getOneAddress({ queryParams, selectOptions = [], transaction = null }) {
    return Address.findOne({
      where: queryParams,
      attributes: selectOptions.concat(["id", "code"]),
      transaction,
    });
  },

  async getAllAddress({ queryParams = null, selectOptions = [], transaction = null }) {
    return Address.findAll({
      where: queryParams,
      attributes: selectOptions.concat(["code"]),
      transaction,
    });
  },

  async createAnAddress({ queryParams, transaction = null }) {
    return Address.create({ ...queryParams }, { transaction });
  },

  async updateAnAddress({ queryParams, updateFields, transaction }) {
    return Address.update(updateFields, { where: queryParams, transaction });
  },
};
