const sequelize = require("sequelize");
const { Balance, BalanceLedger, BalanceType, BankAccount, Status } = require("../models");
const { STATUSES } = require("../models/status");
const { NotFoundError, ValidationError } = require("../utils/error.utils");

const BALANCES_TYPES = {};

const Repo = {
  async find(criteria, includeBankAccount = false) {
    return Balance.findAll({
      where: criteria,
      ...(includeBankAccount && { include: [BankAccount] }),
    });
  },
  async findOrCreate(payload) {
    const [balance] = await Balance.findOrCreate({
      where: payload,
    });
    return balance;
  },

  async create(payload) {
    return Balance.create(payload);
  },

  async createLedger(payload) {
    return BalanceLedger.create(payload);
  },

  async viewBalance(company, currency = "NGN", code = null) {
    return Balance.findOne({
      where: {
        company,
        currency,
        ...(code && { code }),
      },
    });
  },

  async getAvailableBalanceOldWay({ company, currency, code, id, type = "Expenses", transaction = null }) {
    const result = await Balance.findOne({
      subQuery: false,
      attributes: [
        "id",
        [sequelize.col("Balance.amount"), "balance"],
        [sequelize.col("Balance.currency"), "currency"],
        [sequelize.fn("sum", sequelize.col("BalanceLedgers.amount")), "pending"],
      ],
      where: {
        company,
        currency,
        status: STATUSES.ACTIVE,
        ...(id && { id }),
        ...(code && { code }),
      },
      include: [
        {
          model: BalanceType,
          required: true,
        },
        {
          attributes: ["amount", "balance"],
          model: BalanceLedger,
          required: false,
          includeIgnoreAttributes: false,
          where: {
            company,
            status: [STATUSES.PENDING, STATUSES.PROCESSED, STATUSES.REVERSED],
          },
        },
      ],
      transaction,
      group: ["Balance.id", "BalanceLedgers.balance", "BalanceType.id"],
      includeIgnoreAttributes: false,
    });
    const { balance = 0, pending = 0 } = (result && result.toJSON()) || {};
    return parseInt(balance || 0, 10) + parseInt(pending || 0, 10);
  },

  async getAvailableBalance({ company, currency, code, id = null, type = "Expenses", transaction = null }) {
    const balance = await Balance.findOne({
      attributes: ["id", "currency"],
      where: {
        company,
        currency,
        status: [STATUSES.ACTIVE, STATUSES.ARCHIVED],
        ...(code && { code }),
        ...(id && { id }),
      },
      include: [
        {
          model: BalanceType,
          required: true,
          where: {
            ...(!(code || id) && { name: { [sequelize.Op.like]: `%${String(type).toTitle()}%` } }),
          },
        },
        {
          model: BalanceLedger,
          attributes: ["balanceAfter"],
          limit: 1,
          order: [["id", "DESC"]],
          required: false,
          where: {
            company,
            currency,
            status: [STATUSES.PENDING, STATUSES.PROCESSED, STATUSES.REVERSED],
            card: null,
          },
        },
      ],
      transaction,
    });

    //  Checks if the balanceAfter for the balance has a value
    if (balance) {
      const jsonifiedBalance = balance.toJSON();
      if (jsonifiedBalance.BalanceLedgers.length > 0) return jsonifiedBalance.BalanceLedgers[0].balanceAfter;
      return 0;
    }

    return Repo.getAvailableBalanceOldWay({ company, currency, type, transaction });
  },

  async getAvailableBalanceById(id) {
    const balance = await Balance.findOne({
      attributes: ["id", "currency"],
      where: {
        id,
      },
      include: [
        {
          model: BalanceType,
          required: true,
        },
        {
          model: BalanceLedger,
          attributes: ["balanceAfter"],
          limit: 1,
          order: [["id", "DESC"]],
          required: false,
          where: {
            status: [STATUSES.PENDING, STATUSES.PROCESSED, STATUSES.REVERSED],
            card: null,
          },
        },
      ],
    });

    const jsonifiedBalance = balance.toJSON();
    if (jsonifiedBalance.BalanceLedgers.length > 0) return jsonifiedBalance.BalanceLedgers[0].balanceAfter;
    return 0;
  },

  async getBalance({ filter, transaction = null, includeAccount = false, lock = null, skipLocked = null }) {
    const include = [BalanceType, Status];
    if (includeAccount) {
      include.push({
        model: BankAccount,
        order: [["created_at", "DESC"]],
      });
    }
    return Balance.findOne({
      where: filter,
      include,
      transaction,
      lock,
      skipLocked,
    });
  },

  async getBalanceTypes(name) {
    if (!Object.keys(BALANCES_TYPES).length) {
      const types = await BalanceType.findAll();
      types.forEach((type) => {
        BALANCES_TYPES[type.name.toLowerCase()] = type.id;
      });
    }
    return BALANCES_TYPES[name.toLowerCase()];
  },

  async update(criteria, payload) {
    return Balance.update(payload, { where: criteria });
  },

  async isBalanceSufficient({ balance, amount }) {
    const foundBalance = await Repo.getBalance({ filter: { id: balance.id }, includeAccount: true });
    if (!foundBalance) throw new NotFoundError("Balance");
    const { BankAccount: balanceAccount } = foundBalance;
    if (balanceAccount.type === "real") return true; // Don't check balance for real
    const availableBalance = await Repo.getAvailableBalance({
      company: foundBalance.company,
      currency: foundBalance.currency,
      code: foundBalance.code,
    });
    // eslint-disable-next-line consistent-return
    return Number(availableBalance) > Number(amount);
  },
};

module.exports = Repo;
