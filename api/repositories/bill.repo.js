/* eslint-disable no-param-reassign */
/* eslint-disable no-use-before-define */
const { Op, QueryTypes } = require("sequelize");
const {
  Bill,
  User,
  Status,
  BillInstallment,
  ApprovalStage,
  ApproverLevel,
  ApprovalRequest,
  Approval,
  Approver,
  Company,
  Asset,
  BillProduct,
  Product,
  Balance,
  BankAccount,
  ScheduledBill,
  BillPayment,
  Category,
  Budget,
  Transaction,
  Vendor,
  sequelize,
  VendorCategory,
} = require("../models");
const { STATUSES } = require("../models/status");
const { NotFoundError, ValidationError } = require("../utils/error.utils");
const { METHODS } = require("../mocks/constants.mock");
const { isValidDate, paginate, getStatusValues } = require("../utils");

module.exports = {
  async create({ payload, transaction = null }) {
    return Bill.create(payload, { transaction });
  },
  async findOrCreate(payload) {
    const foundBill = await Bill.findOne({ where: payload });
    if (foundBill) return foundBill;
    return Bill.create(payload);
  },

  async update({ filter, data, transaction = null }) {
    return Bill.update(data, { where: filter, transaction });
  },

  async countBills({ filter }) {
    return Bill.count({ where: { status: { [Op.notIn]: [STATUSES.DELETED] }, ...filter } });
  },

  async fetchBills({ filter, transaction = null, meta = {} }) {
    return Bill.findAll({ where: filter, transaction, ...meta });
  },

  async getBill({
    filter,
    transaction = null,
    addCompany = false,
    includeApproval = false,
    includeProducts = false,
    includePaymentMethod = false,
    includePayments = false,
    includeSchedule = false,
    includeSourceOfFunds = false,
    throwErrorIfNotfound = true,
    includeAssets = false,
  }) {
    const include = [
      {
        model: User,
        attributes: ["id", "code", "firstName", "lastName", "email"],
        include: [Company],
      },
      Status,
      {
        model: Category,
        required: false,
      },
      {
        model: Vendor,
        required: false,
        include: [
          {
            model: VendorCategory,
            required: false,
            as: "categories",
            include: [Category],
            where: { status: STATUSES.ACTIVE },
          },
        ],
      },
      {
        model: BillInstallment,
        where: { status: { [Op.notIn]: [STATUSES.DELETED] } },
        required: false,
      },
    ];

    if (includePayments) {
      include.push({
        model: BillPayment,
        attributes: ["transaction", "status", "created_at"],
        required: false,
        include: [{ model: Transaction, attributes: ["amount", "created_at"], required: false }],
        where: { status: STATUSES.PAID },
      });
    }

    if (includePaymentMethod) {
      const balance = {
        model: Balance,
        as: "balance",
        where: {
          "$Bill.paymentMethod$": METHODS.BALANCE,
        },
        required: false,
        include: [BankAccount],
      };
      const budget = {
        model: Budget,
        as: "budget",
        where: {
          "$Bill.paymentMethod$": METHODS.BUDGET,
        },
        required: false,
      };
      const bankAccount = {
        model: BankAccount,
        as: "directDebitAccount",
        where: {
          "$Bill.paymentMethod$": METHODS.DIRECTDEBIT,
        },
        required: false,
      };
      include.push(...[balance, budget, bankAccount]);
    }

    if (includeSchedule) {
      include.push({
        model: ScheduledBill,
        required: false,
      });
    }

    if (addCompany) {
      include.push({
        model: Company,
        attributes: ["id", "name", "code", "contact_phone", "contact_email", "website", "logo"],
        include: [
          {
            model: Asset,
            required: false,
          },
        ],
      });
    }

    if (includeProducts) {
      include.push({
        model: BillProduct,
        include: [Product],
        required: false,
        where: { status: STATUSES.ACTIVE },
      });
    }

    if (includeAssets) {
      include.push({
        model: Asset,
        via: "receipt",
        as: "BillAssets",
        where: {
          entityType: "bill",
        },
        required: false,
      });
    }

    if (includeApproval) {
      const approvalRequest = {
        model: ApprovalRequest,
        required: false,
        where: {
          entity: "bill",
        },
        include: [
          {
            model: ApprovalStage,
            include: [
              {
                model: ApproverLevel,
                include: [
                  {
                    model: Approver,
                    include: [
                      {
                        model: User,
                        where: { status: STATUSES.ACTIVE },
                      },
                    ],
                  },
                ],
              },
            ],
            order: [["created_at", "ASC"]],
          },
          { model: Approval, include: [{ model: Approver, include: [User] }] },
        ],
      };

      include.push(approvalRequest);
    }
    const foundBill = await Bill.findOne({
      where: { ...filter, ...(!filter?.status && { status: { [Op.notIn]: [STATUSES.INACTIVE, STATUSES.DELETED] } }) },
      include,
      order: [["created_at", "DESC"]],
      transaction,
    });

    if (!foundBill && throwErrorIfNotfound) throw new NotFoundError("Bill");

    if (includeSourceOfFunds) {
      foundBill.sourceOfFunds = await this.sourceOfFunds({
        method: foundBill.paymentMethod,
        methodId: foundBill.paymentMethodId,
        company: foundBill.company,
      });
    }

    return foundBill;
  },

  /**
   * List bills based on criteria
   */

  async listBills({ filter, transaction = null }) {
    const { page = 1, perPage = 50 } = preparePagination(filter);
    const billBinding = [filter.company, "scheduled"];

    // Base query for bills and scheduled bills
    let billBaseQuery = buildBillBaseQuery();
    let scheduledBillBaseQuery = buildScheduledBillBaseQuery();

    // Apply filters
    billBaseQuery = applyStatusFilter(filter.status, billBinding, billBaseQuery);
    scheduledBillBaseQuery = applyStatusFilter(filter.status, billBinding, scheduledBillBaseQuery, "ScheduleBll");

    billBaseQuery = applyDateFilter(filter.from, filter.to, billBinding, billBaseQuery);
    scheduledBillBaseQuery = applyDateFilter(filter.from, filter.to, billBinding, scheduledBillBaseQuery, "ScheduleBll");

    billBaseQuery = applyAmountFilter(filter.minAmount, filter.maxAmount, billBinding, billBaseQuery);
    scheduledBillBaseQuery = applyAmountFilter(filter.minAmount, filter.maxAmount, billBinding, scheduledBillBaseQuery, "ScheduleBll");

    billBaseQuery = applySearchFilter(filter.search, billBinding, billBaseQuery);
    scheduledBillBaseQuery = applySearchFilter(filter.search, billBinding, scheduledBillBaseQuery);

    billBaseQuery = applyVendorFilter(filter.vendor, billBinding, billBaseQuery);
    scheduledBillBaseQuery = applyVendorFilter(filter.vendor, billBinding, scheduledBillBaseQuery);

    // Query to count total bills
    const totalBills = await countTotalBills(billBaseQuery, scheduledBillBaseQuery, billBinding, transaction);

    // Pagination handling
    const { total, limit, skip, hasMore, nextPage } = paginate(totalBills, page, perPage);

    // Query to fetch bills
    const bills = await fetchBills(billBaseQuery, scheduledBillBaseQuery, billBinding, limit, skip, transaction);

    return {
      bills,
      meta: {
        total,
        page,
        perPage,
        hasMore,
        nextPage,
      },
    };
  },

  async getBillStats({ filter }) {
    const { company, from, to } = filter;
    let { vendor } = filter;
    vendor = this.normalizeVendor(vendor);

    const draftAndTotalBillBinding = [company, STATUSES.DELETED, STATUSES.DRAFT];
    const totalPaidBinding = [company, STATUSES.PAID];
    const totalPendingBinding = [company, STATUSES.PENDING];
    const totalApprovedBinding = [company, STATUSES.APPROVED];
    const totalOverdueBinding = [STATUSES.PENDING, STATUSES.OVERDUE, company, STATUSES.PARTIAL];

    const filters = this.buildDateFilters(
      from,
      to,
      draftAndTotalBillBinding,
      totalPaidBinding,
      totalApprovedBinding,
      totalPendingBinding,
      totalOverdueBinding
    );

    const vendorFilters = this.buildVendorFilters(
      vendor,
      draftAndTotalBillBinding,
      totalPaidBinding,
      totalApprovedBinding,
      totalPendingBinding,
      totalOverdueBinding
    );

    const totalBillAndDraftAmount = await this.queryTotalBillAndDraftAmount(
      draftAndTotalBillBinding,
      filters.totalBillFilter + vendorFilters.totalBillFilter
    );
    const totalPending = await this.queryTotalPending(totalPendingBinding, filters.totalPendingFilter + vendorFilters.totalPendingFilter);
    const totalApproved = await this.queryTotalApproved(totalApprovedBinding, filters.totalApprovedFilter + vendorFilters.totalApprovedFilter);
    const totalPaid = await this.queryTotalPaid(totalPaidBinding, filters.totalPaidFilter + vendorFilters.totalPaidFilter);
    const totalOverdue = await this.queryTotalOverdue(totalOverdueBinding, filters.totalOverdueFilter + vendorFilters.totalOverdueFilter);

    return this.compileSummary(totalBillAndDraftAmount, totalPending, totalApproved, totalPaid, totalOverdue);
  },

  normalizeVendor(vendor) {
    if (!vendor) {
      return [];
    }
    return Array.isArray(vendor) ? vendor : [vendor];
  },

  buildDateFilters(from, to, draftAndTotalBillBinding, totalPaidBinding, totalApprovedBinding, totalPendingBinding, totalOverdueBinding) {
    let totalBillFilter = "";
    let totalPaidFilter = "";
    let totalOverdueFilter = "";
    let totalApprovedFilter = "";
    let totalPendingFilter = "";

    if (from || to) {
      if (from && isValidDate(from)) {
        const formattedFrom = `${from}: 00:00:00`;
        draftAndTotalBillBinding.push(formattedFrom);
        totalPaidBinding.push(formattedFrom);
        totalOverdueBinding.push(formattedFrom);
        totalPendingBinding.push(formattedFrom);
        totalApprovedBinding.push(formattedFrom);

        totalBillFilter += ` AND Bll.created_at >= $${draftAndTotalBillBinding.length}`;
        totalApprovedFilter += ` AND Bll.created_at >= $${totalApprovedBinding.length}`;
        totalPendingFilter += ` AND Bll.created_at >= $${totalPendingBinding.length}`;
        totalPaidFilter += ` AND bilp.created_at >= $${totalPaidBinding.length}`;
        totalOverdueFilter += ` dueDate >= $${totalOverdueBinding.length}`;
      }

      if (to && isValidDate(to)) {
        const formattedTo = `${to}: 00:00:00`;
        draftAndTotalBillBinding.push(formattedTo);
        totalPaidBinding.push(formattedTo);
        totalOverdueBinding.push(formattedTo);
        totalPendingBinding.push(formattedTo);
        totalApprovedBinding.push(formattedTo);

        totalBillFilter += ` AND Bll.created_at <= $${draftAndTotalBillBinding.length}`;
        totalApprovedFilter += ` AND Bll.created_at <= $${totalApprovedBinding.length}`;
        totalPendingFilter += ` AND Bll.created_at <= $${totalPendingBinding.length}`;
        totalPaidFilter += ` AND bilp.created_at <= $${totalPaidBinding.length}`;
        totalOverdueFilter += ` ${from ? "AND" : ""} dueDate <= $${totalOverdueBinding.length}`;
      }
    } else {
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, "0");
      const day = String(today.getDate()).padStart(2, "0");
      const formattedDate = `${year}-${month}-${day}: 00:00:00`;
      totalOverdueBinding.push(formattedDate);
      totalOverdueFilter = ` dueDate < $${totalOverdueBinding.length}`;
    }

    return {
      totalBillFilter,
      totalPaidFilter,
      totalOverdueFilter,
      totalApprovedFilter,
      totalPendingFilter,
    };
  },

  buildVendorFilters(vendor, draftAndTotalBillBinding, totalPaidBinding, totalApprovedBinding, totalPendingBinding, totalOverdueBinding) {
    const filters = {
      totalBillFilter: "",
      totalPaidFilter: "",
      totalApprovedFilter: "",
      totalPendingFilter: "",
      totalOverdueFilter: "",
    };

    if (vendor.length) {
      const addVendorFilters = (bindingArray, filterKey) => {
        const previousLength = bindingArray.length;
        bindingArray.push(...vendor);

        const vendorPlaceholders = vendor.map((_, index) => `$${previousLength + index + 1}`).join(", ");

        filters[filterKey] += ` AND Vdr.code IN (${vendorPlaceholders})`;
      };

      addVendorFilters(draftAndTotalBillBinding, "totalBillFilter");
      addVendorFilters(totalPaidBinding, "totalPaidFilter");
      addVendorFilters(totalApprovedBinding, "totalApprovedFilter");
      addVendorFilters(totalPendingBinding, "totalPendingFilter");
      addVendorFilters(totalOverdueBinding, "totalOverdueFilter");
    }

    return filters;
  },

  async queryTotalBillAndDraftAmount(binding, filter) {
    const query = `
    SELECT * FROM 
      (SELECT 
        Bll.currency, 
        SUM(CASE WHEN Bll.status NOT IN ($2) THEN Bll.amount ELSE 0 END) AS totalBillAmount,
        SUM(CASE WHEN Bll.status = $3 THEN Bll.amount ELSE 0 END) AS totalDraftAmount 
      FROM Bills AS Bll 
      LEFT JOIN Vendors AS Vdr ON Bll.vendor = Vdr.id 
      WHERE Bll.company = $1 ${filter} 
      GROUP BY Bll.currency) AS totalBillAndDraftAmount`;

    return sequelize.query(query, {
      bind: binding,
      type: QueryTypes.SELECT,
    });
  },

  async queryTotalPending(binding, filter) {
    const query = `
    SELECT 
      Bll.currency, 
      SUM(Bll.amount) AS totalPendingAmount 
    FROM Bills AS Bll 
    LEFT JOIN Vendors AS Vdr ON Bll.vendor = Vdr.id 
    WHERE Bll.company = $1 AND Bll.status = $2 ${filter} 
    GROUP BY Bll.currency`;

    return sequelize.query(query, {
      bind: binding,
      type: QueryTypes.SELECT,
    });
  },

  async queryTotalApproved(binding, filter) {
    const query = `
    SELECT 
      Bll.currency, 
      SUM(Bll.amount) AS totalApprovedAmount 
    FROM Bills AS Bll 
    LEFT JOIN Vendors AS Vdr ON Bll.vendor = Vdr.id 
    WHERE Bll.company = $1 AND Bll.status = $2 ${filter}
    GROUP BY Bll.currency`;

    return sequelize.query(query, {
      bind: binding,
      type: QueryTypes.SELECT,
    });
  },

  async queryTotalPaid(binding, filter) {
    const query = `
    SELECT 
      trxs.currency, 
      SUM(trxs.amount) AS totalPaidAmount 
    FROM BillPayments AS bilp 
    LEFT JOIN Transactions AS trxs ON bilp.transaction = trxs.id 
    LEFT JOIN Bills AS Bll ON bilp.bill = Bll.id 
    LEFT JOIN Vendors AS Vdr ON Bll.vendor = Vdr.id 
    WHERE bilp.company = $1 AND bilp.status = $2 ${filter} 
    GROUP BY trxs.currency`;

    return sequelize.query(query, {
      bind: binding,
      type: QueryTypes.SELECT,
    });
  },

  async queryTotalOverdue(binding) {
    const query = `
    SELECT
      currency, 
      dueDate, 
      SUM(amount) AS totalOverdueAmount, 
      code
    FROM (
      SELECT currency, amount, dueDate, Vdr.code 
      FROM Bills AS Bll
      LEFT JOIN Vendors AS Vdr ON Bll.vendor = Vdr.id
      WHERE Bll.status IN ($1, $2, $4) 
        AND Bll.company = $3
      UNION ALL
      SELECT bll.currency, iin.amount, iin.dueDate, Vdr.code
      FROM Bills AS bll 
      LEFT JOIN BillInstallments AS iin ON bll.id = iin.bill
      LEFT JOIN Vendors AS Vdr ON Vdr.id = bll.vendor
      WHERE iin.status IN ($1, $2, $4) 
        AND bll.company = $3
    ) AS totalOverdue
    GROUP BY currency, dueDate, code;`;

    return sequelize.query(query, {
      bind: binding,
      type: QueryTypes.SELECT,
    });
  },

  compileSummary(totalBillAndDraftResponse, totalPending, totalApproved, totalPaid, totalOverdue) {
    const summary = {};

    summary.totalBill = this.aggregateCurrencyData(totalBillAndDraftResponse, "totalBillAmount");
    summary.totalDraft = this.aggregateCurrencyData(totalBillAndDraftResponse, "totalDraftAmount");
    summary.totalOverdue = this.aggregateCurrencyData(totalOverdue, "totalOverdueAmount");
    summary.totalPaid = this.aggregateCurrencyData(totalPaid, "totalPaidAmount");
    summary.totalPending = this.aggregateCurrencyData(totalPending, "totalPendingAmount");
    summary.totalApproved = this.aggregateCurrencyData(totalApproved, "totalApprovedAmount");

    return summary;
  },

  aggregateCurrencyData(data, amountKey) {
    const mapObject = { NGN: 0 };
    data.forEach((value) => {
      const { currency, [amountKey]: amount } = value;
      mapObject[currency] = (mapObject[currency] || 0) + parseInt(amount || 0, 10);
    });
    return { ...mapObject };
  },

  async sourceOfFunds({ method, methodId, company }) {
    let sourceOfFunds;
    switch (Number(method)) {
      case METHODS.BUDGET:
        {
          if (!methodId) throw new ValidationError("Please specify budget");
          const foundBudget = await Budget.findOne({ where: { id: methodId, company } });
          if (!foundBudget) throw new NotFoundError("Budget");
          sourceOfFunds = foundBudget.name;
        }
        break;

      case METHODS.BALANCE:
        {
          if (!methodId) throw new ValidationError("Please specify balance");
          const foundBalance = await Balance.findOne({
            where: {
              id: methodId,
              company,
            },
          });
          if (!foundBalance) throw new NotFoundError("Balance");
          sourceOfFunds = foundBalance.name;
        }
        break;

      case METHODS.DIRECTDEBIT:
        {
          if (!methodId) throw new ValidationError("Please specify direct debit source");
          const foundBankAccount = await BankAccount.getOneBankAccount({
            where: {
              id: methodId,
            },
          });

          if (!foundBankAccount) throw new NotFoundError("Bank account");

          sourceOfFunds = foundBankAccount.accountName;
        }
        break;

      default: {
        sourceOfFunds = null;
      }
    }
    return sourceOfFunds;
  },
};

function preparePagination({ page = 1, perPage = 50 }) {
  return { page: parseInt(page, 10), perPage: parseInt(perPage, 10) };
}

function buildBillBaseQuery() {
  return `
    SELECT 
        Bll.code AS code, Bll.amount as amount, Bll.vat as vat, Bll.discount as discount,
        Bll.discount_type as discount_type, Bll.reason AS reason,
        Bll.user AS user, Bll.currency AS currency,
        Bll.dueDate as dueDate, Bll.status as status, Bll.balanceDue as balanceDue,
        Bll.created_at AS created_at, 'bill' as type, ast.id as hasFile,
        NULL AS Schedule,
               JSON_OBJECT(
          'code', Vdr.code,
          'name', Vdr.name,
                 'email', Vdr.email,
          'taxWithHolding', Vdr.taxWithHolding,
                 'tin', Vdr.tin,
                 'status', Vdr.status,
                 'description', Vdr.description
        ) AS Vendor,
               JSON_OBJECT(
          'code', Ctg.code,
          'name', Ctg.name,
                 'description', Ctg.description,
                 'status', Ctg.status
        ) AS Category
    FROM Bills as Bll
    LEFT JOIN Vendors as Vdr ON Bll.vendor = Vdr.id
    LEFT JOIN Categories as Ctg ON Bll.category = Ctg.id
    LEFT JOIN Assets as ast ON Bll.id = ast.entityId AND ast.entityType = 'bill'
    WHERE Bll.company = $1`;
}

function buildScheduledBillBaseQuery() {
  return `
    SELECT 
        ScheduleBll.code AS code, ScheduleBll.amount as amount, ScheduleBll.vat as vat, ScheduleBll.discount as discount,
        ScheduleBll.discount_type as discount_type, ScheduleBll.reason AS reason,
        ScheduleBll.user AS user, ScheduleBll.currency AS currency,
        NULL as dueDate, ScheduleBll.status as status, ScheduleBll.balanceDue as balanceDue,
        ScheduleBll.created_at AS created_at, 'scheduled' as type, ast.id as hasFile,
        JSON_OBJECT(
          'code', Schedules.code,
          'cron_id', Schedules.cron_id,
          'status', Schedules.status,
          'cron_expression', Schedules.cron_expression,
          'created_at', Schedules.created_at,
          'updated_at', Schedules.updated_at,
          'nextExecutionDate', Schedules.nextExecutionDate
        ) AS Schedule,
               JSON_OBJECT(
          'code', Vdr.code,
          'name', Vdr.name,
                 'email', Vdr.email,
          'taxWithHolding', Vdr.taxWithHolding,
                 'tin', Vdr.tin,
                 'status', Vdr.status,
                 'description', Vdr.description
        ) AS Vendor,
               JSON_OBJECT(
          'code', Ctg.code,
          'name', Ctg.name,
                 'description', Ctg.description,
                 'status', Ctg.status
        ) AS Category
    FROM ScheduledBills as ScheduleBll
    LEFT JOIN Schedules ON ScheduleBll.schedule = Schedules.id
    LEFT JOIN Vendors as Vdr ON ScheduleBll.vendor = Vdr.id
    LEFT JOIN Categories as Ctg ON ScheduleBll.category = Ctg.id
    LEFT JOIN Assets as ast ON ScheduleBll.id = ast.entityId AND ast.entityType = 'scheduledBill'
    WHERE ScheduleBll.company = $1 AND ScheduleBll.type = $2`;
}

function applyStatusFilter(status, billBinding, baseQuery, table = "Bll") {
  let statusFilter = "";

  if (status) {
    const statuses = Array.isArray(status) ? status : [status];
    const statusValues = getStatusValues(statuses);

    if (statusValues.includes(STATUSES.OVERDUE)) {
      const totalOverdueBinding = [STATUSES.PENDING, STATUSES.OVERDUE, STATUSES.PARTIAL];
      statusFilter += ` AND (${table}.dueDate < NOW() AND ${table}.status IN (${totalOverdueBinding
        .map((overdueStatus) => `${overdueStatus}`)
        .join(", ")}))`;
    } else if (statusValues.includes(STATUSES.APPROVED)) {
      billBinding.push(STATUSES.APPROVED);
      statusFilter += ` AND ${table}.status = $${billBinding.length}`;
    } else {
      const previousLength = billBinding.length;
      billBinding.push(...statusValues);
      statusFilter = ` AND ${table}.status IN (${statusValues.map((_, index) => `$${previousLength + index + 1}`).join(", ")})`;
    }
  } else {
    // Use default status values for filtering when status is not provided
    const defaultStatuses = [STATUSES.DELETED, STATUSES.INACTIVE, STATUSES.SCHEDULED];
    const defaultPlaceholders = defaultStatuses.map((_, index) => `$${billBinding.length + index + 1}`);
    billBinding.push(...defaultStatuses);
    statusFilter = ` AND ${table}.status NOT IN (${defaultPlaceholders.join(", ")})`;
  }

  return `${baseQuery} ${statusFilter} `;
}

function applyDateFilter(from, to, billBinding, baseQuery, table = "Bll") {
  if (from && isValidDate(from)) {
    billBinding.push(`${from}: 00:00:00`);
    baseQuery += ` AND ${table}.created_at >= $${billBinding.length} `;
  }
  if (to && isValidDate(to)) {
    billBinding.push(`${to}: 23:59:59`);
    baseQuery += ` AND ${table}.created_at <= $${billBinding.length} `;
  }
  return baseQuery;
}

function applyAmountFilter(minAmount, maxAmount, billBinding, baseQuery, table = "Bll") {
  if (minAmount) {
    billBinding.push(minAmount);
    baseQuery += ` AND ${table}.amount >= $${billBinding.length} `;
  }
  if (maxAmount) {
    billBinding.push(maxAmount);
    baseQuery += ` AND ${table}.amount <= $${billBinding.length} `;
  }
  return baseQuery;
}

function applySearchFilter(search, billBinding, baseQuery, table = "Bll") {
  if (search) {
    billBinding.push(`%${search}%`);
    return `${baseQuery} AND (${table}.code LIKE $${billBinding.length} OR ${table}.reason LIKE $${billBinding.length} OR Vdr.name LIKE $${billBinding.length}) `;
  }
  return baseQuery;
}

function applyVendorFilter(vendor, billBinding, baseQuery) {
  if (vendor) {
    const vendors = Array.isArray(vendor) ? vendor : [vendor];
    const vendorPlaceholders = vendors.map((_, index) => `$${billBinding.length + index + 1}`);
    billBinding.push(...vendors);
    const vendorQuery = ` IN (${vendorPlaceholders.join(", ")})`;
    return `${baseQuery} AND Vdr.code ${vendorQuery}`;
  }
  return baseQuery;
}

async function countTotalBills(billBaseQuery, scheduledBillBaseQuery, billBinding, transaction) {
  const countQuery = `SELECT COUNT(*) AS total FROM (${billBaseQuery} UNION ALL ${scheduledBillBaseQuery}) AS combinedBills`;
  const [{ total }] = await sequelize.query(countQuery, {
    type: QueryTypes.SELECT,
    bind: billBinding,
    transaction,
  });
  return total;
}

async function fetchBills(billBaseQuery, scheduledBillBaseQuery, billBinding, limit, skip, transaction) {
  const baseQuery = `SELECT * FROM (${billBaseQuery} UNION ALL ${scheduledBillBaseQuery}) AS combinedBills ORDER BY created_at DESC LIMIT ${limit} OFFSET ${skip}`;
  return sequelize.query(baseQuery, {
    type: QueryTypes.SELECT,
    bind: billBinding,
    transaction,
  });
}
