const { BillingAddon, User, Company } = require("../models");

module.exports = {
  async create({ data, transaction = null }) {
    return BillingAddon.create(data, { transaction });
  },

  async bulkCreate({ data, transaction = null }) {
    return BillingAddon.bulkCreate(data, { transaction });
  },

  async getOne({ queryParams, selectOptions = [], transaction = null }) {
    return BillingAddon.findOne({
      where: queryParams,
      ...(selectOptions.length && { attributes: selectOptions.concat(["id", "code"]) }),
      transaction,
    });
  },

  async getMany({ queryParams, selectOptions = [], transaction = null, includeInviter = false, includeCompany = false }) {
    const include = [];

    if (includeInviter) include.push({ model: User, as: "inviter" });
    if (includeCompany) include.push({ model: Company });

    return BillingAddon.findAll({
      where: queryParams,
      ...(selectOptions.length && { attributes: selectOptions.concat(["id", "code"]) }),
      transaction,
      include,
    });
  },

  async update({ queryParams, payload, transaction = null }) {
    return BillingAddon.update(payload, { where: queryParams, transaction });
  },
};
