const { CreditCard: Card } = require("../models");
const { STATUSES } = require("../models/status");

module.exports = {
  async getCard(criteria) {
    return Card.findOne({ where: criteria });
  },

  async getCardForBujetiFetch(payload) {
    const { name, ...criteria } = payload;
    const card = await Card.findOne({ where: criteria });
    if (card) return card;
    await Card.create({ ...payload, isExternal: true, status: STATUSES.ACTIVE });
    return Card.findOne({ where: payload });
  },

  async count(criteria) {
    return Card.count({ where: criteria });
  },

  async update({ filter, data, transaction = null }) {
    return Card.update(data, { where: filter }, { transaction });
  },
};
