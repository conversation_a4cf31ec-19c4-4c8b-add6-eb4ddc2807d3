/* eslint-disable no-param-reassign */
const { Op } = require("sequelize");
const { Category, DisabledCategory, Transaction, sequelize: Sequelize } = require("../models");
const { STATUSES } = require("../models/status");

const Repo = {
  /**
   * List categories
   * @param  {} {queryParams
   * @param  {} selectOptions=[]
   * @param  {} }
   */
  async listCategories({ queryParams, selectOptions = null }) {
    const attributes =
      (Array.isArray(selectOptions) &&
        selectOptions.length && {
          attributes: selectOptions.concat(["code"]),
        }) ||
      {};

    return Category.findAll({
      where: {
        ...queryParams,
        ...(Object.prototype.hasOwnProperty.call(queryParams, "company") && {
          company: { [Op.or]: [null, queryParams.company] },
          "$DisabledCategory.disabledOn$": null,
        }),
        ...(!queryParams.status && {
          status: {
            [Op.ne]: STATUSES.DELETED,
          },
        }),
      },
      ...attributes,
      ...(queryParams.company && {
        include: [
          {
            model: DisabledCategory,
            where: {
              company: queryParams.company,
            },
            required: false,
          },
        ],
      }),
    });
  },

  /**
   * List categories
   * @param  {} {queryParams
   * @param  {} selectOptions=[]
   * @param  {} }
   */
  async paginateCategories({ queryParams, pagination = {}, selectOptions = null }) {
    let { page = 1, perPage = 50 } = pagination;
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    const skip = Math.max(page - 1, 0) * perPage;
    const attributes =
      (Array.isArray(selectOptions) &&
        selectOptions.length && {
          attributes: selectOptions.concat(["id", "code"]),
        }) ||
      {};

    const criteria = {
      subQuery: false,
      where: {
        ...queryParams,
        ...(Object.prototype.hasOwnProperty.call(queryParams, "company") && {
          company: { [Op.or]: [null, queryParams.company] },
          "$DisabledCategory.disabledOn$": null,
        }),
      },
      ...attributes,
      ...(Object.prototype.hasOwnProperty.call(queryParams, "company") && {
        include: [
          {
            model: DisabledCategory,
            where: {
              company: queryParams.company,
            },
            required: false,
          },
          {
            model: Category,
            as: "SubCategories",
            required: false,
            where: {
              status: {
                [Op.ne]: STATUSES.DELETED,
              },
            },
            include: [
              {
                model: Category,
                as: "SubCategories",
                required: false,
              },
            ],
          },
        ],
      }),
      distinct: true,
      col: "id",
    };

    const [total = 0, categories] = await Promise.all([
      Category.count(criteria),
      Category.findAll({
        ...criteria,
        offset: skip,
        limit: perPage,
        orderBy: [["created_at", "DESC"]],
      }),
    ]);
    return {
      categories,
      meta: {
        page,
        total,
        perPage,
        hasMore: total > page * perPage,
        nextPage: page + 1,
      },
    };
  },

  /**
   * Find or Create a category
   * @param  {} payload
   */
  async findOrCreateCategory(payload) {
    const [category] = await Category.findOrCreate({ where: payload });
    return category;
  },

  /**
   * Create a category
   * @param  {} payload
   */
  async createCategory(payload) {
    return Category.create(payload);
  },

  /**
   * Bulk create categories
   * @param  {} payload
   */
  async bulkCreate(payload) {
    return Category.bulkCreate(payload);
  },

  /** View category
   * @param  {} queryParams
   * @param  {} transaction=null
   */
  async getCategory({ queryParams, include, transaction = null }) {
    return Category.findOne(
      {
        where: {
          ...queryParams,
          ...(queryParams.name && {
            name: queryParams.name,
          }),
          ...(queryParams.company && {
            company: { [Op.or]: [null, queryParams.company] },
          }),
        },
        ...(include && { include }),
      },
      transaction && { transaction }
    );
  },

  /**
   * Update a category
   */
  async updateCategory({ queryParams = {}, updateFields, transaction = null }) {
    return Category.update(updateFields, { where: queryParams }, transaction && { transaction });
  },
  /**
   * Delete a category
   * @param  {} {queryParams
   * @param  {} transaction=null}
   */
  async deleteCategory({ queryParams }) {
    return Category.update({ status: STATUSES.DELETED }, { where: queryParams });
  },

  /**
   * Disable a category
   * @param  {} {queryParams
   * @param  {} transaction=null}
   */
  async disableCategory({ queryParams }) {
    const findCategory = await Category.findOne({ where: { code: queryParams.code }, attributes: ["id", "company"], raw: true });

    if (!findCategory || Boolean(findCategory.company)) return;

    const checkForDisabledCategory = await DisabledCategory.findOne({
      where: {
        company: queryParams.company,
        category: findCategory.id,
      },
    });

    if (checkForDisabledCategory) {
      if (checkForDisabledCategory.disabledOn === null) {
        checkForDisabledCategory.disabledOn = new Date();
        return checkForDisabledCategory.save();
      }
    } else {
      return DisabledCategory.create({
        company: queryParams.company,
        category: findCategory.id,
      });
    }
  },

  async getCategoryWithTransactions({ filters, transaction = null }) {
    const { page = 1, perPage = 20, ...rest } = filters;
    const skip = (parseInt(page, 10) - 1) * perPage;

    const includes = [
      {
        model: Transaction,
        limit: 1,
        attributes: ["code"],
        required: true,
      },
    ];

    if (filters.company) {
      includes.push({
        model: DisabledCategory,
        required: false,
        where: {
          company: filters.company,
          disabledOn: null,
        },
      });
    }
    const categoryQuery = {
      where: {
        ...(rest.company && {
          company: { [Op.or]: [null, rest.company] },
        }),
        [Op.exists]: Sequelize.literal(`(SELECT 1 FROM Transactions AS t WHERE t.category = Category.id LIMIT 1)`),
      },
      include: includes,
      distinct: true,
    };

    const [categories, total] = await Promise.all([
      Category.findAll({
        ...categoryQuery,

        subQuery: false,
        offset: skip,
        limit: parseInt(perPage, 10),
        transaction,
      }),
      Category.count(categoryQuery),
    ]);

    return {
      categories,
      meta: {
        total,
        page: parseInt(page, 10),
        perPage: parseInt(perPage),
        hasMore: total > page * perPage,
        nextPage: total > page * perPage ? parseInt(page, 10) + 1 : null,
      },
    };
  },

  async updateChildCategories(parentCategory, payload) {
    if (!parentCategory) return;
    const childCategories = await Category.findAll({ where: { parent: parentCategory } });
    if (childCategories.length === 0) {
      return;
    }
    for (const childCategory of childCategories) {
      if (childCategory.company) await childCategory.update(payload);
      await this.updateChildCategories(childCategory.id, payload);
    }
  },

  async updateCategorySpent({ filter, amount, transaction, action }) {
    const foundCategory = await Category.findOne({ where: filter });

    if (!foundCategory) return null;

    if (foundCategory.spent === null) {
      foundCategory.spent = 0;
      await foundCategory.save({ transaction });
    }

    await foundCategory[action]("spent", {
      by: parseInt(amount, 10),
      transaction,
    });

    return Repo.actOnParent({ transaction, amount, action, category: foundCategory });
  },

  async increaseCategorySpent(params) {
    return Repo.updateCategorySpent({ ...params, action: "increment" });
  },

  async decreaseCategorySpent(params) {
    return Repo.updateCategorySpent({ ...params, action: "decrement" });
  },

  async actOnParent({ transaction, amount, action, category }) {
    if (!category.parent) return null;

    const foundCategory = await Category.findOne({
      where: { id: category.parent },
      transaction,
    });

    if (!foundCategory) return null;

    return foundCategory[action]("spent", {
      by: parseInt(amount, 10),
      transaction,
    });
  },
};

module.exports = Repo;
