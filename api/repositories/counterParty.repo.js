const { CounterParty } = require("../models");

module.exports = {
  async create({ data, transaction = null }) {
    return CounterParty.create(data, { transaction });
  },

  async findOrCreate({ payload, transaction = null }) {
    return CounterParty.findOrCreate({ where: payload, transaction });
  },

  async getCounterParty({ queryParams, transaction = null }) {
    return CounterParty.findOne({
      where: queryParams,
      transaction,
    });
  },
};
