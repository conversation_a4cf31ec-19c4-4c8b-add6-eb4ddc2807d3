const { Op, Sequelize, QueryTypes } = require("sequelize");
const { BankAccount, Customer, Status, PhoneNumber, Invoice, Address, Category, InvoicePayment, Transfer, sequelize } = require("../models");
const { STATUSES } = require("../models/status");
const { paginate, getStatusValues, isValidDate } = require("../utils");

module.exports = {
  async createCustomer({ data, transaction = null }) {
    return Customer.create(data, { transaction });
  },

  async getCustomer({ filter, addAccount = false, transaction = null }) {
    const include = [
      Status,
      PhoneNumber,
      Category,
      Address,
      {
        model: Invoice,
        where: { status: STATUSES.PAID },
        order: [["created_at", "DESC"]],
        limit: 1,
        separate: true,
        attributes: ["paidOn", "amount", "currency", "created_at"],
      },
    ];
    if (addAccount) include.push(BankAccount);
    return Customer.findOne(
      {
        where: {
          ...filter,
          status: {
            [Op.ne]: STATUSES.DELETED,
          },
        },
        attributes: ["id", "status", "code", "company", "email", "tin", "name", "created_at", "zohoIdentifier", "address", "phoneNumber"],
        include,
      },
      { transaction }
    );
  },

  async getCustomers({ filter, transaction = null }) {
    const { query = null, company } = filter;
    let conditions = {
      company,
    };
    if (query) {
      conditions = {
        ...conditions,
        [Op.or]: {
          name: { [Op.like]: `%${query}%` },
          email: { [Op.like]: `%${query}%` },
        },
      };
    }
    return Customer.findAll(
      {
        where: {
          ...conditions,
          status: {
            [Op.ne]: STATUSES.DELETED,
          },
        },
        attributes: ["id", "status", "code", "email", "name", "tin", "created_at"],
        include: [
          Status,
          PhoneNumber,
          Category,
          Address,
          {
            model: Invoice,
            where: { status: STATUSES.PAID },
            order: [["created_at", "DESC"]],
            limit: 1,
            separate: true,
            attributes: ["paidOn", "amount", "currency"],
          },
        ],
        limit: 5,
      },
      { transaction }
    );
  },

  async customerSummary({ filter, transaction = null }) {
    const { company } = filter;
    const debtorsBinding = [STATUSES.PENDING, STATUSES.PARTIAL, STATUSES.OVERDUE, company, STATUSES.ACTIVE];
    const totalDebtorsQuery = `
      SELECT 
        COUNT(DISTINCT Inv.customer) AS debtors 
      FROM Invoices AS Inv
      LEFT JOIN Customers AS Cus ON Inv.customer = Cus.id  
      WHERE Inv.status IN ($1, $2, $3) AND Inv.company = $4 AND Cus.status IN ($5)
    `;

    const totalDebtQuery = `
      SELECT 
        Inv.currency,
        SUM(
          COALESCE(
            (SELECT SUM(IIn.amount) 
             FROM InvoiceInstallments IIn 
             WHERE IIn.invoice = Inv.id AND IIn.status IN ($1, $3)),
            COALESCE(Inv.amount, 0)
          )
        ) AS amount
      FROM Invoices Inv
      LEFT JOIN Customers AS Cus ON Inv.customer = Cus.id
      WHERE Inv.status IN ($1, $2, $3) AND Inv.company = $4 AND Cus.status IN ($5)
      GROUP BY Inv.currency;
`;

    const [[{ debtors = 0 }], totalDebt, totalCustomer] = await Promise.all([
      sequelize.query(totalDebtorsQuery, {
        type: QueryTypes.SELECT,
        bind: debtorsBinding,
        nest: true,
        transaction,
        raw: false,
      }),
      sequelize.query(totalDebtQuery, {
        type: QueryTypes.SELECT,
        bind: debtorsBinding,
        nest: true,
        transaction,
        raw: false,
      }),
      Customer.count({
        where: { company, status: { [Op.notIn]: [STATUSES.DELETED] } },
      }),
    ]);

    return { totalCustomer, debtors, totalDebt: totalDebt.length ? totalDebt : [{ currency: "NGN", amount: 0 }] };
  },

  async listCustomers({ filter, transaction = null }) {
    let { page = 1, perPage = 50 } = filter;
    const { company, from, to, code, type, status, hasUnpaidInvoice = null, search, ...rest } = filter;

    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);

    const bindings = [STATUSES.PENDING, STATUSES.OVERDUE, company, STATUSES.PARTIAL];
    let baseQuery = `
    SELECT Cus.status, Cus.name, Cus.code, Cus.email, Cus.created_at, Cus.type, 
    SUM(
      COALESCE(
        (SELECT SUM(IIn.amount) 
          FROM InvoiceInstallments IIn 
          WHERE IIn.invoice = Inv.id AND IIn.status IN ($1, $2)),
        COALESCE(Inv.amount, 0)
      )
    ) AS outstanding
    FROM Customers AS Cus 
    LEFT JOIN Invoices AS Inv ON Cus.id = Inv.customer AND Inv.company = $3 AND Inv.status IN ($1, $2, $4)
    WHERE Cus.company = $3`;

    if (search) {
      bindings.push(`%${search}%`);
      baseQuery += ` AND (Cus.name LIKE $${bindings.length} OR Cus.email LIKE $${bindings.length} OR Cus.code LIKE $${bindings.length})`;
    }

    if (code) {
      bindings.push(code);
      if (Array.isArray(code)) {
        const previousLength = bindings.length;
        bindings.push(...code);
        baseQuery += ` AND Cus.code IN (${code.map((_, index) => `$${previousLength + index + 1}`).join(", ")})`;
      } else baseQuery += ` AND Cus.code = $${bindings.length}`;
    }

    if (type) {
      bindings.push(type);
      baseQuery += ` AND Cus.type = $${bindings.length}`;
    }

    if (status) {
      const statusValues = getStatusValues(status);
      const previousLength = bindings.length;
      bindings.push(...statusValues);
      baseQuery += ` AND Cus.status IN (${statusValues.map((_, index) => `$${previousLength + index + 1}`).join(", ")})`;
    } else {
      bindings.push(STATUSES.ACTIVE);
      baseQuery += ` AND Cus.status = $${bindings.length}`;
    }

    if (from || to) {
      if (from && isValidDate(from)) {
        bindings.push(`${from}: 00:00:00`);
        baseQuery += ` AND Cus.created_at >= $${bindings.length} `;
      }

      if (to && isValidDate(to)) {
        bindings.push(`${to}: 00:00:00`);
        baseQuery += ` AND Cus.created_at <= $${bindings.length} `;
      }
    }

    baseQuery += ` GROUP BY Cus.id, Cus.name`;

    let countQuery = `SELECT COUNT(*) AS total FROM (${baseQuery}) AS DerivedTable`;

    // Checks if it was sent
    if (hasUnpaidInvoice !== null) {
      if (hasUnpaidInvoice === "true") {
        bindings.push(0);
        baseQuery += ` HAVING outstanding > $${bindings.length}`;
        countQuery += ` WHERE outstanding > $${bindings.length}`;
      } else {
        bindings.push(0);
        baseQuery += ` HAVING outstanding = $${bindings.length}`;
        countQuery += ` WHERE outstanding = $${bindings.length}`;
      }
    }

    const [{ total: totalCustomers }] = await sequelize.query(countQuery, {
      type: QueryTypes.SELECT,
      bind: bindings,
      nest: true,
      raw: false,
    });

    const { total, limit, skip, hasMore, nextPage } = paginate(totalCustomers, page, perPage);

    console.log(totalCustomers);

    baseQuery += ` ORDER BY Cus.created_at DESC LIMIT ${limit} OFFSET ${skip}`;
    const [foundCustomers] = await Promise.all([
      sequelize.query(baseQuery, {
        bind: bindings,
        type: QueryTypes.SELECT,
        transaction,
      }),
    ]);

    return {
      customers: foundCustomers,
      meta: {
        nextPage,
        page,
        perPage,
        hasMore,
        total,
      },
    };
  },

  async findAll({ conditions, paginateOptions, order }) {
    const { page = 1, perPage = 50 } = paginateOptions;
    const count = await Customer.count({
      where: {
        ...conditions,
        status: {
          [Op.ne]: STATUSES.DELETED,
        },
      },
    });
    const { limit, skip, hasMore, nextPage, total } = paginate(count, page, perPage);
    const customers = await Customer.findAll({
      limit,
      offset: skip,
      where: {
        ...conditions,
        status: {
          [Op.ne]: STATUSES.DELETED,
        },
      },
      order,
      attributes: ["id", "status", "code", "email", "name", "created_at", "type"],
      include: [
        Status,
        PhoneNumber,
        Category,
        Address,
        {
          model: Invoice,
          where: { status: STATUSES.PAID },
          order,
          limit: 1,
          separate: true,
          attributes: ["paidOn", "amount", "currency"],
        },
      ],
    });
    return {
      customers,
      meta: { nextPage, page, perPage, hasMore, total },
    };
  },

  async getCustomersTransactions({ conditions }) {
    const customer = await Invoice.findAll({
      where: conditions,
      attributes: [
        [Sequelize.fn("sum", Sequelize.col("Invoice.amount")), "amount"],
        [Sequelize.fn("count", Sequelize.col("Invoice.id")), "count"],
        "currency",
      ],
      group: ["Invoice.currency"],
    });
    return customer;
  },

  async updateCustomer({ queryParams, updateFields, transaction = null }) {
    return Customer.update(updateFields, { where: queryParams }, { transaction });
  },

  async getCustomerTransactions({ queryParams, transaction = null }) {
    const { page = 1, perPage = 20, from, to, customer, ...rest } = queryParams;

    const criteria = {};

    if (from || to) {
      criteria.created_at = {};
      if (from && isValidDate(from)) {
        criteria.created_at[Op.gte] = `${from}: 00:00:00`;
      }

      if (to && isValidDate(to)) {
        criteria.created_at[Op.lte] = `${to}: 00:00:00`;
      }
    }

    const totalCount = await InvoicePayment.count({
      where: { ...criteria, status: STATUSES.PAID, "$Invoice.customer$": customer },
      include: [
        {
          model: Invoice,
          attributes: ["id", "code"],
        },
        {
          model: Transfer,
          attributes: ["id", "code"],
        },
      ],
      transaction,
    });

    const { total, limit, skip, hasMore, nextPage } = paginate(totalCount, page, perPage);

    const customerTransactions = await InvoicePayment.findAll({
      where: { ...criteria, status: STATUSES.PAID, "$Invoice.customer$": customer },
      include: [
        {
          model: Invoice,
          attributes: ["id", "code"],
        },
        {
          model: Transfer,
          attributes: ["id", "code", "amount", "created_at", "currency"],
        },
      ],
      transaction,
      limit,
      offset: skip,
      order: [["created_at", "DESC"]],
    });

    return {
      transactions: customerTransactions,
      meta: {
        total,
        hasMore,
        nextPage,
        page: Number(page),
        perPage: Number(perPage),
      },
    };
  },

  async getSpentOverDuration({ queryParams, transaction = null }) {
    const { from, to, customer } = queryParams;

    const bindings = [STATUSES.PAID, customer];

    let query = `
    SELECT 
      YEAR(Inp.created_at) AS year, DATE_FORMAT(Inp.created_at, '%b') AS month, SUM(Trf.amount) as amount, Trf.currency AS currency 
    FROM InvoicePayments AS Inp 
    LEFT OUTER JOIN Invoices AS Inv ON Inp.invoice = Inv.id 
    LEFT OUTER JOIN Transfers AS Trf ON Inp.transfer = Trf.id 
    WHERE 
      Inp.status = $1 AND Inv.customer = $2`;

    if (from && isValidDate(from)) {
      bindings.push(`${from} 00:00:00`);
      query += ` AND Inp.created_at >= $${bindings.length}`;
    }

    if (to && isValidDate(to)) {
      bindings.push(`${to} 00:00:00`);
      query += ` AND Inp.created_at <= $${bindings.length}`;
    }

    query = `${query} GROUP BY YEAR(Inp.created_at), DATE_FORMAT(Inp.created_at, '%b'), Trf.currency`;

    return sequelize.query(query, {
      bind: bindings,
      type: QueryTypes.SELECT,
      transaction,
    });
  },
};
