const { Op, QueryTypes } = require("sequelize");
const { FundRequest, Vendor, BankAccount, Budget, Company, User, Balance, Transaction, sequelize, Asset } = require("../models");
const { STATUSES } = require("../models/status");

module.exports = {
  async getFundRequest({ queryParams, selectOptions = [], transaction = null }) {
    return FundRequest.findOne({
      where: queryParams,
      include: [
        Vendor,
        BankAccount,
        Budget,
        Company,
        Balance,
        {
          model: User,
          as: "User",
        },
        {
          model: User,
          as: "Reviewer",
        },
        {
          model: Asset,
          as: "FundRequestAssets",
        },
      ],
      ...(selectOptions.length && { attributes: selectOptions.concat(["code", "id", "created_at", "amount", "company"]) }),
      ...(transaction && { transaction }),
    });
  },

  async createFundRequest(payload) {
    return FundRequest.create(payload);
  },

  async updateFundRequest({ queryParams, payload, transaction }) {
    return FundRequest.update(payload, { where: queryParams, transaction });
  },

  async listFundRequests({ queryParams, includeTransaction = false, transaction = null }) {
    const include = [];

    if (includeTransaction) {
      include.push({
        model: Transaction,
      });
    }

    const { status } = queryParams;
    return FundRequest.findAll({
      where: {
        ...queryParams,
        ...(!status && {
          status: {
            [Op.ne]: STATUSES.DELETED,
          },
        }),
      },
      include,
      transaction,
    });
  },

  async fundRequestSummary({ filter, transaction = null }) {
    const { company, user } = filter;
    const bindings = [company];
    let query = `
        SELECT 
          Frq.currency,
          SUM(Frq.amount) AS total, 
          SUM(CASE WHEN Frq.status = ${STATUSES.PENDING} THEN Frq.amount ELSE 0 END) AS totalPending, 
          SUM(CASE WHEN Frq.status = ${STATUSES.APPROVED} THEN Frq.amount ELSE 0 END) AS totalApproved 
        FROM FundRequests AS Frq
        LEFT JOIN ApprovalRequests AS Apr ON Apr.entity_id = Frq.id AND Apr.entity = 'fundRequest'
        LEFT JOIN Approvals AS App ON Apr.id = App.request
        WHERE Frq.company = $1 
    `;

    if (user) {
      bindings.push(user);
      query = `${query} AND (Frq.user = $${bindings.length} OR App.approver = $${bindings.length})`;
    }

    query = `${query} GROUP BY currency`;

    return sequelize.query(query, {
      bind: bindings,
      type: QueryTypes.SELECT,
      transaction,
    });
  },
};
