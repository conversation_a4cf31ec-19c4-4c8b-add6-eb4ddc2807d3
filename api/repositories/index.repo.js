/* eslint-disable global-require */

module.exports = {
  // t: await sequelize.transaction(),
  UserRepo: require("./user.repo"),
  industryRepo: require("./industries.repo"),
  CountryRepo: require("./countries.repo"),
  CompanyRepo: require("./company.repo"),
  CardRepo: require("./card.repo"),
  AddressRepo: require("./address.repo"),
  DocumentRepo: require("./documents.repo"),
  PhoneNumberRepo: require("./phonenumber.repo"),
  IndividualRepo: require("./individual.repo"),
  BeneficiaryRepo: require("./beneficiary.repo"),
  BudgetRepo: require("./budget.repo"),
  StatusRepo: require("./status.repo"),
  UserBudgetRepo: require("./userBudget.repo"),
  AuditLogsRepo: require("./auditLogs.repo"),
  BalanceRepo: require("./balance.repo"),
  BalanceLedgerRepo: require("./balanceLedger.repo"),
  RoleRepo: require("./roles.repo"),
  PolicyRepo: require("./policy.repo"),
  PolicyTypeRepo: require("./policyType.repo"),
  BudgetPolicyRepo: require("./budgetPolicy.repo"),
  PermissionRepo: require("./permissions.repo"),
  TransactionRepo: require("./transaction.repo"),
  PolicyViolationRepo: require("./policyViolation.repo"),
  CategoryRepo: require("./category.repo"),
  MonoAccountHolderRepo: require("./monoAccountHolder.repo"),
  BudgetLedgerRepo: require("./budgetLedger.repo"),
  OnboardingInviteRepo: require("./onboardingInvite.repo"),
  TransactionTypeRepo: require("./transactionType.repo"),
  ApprovalRuleRepo: require("./approvalRule.repo"),
  ApproverRepo: require("./approvers.repo"),
  ApprovalConditionOperandRepo: require("./approvalConditionOperands.repo"),
  ApprovalConditionRepo: require("./approvalCondition.repo"),
  VendorRepo: require("./vendor.repo"),
  ApprovalRequestRepo: require("./approvalRequest.repo"),
  ApprovalStageRepo: require("./approvalStage.repo"),
  ApprovalRepo: require("./approval.repo"),
  ApproverLevelRepo: require("./approverLevel.repo"),
  NotificationRepo: require("./notification.repo"),
  ReimbursementRepo: require("./reimbursement.repo"),
  BatchTransactionRepo: require("./batchTransaction.repo"),
  AccountHolderRepo: require("./accountHolder.repo"),
  BankAccountRepo: require("./bankAccount.repo"),
  InvoiceRepo: require("./invoice.repo"),
  ProductRepo: require("./products.repo"),
  InvoiceProductRepo: require("./invoiceProducts.repo"),
  CustomerRepo: require("./customer.repo"),
  InvoiceAccountRepo: require("./invoiceAccount.repo"),
  VirtualCardRepo: require("./virtualCard.repo"),
  SettlementAccountRepo: require("./settlementAccount.repo"),
  ExternalIntegrationRepo: require("./externalIntegeration.repo"),
  SettlementRepo: require("./settlement.repo"),
  ScheduledTransactionRepo: require("./scheduledTransaction.repo"),
  ScheduleRepo: require("./schedule.repo"),
  CardHolderRepo: require("./cardHolder.repo"),
  SynchronizationRepo: require("./synchronization.repo"),
  CategoryMappingRepo: require("./categoryMapping.repo"),
  BudgetAccountRepo: require("./budgetAccount.repo"),
  TransferRepo: require("./transfer.repo"),
  ReferralRepo: require("./referral.repo"),
  ScheduledBudgetRepo: require("./scheduledBudget.repo"),
  PaymentPlanRepo: require("./paymentPlan.repo"),
  FundRequestRepo: require("./fundRequest.repo"),
  BalanceTypeRepo: require("./balanceType.repo"),
  CardRequestRepo: require("./cardRequest.repo"),
  CompanyLookupRepo: require("./companyLookup.repo"),
  TeamMemberRepo: require("./teamMember.repo"),
  InvoiceInstallmentRepo: require("./invoiceInstallment.repo"),
  ScheduledInvoiceRepo: require("./scheduledInvoice.repo"),
  ScheduledInvoiceProductRepo: require("./scheduledInvoiceProduct.repo"),
  InvoicePaymentRepo: require("./invoicePayment.repo"),
  MccRepo: require("./mcc.repo"),
  CardPolicyRepo: require("./cardPolicy.repo"),
  AccountMemberRepo: require("./accountMember.repo"),
  TransactionAttemptRepo: require("./transactionAttempt.repo"),
  PolicyConditionRepo: require("./policyCondition.repo"),
  PolicyConditionOperandRepo: require("./policyConditionOperands.repo"),
  AccountMigrationRepo: require("./accountMigration.repo"),
  CategorizationRuleRepo: require("./categorizationRule.repo"),
  CategorizationRuleConditionRepo: require("./categorizationRuleCondition.repo"),
  CardAssignmentRepo: require("./cardAssignment.repo"),
  PendingSettlementRepo: require("./pendingSettlement.repo"),
  BillRepo: require("./bill.repo"),
  ScheduledBillRepo: require("./scheduledBill.repo"),
  ScheduledBillProductRepo: require("./scheduledBillProduct.repo"),
  BillProductRepo: require("./billProduct.repo"),
  BillInstallmentRepo: require("./billInstallment.repo"),
  BillPaymentRepo: require("./billPayment.repo"),
  SpendRepo: require("./spend.repo"),
  CounterPartyRepo: require("./counterParty.repo"),
  BillingAddonRepo: require("./billingAddon.repo"),
  InvoiceTemplateRepo: require("./invoiceTemplate.repo"),
  CompanyPreferencesRepo: require("./companyPreferences.repo"),
  ThirdPartyLogRepo: require("./thirdpartylog.repo"),
  LedgerIdentityRepo: require("./ledgerIdentity.repo"),
  TaxRepo: require("./tax.repo"),
  TaxVersionRepo: require("./taxVersion.repo"),
};
