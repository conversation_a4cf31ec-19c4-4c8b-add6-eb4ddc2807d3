const { Mcc, MccCard, MccCardRequest } = require("../models");
const { STATUSES } = require("../models/status");

module.exports = {
  getMccs({ queryParams, selectOptions = [], transaction = null, meta = {} }) {
    return Mcc.findAll({
      where: queryParams,
      attributes: selectOptions.concat(["code", "mcc"]),
      ...(transaction && { transaction }),
      ...meta,
    });
  },

  getAnMcc({ queryParams, selectOptions = [], transaction = null }) {
    return Mcc.findOne({ where: queryParams, attributes: selectOptions.concat(["id", "code", "mcc"]), ...(transaction && { transaction }) });
  },
  count({ queryParams, meta = {} }) {
    return Mcc.count({ where: queryParams, ...meta });
  },

  createMccCards(card, mccs) {
    const payload = mccs.map((mcc) => ({ mcc: mcc.id, card }));
    return MccCard.bulkCreate(payload);
  },

  findOrCreateMccCards(card, mccs) {
    return mccs.map((mcc) => {
      return MccCard.findOrCreate({ where: { mcc: mcc.id, card, status: STATUSES.ACTIVE } });
    });
  },

  createMccCardRequests(cardRequest, mccs) {
    const payload = mccs.map((mcc) => ({ mcc: mcc.id, cardRequest }));
    return MccCardRequest.bulkCreate(payload);
  },

  findOrCreateMccCardRequests(cardRequest, mccs) {
    return mccs.map((mcc) => {
      return MccCardRequest.findOrCreate({ where: { mcc: mcc.id, cardRequest } });
    });
  },

  getMccCardRequests({ queryParams }) {
    return MccCardRequest.findAll({
      where: queryParams,
      include: [Mcc],
    });
  },

  getMccCards({ queryParams }) {
    return MccCard.findAll({
      where: queryParams,
      include: [Mcc],
    });
  },

  updateMccCards({ queryParams, payload }) {
    return MccCard.update(payload, {
      where: {
        ...queryParams,
      },
    });
  },
};
