/* eslint-disable no-param-reassign */
const { Op, Sequelize } = require("sequelize");
const {
  Policy,
  BudgetPolicy,
  Budget,
  PolicyType,
  PolicyCondition,
  PolicyDocument,
  Company,
  Asset,
  Team,
  Balance: Account,
  Vendor,
  Category,
  PolicyConditionOperand,
  PolicyUserException,
  User,
  TransactionType,
} = require("../models");
const { STATUSES } = require("../models/status");
const { ValidationError } = require("../utils/error.utils");

const Repo = {
  async getPolicy({ budgetCode, budget, queryParams, selectOptions = [], transaction = null }) {
    const { company } = queryParams;

    const subIncludes = [];

    subIncludes.push({
      model: Vendor,
      required: false,
      attributes: ["code", "id", "name"],
      as: "VendorOperands",
      where: { "$conditions.trigger$": "vendor" },
    });
    subIncludes.push({
      model: Budget,
      required: false,
      attributes: ["code", "id", "name"],
      as: "BudgetOperands",
      where: { "$conditions.trigger$": "budget" },
    });
    subIncludes.push({
      model: Team,
      required: false,
      attributes: ["code", "id", "name"],
      as: "TeamOperands",
      where: { "$conditions.trigger$": "team" },
    });
    subIncludes.push({
      model: Account,
      required: false,
      attributes: ["code", "id", "name"],
      as: "AccountOperands",
      where: { "$conditions.trigger$": "account" },
    });
    // ...(currency && { model: Currency, as: "VendorOperands", where: { operand: currency } }),
    subIncludes.push({
      model: Company,
      required: false,
      attributes: ["code", "id", "name"],
      as: "CompanyOperands",
      where: { "$conditions.trigger$": "subsidiary" },
    });
    subIncludes.push({
      model: Category,
      required: false,
      attributes: ["code", "id", "name"],
      as: "CategoryOperands",
      where: { "$conditions.trigger$": "category" },
    });
    subIncludes.push({
      model: TransactionType,
      required: false,
      attributes: ["code", "id", "name", "feature_name"],
      as: "TypeOperands",
      where: { "$conditions.trigger$": "type" },
    });

    const include = [
      PolicyType,
      {
        model: PolicyType,
        as: "PolicyTypes",
        required: false,
        where: { "$PolicyTypes.PolicyRestriction.status$": { [Op.notIn]: [STATUSES.DELETED] } },
      },
      {
        model: PolicyCondition,
        as: "conditions",
        where: { status: STATUSES.ACTIVE },
        required: false,
        include: [
          {
            model: PolicyConditionOperand,
            where: { status: STATUSES.ACTIVE },
            required: false,
            include: subIncludes,
          },
        ],
      },
      {
        model: PolicyDocument,
        as: "documents",
        where: { status: STATUSES.ACTIVE },
        required: false,
        include: Asset,
      },
      {
        model: PolicyUserException,
        as: "exceptions",
        where: { status: STATUSES.ACTIVE },
        required: false,
        include: {
          model: User,
          attributes: ["code", "firstName", "lastName"],
        },
      },
      User,
    ];
    if (budget) {
      // including Association
      const budgetPolicyCriteria = {
        model: BudgetPolicy,
        as: "BudgetPolicies",
        required: !!budgetCode,
        include: [
          {
            model: Budget,
            attributes: ["id", "code", "currency", "name", "spent", "amount", "status"],
            where: {
              company,
              ...(budgetCode && { code: budgetCode }),
            },
            required: !!budgetCode,
          },
        ],
      };
      include.push(budgetPolicyCriteria);
    }
    return Policy.findOne(
      {
        where: queryParams,
        ...(selectOptions.length && {
          attributes: selectOptions.concat(["id", "code"]),
        }),
        include,
      },
      transaction
    );
  },

  async getPolicies({ perPage, skip, queryParams = {}, selectOptions = [], transaction = null, search, budget, includeExceptions = false }) {
    const { company } = queryParams;

    if (search) queryParams[Op.or] = [{ name: { [Op.like]: `%${search}%` } }, { description: { [Op.like]: `%${search}%` } }];

    const budgetPolicyCriteria = {
      model: BudgetPolicy,
      as: "BudgetPolicies",
      required: !!budget,
      where: { status: { [Op.ne]: STATUSES.DELETED } },
      include: [
        {
          model: Budget,
          attributes: ["id", "code", "currency", "name", "spent", "amount", "status"],
          on: Sequelize.literal("BudgetPolicies.budget  = `BudgetPolicies->Budget`.`id`"),
          where: {
            company,
            ...(budget && { code: budget }),
          },
        },
      ],
    };

    const { budget: conditionBudget, vendor, account, amount, user, team, subsidiary, type, category, ...rest } = queryParams;

    const documentPolicyCriteria = {
      model: PolicyDocument,
      as: "documents",
      required: false,
      limit: 1,
      where: {
        status: { [Op.ne]: STATUSES.DELETED },
      },
      include: {
        model: Asset,
      },
    };

    const { subIncludes, whereQuery } = enhanceQuery({ conditionBudget, vendor, team, account, category, subsidiary, type, amount, user });

    const include = [
      ...(budget ? [budgetPolicyCriteria] : []),
      documentPolicyCriteria,
      { model: PolicyType, required: false }, // to remove
      User,
      {
        model: PolicyType,
        as: "PolicyTypes",
        required: false,
        where: { "$PolicyTypes.PolicyRestriction.status$": { [Op.ne]: STATUSES.DELETED } },
      },
      {
        model: PolicyCondition,
        as: "conditions",
        required: !!subIncludes.length,
        where: {
          status: STATUSES.ACTIVE,
          ...whereQuery,
        },
        include: {
          model: PolicyConditionOperand,
          where: { status: STATUSES.ACTIVE },
          required: !!subIncludes.length,
          include: subIncludes,
        },
      },
    ];

    if (includeExceptions) {
      include.push({ model: PolicyUserException, as: "exceptions", required: false, where: { status: STATUSES.ACTIVE } });
    }

    return Policy.findAll(
      {
        subQuery: false,
        where: rest,
        ...(selectOptions.length && {
          attributes: selectOptions.concat(["id", "code"]),
        }),
        include,
        order: [["created_at", "DESC"]],
        offset: skip,
        limit: perPage,
      },
      transaction && { transaction }
    );
  },

  async countPolicies({ queryParams = null, selectOptions = [], transaction = null, budget }) {
    const { budget: conditionBudget, vendor, account, amount, user, team, subsidiary, type, category, ...rest } = queryParams;

    const { subIncludes, whereQuery } = enhanceQuery({ conditionBudget, vendor, team, account, category, subsidiary, type, amount, user });

    const budgetPolicyCriteria = {
      model: BudgetPolicy,
      as: "BudgetPolicies",
      required: !!budget,
      where: { status: { [Op.notIn]: [STATUSES.DELETED] } },
      include: [
        {
          model: Budget,
          attributes: ["id", "code", "currency", "name", "spent", "amount", "status"],
          on: Sequelize.literal("BudgetPolicies.budget  = `BudgetPolicies->Budget`.`id`"),
          where: {
            company: rest.company,
            ...(budget && { code: budget }),
          },
        },
      ],
    };

    return Policy.count(
      {
        subQuery: false,
        where: rest,
        ...(selectOptions.length && {
          attributes: selectOptions.concat(["id", "code", "type"]),
        }),
        include: [
          ...(budget ? [budgetPolicyCriteria] : []),
          PolicyType, // to remove
          {
            model: PolicyType,
            as: "PolicyTypes",
            required: false,
            where: { "$PolicyTypes.PolicyRestriction.status$": { [Op.ne]: STATUSES.DELETED } },
          },
          {
            model: PolicyCondition,
            as: "conditions",
            required: !!subIncludes.length,
            where: {
              status: STATUSES.ACTIVE,
              ...whereQuery,
            },
            include: {
              model: PolicyConditionOperand,
              where: { status: STATUSES.ACTIVE },
              required: !!subIncludes.length,
              include: subIncludes,
            },
          },
        ],
        order: [["created_at", "DESC"]],
      },
      transaction && { transaction }
    );
  },

  count({ queryParams = null, transaction = null }) {
    return Policy.count({ where: queryParams, transaction });
  },

  async updatePolicy({ queryParams = null, updateFields, transaction = null }) {
    return Policy.update({ ...updateFields }, { where: queryParams }, transaction && { transaction });
  },

  async createPolicy({ queryParams, transaction }) {
    return Policy.create({ ...queryParams }, transaction && { transaction });
  },

  async linkPolicyDocuments(policy, documents, company) {
    return documents.map((documentId) =>
      PolicyDocument.create({
        policy,
        company,
        asset: documentId,
      })
    );
  },

  async findOrLinkPolicyDocuments({ policy, documents, company }) {
    return Promise.all(
      documents.map((document) => {
        return PolicyDocument.findOrCreate({
          where: {
            policy,
            company,
            asset: document,
            status: STATUSES.ACTIVE,
          },
        });
      })
    );
  },

  async addExceptionList(policy, users, company) {
    return users.map((userId) =>
      PolicyUserException.create({
        policy,
        company,
        user: userId,
      })
    );
  },

  async validateExceptionList({ policy, users }) {
    const foundExceptions = await PolicyUserException.findAll({
      where: {
        code: users,
        policy,
      },
    });

    if (foundExceptions.length !== users.length) {
      throw new ValidationError("Invalid condition sent");
    }

    return foundExceptions;
  },

  async findOrCreateExceptionList({ policy, users, company }) {
    return Promise.all(
      users.map((user) => {
        return PolicyUserException.findOrCreate({
          where: {
            policy,
            company,
            user,
            status: STATUSES.ACTIVE,
          },
        });
      })
    );
  },
};

module.exports = Repo;
function enhanceQuery({ conditionBudget, vendor, team, account, category, subsidiary, type, amount, user }) {
  const subIncludes = [];

  if (conditionBudget) subIncludes.push({ model: Budget, as: "BudgetOperands", where: { id: conditionBudget }, required: false });
  if (vendor) subIncludes.push({ model: Vendor, as: "VendorOperands", where: { id: vendor }, required: false });
  if (team) subIncludes.push({ model: Team, as: "TeamOperands", where: { id: team }, required: false });
  if (account) subIncludes.push({ model: Account, as: "AccountOperands", where: { id: account }, required: false });
  if (category) subIncludes.push({ model: Category, as: "CategoryOperands", where: { id: category }, required: !!category });
  if (subsidiary) subIncludes.push({ model: Company, as: "CompanyOperands", where: { id: subsidiary }, required: false });
  if (type) subIncludes.push({ model: TransactionType, as: "TypeOperands", where: { id: type }, required: false });

  const triggers = [
    { condition: conditionBudget, trigger: "budget" },
    { condition: vendor, trigger: "vendor" },
    { condition: category, trigger: "category" },
    { condition: account, trigger: "account" },
    { condition: amount, trigger: "amount" },
    { condition: user, trigger: "user" },
    { condition: team, trigger: "team" },
    { condition: subsidiary, trigger: "subsidiary" },
    { condition: type, trigger: "type" },
  ];

  const queries = triggers
    .filter(({ condition }) => !!condition)
    .map(({ trigger }) => ({
      "$conditions.trigger$": trigger,
    }));

  const whereQuery = queries.length ? { [Op.or]: queries } : {};
  return { subIncludes, whereQuery };
}
