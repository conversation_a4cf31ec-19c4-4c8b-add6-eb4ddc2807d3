const { Product } = require("../models");

module.exports = {
  async createProduct({ data, transaction = null }) {
    return Product.create(data, { transaction });
  },

  async findOrCreateProduct({ data, transaction = null }) {
    const [product] = await Product.findOrCreate({ where: data, transaction });
    return product;
  },

  async createProducts({ data, transaction }) {
    return Product.bulkCreate(data, { transaction, returning: true });
  },

  async getProducts({ filter, selectOptions = [], transaction = null }) {
    return Product.findAll(
      {
        where: filter,
        attributes: selectOptions.concat(["id", "code"]),
      },
      { transaction }
    );
  },

  async update({ filter, payload, transaction = null }) {
    return Product.update(payload, { where: filter }, { transaction });
  },
};
