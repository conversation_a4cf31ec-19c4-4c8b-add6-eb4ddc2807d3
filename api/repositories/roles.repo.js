const { Op, Sequelize } = require("sequelize");
const { Role, Permission, User, Company } = require("../models");
const { STATUSES } = require("../models/status");

module.exports = {
  async createRole({ queryParams: { company, name, value, description } }) {
    return Role.create({ company, status: STATUSES.ACTIVE, name, value, description });
  },

  async getRole({ queryParams: { code, company, value, name, ...rest }, selectOptions = [], transaction = null }) {
    return transaction
      ? Role.findOne(
          {
            where: {
              ...(code && { code }),
              ...(company && { company }),
              ...(value && { value }),
              ...(name && { name }),
              ...rest,
            },
            ...(selectOptions.length && {
              attributes: selectOptions.concat(["name", "code"]),
            }),
          },
          { transaction }
        )
      : Role.findOne({
          where: {
            ...(code && { code }),
            ...(company && { company }),
            ...(value && { value }),
            ...(name && { name }),
            ...rest,
          },
          ...(selectOptions.length && {
            attributes: selectOptions.concat(["name", "code"]),
          }),
        });
  },

  async getRoleWithPermissions(codeOrId) {
    return Role.findOne({
      where: {
        [Op.or]: [{ code: codeOrId }, { id: codeOrId }],
        status: { [Op.notIn]: [STATUSES.DELETED] },
      },
      include: [
        {
          model: Permission,
          attributes: ["name", "bit", "description", "code"],
          on: Sequelize.literal("Role.value & Permissions.bit"),
        },
      ],
    });
  },

  async getAllRoles({ queryParams = null, selectOptions = [] }) {
    const { company, ...rest } = queryParams;

    const companyCriteria = {};
    companyCriteria[Op.or] = [{ company: null }, { company }];

    return Role.findAll({
      where: { ...companyCriteria, ...rest },
      ...(selectOptions.length && {
        attributes: selectOptions.concat(["name", "code", "value"]),
      }),
      include: [
        Company,
        {
          model: Permission,
          attributes: ["name", "bit", "description", "code"],
          on: Sequelize.literal("Role.value & Permissions.bit"),
        },
      ],
      order: [
        ["company", "ASC"],
        ["id", "ASC"],
      ],
    });
  },

  async getUserRoles({ queryParams = null, selectOptions = [], transaction = null }) {
    const { company, ...rest } = queryParams;

    const companyCriteria = {};
    companyCriteria[Op.or] = [{ company: null }, { company }];

    return Role.findAll(
      {
        where: { ...companyCriteria, ...rest },
        ...(selectOptions.length && {
          attributes: selectOptions.concat(["name", "code"]),
        }),
        include: [
          {
            model: User,
            where: {
              company,
              status: STATUSES.ACTIVE,
            },
          },
        ],
      },
      transaction && { transaction }
    );
  },

  async updateRole({ queryParams = null, updateFields }) {
    return Role.update(updateFields, { where: queryParams });
  },

  async deleteRole(queryParams) {
    return Role.update({ status: STATUSES.DELETED }, { where: queryParams });
  },

  async getEmployeeRoleId() {
    const employeeRole = await Role.findOne({
      where: {
        name: 'Employee',
        status: STATUSES.ACTIVE
      }
    });

    if (!employeeRole) {
      return null;
    }

    return employeeRole.id;
  },
};
