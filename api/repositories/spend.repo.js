const { startOfMonth, endOfMonth, startOfYear, endOfYear, format } = require("date-fns");
const { fn, col, Op } = require("sequelize");
const { Asset, SpendTransaction, Spend, SpendPlan, Vendor, CreditCard: Card, SpendAsset, Transaction } = require("../models");
const { STATUSES } = require("../models/status");

module.exports = {
  async getStatistics(company) {
    // eslint-disable-next-line prefer-const
    let [{ totalCount = 0, amount: thisMonth = 0, nextBillingDate } = {}] = await Spend.findAll({
      attributes: [
        [fn("COUNT", col("id")), "totalCount"],
        [fn("SUM", col("amount")), "amount"],
        [fn("MIN", col("renewalDate")), "nexBillingDate"], // Earliest 'renewalDate' of the subs
      ],
      where: {
        company,
        status: STATUSES.ACTIVE,
        renewalDate: {
          [Op.between]: [startOfMonth(new Date()), endOfMonth(new Date())],
        },
      },
    });
    const [{ amount: totalAmount = 0 }] = await SpendTransaction.findAll({
      attributes: [[fn("SUM", col("Spend.amount")), "amount"]],
      where: {
        "$Spend.status$": STATUSES.ACTIVE,
        "$Spend.company$": company,
        created_at: {
          [Op.between]: [startOfYear(new Date()), endOfYear(new Date())],
        },
      },
      include: [Spend],
    });

    if (!nextBillingDate) {
      const closestRenewalSpend = await Spend.findOne({
        where: {
          company,
          status: STATUSES.ACTIVE,
        },
        limit: 1,
        order: [["renewalDate", "DESC"]],
      });
      nextBillingDate = closestRenewalSpend?.renewalDate ? format(closestRenewalSpend.renewalDate, "MMMM dd, yyyy") : "TBD";
    }

    return {
      thisMonth,
      totalAmount,
      total: totalCount,
      nextBillingDate,
    };
  },
  async listSubscriptions(criteria, options = { page: 1 }) {
    const { page = 1, perPage = 50 } = options;
    const { search, ...remainingCriteria } = criteria;
    const filter = { ...remainingCriteria };
    if (!filter.status) filter.status = STATUSES.ACTIVE;
    const total = await Spend.count({
      where: filter,
    });

    if (search) {
      filter[Op.or] = [
        { code: { [Op.like]: `%${search}%` } },
        { "$Vendor.name$": { [Op.like]: `%${search}%` } },
        { "$SpendPlan.name$": { [Op.like]: `%${search}%` } },
      ];
    }

    return {
      meta: {
        total,
        page,
        hasMore: page * perPage < total,
      },
      spends: await Spend.findAll({
        where: filter,
        include: [SpendPlan, Vendor, { model: Card, required: false }],
      }),
    };
  },
  async viewSubscription(codeOrId) {
    try {
      const spend = await Spend.findOne({
        where: {
          ...(String(codeOrId).startsWith("spd_") && { code: codeOrId }),
          ...(!String(codeOrId).startsWith("spd_") && { id: codeOrId }),
        },
        include: [SpendPlan, Vendor, { model: Card, required: false }],
      });
      return spend;
    } catch (error) {
      console.error(error);
      return null;
    }
  },
  async viewSubscriptionReceipts(spendId) {
    const assets = await SpendAsset.findAll({
      where: {
        spend: spendId,
      },
      include: [Asset],
    });
    return assets;
  },
  async viewSubscriptionTransactions(spendId) {
    const transactions = await SpendTransaction.findAll({
      where: {
        spend: spendId,
      },
      include: [Transaction],
    });
    return transactions;
  },
  async createSpendTransaction(spend, transaction, amount, currency) {
    return SpendTransaction.findOrCreate({
      where: {
        spend,
        transaction,
        amount,
        currency,
      },
    });
  },
  async fetchOrCreateSpendForBujetiFetch(payload) {
    // eslint-disable-next-line no-console
    const criteria = {};
    Object.entries(payload).forEach(([entry, value]) => {
      if (value) criteria[entry] = value;
    });
    try {
      const response = await Spend.findOne({
        where: criteria,
      });
      if (response) return response;
      await Spend.create(payload);
      return Spend.findOne({
        where: payload,
      });
    } catch (error) {
      console.error(error);
      return null;
    }
  },
  async update(criteria, payload) {
    return Spend.update(payload, { where: criteria });
  },
  async getSpendPlanForBujetiFetch(payload) {
    try {
      const foundPlan = await SpendPlan.findOne({
        where: {
          ...payload,
          ...(payload.company && {
            company: { [Op.or]: [null, payload.company] },
          }),
        },
      });
      if (foundPlan) return foundPlan;
      await SpendPlan.create(payload);
      return SpendPlan.findOne({
        where: payload,
      });
    } catch (error) {
      return console.error(error);
    }
  },
  async countTransactions(criteria) {
    const { to, from, ...filter } = criteria;
    if (to || from) {
      filter.created_at = {};
      if (from) filter.created_at[Op.gte] = from;
      if (to) filter.created_at[Op.lte] = to;
    }
    return SpendTransaction.count({ where: filter });
  },
};
