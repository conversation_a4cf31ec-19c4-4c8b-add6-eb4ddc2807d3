const { Op } = require("sequelize");
const { Address, Status } = require("../models");

let STATUSES = null;

module.exports = {
  async getOneStatus({ queryParams, selectOptions = [], transaction = null }) {
    return transaction
      ? Status.findOne(
          {
            where: queryParams,
            attributes: selectOptions.concat(["id", "value"]),
          },
          { transaction }
        )
      : Status.findOne({
          where: queryParams,
          attributes: selectOptions.concat(["id", "value"]),
        });
  },

  async getAllStatus({ queryParams = null, selectOptions = [], transaction = null }) {
    return transaction
      ? Status.findAll(
          {
            where: queryParams,
            attributes: selectOptions.concat(["value"]),
          },
          { transaction }
        )
      : Status.findAll({
          where: queryParams,
          attributes: selectOptions.concat(["value"]),
        });
  },

  async getStatuses() {
    if (STATUSES) return STATUSES;
    STATUSES = {};
    const statuses = await Status.findAll();
    statuses.forEach((status) => {
      STATUSES[status.value] = status.id;
    });
    return STATUSES;
  },

  // async createAnAddress({ queryParams, transaction = null }) {
  //     return transaction ? Address.create({ ...queryParams }, { transaction })
  //     : Address.create({ ...queryParams });
  // },

  // async updateAnAddress({ queryParams, updateFields, t }) {
  //     return transaction ? Address.update(updateFields, { where: queryParams }, { transaction })
  //     : Address.update(updateFields, { where: queryParams });
  // }
};
