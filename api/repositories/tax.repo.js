const { Op } = require("sequelize");
const { Tax, TaxVersion, TaxGroup, TaxGroupTax, TaxApplication, User, Company } = require("../models");
const { paginate } = require("../utils/utils");

const TaxRepo = {
  async create({ payload, transaction = null }) {
    return Tax.create(payload, { transaction });
  },

  async update({ filter, data, transaction = null }) {
    const [updatedRowsCount] = await Tax.update(data, {
      where: filter,
      transaction,
    });
    return updatedRowsCount;
  },

  async getTax({ filter, include = [], transaction = null }) {
    const defaultIncludes = [
      {
        model: User,
        as: "creator",
        attributes: ["id", "firstName", "lastName", "email"],
      },
      {
        model: Company,
        as: "companyDetails",
        attributes: ["id", "name", "code"],
      },
    ];

    if (include.includes("versions")) {
      defaultIncludes.push({
        model: TaxVersion,
        as: "versions",
        order: [["effective_from", "DESC"]],
      });
    }

    return Tax.findOne({
      where: filter,
      include: defaultIncludes,
      transaction,
    });
  },

  async listTaxes({ filter, transaction = null }) {
    const { page = 1, perPage = 50, search, ...rest } = filter;

    const where = {
      ...rest,
      ...(search && {
        [Op.or]: [{ name: { [Op.like]: `%${search}%` } }, { description: { [Op.like]: `%${search}%` } }],
      }),
    };

    const include = [
      {
        model: User,
        as: "creator",
        attributes: ["id", "firstName", "lastName", "email"],
      },
      {
        model: Company,
        as: "companyDetails",
        attributes: ["id", "name", "code"],
      },
      {
        model: TaxVersion,
        as: "versions",
        order: [["effective_from", "DESC"]],
        limit: 1,
      },
    ];

    const { count, rows: taxes } = await Tax.findAndCountAll({
      where,
      include,
      limit: parseInt(perPage, 10),
      offset: (parseInt(page, 10) - 1) * parseInt(perPage, 10),
      order: [["created_at", "DESC"]],
      transaction,
    });

    return {
      taxes,
      meta: {
        total: count,
        page: parseInt(page, 10),
        perPage: parseInt(perPage, 10),
      },
    };
  },

  async getActiveTaxes({ companyId, scope, transaction = null }) {
    return Tax.findAll({
      where: {
        company: companyId,
        is_active: true,
        scope,
      },
      include: [
        {
          model: TaxVersion,
          as: "versions",
          where: {
            [Op.or]: [{ effective_to: null }, { effective_to: { [Op.gt]: new Date() } }],
          },
          order: [["effective_from", "DESC"]],
          limit: 1,
        },
      ],
      transaction,
    });
  },

  async createTaxGroup(payload) {
    return TaxGroup.create(payload);
  },

  async getTaxGroup(filter = {}, options = {}) {
    const { include = [] } = options;

    const defaultIncludes = [
      {
        model: User,
        as: "creator",
        attributes: ["id", "firstName", "lastName", "email"],
      },
      {
        model: Company,
        as: "companyDetails",
        attributes: ["id", "name"],
      },
      {
        model: TaxGroupTax,
        as: "taxes",
        include: [
          {
            model: Tax,
            include: [
              {
                model: TaxVersion,
                as: "versions",
                where: {
                  [Op.or]: [{ effective_to: null }, { effective_to: { [Op.gt]: new Date() } }],
                },
                required: false,
                limit: 1,
              },
            ],
          },
        ],
        order: [["sequence_number", "ASC"]],
      },
    ];

    return TaxGroup.findOne({
      where: { ...filter, is_active: true },
      include: [...defaultIncludes, ...include],
    });
  },

  async findTaxGroups(query = {}) {
    const { page = 1, perPage = 50, where = {}, ...rest } = query;
    const { limit, skip } = paginate(0, page, perPage);

    const { count, rows: taxGroups } = await TaxGroup.findAndCountAll({
      where: {
        is_active: true,
        ...where,
        ...rest
      },
      include: [
        {
          model: User,
          as: "creator",
          attributes: ["id", "firstName", "lastName", "email"],
        },
        {
          model: Company,
          as: "companyDetails",
          attributes: ["id", "name"],
        },
        {
          model: TaxGroupTax,
          as: "taxes",
          required: false,
          attributes: ["id", "tax_group", "tax", "sequence_number", "user", "created_at"],
          include: [
            {
              model: Tax,
              required: false,
              include: [
                {
                  model: TaxVersion,
                  as: "versions",
                  required: false,
                  separate: true,
                  order: [["effective_from", "DESC"]],
                  limit: 1
                },
              ],
            },
          ],
          order: [["sequence_number", "ASC"]],
        },
      ],
      order: [["created_at", "DESC"]],
      limit,
      offset: skip
    });

    const paginationMeta = paginate(count ?? 0, page, perPage);

    return {
      taxGroups,
      meta: paginationMeta
    };
  },

  async updateTaxGroup(filter, payload, transaction = null) {
    await TaxGroup.update(payload, {
      where: filter,
      transaction
    });
    return this.getTaxGroup(filter);
  },

  async addTaxToGroup(payload) {
    return TaxGroupTax.create(payload);
  },

  async removeTaxFromGroup(taxGroup, tax) {
    return TaxGroupTax.destroy({
      where: { tax_group: taxGroup, tax },
    });
  },

  async updateTaxGroupSequence({ taxGroup, tax, sequenceNumber, transaction = null }) {
    await TaxGroupTax.update(
      { sequence_number: sequenceNumber },
      {
        where: { tax_group: taxGroup, tax },
        transaction
      }
    );
    
    return this.listTaxesInAGroup(taxGroup);
  },

  async reorderTaxGroupSequences(taxGroup, startingSequence, insertedId) {
    const sequelize = TaxGroupTax.sequelize;
    const transaction = await sequelize.transaction();

    try {
      // Get all taxes in order, excluding the newly inserted one
      const taxes = await TaxGroupTax.findAll({
        where: { 
          tax_group: taxGroup,
          id: { [Op.ne]: insertedId } // Exclude the newly inserted tax
        },
        order: [['sequence_number', 'ASC']],
        transaction
      });

      // Update sequences to be sequential, shifting everything from the startingSequence
      for (let i = 0; i < taxes.length; i++) {
        const tax = taxes[i];
        const newSequence = tax.sequence_number < startingSequence 
          ? tax.sequence_number  // Keep original sequence if before insertion point
          : tax.sequence_number + 1;  // Shift up by 1 if at or after insertion point

        await TaxGroupTax.update(
          { sequence_number: newSequence },
          {
            where: { id: tax.id },
            transaction
          }
        );
      }

      await transaction.commit();
      return this.listTaxesInAGroup(taxGroup);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async findTaxGroupTaxes(taxGroup) {
    return TaxGroupTax.findAll({
      where: { tax_group: taxGroup },
      include: [
        {
          model: Tax,
          include: [
            {
              model: TaxVersion,
              as: "versions",
              where: {
                effective_to: null,
              },
              required: false,
              limit: 1,
            },
          ],
        },
      ],
      order: [["sequence_number", "ASC"]],
    });
  },

  async createTaxApplication(payload) {
    return TaxApplication.create(payload);
  },

  async getTaxApplication(filter) {
    return TaxApplication.findOne({
      where: filter,
      include: [
        {
          model: TaxVersion,
          as: "taxVersionDetails",
          include: [
            {
              model: Tax,
              as: "tax",
            },
          ],
        },
        {
          model: User,
          as: "creator",
          attributes: ["id", "firstName", "lastName", "email"],
        },
      ],
    });
  },

  async listTaxApplications(criteria) {
    const { page = 1, perPage = 50, ...filter } = criteria;

    const { count, rows: applications } = await TaxApplication.findAndCountAll({
      where: filter,
      include: [
        {
          model: TaxVersion,
          as: "taxVersionDetails",
          include: [
            {
              model: Tax,
              as: "tax",
            },
          ],
        },
        {
          model: User,
          as: "creator",
          attributes: ["id", "firstName", "lastName", "email"],
        },
      ],
      limit: parseInt(perPage, 10),
      offset: (parseInt(page, 10) - 1) * parseInt(perPage, 10),
      order: [["created_at", "DESC"]],
    });

    return {
      applications,
      meta: {
        total: count,
        page: parseInt(page, 10),
        perPage: parseInt(perPage, 10),
      },
    };
  },

  async listTaxApplicationsByApplicable(filter) {
    const { page = 1, perPage = 50, applicableType, applicableId, ...rest } = filter;

    const { count, rows: applications } = await TaxApplication.findAndCountAll({
      where: {
        applicable_type: applicableType,
        applicable_id: applicableId,
        ...rest,
      },
      include: [
        {
          model: TaxVersion,
          as: "taxVersionDetails",
          include: [
            {
              model: Tax,
              as: "tax",
            },
          ],
        },
        {
          model: User,
          as: "creator",
          attributes: ["id", "firstName", "lastName", "email"],
        },
      ],
      limit: parseInt(perPage, 10),
      offset: (parseInt(page, 10) - 1) * parseInt(perPage, 10),
      order: [["sequence_number", "ASC"]],
    });

    return {
      applications,
      meta: {
        total: count,
        page: parseInt(page, 10),
        perPage: parseInt(perPage, 10),
      },
    };
  },

  async listTaxesInAGroup(taxGroup) {
    return TaxGroupTax.findAll({
      where: { tax_group: taxGroup },
      include: [
        {
          model: Tax,
          include: [
            {
              model: TaxVersion,
              as: "versions",
              where: {
                effective_to: null,
              },
              required: false,
              limit: 1,
            },
          ],
        },
      ],
      order: [["sequence_number", "ASC"]],
    });
  },
};

module.exports = TaxRepo;
