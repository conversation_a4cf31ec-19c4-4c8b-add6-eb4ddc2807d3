const { Op } = require("sequelize");
const { Team, TeamMember, User, Role, Status, PhoneNumber, TeamBudget, Budget, UserBudget, Transaction, sequelize } = require("../models");
const { SYSTEM_ROLES } = require("../models/role");
const { STATUSES } = require("../models/status");
const { TeamMemberRepo } = require("./index.repo");

module.exports = {
  async createTeam({ data, transaction }) {
    return Team.create({ ...data }, transaction && { transaction });
  },

  async getTeam({ filter, selectOptions = [], transaction = null, includeMembers = false, includeBudget = false }) {
    let include = [];
    const { user = null } = filter;

    if (includeBudget) {
      const budgetCriteria = {
        model: TeamBudget,
        required: false,
        include: [Budget],
      };

      include.push(budgetCriteria);
    }

    const teamTransactionCriteria = {
      model: Transaction,
      required: false,
      attributes: ["amount"],
      where: {
        status: STATUSES.SUCCESS,
      },
    };
    include.push(teamTransactionCriteria);

    const teamMembersCriteria = {
      model: TeamMember,
      required: !!user,
      include: [
        {
          model: User,
          include: [
            {
              model: UserBudget,
              attributes: ["amount", "spent"],
              include: [Budget],
            },
          ],
        },
        Role,
        Status,
      ],
      where: {
        status: [STATUSES.ACTIVE, STATUSES.INACTIVE],
        ...(user && { user }),
      },
    };

    include.push(teamMembersCriteria);

    const teamBudgetsCriteria = {
      model: TeamBudget,
      required: false,
      include: [Budget],
      where: {
        status: STATUSES.ACTIVE,
      },
    };

    include.push(teamBudgetsCriteria);

    return Team.findOne(
      {
        where: { ...filter, status: { [Op.notIn]: [STATUSES.DELETED] } },
        ...(selectOptions.length > 0 && { attributes: selectOptions }),
        include,
      },
      transaction && { transaction }
    );
  },

  async getAllTeams({ filter, selectOptions = [], transaction = null, includeMembers = false, includeTransaction = false }) {
    const { skip, limit, budget, manager, member, user = null, ...rest } = filter;
    let include = [
      {
        model: TeamBudget,
        required: !!budget,
        include: [Budget],
        where: {
          status: STATUSES.ACTIVE,
          ...(budget && { budget }),
        },
      },
    ];

    if (includeTransaction) {
      const teamTransactionCriteria = {
        model: Transaction,
        required: false,
        attributes: ["amount"],
        where: {
          status: STATUSES.SUCCESS,
        },
      };
      include.push(teamTransactionCriteria);
    }

    const teamMembersCriteria = {
      model: TeamMember,
      required: !!user || !!manager || !!member,
      include: [
        {
          model: User,
          include: [PhoneNumber],
        },
        Role,
        Status,
      ],
      where: {
        status: [STATUSES.ACTIVE, STATUSES.INACTIVE],
        ...(user && { user }),
        ...(manager && { role: SYSTEM_ROLES.MANAGER, user: manager }),
        ...(member && { role: SYSTEM_ROLES.EMPLOYEE, user: member }),
      },
    };

    include.push(teamMembersCriteria);

    return Team.findAndCountAll(
      {
        where: rest,
        ...(selectOptions.length > 0 && { attributes: selectOptions }),
        include,
        offset: skip,
        limit,
        order: [["created_at", "DESC"]],
        distinct: true,
      },
      transaction && { transaction }
    );
  },

  async updateTeam({ filter, payload, transaction }) {
    return Team.update(payload, { where: filter }, transaction && { transaction });
  },

  async getTeamsBudgets(teamIds, transaction = false) {
    const filter = { team: teamIds, status: STATUSES.ACTIVE };
    return TeamBudget.findAll(
      {
        where: filter,
        attributes: ["team", "budget"],
        include: [Budget],
      },
      transaction && { transaction }
    );
  },

  async findOneTeam({ conditions }) {
    return Team.findOne({
      where: conditions,
    });
  },

  async listUserTeams(user, company) {
    const teams = await TeamMemberRepo.getTeamsUserBelongsTo({
      filter: {
        user,
        company,
      },
    });
    return teams;
  },
};
