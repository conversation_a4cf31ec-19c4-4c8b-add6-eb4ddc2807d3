const AWS = require("aws-sdk");
const Utils = require("../utils/utils");
const ValidationError = require("../utils/validation-error");

const AWSClient = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION,
  signatureVersion: "v4",
});

const MIMES = {
  // Text and Document formats
  "text/plain": "txt",
  "text/html": "html",
  "text/css": "css",
  "text/csv": "csv",
  "application/json": "json",
  "application/xml": "xml",
  "application/pdf": "pdf",
  "application/msword": "doc",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "docx",
  "application/vnd.ms-excel": "xls",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "xlsx",
  "application/vnd.ms-powerpoint": "ppt",
  "application/vnd.openxmlformats-officedocument.presentationml.presentation": "pptx",
  "application/rtf": "rtf",

  // Images
  "image/png": "png",
  "image/jpeg": "jpg",
  "image/jpg": "jpg",
  "image/gif": "gif",
  "image/bmp": "bmp",
  "image/webp": "webp",
  "image/svg+xml": "svg",
  "image/tiff": "tiff",
  "image/x-icon": "ico",

  // Audio
  "audio/mpeg": "mp3",
  "audio/wav": "wav",
  "audio/ogg": "ogg",
  "audio/x-wav": "wav",
  "audio/x-aac": "aac",
  "audio/aac": "aac",
  "audio/flac": "flac",
  "audio/x-flac": "flac",
  "audio/mp4": "m4a",

  // Video
  "video/mp4": "mp4",
  "video/x-msvideo": "avi",
  "video/mpeg": "mpeg",
  "video/quicktime": "mov",
  "video/x-flv": "flv",
  "video/x-ms-wmv": "wmv",
  "video/webm": "webm",
  "video/ogg": "ogv",
  "video/x-matroska": "mkv",
};

const Service = {
  /**
   * Validate upload URL payload request
   * @param payload
   * @returns {{message: string}}
   */
  validateUploadUrlPayload(payload) {
    const { fileName, fileMime } = payload;
    if (!(fileName && fileMime)) throw new ValidationError('"fileName" and "fileMime" are required');
    if (!MIMES[fileMime]) throw new ValidationError("File type not supported");
    return { message: "Valid payload" };
  },

  getFileKey(companyName, companyCode, fileMime) {
    const extension = MIMES[fileMime];
    const slug = `${Utils.cleanUpString(companyName)}_${companyCode.replace("cmp_", "")}`;
    const fileName = `${Utils.generateRandomString(20)}.${extension}`;
    return `${slug}/${extension}/${fileName}`;
  },

  /**
   * Get S3 upload URL
   * @param fileKey
   * @returns {{signed_url: *, original_filename, key}}
   */
  getUploadUrl({ companyName, companyCode, fileMime, fileName }) {
    const fileKey = Service.getFileKey(companyName, companyCode, fileMime);

    const options = {
      Bucket: process.env.AWS_BUCKET,
      Expires: 300,
      Key: fileKey,
    };

    return {
      signedUrl: AWSClient.getSignedUrl("putObject", options),
      originalFilename: fileName,
      key: fileKey,
      url: Service.getAccessUrl(fileKey),
    };
  },

  getAccessUrl(fileKey) {
    const options = {
      Bucket: process.env.AWS_BUCKET,
      Key: fileKey,
    };
    return AWSClient.getSignedUrl("getObject", options);
    // return `https://${process.env.AWS_BUCKET}.s3.${process.env.AWS_REGION}.amazonaws.com/${fileKey}`;
  },

  /**
   * Upload a file
   * @param {*} filePath
   * @param {*} data
   */
  async uploadFileBuffer(filePath, data) {
    const options = {
      Bucket: process.env.AWS_BUCKET,
      Key: filePath,
      Body: data,
    };

    return AWSClient.upload(options);
  },

  deleteFile(fileKey) {
    const options = {
      Bucket: process.env.AWS_BUCKET,
      Key: fileKey,
    };

    return new Promise((resolve, reject) => {
      AWSClient.deleteObject(options, (err, data) => {
        if (err) {
          return reject(err); // Reject the promise if an error occurs
        }
        return resolve(data); // Resolve the promise with the response data
      });
    });
  },
};

module.exports = Service;
