const { STATUSES } = require("../models/status");
const {
  BalanceRepo,
  BudgetRepo,
  BankAccountRepo,
  BudgetAccountRepo,
  AccountMigrationRepo,
  AccountHolderRepo,
  TransferRepo,
} = require("../repositories/index.repo");
const SettingsService = require("./settings");
const { ValidationError, NotFoundError } = require("../utils/error.utils");
const { CARD_ISSUER } = require("../models/cardissuer");
const Providers = require("./providers");
const RedisService = require("./redis");
const ResponseUtils = require("../utils/response.utils");
const QueueService = require("./queue.service");
const Utils = require("../utils");

const AccountService = {
  async getSubAccountBookTransferDetails({ oldAccount, newAccount, company }) {
    const foundAccounts = await BudgetAccountRepo.getBudgetAccounts({
      filter: { id: [oldAccount, newAccount] },
    });
    if (!foundAccounts.length || foundAccounts.length !== 2) throw new ValidationError("Cannot find accounts");
    const { externalIdentifier: recipientId, type: recipientType } = foundAccounts.find((account) => account.id === newAccount);
    const { externalIdentifier: senderId, type: senderType } = foundAccounts.find((account) => account.id === oldAccount);
    const recipientAndSender = { recipientId, recipientType, senderId, senderType };

    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[company] || payment.defaultProvider;

    const amount = await Providers[providerToUse].virtualAccount.getAccountBalance(senderId, company);
    return { amount, ...recipientAndSender };
  },

  async getDepositAccountBookTransferDetails({ oldAccount, newAccount, company }) {
    const foundAccounts = await BankAccountRepo.getAllBankAccounts({
      queryParams: { id: [oldAccount, newAccount] },
      selectOptions: ["externalBankAccountId", "id"],
    });
    if (!foundAccounts.length || foundAccounts.length !== 2) throw new ValidationError("Cannot find accounts");
    const { externalBankAccountId: recipientId } = foundAccounts.find((account) => account.id === newAccount);
    const { externalBankAccountId: senderId } = foundAccounts.find((account) => account.id === oldAccount);
    const isSubaccount = String(senderId).endsWith("anc_subacc");
    const recipientAndSender = {
      recipientId,
      recipientType: "DepositAccount",
      senderId,
      senderType: isSubaccount ? "SubAccount" : "DepositAccount",
    };

    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[company] || payment.defaultProvider;

    const amount = await Providers[providerToUse].virtualAccount.getAccountBalance(senderId, company);
    return { amount, ...recipientAndSender };
  },

  async initiateAccountFunding(payload) {
    const { accountMigration, providerToUse } = payload;
    const foundAccountMigration = await AccountMigrationRepo.getAccountMigration({
      queryParams: { code: accountMigration },
    });
    if (!foundAccountMigration) throw new NotFoundError("Account Migration");
    const { oldAccount, newAccount, tableType, company } = foundAccountMigration;
    let bookTransferDetails;
    if (tableType === "BudgetAccount")
      bookTransferDetails = await AccountService.getSubAccountBookTransferDetails({ oldAccount, newAccount, company });
    else bookTransferDetails = await AccountService.getDepositAccountBookTransferDetails({ oldAccount, newAccount, company });

    const description = "Account Migration(Funding new Account)";
    const transferPayload = {
      currency: "NGN",
      description,
      amount: -1 * bookTransferDetails.amount,
      company,
      reference: Utils.generateRandomString(17).toLowerCase(),
      narration: description,
    };
    const transfer = await TransferRepo.createTransfer({
      data: transferPayload,
    });
    const bookTransferPayload = {
      ...bookTransferDetails,
      currency: transfer.currency,
      amount: Math.abs(bookTransferDetails.amount),
      reason: description,
      reference: transfer.reference,
      company,
      purpose: "ACCOUNT_MIGRATION",
      accountMigration,
      transfer: transfer.code,
      providerToUse,
    };

    const data = {
      data: bookTransferPayload,
      id: Utils.generateRandomString(17),
      path: `/transfers/bookTransfer/${transfer.code}/process`,
      key: process.env.INTRA_SERVICE_TOKEN,
    };

    await QueueService.addDelayedJob({}, data, `BookTransferReference:${payload.reference}`, 5);
    return ResponseUtils.sendObjectResponse("Ok");
  },

  async revertBalanceMigration(payload) {
    const { foundMigration } = payload;
    const { oldAccount } = foundMigration;

    await Promise.all([
      BankAccountRepo.updateABankAccount({ queryParams: { id: oldAccount }, updateFields: { status: STATUSES.ACTIVE } }),
      BalanceRepo.update({ bankAccount: oldAccount }, { status: STATUSES.ACTIVE }),
    ]);
  },

  async revertBudgetMigration(payload) {
    const { foundMigration } = payload;
    const { oldAccount } = foundMigration;

    await BudgetAccountRepo.update({ filter: { id: oldAccount }, payload: { status: STATUSES.ACTIVE } });
  },

  async revertMigration(payload) {
    const { accountMigration } = payload;

    const foundMigration = await AccountMigrationRepo.getAccountMigration({
      queryParams: { code: accountMigration },
    });

    if (foundMigration.tableType === "BankAccounts") {
      // Create new Deposit account
      await AccountService.revertBalanceMigration({ foundMigration });
    } else {
      // Create new Sub
      await AccountService.revertBudgetMigration({ foundMigration });
    }

    await AccountMigrationRepo.updateAccountMigration({
      queryParams: { id: foundMigration.id },
      payload: { status: STATUSES.FAILED },
    });
  },

  async createProviderAccount(payload) {
    const { accountMigration, trial = 0 } = payload;

    if (trial + 1 === 3) return AccountService.revertMigration(payload);

    const foundMigration = await AccountMigrationRepo.getAccountMigration({
      queryParams: { code: accountMigration },
    });

    if (!foundMigration) throw new NotFoundError("Account Migration Record");

    try {
      let response;
      if (foundMigration.tableType === "BankAccounts") {
        // Create new Deposit account
        response = await AccountService.createNewBalanceAccount({ accountMigration: foundMigration });
      } else {
        // Create new Sub
        response = await AccountService.createNewSubAccount({ accountMigration: foundMigration });
      }
      return response;
    } catch (error) {
      const SQSPayload = {
        id: `${foundMigration.code}`,
        idempotencyKey: `${foundMigration.code}`,
        path: `/accounts/${foundMigration.code}/process-migration`,
        key: process.env.INTRA_SERVICE_TOKEN,
        accountMigration: foundMigration.code,
        trial: Number(trial) + 1,
      };
      await QueueService.addDelayedJob({}, SQSPayload, `account_migration:${foundMigration.code}`, 5);
      throw error;
    }
  },

  async createNewSubAccount(payload) {
    const { accountMigration } = payload;
    const { company, oldAccount, code: accountMigrationCode } = accountMigration;

    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[company] || payment.defaultProvider;

    const [foundAccountHolder, foundBudgetAccount] = await Promise.all([
      AccountHolderRepo.getAccountHolder({
        filter: { company, provider: CARD_ISSUER[providerToUse] },
      }),
      BudgetAccountRepo.getBudgetAccount({
        filter: { id: oldAccount },
      }),
    ]);

    if (!foundAccountHolder) throw new NotFoundError("Account Holder");

    const { error, data } = await Providers[providerToUse].virtualAccount.createSubAccount(foundAccountHolder.externalIdentifier, company);
    if (error) throw Providers[providerToUse].throwProviderError(data);

    const {
      id,
      type,
      relationships: { virtualNubans },
    } = data;

    if (!virtualNubans.data.length) throw new ValidationError("Virtual NUBAN not generated");

    const { error: virtualAccountError, data: virtualAccountData } = await Providers[providerToUse].virtualAccount.getVirtualAccount(
      virtualNubans.data[0].id
    );

    if (virtualAccountError) throw Providers[providerToUse].throwProviderError(data);

    const budgetAccountPayload = {
      budget: foundBudgetAccount.budget,
      company,
      type,
      externalIdentifier: id,
      accountId: virtualNubans.data[0].id,
      provider: CARD_ISSUER[providerToUse],
      number: virtualAccountData.attributes.accountNumber,
      bankName: virtualAccountData.attributes.bank.name,
      accountName: virtualAccountData.attributes.accountName,
      bankCode: virtualAccountData.attributes.bank.nipCode,
      status: STATUSES.PROCESSING,
    };

    const createdSubaccount = await BudgetAccountRepo.createBudgetAccount({ payload: budgetAccountPayload });

    await AccountMigrationRepo.updateAccountMigration({
      queryParams: { code: accountMigrationCode },
      payload: { newAccount: createdSubaccount.id },
    });

    await AccountService.initiateAccountFunding({ accountMigration: accountMigrationCode, providerToUse });

    return ResponseUtils.sendObjectResponse("Ok");
  },

  async createNewBalanceAccount(payload) {
    const { accountMigration } = payload;
    const { company, oldAccount, code: accountMigrationCode } = accountMigration;

    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[company] || payment.defaultProvider;

    const [foundBalance, accountHolder] = await Promise.all([
      BalanceRepo.getBalance({
        filter: { bankAccount: oldAccount },
        includeAccount: true,
      }),

      AccountHolderRepo.getAccountHolder({
        filter: { company, provider: CARD_ISSUER[providerToUse] },
      }),
    ]);

    if (!foundBalance) throw new NotFoundError("Balance");
    if (!accountHolder) throw new NotFoundError("Account Holder");

    const {
      BalanceType: { code: balanceType },
      BankAccount: bankAccountObject,
    } = foundBalance;

    const { error, data } = await Providers[providerToUse].virtualAccount.createBankAccount(accountHolder.externalIdentifier, company);
    if (error) throw Providers[providerToUse].throwProviderError(data);
    // Save in redis to process again
    const { id, type, relationships } = data;

    await RedisService.set(id, JSON.stringify({ type: balanceType, company, action: "ACCOUNT_MIGRATION", accountMigrationCode }));
    const ngnBankAccountPayload = {
      owner: company,
      ownerType: bankAccountObject.ownerType,
      type: "virtual",
      subtype: "deposit",
      ...(bankAccountObject.parent && { parent: bankAccountObject.parent }),
      status: STATUSES.PENDING,
      issuer: CARD_ISSUER[providerToUse],
      currency: bankAccountObject.currency,
      externalBankAccountId: id,
      company,
      ...(bankAccountObject.purpose && { purpose: bankAccountObject.purpose }),
      ...(bankAccountObject.name && { name: bankAccountObject.name }),
    };

    const newCreatedAccount = await BankAccountRepo.createABankAccount({ queryParams: ngnBankAccountPayload });

    await Promise.all([
      AccountMigrationRepo.updateAccountMigration({
        queryParams: { code: accountMigrationCode },
        payload: { newAccount: newCreatedAccount.id },
      }),
    ]);

    return ResponseUtils.sendObjectResponse("Ok");
  },

  async migrateAccount(payload) {
    const { account } = payload;
    if (!account) throw new ValidationError("Please specify account");
    let foundAccount;
    const isBalance = String(account).startsWith("blc_");
    if (isBalance) {
      foundAccount = await BalanceRepo.getBalance({ filter: { code: account, status: STATUSES.ACTIVE }, includeAccount: true });
      if (!foundAccount) throw new NotFoundError("Balance");
    } else {
      foundAccount = await BudgetRepo.getBudget({ code: account }, true);
      if (!foundAccount) throw new NotFoundError("Budget Account");
    }
    let bankAccount;
    if (isBalance) ({ BankAccount: bankAccount } = foundAccount);
    else ({ BudgetAccount: bankAccount } = foundAccount);

    if (!bankAccount) throw new NotFoundError("Bank Account");

    if (isBalance && bankAccount.type !== "virtual") throw new ValidationError("Only virtual account can be migrated");

    if (bankAccount.status !== STATUSES.ACTIVE) throw new ValidationError("Cannot migrate this account as it's not active");

    try {
      if (isBalance) {
        await Promise.all([
          BankAccountRepo.updateABankAccount({ queryParams: { id: bankAccount.id }, updateFields: { status: STATUSES.MIGRATING } }),
          BalanceRepo.update({ id: foundAccount.id }, { status: STATUSES.MIGRATING }),
        ]);
      } else {
        // TODO: Confirm if you want to change BUDGET status
        await Promise.all([BudgetAccountRepo.update({ filter: { id: bankAccount.id }, payload: { status: STATUSES.MIGRATING } })]);
      }
      //   Create migration record
      const migrationPayload = {
        tableType: isBalance ? "BankAccounts" : "BudgetAccount",
        status: STATUSES.PROCESSING,
        company: foundAccount.company,
        oldAccount: bankAccount.id,
      };

      const createdMigration = await AccountMigrationRepo.createAccountMigration({
        data: migrationPayload,
      });

      const SQSPayload = {
        id: `${createdMigration.code}`,
        idempotencyKey: `${createdMigration.code}`,
        path: `/accounts/${createdMigration.code}/process-migration`,
        key: process.env.INTRA_SERVICE_TOKEN,
        accountMigration: createdMigration.code,
        trial: 0,
      };
      await QueueService.addDelayedJob({}, SQSPayload, `account_migration:${createdMigration.code}`, 5);
      return ResponseUtils.sendObjectResponse("Account migration is processing");
    } catch (error) {
      // Return account to previous status
      if (isBalance) {
        await Promise.all([
          BankAccountRepo.updateABankAccount({ queryParams: { id: bankAccount.id }, updateFields: { status: bankAccount.status } }),
          BalanceRepo.update({ id: foundAccount.id }, { status: foundAccount.status }),
        ]);
      } else {
        await Promise.all([BudgetAccountRepo.update({ filter: { id: bankAccount.id }, payload: { status: bankAccount.status } })]);
      }
      throw error;
    }
  },

  async finalizeMigration(payload) {
    const { accountMigration } = payload;

    const foundAccountMigration = await AccountMigrationRepo.getAccountMigration({
      queryParams: { code: accountMigration },
    });
    if (!foundAccountMigration) throw new NotFoundError("Account Migration");

    const { oldAccount, newAccount, tableType } = foundAccountMigration;
    if (tableType === "BudgetAccount") {
      await Promise.all([
        BudgetAccountRepo.update({ filter: { id: oldAccount }, payload: { status: STATUSES.MIGRATED } }),
        BudgetAccountRepo.update({ filter: { id: newAccount }, payload: { status: STATUSES.ACTIVE } }),
      ]);
    } else {
      const bankBalance = await BalanceRepo.getBalance({ filter: { bankAccount: oldAccount } });
      if (!bankBalance) throw new NotFoundError("Balance");
      await Promise.all([
        BankAccountRepo.updateABankAccount({ queryParams: { id: oldAccount }, updateFields: { status: STATUSES.MIGRATED } }),
        BalanceRepo.update({ id: bankBalance.id }, { bankAccount: newAccount, status: STATUSES.ACTIVE }),
      ]);
    }
    await AccountMigrationRepo.updateAccountMigration({
      queryParams: { id: foundAccountMigration.id },
      payload: { status: STATUSES.MIGRATED },
    });
    return ResponseUtils.sendObjectResponse("Ok");
  },
};

module.exports = AccountService;
