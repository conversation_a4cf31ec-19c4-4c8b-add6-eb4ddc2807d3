const NotFoundError = require("../utils/not-found-error");
const { Address } = require("../models");
const { CountryRepo } = require("../repositories/index.repo");
const addressRepo = require("../repositories/address.repo");
const { ValidationError } = require("../utils/error.utils");

const Service = {
  async createAddress(payload) {
    if (payload.country.includes("ctry_")) {
      const existingCountry = await CountryRepo.getOneCountry({
        queryParams: { code: payload.country, activate: true },
        selectOptions: ["code", "name", "iso_code", "caller_code"],
      });
      payload.country = existingCountry.name;
    }
    const data = {
      ...payload,
      ...(payload.stateOrProvince && { state: payload.stateOrProvince }),
      ...(payload.lga && { state: payload.lga }),
    };
    return Address.create(data);
  },
  async getAddress(criteria, required = true) {
    const address = await Address.findOne({ where: criteria });
    if (!address && required) throw new NotFoundError("Address");
    return address;
  },
  async findOrCreate(payload) {
    const { stateOrProvince: state, country = "NIGERIA", ...data } = payload;
    data.state = state;
    if (!country.startsWith("ctry_")) data.country = country;
    else {
      const foundCountry = await CountryRepo.getOneCountry({
        queryParams: { code: payload.country, activate: true },
        selectOptions: ["name"],
      });
      data.country = foundCountry.name;
    }
    const address = await Service.getAddress(data, false);
    if (address) return address;
    return Service.createAddress(data);
  },
};
module.exports = Service;
