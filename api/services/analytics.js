/* eslint-disable no-param-reassign */
const { Op } = require("sequelize");
const UserService = require("./user");
const TransactionService = require("./transaction");
const ReimbursementService = require("./reimbursementService");
const FundRequestService = require("./fundRequest.service");
const BalanceService = require("./balance");
const BudgetService = require("./budget");
const SanitizeService = require("../utils/sanitizer");
const HelperService = require("./helper.service");
const TransferService = require("./transfer");
const Utils = require("../utils");
const { STATUSES } = require("../models/status");
const { ReimbursementRepo, InvoiceRepo, ApprovalRequestRepo, TransactionRepo, BudgetRepo, BalanceRepo } = require("../repositories/index.repo");
const { NotFoundError } = require("../utils/error.utils");
const { Transaction, BatchTransaction, Reimbursement } = require("../models");

const Analytics = {
  async getCompanyStats(company, user, filters = { currency: "NGN" }) {
    if (filters.balance) {
      const balance = await BalanceRepo.getBalance({
        filter: {
          code: filters.balance,
          company,
        },
      });

      if (!balance) throw new NotFoundError("Balance");
      // eslint-disable-next-line no-param-reassign
      filters.balance = balance.id;
    }
    const [
      topSpenders,
      balances,
      disbursed,
      totalSpent,
      totalPaymentAmount,
      totalPaymentCount,
      totalReimbursementAmount,
      totalReimbursementCount,
      totalFundRequestAmount,
      totalFundRequestCount,
      totalOverdueInvoiceData,
      totalInReviewInvoiceData,
      recentTransactions,
      topSources,
      totalReceived,
    ] = await Promise.all([
      // get transactions grouped by user and sum of amounts, limit to top 5,
      // get total available budgets + company's balance
      TransactionService.getTopSpenders(company, filters),
      BalanceService.getBalances(company, filters.balance && { id: filters.balance }),
      BudgetService.getTotalBudgets(company),
      TransactionService.getTotalSpent({ ...filters, company }),
      TransactionService.getTotalPendingAmount({ company, user, ...filters }),
      TransactionService.totalPendingCount({ ...filters, user, company }),
      ReimbursementService.totalPendingAmount({ ...filters, company, loggedInUser: user }),
      ReimbursementService.totalPendingCount({ ...filters, company, loggedInUser: user }),
      FundRequestService.totalPendingAmount({ ...filters, company, loggedInUser: user }),
      FundRequestService.totalPendingCount({ ...filters, company, loggedInUser: user }),
      InvoiceRepo.totalOverdueAmountAndCount({ company, ...filters }),
      InvoiceRepo.totalInReviewCountAndAmount({ company, ...filters }),
      TransactionService.recentTransactions({ company, ...filters }),
      TransferService.getTopSources(company, filters),
      TransferService.totalReceived(company, filters),
    ]);
    filters.status = filters.status ? filters.status : [];
    const chartTransactions = await Analytics.getTransactionsChart(company, filters);
    const pendingTransactionRequests = await ApprovalRequestRepo.count({
      filters: { company, status: STATUSES.PENDING },
      attributes: ["entity", "entity_id"],
      group: ["entity", "entity_id"],
      include: [
        {
          model: Transaction,
          required: !!filters.balance,
          where: {
            "$ApprovalRequest.entity$": "transactions",
            ...(filters.balance && { balance: filters.balance }),
          },
        },
        {
          model: BatchTransaction,
          required: !!filters.balance,
          where: {
            "$ApprovalRequest.entity$": "batchTransactions",
            ...(filters.balance && { balance: filters.balance }),
          },
        },
        {
          model: Reimbursement,
          required: !!filters.balance,
          where: {
            "$ApprovalRequest.entity$": "reimbursement",
            ...(filters.balance && { balance: filters.balance }),
          },
        },
      ],
    });

    const { customer, ...rest } = filters;
    const { transactions } = await TransactionService.listTransactions({
      company,
      ...rest,
      status: "pending",
      perPage: 10,
    });
    const currencyFormattedChart = chartTransactions.reduce((collection, element) => {
      const { currency } = element;
      if (collection[currency]) {
        collection[currency].push(element);
      } else collection[currency] = [element];
      return collection;
    }, {});
    return {
      chart: Object.keys(currencyFormattedChart).reduce((collection, element) => {
        collection[element] = Utils.groupByKey(currencyFormattedChart[element], "created_at");
        return collection;
      }, {}),
      totalSpent: totalSpent.reduce((collection, element) => {
        const { currency } = element;
        collection[currency] = +element.amount;
        return collection;
      }, {}),
      totalReceived: HelperService.groupAmountsByCurrency(totalReceived),
      transactions: SanitizeService.sanitizeTransactions(transactions),
      recentTransactions,
      topSpenders: topSpenders.reduce((collection, element) => {
        const { currency } = element;
        element.amount = parseInt(element.amount, 10);
        if (collection[currency]) {
          collection[currency].push(element);
        } else collection[currency] = [element];
        return collection;
      }, {}),
      topSources: topSources.reduce((collection, element) => {
        const { currency } = element;
        element.amount = parseInt(element.amount, 10);
        if (collection[currency]) {
          collection[currency].push(element);
        } else collection[currency] = [element];
        return collection;
      }, {}),
      balances: balances.reduce((collection, element) => {
        const { currency, amount, type } = element;
        const balanceType = collection[type] || { [currency]: 0 };

        balanceType[currency] += parseInt(amount, 10);
        collection[type] = balanceType;
        return collection;
      }, {}),
      available: balances.reduce((collection, element) => {
        const { currency, amount } = element;
        if (!collection[currency]) collection[currency] = 0;
        collection[currency] += parseInt(amount, 10);
        return collection;
      }, {}),
      disbursed: disbursed.reduce((collection, element) => {
        const { currency, ...rest } = element;
        element.amount = parseInt(element.amount, 10);
        element.spent = parseInt(element.spent, 10);
        collection[currency] = rest;
        return collection;
      }, {}),
      pending: {
        reimbursements: await ReimbursementRepo.count({
          filters: { company, status: STATUSES.PENDING, ...(filters.balance && { balance: filters.balance }) },
        }),
        invoices: await InvoiceRepo.count({ filters: { company, status: STATUSES.PENDING } }),
        transactions: pendingTransactionRequests.length,
      },
      pendingAmountsAndCounts: {
        transactions: HelperService.groupByCurrency([...(totalPaymentAmount || []), ...(totalPaymentCount || [])]),
        reimbursements: HelperService.groupByCurrency([...(totalReimbursementAmount || []), ...(totalReimbursementCount || [])]),
        fundRequests: HelperService.groupByCurrency([...(totalFundRequestAmount || []), ...(totalFundRequestCount || [])]),
      },
      overDueAmountsAndCounts: {
        invoices: HelperService.groupByCurrency(totalOverdueInvoiceData),
      },
      inReviewAmountsAndCounts: {
        invoices: HelperService.groupByCurrency(totalInReviewInvoiceData),
      },
      alerts: {
        activeBudgets: await BudgetRepo.count({ company, status: STATUSES.ACTIVE }),
        lowBudgets: await BudgetRepo.countLowBudgets({ company }),
        uncategorizedTransactions: await TransactionRepo.count({
          filters: {
            company,
            category: {
              [Op.is]: null,
            },
            ...(filters.balance && { balance: filters.balance }),
          },
        }),
        violatedPolicies: await TransactionRepo.count({
          filters: {
            company,
            policiesViolated: {
              [Op.gt]: 0,
            },
            ...(filters.balance && { balance: filters.balance }),
          },
        }),
      },
    };
  },

  async getAnalytics(user, query) {
    const [stats, transactions] = await Promise.all([
      UserService.getCompanyStats(user, query),
      TransactionService.listTransactions({ ...query, perPage: 10 }),
    ]);
  },

  async getTransactionsChart(company, filters) {
    const { from, to, groupBy = "days", currency } = Utils.buildAnalyticsFilters(filters);
    const transactions = await TransactionService.listTransactionsRaw(company, {
      from,
      to,
      groupBy,
      currency,
      status: ["success", "imported"],
      ...(filters.balance && { balance: filters.balance }),
    });
    let datesRange = [];

    switch (groupBy) {
      case "days":
        datesRange = Utils.generateDaysInRange(from, to, "MMM do");
        break;
      case "weeks":
        datesRange = Utils.generateWeeksInRange(from, to, "io 'Week'");
        break;
      default:
        datesRange = Utils.generateDaysInRange(Utils.removeDays(new Date(), 30), new Date(), "MMM do");
    }
    const datesObject = [];
    // eslint-disable-next-line array-callback-return
    datesRange.forEach((element) => {
      datesObject[element] = { created_at: element, amount: 0, currency: filters.currency || "NGN" };
    });
    transactions.forEach((transaction) => {
      datesObject[transaction.created_at] = {
        ...transaction,
        amount: parseInt(transaction.amount, 10),
      };
    });
    return Object.values(datesObject);
  },

  async getMoneyMovementCharts(company, filters) {
    const { direction = "both" } = filters;

    let moneyIn = [];
    let moneyOut = [];

    const isMoneyIn = direction === "in" || direction === "both";
    const isMoneyOut = direction === "out" || direction === "both";

    if (isMoneyIn) {
      moneyIn = await TransferService.getTotalReceivedOverDuration(company, filters);
    }

    if (isMoneyOut) {
      moneyOut = await TransactionRepo.getExpensesBreakDown(company, filters);
    }

    return {
      ...(isMoneyIn && { moneyIn: HelperService.formatBreakDown(moneyIn) }),
      ...(isMoneyOut && { moneyOut: HelperService.formatBreakDown(moneyOut) }),
    };
  },
};

module.exports = Analytics;
