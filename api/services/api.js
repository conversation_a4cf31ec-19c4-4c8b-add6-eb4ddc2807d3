const axios = require("axios");
require("dotenv").config();

const requestHandler = async (request) => {
  try {
    const { data: response } = await request;
    const { message, data } = response;
    return {
      error: false,
      message,
      body: data,
    };
  } catch (error) {
    console.log(JSON.stringify(error.response?.data || error));
    const {
      response: { data },
      message,
    } = error;
    if (!data) {
      return {
        message,
        data,
        error: true,
      };
    }
    return {
      message,
      data,
      error: true,
    };
  }
};

const http = axios.create({
  headers: {
    authorization: `Bearer ${process.env.INTRA_SERVICE_TOKEN}`,
  },
  baseURL: `http://localhost:${process.env.PORT}`,
  timeout: 3000,
});

const sqsHttpClient = axios.create({
  headers: {
    "x-aws-sqsd-attr-key": `${process.env.INTRA_SERVICE_TOKEN}`,
    "x-aws-sqsd-attr-sqsjob": "true",
  },
  baseURL: `http://localhost:${process.env.PORT}`,
  timeout: 1000,
});

module.exports = {
  post(endpoint, payload, caller = null) {
    const client = caller === "sqs" ? sqsHttpClient : http;
    const request = client.post(endpoint, payload);
    return requestHandler(request);
  },

  put(endpoint, payload) {
    const request = http.put(endpoint, payload);
    return requestHandler(request);
  },
};
