const FileService = require("./FileService");
const { Asset } = require("../models");
const AssetValidator = require("../validators/asset");
const ValidationError = require("../utils/validation-error");
const policy = require("../models/policy");

const Service = {
  validateAssetCreationPayload(payload) {
    const { error } = AssetValidator.create.validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: true, message: "Valid payload" };
  },

  validateAssetGetPayload(payload) {
    const { error } = AssetValidator.get.validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: true, message: "Valid payload" };
  },

  async getAsset(code) {
    const asset = await Asset.findOne({
      where: { code },
    });
    if (!asset) return null;
    return {
      ...asset.toJSON(),
      key: undefined,
      url: await Service.getAssetDownloadURL({ key: asset.key }),
    };
  },
  async getAssets(criteria) {
    const assets = await Asset.findAll({
      where: criteria,
    });
    if (!assets) return [];
    return Promise.all(
      assets.map(async (asset) => {
        return {
          ...asset.toJSON(),
          key: undefined,
          url: await Service.getAssetDownloadURL({ key: asset.key }),
        };
      })
    );
  },

  async createAsset(payload) {
    const { source, sourceCode, ...rest } = payload;
    if (payload.source === "policy") {
      rest.entityType = "Policies";
    }
    return Asset.create(rest);
  },

  async updateAsset({ queryParams = null, updateFields, transaction = null }) {
    return Asset.update(updateFields, { where: queryParams }, { transaction });
  },

  async getAssetDownloadURL({ key }) {
    return FileService.getAccessUrl(key);
  },
};

module.exports = Service;
