const ValidationError = require("../utils/validation-error");
const { AuditLogsRepo } = require("../repositories/index.repo");
// const models = require("../models")
const models = require("../models");

module.exports = {
  async createLog({
    event,
    user,
    table_type: tableName,
    table_id: rowId,
    code,
    initial_state: initialState,
    delta,
  }) {
    if (!(event && user && tableName && (rowId || code))) {
      throw new ValidationError(
        "event, user, table_type, table_id are required"
      );
    }
    const foundTable =
      code &&
      (await models[tableName].findOne({
        where: { code },
        attributes: ["id"],
      }));

    return AuditLogsRepo.createAnAuditLogs({
      queryParams: {
        event,
        user,
        initial_state: initialState,
        delta,
        table_type: tableName,
        table_id: rowId || foundTable.id,
      },
    });
  },

  updateLog(code, payload) {
    return AuditLogsRepo.updateAAuditLogs(payload, { where: { code } });
  },
};
