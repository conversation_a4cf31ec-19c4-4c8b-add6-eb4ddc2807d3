const bcrypt = require("bcrypt");
const JWT = require("jsonwebtoken");
const Utils = require("../utils");
const secretKey = process.env.JWT_PRIVATE_KEY;
const { UserRepo } = require("../repositories/index.repo");
const { ValidationError, NotFoundError } = require("../utils/error.utils");
const RedisService = require("./redis");

const SALT_ROUNDS = 8;

module.exports = {
  /**
   * Generate JWT Token
   * @param {Object} payload
   * @param {String} payload.code user code
   * @param {Number} payload.id user id
   * @param {Number} payload.company company code
   * @returns { refreshToken: String, token: String } Object
   */
  generateJwtToken({ code, id, company = null }) {
    const payload = {
      code,
      company,
      sessionId: Utils.generateUniqueId(Utils.alphabet.alphanumeric, parseInt(process.env.JWT_SESSION_ID_LENGTH || 17, 10)),
    };

    const refreshToken = Utils.generateUniqueId(Utils.alphabet.alphanumeric, 40);
    const data = {
      token: refreshToken,
      user: id,
    };
    RedisService.setex(`refreshToken:${refreshToken}`, JSON.stringify(data), 259200); // TOKEN IS SET TO EXPIRE after 3 days
    return {
      refreshToken,
      token: JWT.sign(payload, secretKey, { expiresIn: "1h" }),
    };
  },
  /**
   * Decrypt a JWT Token
   * @param encodedToken
   * @returns {{payload: { code }, signature: *, header: *}|*}
   */
  decryptJwtToken(encodedToken) {
    try {
      return JWT.verify(encodedToken, secretKey);
    } catch (error) {
      return null;
    }
  },
  /**
   * Compare password to hash
   * @param password
   * @param hash
   * @returns {Promise<void|Boolean>}
   */
  async comparePassword(password, hash) {
    return bcrypt.compare(password, hash);
  },
  /**
   * Encrypt password
   * @param plainPassword
   * @returns {Promise<void|String>}
   */
  async encryptPassword(plainPassword) {
    return bcrypt.hash(plainPassword, SALT_ROUNDS);
  },

  async refreshToken({ user, token }) {
    const previousToken = Utils.parseJSON(await RedisService.get(`refreshToken:${token}`));

    if (!previousToken) throw new ValidationError("Invalid refresh token");
    if (previousToken.user !== user) throw new ValidationError(`Token invalid`); //WE CAN BLOCK THE USER'S ACCOUNT HERE LATER, TOKEN HIJACKED
    // GET USER BY CODE
    RedisService.delete(`refreshToken:${token}`);
    const foundUser = await UserRepo.getOneUser({
      queryParams: { id: previousToken.user },
    });
    if (!foundUser) throw new NotFoundError("User");
    return this.generateJwtToken(foundUser);
  },
};
