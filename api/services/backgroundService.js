const { Queue, Worker } = require("bullmq");

const queueCache = {};

const redisConnection = {
  url: process.env.REDIS_URL,
};

const BackgroundService = {
  initQueue(queueName) {
    if (!queueCache[queueName]) {
      queueCache[queueName] = new Queue(queueName, { connection: redisConnection });
    }
    return queueCache[queueName];
  },

  addToQueue(queueName, jobName, data, options = {}) {
    if (!data) return data;

    const queue = BackgroundService.initQueue(queueName);

    return queue.add(jobName, data, {
      removeOnComplete: options.removeOnComplete ?? true,
      removeOnFail: options.removeOnFail ?? {
        age: 24 * 3600, // 1 day (in seconds)
      },
      attempts: options.attempts ?? 5,
      backoff: options.backoff ?? {
        type: "exponential",
        delay: 3000, // 3 secs
      },
      delay: options.delay ?? 0,
    });
  },

  initWorker(queueName, processor) {
    return new Worker(queueName, processor, {
      connection: redisConnection,
    });
  },
};

module.exports = BackgroundService;
