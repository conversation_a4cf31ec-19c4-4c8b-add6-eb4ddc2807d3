const sequelize = require("sequelize");
const { <PERSON>lance, BalanceLedger, BalanceType } = require("../models");
// const { startOfWeek, endOfWeek } = require('date-fns');
const { STATUSES } = require("../models/status");
const Providers = require("./providers");
const RedisService = require("./redis");
const ValidationError = require("../utils/validation-error");
const Utils = require("../utils/utils");
const SettingsService = require("./settings");
const { BalanceRepo, BankAccountRepo, TransferRepo, TransactionRepo, CompanyRepo } = require("../repositories/index.repo");
const { NotFoundError } = require("../utils/error.utils");
const ResponseUtils = require("../utils/response.utils");
const { CARD_ISSUER } = require("../models/cardissuer");
const ChargeService = require("./charge");
const ApprovalService = require("./approval.service");
const BillingService = require("./billing");
const SettingService = require("./settings");
const QueueService = require("./queue.service");
const { METHODS, BANK_ACCOUNT_SUBTYPES } = require("../mocks/constants.mock");
const { TRANSACTION_TYPES } = require("../models/transaction");

const Service = {
  async topUpBalance(payload) {
    // TODO this needs to be done atomically
    const [balance] = await Balance.findOrCreate({
      where: {
        company: payload.company,
        currency: payload.currency,
      },
    });
    await balance.increment({
      amount: payload.amount,
    });
    return balance;
  },

  async findOrCreate(payload) {
    const [balance] = await Balance.findOrCreate({
      where: {
        company: payload.company,
        currency: payload.currency,
        ...(payload.code && { code: payload.code }),
      },
    });
    return balance;
  },
  async createLedger(payload) {
    const { balanceBefore, balanceAfter, ...criteria } = payload;
    const existingLedger = await BalanceLedger.findOne({ where: criteria });
    if (existingLedger) return existingLedger;
    return BalanceLedger.create(payload);
  },
  async viewBalance(company, currency = "NGN") {
    return Balance.findOne({
      where: {
        company,
        currency,
      },
    });
  },
  async getBalance(company, currency = "NGN", code = null) {
    const balance = await Balance.findOne({
      where: {
        company,
        currency,
        ...(code && { code }),
      },
    });
    const { id, ...rest } = balance || {};
    return rest;
  },
  async getBalances(company, filters = {}) {
    const balances = await Balance.findAll({
      attributes: ["id", "currency", "name"],
      where: {
        company,
        status: STATUSES.ACTIVE,
        ...filters,
      },
      include: [
        {
          model: BalanceType,
          attributes: ["id", "name"],
        },
        {
          attributes: ["balanceAfter"],
          model: BalanceLedger,
          limit: 1,
          order: [["created_at", "DESC"]],
          required: false,
          where: {
            company,
            status: [STATUSES.PENDING, STATUSES.PROCESSED, STATUSES.IMPORTED],
          },
        },
      ],
    });

    return balances.map((balance) => {
      const { currency, name, BalanceLedgers = [], BalanceType: balanceType } = balance.toJSON();
      return {
        name,
        type: balanceType?.name,
        currency,
        amount: BalanceLedgers.length > 0 ? BalanceLedgers[0].balanceAfter : 0,
      };
    });
  },
  async getAvailableBalance({ company, currency, code }) {
    const foundBalance = await Balance.findOne({
      where: { company, currency, status: STATUSES.ACTIVE, ...(code && { code }) },
    });

    if (!foundBalance) throw new NotFoundError("Balance");

    const result = await BalanceLedger.findOne({
      subQuery: false,
      attributes: [
        [sequelize.col("id"), "id"],
        [sequelize.col("balanceAfter"), "balance"],
        [sequelize.col("currency"), "currency"],
      ],
      where: {
        balance: foundBalance?.id,
      },
      order: [["id", "DESC"]],
    });
    const { balance = 0 } = (result && result.toJSON()) || {};
    return parseInt(balance || 0, 10);
  },

  /**
   * Finds or create a USD balance for a company, and also creates the balance ledgers.
   * @param {*} payload { amount, company, debitAmount, baseCurrency }
   * @param payload.amount is the amount in USD
   * @param payload.debitAmount is the amount in naira
   * @returns
   */
  async fundUSDBalanceFromNairaBalance(payload) {
    if (!payload) throw new ValidationError(`Invalid or empty payload`);
    const { amount, company, debitAmount, baseCurrency: currency = "NGN", recipient, source } = payload; // Base currency is the default currency, NGN

    const [{ id: baseCurrencyBalance }, { id: targetCurrencyBalance }] = await Promise.all([
      Service.findOrCreate({ company, currency, ...(source && { code: source }) }),
      Service.findOrCreate({ company, currency: "USD", ...(recipient && { code: recipient }) }),
    ]);
    const description = "Fund USD balance from Naira balance";
    // CREATE NGN LEDGER
    await Promise.all([
      Service.createLedger({
        company,
        currency,
        balance: baseCurrencyBalance,
        amount: -1 * debitAmount,
        description,
        status: STATUSES.PENDING,
      }),

      Service.createLedger({
        amount,
        company,
        currency: "USD",
        balance: targetCurrencyBalance,
        description,
        status: STATUSES.PENDING,
      }),
    ]);
  },

  /**
   * Get's USD Bank Account from Mono
   * @param {*} payload { amount, company }
   * @param {*} provider
   * @returns
   */
  async getUSDTransferAccountDetails(payload, provider = "mono") {
    const { amount, company } = payload;
    const { error, data, message } = await Providers[provider].getUSDTransferAccountDetails(payload);
    if (error) throw new ValidationError(message);
    const redisData = { company, reference: data.reference, amount };
    await RedisService.setex(
      `usd_funding_reference:${data.reference}`,
      JSON.stringify(redisData),
      SettingsService.get("CONFIG").fund_reference_expiry
    ); // SET TO EXPIRE AFTER 7 DAYS
    return { ...data, amount };
  },

  /**
   * Get's the current exchange rate from Mono, puts it in Redis and expires after 2 minutes
   * @param {*} provider //defaults: mono
   * @returns {
   *  rate: number,
   *  meta: {}
   * }
   */
  async getExchangeRate(provider = "mono") {
    let rate = Utils.parseJSON(await RedisService.get("usd_rate"));
    if (!rate) {
      rate = await Providers[provider].getExchangeRate();
      RedisService.setex("usd_rate", JSON.stringify(rate), 120);
    }
    return rate;
  },

  /**
   * Converts amount between NGN and USD
   * @param {*} param.targetCurrency Currency to convert to
   * @param  {*} param.amount amount to convert
   * @returns number
   */
  async getExchangeAmount({ targetCurrency, amount }) {
    const { rate } = await Service.getExchangeRate();
    let targetAmount = 0;
    if (Utils.isDollar(targetCurrency) === "USD") targetAmount = parseInt(amount, 10) / (parseInt(rate, 10) * 100);
    else if (Utils.isNaira(targetCurrency) === "NGN") targetAmount = parseInt(amount, 10) * parseInt(rate, 10);
    return targetAmount;
  },

  /**
   * Log a successful transfer on a balance
   * @param transaction
   * @returns {Promise<*>}
   */
  async logSuccessfulTransfer(transaction, ledger = null, extra = {}) {
    const {
      amount,
      id: transactionId,
      currency,
      narration,
      description,
      processor_fee: processorFee = 0,
      bujeti_fee: bujetiFee = 0,
      company,
    } = transaction;
    const balance = await Balance.findOne({
      where: { currency, company, status: STATUSES.ACTIVE },
    });
    const availableBalance = await Service.getAvailableBalance({
      company,
      currency,
    });

    const totalAmount = amount + processorFee + bujetiFee;
    return Service.createLedger({
      company,
      currency,
      amount: -1 * totalAmount,
      description: narration || description,
      transaction: transactionId,
      status: STATUSES.PENDING,
      balance: balance.id,
      balanceBefore: parseInt(availableBalance, 10),
      balanceAfter: parseInt(availableBalance, 10) - totalAmount,
      ...(ledger && ledger.id && { budgetLedger: ledger.id }),
      ...extra,
    });
  },

  /**
   * This checks if a balance/account is ready to handle transactions
   * @param {*} balance The balance id
   * @returns boolean
   */
  async canBalanceProcessTransaction(balance) {
    const foundBalance = await BalanceRepo.getBalance({
      filter: { id: balance },
      includeAccount: true,
    });
    if (!foundBalance) throw new NotFoundError("Balance");
    const {
      Status: { value },
      BankAccount,
    } = foundBalance;
    if (foundBalance.status !== STATUSES.ACTIVE)
      throw new ValidationError(`Cannot make payment from this balance as it is ${String(value).toLowerCase()}`);
    if ([STATUSES.MIGRATING, STATUSES.PROCESSING].includes(BankAccount.status))
      throw new ValidationError(`We are still migrating this account, Please try again after some time`);
    if (BankAccount.status === STATUSES.MIGRATED) throw new ValidationError(`This account has been migrated, Please reach out to support`);
    return true;
  },

  /**
   * This checks if Balance has sufficient funds and also checks if the status is good to go
   */

  async canBalanceHandleTransaction({ balance, amount, totalAmount, company, shouldCheckBalance = true }) {
    const filter = { company };
    if (String(balance).startsWith("blc_")) filter.code = balance;
    else filter.id = balance;
    const foundBalance = await BalanceRepo.getBalance({
      filter,
    });
    if (!foundBalance) throw new NotFoundError("Balance");

    await Service.canBalanceProcessTransaction(foundBalance.id);

    if (shouldCheckBalance) {
      const availableBalance = await BalanceRepo.getAvailableBalance({
        company,
        currency: foundBalance.currency,
        code: foundBalance.code,
      });

      // Without adding fees, if balance is less than amount, throw error
      if (availableBalance < amount) throw new ValidationError("Insufficient balance, please top up to make the payment");
      if (availableBalance < totalAmount)
        // in the future check if the balance is the bearer first, if it the balance it goes to the next instruction
        throw new ValidationError("This balance cannot bear the fees of the transaction, please top up to complete your transaction");
    }

    return ResponseUtils.sendObjectResponse("Balance is sufficient", { balance: foundBalance });
  },

  async updateBalance({ filter, payload }) {
    const { user, type, status, ...rest } = payload;
    const updatePayload = { ...rest };

    const foundBalance = await BalanceRepo.getBalance({
      filter,
      includeAccount: true,
    });

    if (!foundBalance) throw new NotFoundError("Balance");

    if (status) updatePayload.status = Utils.getStatusValues(status);

    if (type) updatePayload.type = await BalanceRepo.getBalanceTypes(type);

    if (updatePayload?.status === STATUSES.ARCHIVED) {
      await Service.liquidateArchivedBalance({ foundBalance, user });
    }

    await BalanceRepo.update(filter, updatePayload);

    if (foundBalance.status === STATUSES.ARCHIVED && updatePayload?.status === STATUSES.ACTIVE) {
      // Unfreeze account
      await Service.unfrezeeAccount({ balance: foundBalance });
    }
  },

  async unfrezeeAccount({ balance }) {
    const { company, BankAccount: { issuer, type = null, externalBankAccountId = null } = {} } = balance;
    // eslint-disable-next-line no-useless-return
    if (!externalBankAccountId || type !== "virtual") return;

    // eslint-disable-next-line no-useless-return
    if (issuer !== CARD_ISSUER.Anchor) return; // Only unfreeze anchor account for now

    const { payment } = SettingService.get("providers");
    const providerToUse = payment[company] || payment.defaultProvider;

    await Providers[providerToUse].virtualAccount.unfreezeAccount({ externalIdentifier: externalBankAccountId, company });
  },

  async liquidateArchivedBalance({ foundBalance = {}, user }) {
    const { BankAccount: { issuer, externalBankAccountId = null } = {} } = foundBalance;

    const availableAmount = await BalanceRepo.getAvailableBalance({
      company: foundBalance.company,
      currency: foundBalance.currency,
      id: foundBalance.id,
    });

    if (availableAmount) {
      // Checks that there's an available account to liquidate to
      const hasParentAccount = !!foundBalance.BankAccount.parent;
      if (!hasParentAccount) {
        const foundMainBalance = await BalanceRepo.getBalance({
          filter: { status: STATUSES.ACTIVE, company: foundBalance.company, id: { [sequelize.Op.ne]: foundBalance.id } },
          includeAccount: true,
        });
        if (!foundMainBalance) throw new NotFoundError("Account to liquidate balance to");
      }
    }

    const { payment } = SettingService.get("providers");
    const providerToUse = payment[foundBalance.company] || payment.defaultProvider;

    if (availableAmount <= 0) {
      // Just freeze the account on provider
      if (issuer === CARD_ISSUER.Anchor)
        Providers[providerToUse].virtualAccount.freezeAccount({ externalIdentifier: externalBankAccountId, company: foundBalance.company });
      return;
    }

    // Liquidate Balance and move money to either parent account or Main Balance
    const { BankAccount: sourceBankAccount, company, currency } = foundBalance;

    // Archive bank account
    await BankAccountRepo.updateABankAccount({
      queryParams: { id: foundBalance.bankAccount },
      updateFields: { status: STATUSES.ARCHIVED },
    });

    if (!sourceBankAccount.externalBankAccountId || sourceBankAccount.subType === BANK_ACCOUNT_SUBTYPES.LINKED) return; // Prevent moving funds from linked accounts

    const foundCompany = await CompanyRepo.getOneCompany({ queryParams: { id: company }, addPaymentPlan: true });

    let recipientAccount;
    const hasParentAccount = !!sourceBankAccount.parent;
    if (hasParentAccount) {
      recipientAccount = await BankAccountRepo.getOneBankAccount({
        queryParams: { id: sourceBankAccount.parent },
      });
    } else {
      // Get Main Balance account
      const foundMainBalance = await BalanceRepo.getBalance({
        filter: { status: STATUSES.ACTIVE, company: foundBalance.company, id: { [sequelize.Op.ne]: foundBalance.id } },
        includeAccount: true,
      });
      if (!foundMainBalance) throw new NotFoundError("Account to liquidate balance to");
      ({ BankAccount: recipientAccount } = foundMainBalance);
    }

    if (!recipientAccount) throw new ValidationError("Cannot find an account to liquidate your available balance to");

    // Check if both account are anchor
    const shouldUseBookTransfer = recipientAccount.issuer === sourceBankAccount.issuer && sourceBankAccount.issuer === CARD_ISSUER.Anchor;

    // Get provider balance
    const liquidationAmount = await Providers[providerToUse].virtualAccount.getAccountBalance(
      sourceBankAccount.externalBankAccountId,
      foundBalance.company
    );

    const narration = `Liquidating archived account into main account`;
    if (shouldUseBookTransfer) {
      // Create transfer
      const transferPayload = {
        amount: -1 * liquidationAmount,
        description: narration,
        company,
        currency,
        status: STATUSES.PENDING,
        reference: Utils.generateRandomString(14).toLowerCase(),
        processor_fee: 0,
        bujeti_fee: 0,
        narration,
      };
      const createdTransfer = await TransferRepo.createTransfer({
        data: transferPayload,
      });

      const bookTransferPayload = {
        senderId: sourceBankAccount.externalBankAccountId,
        senderType: Utils.getAccountType(sourceBankAccount.externalBankAccountId),
        recipientId: recipientAccount.externalBankAccountId,
        recipientType: Utils.getAccountType(recipientAccount.externalBankAccountId),
        currency,
        amount: Math.abs(liquidationAmount),
        reason: narration,
        reference: createdTransfer.reference,
        company,
        ...(user && { user }),
        balance: foundBalance.code,
        purpose: TRANSACTION_TYPES.ARCHIVED_ACCOUNT_LIQUIDATION,
        providerToUse,
      };

      const data = {
        data: bookTransferPayload,
        id: Utils.generateRandomString(17),
        path: `/transfers/bookTransfer/${createdTransfer.code}/process`,
        key: process.env.INTRA_SERVICE_TOKEN,
      };

      // eslint-disable-next-line consistent-return
      QueueService.addDelayedJob({}, data, `BookTransferReference:${createdTransfer.reference}`, 5);
    } else {
      // Use NIP
      const { cbnFee, bujetiFee } = BillingService.computeFees({
        amount: liquidationAmount,
        companyCode: foundCompany.code,
        currency,
        plan: Utils.parseJSON(foundCompany.PaymentPlan?.configuration),
        isDirectDebit: false,
      });

      const createdTransaction = await TransactionRepo.createTransaction({
        data: {
          amount: liquidationAmount,
          description: narration,
          payer: user,
          balance: foundBalance.id,
          company: foundCompany.id,
          currency,
          status: STATUSES.PROCESSING,
          narration,
          bank_account: recipientAccount.id,
          recipient: recipientAccount.owner,
          recipient_type: recipientAccount.ownerType,
          processor_fee: cbnFee,
          bujeti_fee: bujetiFee,
          type: TRANSACTION_TYPES.ARCHIVED_ACCOUNT_LIQUIDATION,
        },
      });

      await ApprovalService.addPaymentToQueue(createdTransaction.code);
    }
  },
};

module.exports = Service;
