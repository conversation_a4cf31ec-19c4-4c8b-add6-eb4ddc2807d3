/* eslint-disable camelcase */
const { Op, literal } = require("sequelize");
const { differenceInHours, format, isValid } = require("date-fns");
const { STATUSES } = require("../models/status");
const NotFoundError = require("../utils/not-found-error");
const Providers = require("./providers");
const {
  Bank,
  Mandate,
  DirectDebit,
  User,
  BankAccount,
  MonoAccountHolder,
  Status,
  CardIssuer: Provider,
  AccountLinking,
  Balance,
  Asset,
} = require("../models");
const ValidationError = require("../utils/validation-error");
const { CARD_ISSUER } = require("../models/cardissuer");
const Sanitizer = require("../utils/sanitizer");
const { log, Log } = require("../utils/logger.utils");
const VerificationService = require("./verification");
const responseUtils = require("../utils/response.utils");
const SettingsService = require("./settings");
const RedisService = require("./redis");
const Utils = require("../utils/utils");
const NotificationService = require("./notification");
const {
  AccountHolderRepo,
  BankAccountRepo,
  BalanceRepo,
  BalanceTypeRepo,
  DocumentRepo,
  CompanyRepo,
  TransactionRepo,
  BeneficiaryRepo,
  AccountMemberRepo,
  TransactionAttemptRepo,
  IndividualRepo,
} = require("../repositories/index.repo");
const { ExistsError } = require("../utils/error.utils");
const HelperService = require("./helper.service");
const { getStatusValues, parseISO, formatPhoneNumber, parseJSON } = require("../utils");
const AuditLogsService = require("./auditLogs");
const { MANDATE_TYPES, MANDATE_STATUS } = require("../models/mandate");
const QueueService = require("./queue.service");
const { VIRTUAL_ACCOUNT_PROVIDERS, CURRENCY } = require("../mocks/constants.mock");
const mockBanks = require("../mocks/banks.mock");
const { DOCUMENT_MAPPER, ID_TYPES } = require("../models/document");
const { getAssetDownloadURL } = require("./asset");

const Service = {
  async createBankAccount(payload, verify = true) {
    if (!payload) throw new ValidationError(`Invalid or empty payload`);

    payload.type = payload.type || "real";

    const { payment } = SettingsService.get("providers");
    const providerToUse = payment.defaultProvider;

    const companyQuery = [{ [Op.is]: null }];

    if (payload.company) {
      companyQuery.push({ [Op.eq]: payload.company });
    }

    const queryParams = {
      owner: payload.owner,
      number: payload.number,
      ownerType: payload.ownerType,
      company: {
        [Op.or]: companyQuery,
      },
    };

    const bank = await BankAccountRepo.getOneBankAccount({
      queryParams,
      selectOptions: ["isVerified", "number", "status", "type", "currency", "accountName", "bankName", "bankCode", "created_at"],
    });

    if (bank) {
      if (bank.isVerified) return bank;
      if (verify)
        return Service.verifyBank({
          bankCode: bank.bankCode,
          accountNumber: bank.number,
          bankId: bank.id,
        });
    }

    let bankPayload = payload;
    if (Utils.isLive()) {
      const bankAccount = await Service.verifyAccountNameFromProvider({ ...payload, accountNumber: payload.number }, providerToUse);
      const { accountName, ...rest } = payload;
      bankPayload = { ...rest, accountName: bankAccount.account_name };
    }

    const exisitingBanks = await BankAccountRepo.getAllBankAccounts({
      queryParams: {
        owner: payload.owner,
        ownerType: payload.ownerType,
        type: "real",
        company: {
          [Op.or]: companyQuery,
        },
      },
    });

    // If no exisiting account, create this one and make it default
    if (!exisitingBanks.length) return BankAccountRepo.createABankAccount({ queryParams: bankPayload });

    if (payload.defaultBank)
      await BankAccountRepo.updateABankAccount({
        queryParams: {
          owner: payload.owner,
          ownerType: payload.ownerType,
          type: "real",
          status: STATUSES.ACTIVE,
          company: {
            [Op.or]: companyQuery,
          },
        },
        updateFields: { status: STATUSES.INACTIVE },
      });

    return BankAccountRepo.createABankAccount({
      queryParams: { ...bankPayload, status: payload.defaultBank ? STATUSES.ACTIVE : STATUSES.INACTIVE },
      company: {
        [Op.or]: companyQuery,
      },
    });
  },

  async createVirtualAccount(payload) {
    const { currency } = payload;
    let account = {};
    switch (currency) {
      case "NGN":
        account = await this.createNairaVirtualDepositAccount(payload);
        break;
      case "USD":
        account = await this.createDollarVirtualDepositAccount(payload);
        break;
      default:
        break;
    }
    return account;
  },

  async createNairaVirtualDepositAccount(payload) {
    return this.generateVirtualAccount({ ...payload, additionalAccount: true });
  },

  async createDollarVirtualDepositAccount(payload) {
    return this.generateVirtualAccount({ ...payload, currency: "USD", additionalAccount: true });
  },

  async list(criteria) {
    if (!criteria.owner) throw new ValidationError("owner not specified");

    const bank = await BankAccount.findAll({
      where: {
        ...criteria,
        status: { [Op.notIn]: [STATUSES.DELETED] },
      },
      order: [
        ["status", "ASC"],
        ["created_at", "DESC"],
      ],
    });
    return bank;
  },

  async deleteBankAccount({ owner, bankCode }) {
    if (!owner) throw new ValidationError("owner not specified");
    const criteria = { code: bankCode, owner };

    const foundBank = await BankAccountRepo.getOneBankAccount({ queryParams: { ...criteria, status: { [Op.notIn]: [STATUSES.DELETED] } } });
    if (!foundBank) throw new NotFoundError("Bank Account");
    if (foundBank.status === STATUSES.ACTIVE) throw new ValidationError("A defaulted bank account can not be deleted");

    await BankAccountRepo.deleteBankAccount({ queryParams: criteria });
    return responseUtils.sendObjectResponse(`Bank deleted successfully`);
  },

  async defaultBankAccount({ bankCode, defaultBank = false, user }) {
    const criteria = { code: bankCode };
    const foundBank = await BankAccountRepo.getOneBankAccount({
      queryParams: { ...criteria, status: { [Op.notIn]: [STATUSES.DELETED] } },
      selectOptions: ["ownerType", "type", "status"],
    });
    if (!foundBank) throw new NotFoundError("Bank Account");
    if (foundBank.ownerType !== "user" || foundBank.type !== "real")
      throw new ValidationError("This action can not be performed on this bank account");
    if (defaultBank && foundBank.status === STATUSES.ACTIVE) throw new ValidationError("This bank account is already been set as default");

    // eslint-disable-next-line prettier/prettier
    const defaultedBanks = await BankAccountRepo.getAllBankAccounts({
      selectOptions: ["id"],
      queryParams: { status: STATUSES.ACTIVE, owner: user.id, ownerType: "user", type: "real" },
    });
    if (!defaultBank && defaultedBanks.length === 1) throw new ValidationError("One bank must be active");

    // banks to Un-default
    if (defaultedBanks.length && defaultBank) {
      await Promise.all(defaultedBanks.map(({ id }) => BankAccountRepo.unDefaultBankAccount({ queryParams: { id } })));
    }

    // eslint-disable-next-line no-unused-expressions
    defaultBank
      ? await BankAccountRepo.defaultBankAccount({ queryParams: criteria })
      : await BankAccountRepo.unDefaultBankAccount({ queryParams: criteria });
    return responseUtils.sendObjectResponse(`Bank ${defaultBank ? "defaulted" : "un-defaulted"} successfully`);
  },

  async findOrCreate(payload) {
    if (typeof payload !== "object") throw new ValidationError("Invalid criteria sent");
    if (payload && payload.code) return Service.getBankAccount({ code: payload.code });
    return Service.createBankAccount(payload);
  },

  async getBankAccount(criteria) {
    if (typeof criteria !== "object") throw new ValidationError("Invalid criteria sent");

    const bank = await BankAccount.findOne(criteria.where ? criteria : { where: criteria });
    if (!bank) throw new NotFoundError("Bank Account");
    if (!bank.isVerified)
      return Service.verifyBank({
        bankCode: bank.bankCode,
        accountNumber: bank.number,
        bankId: bank.id,
      });
    return bank;
  },

  async updateBankAccount({ id: owner }, payload) {
    if (!owner) throw new NotFoundError("User");
    if (!payload) throw new ValidationError(`Invalid or empty payload`);

    const bank = await BankAccount.findOne({
      where: { owner, ownerType: "user", type: "real" },
    });
    if (!bank) throw new NotFoundError(`Bank Account`);

    const updatedBank = await BankAccount.update(payload, {
      where: { owner, ownerType: "user", type: "real" },
    });
    return true;
  },

  async getVirtualAccount(company, accountProvider = "mono", { user, isManager, BalanceTenureService } = {}) {
    const account = await BankAccount.findOne({
      where: {
        owner: company,
        ownerType: "company",
        type: "virtual",
        status: STATUSES.ACTIVE,
        issuer: CARD_ISSUER[accountProvider],
      },
      include: [Status, Balance],
    });
    if (!account) throw new NotFoundError("Bank Account");

    let balance;

    if (account?.Balance?.code) {
      balance = await BalanceTenureService.getSingleBalance(company, account?.Balance?.code, { user, isManager, throwIfNotFound: false });
    }

    const { externalIdentifier, number, code, issuer } = account;

    if (externalIdentifier && number) return { bankAccount: account, balance };

    // Get Provider
    const provider = Sanitizer.getStatusById(CARD_ISSUER, issuer);
    log(Log.fg.blue, `INFO:Getting Account Details from ${provider}`);

    // Get Created Account Details from Provider
    const { error: getError, message: getMessage, body: getBody } = await Providers[provider.toLowerCase()].getVirtualAccount(externalIdentifier);

    // Updata BankAccount Record
    const { account_number, account_name, bank_name, bank_code, currency } = getBody;
    const updateFields = {
      number: account_number,
      accountName: account_name,
      bankName: bank_name,
      bankCode: bank_code,
      currency,
    };
    await BankAccountRepo.updateABankAccount({
      updateFields,
      queryParams: { externalIdentifier, code },
    });

    const foundAccount = await BankAccount.findOne({
      where: {
        owner: company,
        ownerType: "company",
        type: "virtual",
        status: STATUSES.ACTIVE,
      },
      include: [Status, Balance],
    });

    return { bankAccount: foundAccount, balance };
  },

  async getExtraData(company, provider) {
    if (provider === "mono") {
      const accountHolder = await MonoAccountHolder.findOne({
        where: { type: "main", company },
      });
      // if (!accountHolder) throw new ValidationError('Your account is still being reviewed.');
      if (!accountHolder) {
        VerificationService.concludeVerification({ companyId: company });
        return responseUtils.sendObjectResponse("Your account is being generated");
        // const accountHolderRecheck = await MonoAccountHolder.findOne({ where: { type: 'main', company }});
        // if (!accountHolderRecheck) throw new ValidationError('Your account is still being reviewed.');
      }

      return {
        holderId: accountHolder.holderId,
      };
    }
    throw new ValidationError("Your account is still being reviewed.");
  },

  async generateVirtualAccount(payload) {
    const {
      company,
      balanceType,
      accountType,
      companyCode,
      parent,
      provider = "mono",
      beneficiaries,
      isManager,
      user,
      purpose,
      accountNumber,
      bankCode,
      ...rest
    } = payload;

    let membersPayload = null;

    const key = `get_virtual_account_request:${companyCode}`;
    const hasExistingRequest = JSON.parse(await RedisService.get(key));
    if (hasExistingRequest) {
      await RedisService.setex(key, true, 120);
      return responseUtils.sendObjectResponse("Your account is being generated");
    }

    await RedisService.setex(key, true, 300);

    let foundBalanceType = "";
    if (balanceType || accountType || purpose) {
      foundBalanceType = await BalanceTypeRepo.getBalanceType({
        filter: {
          [Op.or]: [
            { code: balanceType || accountType || purpose },
            { name: { [Op.like]: `%${accountType}%` } },
            { name: { [Op.like]: `%${balanceType}%` } },
            { name: { [Op.like]: `%${purpose}%` } },
          ],
        },
      });

      if (!foundBalanceType) throw new NotFoundError("Balance Type");

      if (String(foundBalanceType.name).includes("Taxes")) {
        const existingTaxAccount = await BalanceRepo.getBalance({
          filter: { type: foundBalanceType.id, status: STATUSES.ACTIVE, company },
        });

        if (existingTaxAccount) throw new ExistsError("Tax account");
      }
    }

    // make request initiator account owner by default
    membersPayload = {
      isManager,
      beneficiaries: [],
      company,
    };

    if (user) {
      const foundBeneficiary = await BeneficiaryRepo.getOneBeneficiary({
        queryParams: {
          user,
          company,
        },
      });

      if (!foundBeneficiary) throw new Error("Beneficiary");
      membersPayload.beneficiaries = [{ beneficiary: foundBeneficiary.code, designation: "Owner" }];
      membersPayload.user = { id: user };
    }

    if (beneficiaries?.length) {
      membersPayload.beneficiaries = [...membersPayload.beneficiaries, ...beneficiaries];
    }

    if (rest.currency === CURRENCY.USD) {
      return createUsdAccount({
        company,
        accountNumber,
        bankCode,
        purpose,
        name: rest.name,
        foundBalanceType,
        membersPayload,
        redisKey: key,
      });
    }

    if (provider === VIRTUAL_ACCOUNT_PROVIDERS.PAYSTACK) {
      /* eslint-disable no-use-before-define */
      return createVirtualAccountOnPaystack({
        company,
        accountNumber,
        bankCode,
        currency: rest.currency,
        purpose,
        name: rest.name,
        foundBalanceType,
        membersPayload,
        provider: VIRTUAL_ACCOUNT_PROVIDERS.PAYSTACK,
        redisKey: key,
      });
    }

    if (provider === VIRTUAL_ACCOUNT_PROVIDERS.ANCHOR) {
      const accountHolder = await AccountHolderRepo.getAccountHolder({
        filter: { company, provider: CARD_ISSUER[provider] },
        selectOptions: ["externalIdentifier"],
      });
      if (!accountHolder && !payload.additionalAccount) {
        const companyWithAdmin = await CompanyRepo.getCompanyWithAdmins({ id: company }, true);
        NotificationService.sendEmail(
          [SettingsService.get("customer_experience_email")],
          "new-company-registration",
          {
            company: companyWithAdmin.name,
            name: companyWithAdmin.Users[0].firstName,
            contact: companyWithAdmin.Users[0].email,
          },
          { subject: "Reminder to review" }
        );
        return { error: true, message: "Your account is pending approval" };
      }
      if (rest?.currency && rest.currency !== "NGN") {
        await RedisService.delete(key);
        const bankData = await Providers[provider].virtualAccount.createForeignAccountNumber({ payload: { ...rest, company } });
        const bankPayload = {
          ...bankData,
          status: STATUSES.ACTIVE,
          owner: company,
          ownerType: "company",
          company,
          type: "virtual",
          subType: bankData.type,
          issuer: CARD_ISSUER[provider],
          isVerified: 1,
          parent,
          ...(purpose && { purpose }),
        };
        const createdBankAccount = await BankAccountRepo.createABankAccount({ queryParams: bankPayload });
        let createdBalance;
        if (foundBalanceType) {
          createdBalance = await BalanceRepo.findOrCreate({
            currency: rest.currency,
            company,
            status: STATUSES.ACTIVE,
            type: foundBalanceType.id,
            bankAccount: createdBankAccount.id,
            name: rest?.name,
            ...(purpose && { purpose }),
          });

          if (membersPayload) {
            await this.addMembers({ ...membersPayload, code: createdBalance.code });
          }
        }

        return createdBankAccount;
      }
      const response = await Providers[provider].virtualAccount.createBankAccount(accountHolder.externalIdentifier, company);
      if (response.error) Providers[provider].throwProviderError(response.data);

      const { id } = response.data;
      await RedisService.set(id, JSON.stringify({ type: foundBalanceType?.code || "Main", company, parent }));
      const ngnBankAccountPayload = {
        owner: company,
        ownerType: "company",
        company,
        type: "virtual",
        subtype: "deposit",
        ...(parent && { parent }),
        status: STATUSES.PENDING,
        issuer: CARD_ISSUER[provider],
        currency: rest.currency,
        externalBankAccountId: id,
        ...(purpose && { purpose }),
        name: rest?.name,
      };
      const createdNgnBankAccount = await BankAccountRepo.createABankAccount({ queryParams: ngnBankAccountPayload });

      let createdBalance;
      if (foundBalanceType) {
        createdBalance = await BalanceRepo.findOrCreate({
          currency: rest.currency,
          company,
          status: STATUSES.PENDING,
          type: foundBalanceType.id,
          bankAccount: createdNgnBankAccount.id,
          name: rest?.name,
          ...(purpose && { purpose }),
        });

        if (membersPayload) {
          await this.addMembers({ ...membersPayload, code: createdBalance.code });
        }
      }

      return response;
    }

    const extraData = await Service.getExtraData(company, provider || VIRTUAL_ACCOUNT_PROVIDERS.MONO);
    if (extraData.success) return { error: false, message: extraData.message };

    log(Log.fg.blue, `INFO:Creating Virtual Account`);
    const { error, message, body } = await Providers[provider].generateVirtualAccount({ ...rest, ...extraData });
    if (error) return { error, message };

    log(Log.fg.blue, `INFO:Getting Virtual Account`);
    const { error: getError, message: getMessage, body: getBody } = await Providers[provider].getVirtualAccount(body.id);
    if (getError) return { error: getError, message: getMessage };
    // eslint-disable-next-line camelcase
    const { account_number, account_name, bank_name, bank_code, currency } = getBody;

    const creationPayload = {
      externalIdentifier: body.id,
      owner: company,
      ownerType: "company",
      type: "virtual",
      issuer: CARD_ISSUER[provider],
      // eslint-disable-next-line camelcase
      ...(account_number && { number: account_number }),
      // eslint-disable-next-line camelcase
      ...(account_name && { accountName: account_name }),
      // eslint-disable-next-line camelcase
      ...(bank_name && { bankName: bank_name }),
      // eslint-disable-next-line camelcase
      ...(bank_code && { bankCode: bank_code }),
      ...(currency && { currency }),
      ...(rest.name && { name: rest.name }),
    };

    log(Log.fg.blue, `INFO:Saving Account Details in DB`);
    const bankAccount = await BankAccount.create(creationPayload);
    return {
      error: false,
      message: "Account is being generated",
      account: bankAccount,
    };
  },

  async fundBankAccount({ company, amount, bank, provider = "anchor" }) {
    const criteria = {
      owner: company,
      ownerType: "company",
      status: STATUSES.ACTIVE,
      type: "virtual",
      issuer: CARD_ISSUER[provider],
      ...(bank && { code: bank }),
    };
    const { externalIdentifier: accountId } = await Service.getBankAccount(criteria);
    if (!accountId) throw new NotFoundError("Bank account");
    return Providers[provider].fundBank(accountId, amount);
  },

  async verifyAccountName(payload, provider = "mono") {
    const { bankCode, accountNumber: number } = payload;
    const bankAccount = await BankAccount.findOne({
      attributes: [
        ["accountName", "account_name"],
        ["number", "account_number"],
      ],
      where: { bankCode, number },
    });
    if (!bankAccount) return Service.verifyAccountNameFromProvider(payload, provider);
    return bankAccount;
  },

  async handleAccountNameVerificationFailure({ payload, provider }) {
    const { attempt = 0, trials = 0, accountNumber, bankCode } = payload;

    const { accountVerificationProviders } = SettingsService.get("providers");
    const defaultProvider = accountVerificationProviders.find((singleProvider) => !!singleProvider.isDefault);

    const isAutoQueryRunning = Utils.parseJSON(await RedisService.get("autoVerifyRunning")); // Use this to prevent adding more account to the queue
    const isUsingDefaultProvider = provider === defaultProvider?.provider;
    // Checks if there's an auto query already checking enabled for default provider.
    // This also ensures we use only one account number for the checks
    // It stops checking after 4 failed attempts.
    const isAutoQueryRunningForCurrentAccount = !(isAutoQueryRunning && isAutoQueryRunning.accountNumber !== accountNumber);
    // We ensure there's only one account number in the retrial queue so we don't

    if (!isAutoQueryRunningForCurrentAccount || !isUsingDefaultProvider) return;

    // eslint-disable-next-line consistent-return
    if (attempt === 4) return RedisService.delete("autoVerifyRunning");

    // ADD TO QUEUE FOR INTERNAL ATTEMPTS
    const SQSPayload = {
      id: `${accountNumber}:${trials}`,
      idempotencyKey: `${accountNumber}:${trials}`,
      path: `/banks/verify/account/internal`,
      key: process.env.INTRA_SERVICE_TOKEN,
      accountNumber,
      bankCode,
      attempt: Number(attempt || 0) + 1,
      provider,
    };
    // eslint-disable-next-line no-await-in-loop
    await RedisService.set("autoVerifyRunning", JSON.stringify({ accountNumber }));
    QueueService.addDelayedJob({ tag: "verify:account" }, SQSPayload, `verifyAccount:${accountNumber}:${trials}`, trials + 10);
  },

  async verifyAccountNameFromProvider(payload, provider, options = {}) {
    const { bankCode, accountNumber, company = null } = payload;
    let error;
    let data;
    let status;
    let shouldTryNextProvider = true;

    const { isInternal = false } = options;

    const { accountVerificationProviders } = SettingsService.get("providers");

    const cachedProviderToUse = Utils.parseJSON(await RedisService.get("AccountNameVerificationActiveProvider"));
    let providerToUse = isInternal ? provider : cachedProviderToUse || provider;
    let trials = 0;

    while (shouldTryNextProvider) {
      // eslint-disable-next-line no-await-in-loop
      ({ error, data, status } = await Providers[providerToUse].verifyAccountName({
        bankCode,
        accountNumber,
        company,
      }));

      // If you get success or have gone through all providers
      if (status === 200 || trials + 1 === accountVerificationProviders.length) {
        shouldTryNextProvider = false; // break from the loop
        if (status === 200) RedisService.set("AccountNameVerificationActiveProvider", providerToUse);
      } else {
        trials += 1; // Keeps track of trials
        // eslint-disable-next-line no-await-in-loop
        await Service.handleAccountNameVerificationFailure({
          provider: providerToUse,
          payload: {
            accountNumber,
            bankCode,
            trials,
            ...(company && { company }),
            ...(payload?.attempt && { attempt: payload.attempt }),
          },
        });

        if (isInternal) {
          shouldTryNextProvider = false;
        } else {
          providerToUse = Utils.getNextProvider({ providers: accountVerificationProviders, currentProvider: providerToUse });
        }
      }
    }

    if (error) {
      if (providerToUse === "anchor") {
        const { errors = [] } = data || {};
        const [{ detail: message = "Could not verify account" } = {}] = errors;
        throw new ValidationError(message);
      }
      if (providerToUse === "mono") throw new ValidationError(data.message);
    }
    return Sanitizer.sanitizeVerifyAccountName(data, providerToUse);
  },

  async verifyBank(payload) {
    const { bankCode, accountNumber, bankId } = payload;

    const { payment } = SettingsService.get("providers");
    const providerToUse = payment.defaultProvider;

    if (Utils.isLive()) {
      const bankAccount = await Service.verifyAccountNameFromProvider({ bankCode, accountNumber }, providerToUse);
      await BankAccount.update(
        {
          accountName: bankAccount.account_name,
          isVerified: true,
        },
        {
          where: { id: bankId },
        }
      );
      return BankAccount.findOne({ where: { id: bankId } });
    }
    await BankAccount.update({ isVerified: true }, { where: { id: bankId } });
    return BankAccountRepo.getOneBankAccount({
      queryParams: { id: bankId },
      selectOptions: ["ownerType", "isVerified", "number", "type", "currency", "accountName", "bankName", "bankCode", "status"],
    });
  },

  async bulkVerifyAccount(payload, company) {
    const { payment } = SettingsService.get("providers");
    const provider = payment.defaultProvider;
    const { error, data } = await Providers[provider].bulkVerifyAccountName(payload, company);
    if (error) throw Providers[provider].throwProviderError(data);
    const formattedResponse = Sanitizer.sanitizeBulkVerifyAccountName({ provider, accounts: payload, verifiedResponse: data });

    return formattedResponse;
  },

  async getConnectedAccounts(filters) {
    let { page = 1, perPage = 50, search, status, ...remainingQuery } = filters;
    const { requiresReauth, from, to, owner } = remainingQuery;
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    const skip = Math.max(page - 1, 0) * perPage;

    const issuer = (await Provider.findOne({ where: { name: "Mono" } })).id;
    const criteria = {
      issuer,
      owner,
      type: "real",
      subType: "linked",
      ownerType: "company",
      ...(requiresReauth && { requiresReauth: true }),
    };

    if (from && to) {
      criteria.created_at = {};
      if (from && Utils.isValidDate(from)) {
        criteria.created_at[Op.gte] = from;
      }
      if (to && Utils.isValidDate(to)) {
        criteria.created_at[Op.lte] = to;
      }
    }

    if (search) {
      criteria[Op.or] = [
        { accountName: { [Op.like]: `%${search}%` } },
        { bankName: { [Op.like]: `%${search}%` } },
        { number: { [Op.like]: `%${search}%` } },
      ];
    }

    if (status) {
      status = typeof status === "object" ? status.map((item) => STATUSES[item.toUpperCase()]) : STATUSES[status.toUpperCase()];
    }

    const [total = 0, accounts] = await Promise.all([
      BankAccount.count({
        where: {
          ...criteria,
          ...(!status && { status: { [Op.notIn]: [STATUSES.DELETED] } }),
          ...(status && { status }),
        },
        order: [
          ["status", "ASC"],
          ["created_at", "DESC"],
        ],
      }),
      BankAccount.findAll({
        where: {
          ...criteria,
          ...(!status && { status: { [Op.notIn]: [STATUSES.DELETED] } }),
          ...(status && { status }),
        },
        distinct: true,
        col: "id",
        offset: skip,
        limit: perPage,
        order: [
          ["status", "ASC"],
          ["created_at", "DESC"],
        ],
      }),
    ]);

    return {
      accounts,
      meta: {
        page,
        total,
        perPage,
        hasMore: total > page * perPage,
        nextPage: page + 1,
      },
    };
  },

  async getConnectedAccount(code, criteria) {
    const { paymentPlan, owner } = criteria;

    const configurations = paymentPlan.configuration;
    const connectedAccountsConfig = configurations.connectedAccounts;
    let balance = null;

    const issuer = (await Provider.findOne({ where: { name: "Mono" } })).id;
    const foundAccount = await BankAccount.findOne({
      where: {
        code,
        issuer,
        owner,
        type: "real",
        subType: "linked",
        ownerType: "company",
        status: { [Op.notIn]: [STATUSES.DELETED] },
      },
    });

    if (!foundAccount) throw new NotFoundError("Connected account");

    const { connect } = SettingsService.get("providers");
    const provider = connect[owner] || connect.defaultProvider;

    const id = foundAccount.externalIdentifier;

    if (id && connectedAccountsConfig.canRequestRealTimeData) {
      const key = `${owner}:getMonoRealTimeData`;

      const cannotFetchRealTimeData = (await RedisService.get(key)) || foundAccount.requiresReauth;

      // fetch real time data if cannotFetchRealTimeData = false
      balance = await Providers[provider].account.details(id, owner, !cannotFetchRealTimeData);

      // if call was successful, use logic to ensure they never go beyond their plan and call the API at a reasonable pace
      if (!balance.error && !Service.isReAuthRequired(balance?.data?.code)) {
        // set new rate limit
        await RedisService.setex(key, 1, (24 / connectedAccountsConfig.maxRealTimeDataPerDay) * 60);
      }
    } else {
      balance = await Providers[provider].account.details(id, owner);
    }

    if (Service.isReAuthRequired(balance?.data?.code)) {
      foundAccount.requiresReauth = true;
      await foundAccount.save();
    }

    if (balance && !balance.error) {
      balance = balance?.data?.account?.balance ?? null;
    }

    return {
      ...foundAccount.dataValues,
      availableBalance: balance,
    };
  },

  async getConnectedAccountStatement(code, criteria) {
    const { paymentPlan, owner, previousMonthDuration = 1 } = criteria;

    let statement = [];
    const configurations = paymentPlan.configuration;
    const connectedAccountsConfig = configurations.connectedAccounts;

    const issuer = (await Provider.findOne({ where: { name: "Mono" } })).id;
    const foundAccount = await BankAccount.findOne({
      where: {
        code,
        issuer,
        owner,
        type: "real",
        subType: "linked",
        ownerType: "company",
        status: { [Op.notIn]: [STATUSES.DELETED] },
      },
    });

    if (!foundAccount) throw new NotFoundError("Connected account");

    const { connect } = SettingsService.get("providers");
    const provider = connect[owner] || connect.defaultProvider;

    const id = foundAccount.externalIdentifier;

    if (id && connectedAccountsConfig.canRequestRealTimeData) {
      const key = `${owner}:getMonoRealTimeData`;

      const cannotFetchRealTimeData = (await RedisService.get(key)) || foundAccount.requiresReauth;

      // fetch real time data if cannotFetchRealTimeData = false
      statement = await Providers[provider].account.getAccountStatement(
        { id, period: previousMonthDuration, company: owner },
        !cannotFetchRealTimeData
      );

      // if call was successful, use logic to ensure they never go beyond their plan and call the API at a reasonable pace
      if (!statement.error && !Service.isReAuthRequired(statement?.data?.code)) {
        // set new rate limit
        await RedisService.setex(key, 1, (24 / connectedAccountsConfig.maxRealTimeDataPerDay) * 60);
      }
    } else {
      statement = await Providers[provider].account.getAccountStatement({ id, period: previousMonthDuration, company: owner });
    }

    if (Service.isReAuthRequired(statement?.data?.code)) {
      foundAccount.requiresReauth = true;
      await foundAccount.save();
    }

    if (statement && !statement.error) {
      statement = statement?.data?.data ?? null;
    }

    return statement;
  },

  async initiateManualSync(code, payload, shouldFetchLiveData = true) {
    const { owner, doNotSendReAuthMail = false } = payload;

    let result = false;
    const issuer = (await Provider.findOne({ where: { name: "Mono" } })).id;
    const foundAccount = await BankAccount.findOne({
      where: {
        code,
        issuer,
        owner,
        type: "real",
        subType: "linked",
        ownerType: "company",
        status: { [Op.notIn]: [STATUSES.DELETED] },
      },
    });

    if (!foundAccount) throw new NotFoundError("Connected account");

    if (foundAccount.requiresReauth) {
      throw new ValidationError("Reauth required");
    }

    const { connect } = SettingsService.get("providers");
    const provider = connect[owner] || connect.defaultProvider;

    const id = foundAccount.externalIdentifier;

    if (!id) throw new ValidationError("Account not linked");

    const key = `${owner}:initiateDataSync`;

    const cannotFetchRealTimeData = await RedisService.get(key);

    if (!cannotFetchRealTimeData) {
      result = await Providers[provider].account.initiateManualSync(id, owner, { shouldFetchLiveData });
    }

    if (Service.isReAuthRequired(result?.data?.code)) {
      foundAccount.requiresReauth = true;
      await foundAccount.save();

      await AccountLinking.update({ syncStatus: STATUSES.FAILED }, { where: { bankAccount: foundAccount.id } });

      // send mail
      if (!doNotSendReAuthMail) await Service.sendReAuthRequiredMail(owner, foundAccount);

      return {
        requiresReauth: true,
        id,
      };
    }

    if (result && !result.error) {
      const updatedBalance = result?.data?.data?.balance;

      if (updatedBalance) {
        foundAccount.balance = updatedBalance;
        await foundAccount.save();
      }

      result = result?.data?.hasNewData ?? !!result?.data;
    }

    return result;
  },

  /**
   *
   * @param {*} company - req.company
   * @param {*} payload - {  accountNumber: string; startDate: string; endDate: string; signature: string; debitType: string; bankCode: string; amount: number }
   * @returns null
   */

  async createMandateForDirectDebit(company, payload) {
    const provider = getProvider(company.id);
    const providerHandler = await Providers[provider].directDebit;

    const foundCompany = await CompanyRepo.getOneCompany({
      queryParams: { id: company.id },
      selectOptions: [
        "code",
        "dateOfRegistration",
        "industry",
        "website",
        "address",
        "name",
        "description",
        "phoneNumber",
        "businessType",
        "document_reference",
        "director",
        "contactEmail",
      ],
      addIndustry: true,
      addPhoneNumber: true,
      addAddress: true,
    });

    if (!foundCompany) throw new NotFoundError("Company");

    const issuer = (await Provider.findOne({ where: { name: "Mono" } }))?.id;

    if (payload.bankAccount) {
      const foundBankAccount = await BankAccountRepo.getOneBankAccount({
        queryParams: {
          owner: company.id,
          code: payload.bankAccount,
        },
        selectOptions: ["number", "status", "type", "currency", "accountName", "bankName", "bankCode", "created_at"],
      });

      if (!foundBankAccount) throw new NotFoundError("Bank account");

      payload.bankAccount = foundBankAccount.id;
      payload.accountNumber = foundBankAccount.number;
      payload.bankCode = foundBankAccount.bankCode;
    } else {
      const createdBankAccount = await Service.findOrCreate({
        owner: company.id,
        ownerType: "company",
        bankCode: payload.bankCode,
        number: payload.accountNumber,
        accountName: payload.accountName,
        type: "real",
        subType: "linked",
        bankName: payload.bankName,
        status: STATUSES.ACTIVE,
      });
      payload.bankAccount = createdBankAccount.id;
    }

    // check if external customer was already created
    const foundExternalCustomer = await Mandate.findOne({
      where: {
        company: foundCompany.id,
        externalCustomerId: {
          [Op.ne]: null,
        },
      },
    });

    // create if customer not created
    if (!foundExternalCustomer) {
      const foundBvn = await DocumentRepo.getOneDocument({
        queryParams: { reference: foundCompany.document_reference, type: "bvn", status: { [Op.ne]: STATUSES.DELETED } },
        selectOptions: ["number", "table_id"],
      });

      if (!foundBvn) {
        log(Log.fg.red, `ERROR:BVN not found ${foundBvn}`);
        throw new ValidationError("Unable to create mandate as bvn not found");
      }

      const companyName = foundCompany.name;

      const reversedName = companyName?.split(" ").reverse();
      const mid = Math.floor(reversedName.length / 2);

      const firstName = reversedName.slice(mid).reverse().join(" ");
      const lastName = reversedName.slice(0, mid).reverse().join(" ");

      const createCustomer = await providerHandler.createCustomer(
        {
          identity: {
            type: "bvn",
            number: foundBvn.number,
          },
          email: foundCompany.contactEmail,
          firstName,
          lastName: lastName || firstName,
          address: foundCompany.Address?.street,
          phone: foundCompany.PhoneNumber?.localFormat,
        },
        foundCompany.id
      );

      if (createCustomer.error || !createCustomer.data?.data?.id) {
        log(Log.fg.red, `ERROR:Unable to create customer ${JSON.stringify(createCustomer)}`);
        throw new ValidationError("Unable to create mandate as customer cannot be profiled");
      }
      return createMandate({ foundCompany, payload, createCustomer, providerHandler, issuer });
    }

    // check if mandate exists for account number
    const existingMandate = await Mandate.findOne({
      where: {
        bankAccount: payload.bankAccount,
        status: {
          [Op.in]: ["pending", "granted"],
        },
      },
    });

    const canCreateNewMandate = await autoExpireEMandate(existingMandate);

    if (existingMandate && !canCreateNewMandate) {
      let transferDestinations = await RedisService.get(`mandate-banks:${existingMandate.code}`);
      const isStaging = Utils.isStaging();
      const isPendingMandate = existingMandate.status === MANDATE_STATUS.PENDING;
      const canMock = existingMandate.mandateType === MANDATE_TYPES.E_MANDATE && isStaging && isPendingMandate;

      if (canMock) {
        transferDestinations = [
          {
            bank_name: "Fidelity",
            account_number: "**********",
            icon: "https://mono-public-bucket.s3.eu-west-2.amazonaws.com/images/fidelity-bank-icon.png",
          },
          {
            bank_name: "Paystack Titan",
            account_number: "**********",
            icon: "https://mono-public-bucket.s3.eu-west-2.amazonaws.com/images/paystack-icon.png",
          },
        ];
      }

      const responseMap = {
        pending: "Mandate already created and pending approval",
        granted: "Mandate already active",
      };
      return {
        code: existingMandate.code,
        message: responseMap[existingMandate.status],
        transferDestinations: transferDestinations ? Utils.parseJSON(transferDestinations) : null,
      };
    }

    // create mandate if it does not exist
    return createMandate({
      foundCompany,
      payload,
      createCustomer: { data: { data: { id: foundExternalCustomer.externalCustomerId } } },
      providerHandler,
      issuer,
    });
  },

  /**
   *
   * @param {*} company - req.company
   * @param { object } payload - { bankCode: string; accountNumber: string; amount: number; narration: string, createdDirectDebit: DirectDebit  }
   */

  async directDebit(company, payload) {
    const existingMandate = await Mandate.findOne({
      where: {
        id: payload.createdDirectDebit?.mandate,
        status: "granted",
      },
    });

    if (!existingMandate) throw new ValidationError("No mandate found");

    if (+payload.amount > +existingMandate.amount) {
      throw new ValidationError("Amount greater than amount granted by mandate");
    }

    const { providerHandler, accountBalance } = await Service.confirmFundsAvailability(payload, existingMandate, company);

    const reference = Utils.generateRandomString(14);

    // create direct debit log and set to pending
    const createDirectDebit =
      payload.createdDirectDebit ||
      (await DirectDebit.create({
        mandate: existingMandate.id,
        company: company.id,
        amount: payload.amount,
        beneficiaryAccountNumber: payload.accountNumber,
        beneficiaryBankCode: payload.bankCode,
        narration: payload.narration,
        status: STATUSES.PENDING,
        reference,
      }));

    const createdDirectDebit = await DirectDebit.findOne({
      where: {
        reference: createDirectDebit.reference,
      },
      include: [
        {
          model: Mandate,
          include: [BankAccount],
        },
      ],
    });

    let transaction;

    if (createdDirectDebit.status === STATUSES.PROCESSING) {
      return { reference: createdDirectDebit.code };
    }

    if (payload.transactionPayload) {
      transaction = await TransactionRepo.createTransaction({ data: payload.transactionPayload });
    } else {
      transaction = await TransactionRepo.getTransaction({
        queryParams: {
          directDebitId: createdDirectDebit.id,
        },
      });
    }

    const existingTransactionAttempt = await TransactionAttemptRepo.getTransactionAttempt({
      filter: {
        transaction: transaction.id,
        status: STATUSES.PENDING,
      },
    });

    if (!existingTransactionAttempt) {
      await TransactionAttemptRepo.createTransactionAttempt({
        payload: {
          transaction: transaction.id,
          status: STATUSES.PENDING,
          amount: payload.amount,
          externalIdentifier: createDirectDebit.reference,
          reference: createDirectDebit.reference,
        },
      });
    }

    // initiate on provider
    const initiateDirectDebit = await providerHandler.debitAccount(
      {
        amount: payload.amount,
        reference: createDirectDebit.reference,
        narration: payload.narration,
        beneficiary: {
          nuban: payload.accountNumber,
          nip_code: Utils.getNIPCodeFromBankCode(payload.bankCode),
        },
      },
      existingMandate.externalIdentifier,
      company.id
    );

    if (initiateDirectDebit.error) {
      log(Log.fg.red, `ERROR:Unable to initiate direct debit ${JSON.stringify(initiateDirectDebit)}`);
      await DirectDebit.update(
        {
          status: STATUSES.FAILED,
        },
        { where: { reference: createDirectDebit.reference } }
      );
      transaction.status = STATUSES.FAILED;
      transaction.failure_reason = Utils.toTitle(String(initiateDirectDebit.data?.message).replace(/_/gi, " ").toLowerCase());
      await transaction.save();
      throw new ValidationError("Unable to debit account");
    }

    // set direct debit log to processing
    createdDirectDebit.status = STATUSES.PROCESSING;
    await createdDirectDebit.save();

    const balance = await BalanceRepo.findOrCreate({
      bankAccount: existingMandate.bankAccount,
      company: company.id,
      currency: "NGN", // TODO: make this dynamic if direct debit ever branches out to other currencies
    });

    // update name here and not above because names can be transient
    if (!balance.name) {
      balance.name = createdDirectDebit.Mandate?.BankAccount?.accountName || company.name;
      await balance.save();
    }

    const balanceAfter = Utils.money(accountBalance).minus(payload.amount).toNumber();

    // create balance ledger
    await BalanceRepo.createLedger({
      amount: Math.abs(payload.amount) * -1,
      currency: "NGN",
      company: company.id,
      balanceAfter: Math.max(balanceAfter, 0),
      balanceBefore: Math.max(Utils.money(accountBalance).toNumber(), payload.amount),
      balance: balance.id,
      description: payload.narration,
      transaction: transaction?.id,
    });

    return { reference: createdDirectDebit.code, transaction };
  },

  async confirmFundsAvailability(payload, existingMandate, company) {
    const provider = getProvider(company.id);
    const providerHandler = await Providers[provider].directDebit;

    // confirm funds availability
    const checkFundsAvailability = await providerHandler.confirmFundsAvailability(
      payload.amount / 100,
      existingMandate.externalIdentifier,
      company.id
    );

    if (checkFundsAvailability.error) {
      log(Log.fg.red, `ERROR:Unable to confirm funds availability ${JSON.stringify(checkFundsAvailability)}`);
      //   throw new ValidationError("Unable to debit account as funds availability cannot be confirmed");
    }

    if (!checkFundsAvailability.data?.data?.has_sufficient_balance) {
      log(Log.fg.red, `ERROR:Insufficient balance ${JSON.stringify(checkFundsAvailability)}`);
      //   throw new ValidationError("Insufficient balance");
    }
    return { providerHandler, accountBalance: checkFundsAvailability.data?.data?.account_balance };
  },

  async listMembers(filters) {
    let { page = 1, perPage = 50, status, code: bankCode, company, role, ...rest } = filters;

    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    const skip = Math.max(page - 1, 0) * perPage;

    const foundBalance = await BalanceRepo.getBalance({
      filter: {
        code: bankCode,
        company,
      },
      includeAccount: true,
    });

    if (!foundBalance) throw new NotFoundError("Balance");

    const { BankAccount: bankAccount } = foundBalance;

    if (!bankAccount) throw new NotFoundError("Bank Account");
    const criteria = {
      ...rest,
      company,
      bankAccount: bankAccount.id,
      ...(role && { designation: role }),
    };

    if (status) {
      criteria.status = getStatusValues(status);
    } else {
      criteria.status = {
        [Op.ne]: STATUSES.INACTIVE,
      };
    }

    const [total = 0, members = []] = await Promise.all([
      AccountMemberRepo.countMembers({
        filter: criteria,
        meta: {
          distinct: true,
          col: "id",
          offset: skip,
          limit: perPage,
          order: [["created_at", "DESC"]],
        },
      }),
      AccountMemberRepo.getMembers({
        filter: criteria,
        meta: {
          distinct: true,
          col: "id",
          offset: skip,
          limit: perPage,
          order: [["created_at", "DESC"]],
          include: [User],
        },
      }),
    ]);

    return {
      members,
      meta: {
        page,
        total,
        perPage,
        hasMore: total > page * perPage,
        nextPage: page + 1,
      },
    };
  },

  async addMembers(payload) {
    const { beneficiaries: incomingBeneficiaries, code, isManager, user, company } = payload;

    if (!incomingBeneficiaries) return null;

    const foundBalance = await BalanceRepo.getBalance({
      filter: {
        code,
        company,
      },
      includeAccount: true,
    });

    if (!foundBalance) throw new NotFoundError("Balance");

    const { BankAccount: bankAccount } = foundBalance;

    if (!bankAccount) throw new NotFoundError("Bank Account");

    const isAccountManagerActivity = await HelperService.checkIfCanManageAccount({ isManager, user: user.id }, bankAccount);
    const accountMembersMap = HelperService.buildAccountMembers(incomingBeneficiaries);

    const beneficiariesCodes = Utils.mapAnArray(incomingBeneficiaries, "beneficiary");

    const foundBeneficiaries = await BeneficiaryRepo.getAllBeneficiaries({
      queryParams: { code: beneficiariesCodes },
      selectOptions: ["id", "user", "code", "owner"],
      addUserAndPhone: true,
    });

    if (!foundBeneficiaries.length || foundBeneficiaries.length !== beneficiariesCodes.length)
      throw new ValidationError("One or more beneficiaries not found");

    const existingMembers = await AccountMemberRepo.getMembers({
      filter: {
        user: Utils.mapAnArray(foundBeneficiaries, "user"),
        status: STATUSES.ACTIVE,
        bankAccount: bankAccount.id,
      },
    });

    if (existingMembers.length) throw new ExistsError("Users");

    const addMembersPayload = foundBeneficiaries.map((beneficiary) => {
      return {
        bankAccount: bankAccount.id,
        user: beneficiary.user,
        status: STATUSES.ACTIVE,
        company,

        ...accountMembersMap[beneficiary.code],
      };
    });

    await AccountMemberRepo.addMembers(addMembersPayload);

    return Promise.all(
      foundBeneficiaries.map((beneficiary) =>
        Service.completeAddingMembers({
          beneficiary,
          user,
          accountMembersMap,
          foundBalance,
        })
      )
    );
  },

  async completeAddingMembers(data) {
    const { beneficiary, user, accountMembersMap, foundBalance } = data;
    AuditLogsService.createLog({
      event: "added-an-account-member",
      user: user.id,
      table_type: "Beneficiary",
      table_id: beneficiary.id,
    });

    const { firstName, email } = beneficiary.User;

    const payload = {
      firstName,
      designation: accountMembersMap[beneficiary.code].designation,
      accountName: foundBalance.name,
      managerName: `${user.firstName} ${user.lastName}`,
      accountUrl: `${Utils.getDashboardURL()}/accounts/subaccounts/${foundBalance.code}`,
    };

    const notificationPayload = {
      company: beneficiary.company,
      user_id: beneficiary.id,
      type: `info`,
      badge: `info`,
      title: `Account Member`,
      message: `You have been made a member on account (${foundBalance.name}). Your designation is ${
        accountMembersMap[beneficiary.code].designation
      }`,
      table: {
        code: foundBalance.code,
        entity: "Balance",
      },
      event: "accountMember",
    };

    NotificationService.saveNotification(notificationPayload);

    const sourceConfig = {
      subject: `You have been made a member on account (${foundBalance.name})`,
      from_name: "Bujeti",
    };

    NotificationService.notifyUser(email, "new-account-member", payload, sourceConfig);
  },

  async removeMember(payload) {
    const { code, member, isManager, user, company } = payload;

    const foundBalance = await BalanceRepo.getBalance({
      filter: {
        code,
        company,
      },
      includeAccount: true,
    });

    if (!foundBalance) throw new NotFoundError("Balance");

    const { BankAccount: bankAccount } = foundBalance;

    if (!bankAccount) throw new NotFoundError("Bank Account");

    const foundMember = await AccountMemberRepo.getMember({
      filter: {
        code: member,
        company,
        bankAccount: bankAccount.id,
      },
      selectOptions: ["user", "designation"],
    });

    if (!foundMember) throw new NotFoundError("Member");

    if (foundMember.designation === "Owner") throw new ValidationError("Cannot remove account owner");

    const isAccountManagerActivity = await HelperService.checkIfCanManageAccount({ isManager, user }, bankAccount);

    if (isAccountManagerActivity) {
      const beneficiaryIsManager = await HelperService.isManager(foundMember.user);
      if (beneficiaryIsManager) throw new ValidationError("Unauthorized to remove this user");
    }

    const accountMember = await AccountMemberRepo.getMember({
      filter: {
        bankAccount: bankAccount.id,
        user: foundMember.user,
        company,
      },
    });

    if (!accountMember) throw new NotFoundError("Member");

    await AccountMemberRepo.updateMember({
      payload: { status: STATUSES.INACTIVE },
      criteria: {
        bankAccount: bankAccount.id,
        user: foundMember.user,
        company,
      },
    });

    AuditLogsService.createLog({
      event: "removed-an-account-member",
      user,
      table_type: "AccountMember",
      table_id: foundMember.id,
    });
  },

  async getMandateStatus(payload) {
    const { code: mandateCode, company } = payload;

    const existingMandate = await Mandate.findOne({
      where: {
        code: mandateCode,
        company,
      },
    });

    if (!existingMandate) throw new NotFoundError("Mandate");

    let transferDestinations = await RedisService.get(`mandate-banks:${existingMandate.code}`);
    const isStaging = Utils.isStaging();
    const isPendingMandate = existingMandate.status === MANDATE_STATUS.PENDING;
    const canMock = existingMandate.mandateType === MANDATE_TYPES.E_MANDATE && isStaging && isPendingMandate;

    await autoExpireEMandate(existingMandate);

    if (canMock) {
      transferDestinations = [
        {
          bank_name: "Fidelity",
          account_number: "**********",
          icon: "https://mono-public-bucket.s3.eu-west-2.amazonaws.com/images/fidelity-bank-icon.png",
        },
        {
          bank_name: "Paystack Titan",
          account_number: "**********",
          icon: "https://mono-public-bucket.s3.eu-west-2.amazonaws.com/images/paystack-icon.png",
        },
      ];
    }

    return {
      code: existingMandate.code,
      status: existingMandate.status,
      transferDestinations: transferDestinations ? Utils.parseJSON(transferDestinations) : null,
    };
  },

  async cancelMandateOrSync(company, payload) {
    const { code, mandate, bankSync, directDebit } = payload;

    const bankAccount = await BankAccountRepo.getOneBankAccount({
      queryParams: {
        code,
      },
    });

    if (!bankAccount) throw new NotFoundError("Bank Account");

    if (directDebit) {
      const foundMandate = await Mandate.findOne({
        where: {
          code: mandate,
        },
      });
      if (!foundMandate) throw new NotFoundError("Mandate");

      if (foundMandate.status === "cancelled") throw new ValidationError("Mandate already cancelled");

      const provider = getProvider(company.id);
      const providerHandler = await Providers[provider].directDebit;

      if (foundMandate.externalIdentifier) {
        const cancelMandate = await providerHandler.cancelMandate(foundMandate.externalIdentifier);
        if (cancelMandate.error) {
          log(Log.fg.red, `ERROR:Unable to cancel mandate ${JSON.stringify(cancelMandate)}`);
          throw new ValidationError("Unable to cancel mandate");
        }
      }
      await foundMandate.update({ status: "cancelled" });
    }

    if (bankSync) {
      await AccountLinking.update({ status: STATUSES.PAUSE }, { where: { bankAccount: bankAccount.id } });
    }
  },

  async getProviderBanks({ prev, next, perPage, provider = VIRTUAL_ACCOUNT_PROVIDERS.PAYSTACK }) {
    if (provider === VIRTUAL_ACCOUNT_PROVIDERS.PAYSTACK) {
      return Providers[provider].accounts.fetchBanks({ prev, next, perPage });
    }
    return mockBanks.Banks.filter(({ isEnabled = true }) => isEnabled).slice(0, 50);
  },

  async canAccountProcessPayment({ directDebit, amount, shouldCheckBalance = true }) {
    const { bankAccount } = directDebit;

    const foundBankAccount = await BankAccountRepo.getOneBankAccount({
      queryParams: {
        [Op.or]: [{ code: bankAccount }, { id: bankAccount }],
      },
      selectOptions: ["owner"],
      addBalance: true,
    });

    if (!foundBankAccount) throw new NotFoundError("Bank account");

    const existingMandate = await Mandate.findOne({
      where: {
        bankAccount: foundBankAccount.id,
        status: "granted",
        isReadyForDebit: true,
      },
    });
    if (!existingMandate) throw new ValidationError("No active mandate found");

    if (shouldCheckBalance) {
      const { accountBalance } = await Service.confirmFundsAvailability({ amount }, existingMandate, { id: foundBankAccount.owner });
      //   if (accountBalance < amount) throw new ValidationError("Insufficient balance in account, Please top up to make payment");
    }

    return responseUtils.sendObjectResponse("Balance confirmed", { balance: foundBankAccount.Balance?.id, existingMandate });
  },

  isReAuthRequired(status) {
    return status === "REAUTHORISATION_REQUIRED" || status === "REAUTHRISATION_REQUIRED";
  },

  async sendReAuthRequiredMail(owner, foundAccount) {
    const companyWithAdmin = await CompanyRepo.getCompanyWithAdmins({ id: owner }, true);

    if (!companyWithAdmin?.Users?.length) return;

    const admins = companyWithAdmin.Users;

    Promise.all(
      admins.map((admin) => {
        NotificationService.notifyUser(
          admin,
          "reauth-required",
          {
            companyName: companyWithAdmin.name,
            firstName: admin.firstName,
            bankName: foundAccount.bankName,
            accountName: foundAccount.accountName,
            accountNumber: foundAccount.number,
          },
          { subject: `Reauthorization required for your ${foundAccount.bankName} account (${foundAccount.number})` }
        );

        const systemNotificationPayload = {
          company: companyWithAdmin.id,
          user_id: admin.id,
          type: `info`,
          badge: `info`,
          title: `Data sync update`,
          message: `Reauthorization required for your ${foundAccount.bankName} account (${foundAccount.number})`,
          event: "dataSync",
          body: {
            code: companyWithAdmin.code,
            entity: "Company",
          },
          table: {
            code: companyWithAdmin.code,
            entity: "Company",
          },
        };
        return NotificationService.saveNotification(systemNotificationPayload);
      })
    );
  },

  async listBanks({ filter }) {
    const { search, limit = 50 } = filter;

    const redisValue = Utils.parseJSON(await RedisService.get("bank:list")) || [];
    let banks;
    if (redisValue && redisValue?.banks?.length) {
      if (search) banks = redisValue.banks.filter(({ bankName }) => new RegExp(String(search).toLowerCase()).test(bankName.toLowerCase()));
      else banks = redisValue.banks.slice(0, parseInt(limit, 10));

      return banks;
    }

    const foundBanks = await Bank.findAll({
      where: { status: STATUSES.ACTIVE },
      attributes: ["status", "code", "bankName", "bankCode", "nipCode", "supportsDirectDebit"],
    });

    // Add to redis
    await RedisService.setex("bank:list", JSON.stringify({ banks: foundBanks }), 60 * 60 * 24);

    if (search) banks = foundBanks.filter(({ bankName }) => new RegExp(String(search).toLowerCase()).test(bankName.toLowerCase()));
    else banks = foundBanks.slice(0, parseInt(limit, 10));

    return banks;
  },
};

module.exports = Service;

async function autoExpireEMandate(existingMandate) {
  if (!existingMandate) return true;

  let canCreateNewMandate = false;

  if (existingMandate.status === MANDATE_STATUS.PENDING) {
    if (existingMandate.mandateType === MANDATE_TYPES.E_MANDATE) {
      const parsedDate = existingMandate.created_at;
      const now = new Date();
      const hoursDifference = differenceInHours(now, parsedDate);

      // auto-expire this mandate after 6 hours
      if (hoursDifference > 6) {
        canCreateNewMandate = true;
        // eslint-disable-next-line no-param-reassign
        existingMandate.status = MANDATE_STATUS.FAILED;
        await existingMandate.save();
      }
    }
  }
  return canCreateNewMandate;
}

function getProvider(owner) {
  const { connect } = SettingsService.get("providers");
  const provider = connect[owner] || connect.defaultProvider;
  return provider;
}

async function createMandate({ foundCompany, payload, createCustomer, providerHandler, issuer }) {
  const startDate = payload.signature ? payload.startDate : literal("CURRENT_TIMESTAMP");

  const loggedMandate = await Mandate.create({
    company: foundCompany.id,
    startDate,
    endDate: payload.endDate,
    externalCustomerId: createCustomer.data.data?.id,
    amount: payload.amount,
    bankAccount: payload.bankAccount,
    issuer,
    signature: payload.signature,
    mandateType: payload.signature ? "signed" : "emandate",
    status: "pending",
    debitType: payload.debitType,
  });

  const createdMandate = await Mandate.findOne({
    where: {
      id: loggedMandate.id,
    },
  });

  // create mandate on issuer
  const setUpMandateWithProvider = await providerHandler.setUpMandate(
    {
      debit_type: payload.debitType || "variable",
      mandate_type: payload.signature ? "signed" : "emandate",
      end_date: payload.endDate,
      start_date: createdMandate.startDate,
      description: "Direct debit",
      reference: loggedMandate.reference,
      amount: payload.amount,
      customer: createCustomer.data.data?.id,
      account_number: payload.accountNumber,
      bank_code: payload.bankCode,
      signature: payload.signature,
    },
    foundCompany.id
  );

  if (setUpMandateWithProvider.error) {
    log(Log.fg.red, `ERROR:Unable to create mandate ${JSON.stringify(setUpMandateWithProvider)}`);
    loggedMandate.status = "failed";
    await loggedMandate.save();
    throw new ValidationError("Unable to provision mandate");
  }

  if (setUpMandateWithProvider.data?.data?.id) {
    loggedMandate.externalIdentifier = setUpMandateWithProvider.data?.data?.id;
    await loggedMandate.save();
  }

  const foundBalance = await BalanceRepo.getBalance({
    filter: {
      bankAccount: payload.bankAccount,
      company: foundCompany.id,
      currency: "NGN",
    },
  });

  if (!foundBalance) {
    const foundBankAccount = await BankAccountRepo.getOneBankAccount({
      queryParams: {
        owner: foundCompany.id,
        id: payload.bankAccount,
      },
      selectOptions: ["accountName"],
    });

    await BalanceRepo.create({
      bankAccount: payload.bankAccount,
      company: foundCompany.id,
      currency: "NGN",
      status: STATUSES.PENDING,
      name: foundBankAccount.accountName,
      purpose: "expenses",
      type: await BalanceRepo.getBalanceTypes("expenses"),
    });
  }

  if (!payload.signature) {
    RedisService.setex(
      `mandate-banks:${loggedMandate.code}`,
      JSON.stringify(setUpMandateWithProvider.data?.data?.transfer_destinations),
      6 * 60 * 60 // 6 hours
    );
  }

  return payload.signature
    ? { code: loggedMandate.code }
    : {
        code: loggedMandate.code,
        message: setUpMandateWithProvider.data?.message,
        transferDestinations: setUpMandateWithProvider.data?.data?.transfer_destinations,
      };
}

async function createVirtualAccountOnPaystack({
  company,
  accountNumber,
  bankCode,
  currency,
  purpose,
  name,
  foundBalanceType,
  membersPayload,
  provider,
  redisKey,
}) {
  // check if bank account already exists
  const foundBankAccount = await BankAccountRepo.getOneBankAccount({
    queryParams: {
      owner: company,
      ownerType: "company",
      status: {
        [Op.notIn]: [STATUSES.DELETED, STATUSES.FAILED, STATUSES.ARCHIVED],
      },
      issuer: CARD_ISSUER.Paystack,
      type: "virtual",
      subType: "deposit",
      currency,
    },
  });

  if (foundBankAccount) {
    log(Log.fg.red, `ERROR:Bank account already exists ${foundBankAccount.code}`);
    if (redisKey) {
      await RedisService.delete(redisKey);
    }
    throw new ValidationError("Virtual account already generated");
  }

  const foundCompany = await CompanyRepo.getOneCompany({
    queryParams: { id: company },
    selectOptions: [
      "code",
      "dateOfRegistration",
      "industry",
      "website",
      "address",
      "name",
      "description",
      "phoneNumber",
      "businessType",
      "document_reference",
      "director",
      "contactEmail",
    ],
    addIndustry: true,
    addPhoneNumber: true,
    addAddress: true,
  });

  if (!foundCompany) {
    if (redisKey) {
      await RedisService.delete(redisKey);
    }
    throw new NotFoundError("Company");
  }

  const redLock = await RedisService.getRedLock();
  const ttl = 5000; // 5 seconds
  const lockKey = `lock:dedicated_account_create:${foundCompany.id}`;
  let lock;

  try {
    lock = await redLock.acquire([lockKey], ttl);
    log(Log.fg.green, `Lock (${lockKey}) acquired for company ${foundCompany.id}`);

    const companyName = foundCompany.name;

    const reversedName = companyName?.split(" ").reverse();
    const mid = Math.floor(reversedName.length / 2);

    const firstName = reversedName.slice(0, mid).reverse().join(" ");
    const lastName = reversedName.slice(mid).reverse().join(" ");

    const foundBvn = await DocumentRepo.getOneDocument({
      queryParams: { reference: foundCompany.document_reference, type: "bvn", status: { [Op.ne]: STATUSES.DELETED } },
      selectOptions: ["number", "table_id"],
    });

    if (!foundBvn) {
      log(Log.fg.red, `ERROR:BVN not found ${foundBvn}`);
      throw new ValidationError("Unable to create account as bvn not found");
    }

    const dedicatedAccountPaylaod = {
      email: foundCompany.contactEmail,
      firstName,
      lastName,
      phone: foundCompany.PhoneNumber?.localFormat,
      country: "NG",
      accountNumber,
      bvn: foundBvn.number,
      bankCode,
      ...(!Utils.isProd() && { preferred_bank: "test-bank" }),
    };

    await Providers[provider].accounts.assignDedicatedAccount(dedicatedAccountPaylaod);

    const { createdBalance, createdBankAccount } = await finalizeAccountCreation({
      company,
      currency,
      purpose,
      name,
      foundBalanceType,
      membersPayload,
      issuer: CARD_ISSUER.Paystack,
    });

    return { balance: createdBalance?.code, bankAccount: createdBankAccount.code };
  } catch (error) {
    log(Log.fg.red, `ERROR:Unable to create virtual account ${error.stack}`);
    if (redisKey) {
      await RedisService.delete(redisKey);
    }
    throw error;
  } finally {
    if (lock) await redLock.unlock(lock);
  }
}

async function createUsdAccount({ company, purpose, name, foundBalanceType, membersPayload, redisKey }) {
  const currency = CURRENCY.USD;
  const foundCompany = await CompanyRepo.getOneCompany({
    queryParams: { id: company },
    selectOptions: [
      "code",
      "dateOfRegistration",
      "industry",
      "website",
      "address",
      "name",
      "description",
      "phoneNumber",
      "businessType",
      "document_reference",
      "contactEmail",
      "onboardingLevel",
      "onboardingStatus",
      "graphIdentifier",
    ],
    addIndustry: true,
    addPhoneNumber: true,
    addAddress: true,
  });

  if (!foundCompany) {
    if (redisKey) {
      await RedisService.delete(redisKey);
    }
    throw new NotFoundError("Company");
  }

  const redLock = await RedisService.getRedLock();
  const ttl = 5000; // 5 seconds
  const lockKey = `lock:dedicated_account_create:${foundCompany.id}`;
  let lock;

  try {
    lock = await redLock.acquire([lockKey], ttl);
    log(Log.fg.green, `Lock (${lockKey}) acquired for company ${foundCompany.id}`);

    // Do Provider Relevant Stuff

    const { virtual_accounts } = SettingsService.get("providers");
    const providerToUse = virtual_accounts[currency.toLowerCase()]?.[company] || virtual_accounts[currency.toLowerCase()]?.defaultProvider;

    if (!providerToUse) {
      throw new ValidationError("Virtual account creation for selected currency is unavailable");
    }

    let externalIdentifier;

    if (providerToUse === VIRTUAL_ACCOUNT_PROVIDERS.GRAPH) {
      externalIdentifier = await createVirtualAccountOnGraph({
        currency,
        redisKey,
        company: foundCompany,
      });
    }

    const { createdBalance, createdBankAccount } = await finalizeAccountCreation({
      company,
      currency,
      purpose,
      name,
      foundBalanceType,
      membersPayload,
      issuer: CARD_ISSUER.Graph,
      externalIdentifier,
    });

    return { balance: createdBalance?.code, bankAccount: createdBankAccount.code };
  } catch (error) {
    log(Log.fg.red, `ERROR:Unable to create virtual account ${error.stack}`);
    if (redisKey) {
      await RedisService.delete(redisKey);
    }
    throw error;
  } finally {
    if (lock) await redLock.unlock(lock);
  }
}

async function createVirtualAccountOnGraph({ company: foundCompany, currency, redisKey }) {
  // check if bank account already exists
  await ensureBankAccountDoesNotExist({ company: foundCompany, currency, redisKey, issuer: CARD_ISSUER.Graph });

  const providerHandler = Providers[VIRTUAL_ACCOUNT_PROVIDERS.GRAPH];

  // get individual with highest ownership
  const [majorShareHolder] = await HelperService.returnShareHoldersDesc(foundCompany);

  if (!majorShareHolder) {
    log(Log.fg.red, `ERROR:Major shareholder not found ${majorShareHolder}`);
    throw new ValidationError("Unable to create account as major shareholder not found");
  }

  const foundCacNumber = await DocumentRepo.getOneDocument({
    queryParams: {
      reference: foundCompany.document_reference,
      type: [DOCUMENT_MAPPER.RC_NUMBER],
      status: { [Op.notIn]: [STATUSES.DELETED, STATUSES.REJECTED] },
    },
    selectOptions: ["number", "table_id"],
  });

  if (!foundCacNumber) {
    log(Log.fg.red, `ERROR:CAC number not found ${foundCacNumber}`);
    throw new ValidationError("Unable to create account as CAC number not found");
  }

  const foundCacDoc = await DocumentRepo.getOneDocument({
    queryParams: {
      reference: foundCompany.document_reference,
      type: [DOCUMENT_MAPPER.INCORP_C, DOCUMENT_MAPPER.CAC],
      status: { [Op.ne]: STATUSES.DELETED },
    },
    selectOptions: ["number", "table_id", "asset"],
  });

  if (!foundCacDoc) {
    log(Log.fg.red, `ERROR: CAC doc not found`);
    throw new ValidationError("Unable to create account as CAC doc not found");
  }

  const personId = await createOrReturnPerson(majorShareHolder, providerHandler);

  const businessId = await createOrReturnBusiness({ foundCacDoc, foundCacNumber, foundCompany, providerHandler, personId, majorShareHolder });

  const createAccountPayload = {
    businessId,
    label: foundCompany.name,
    currency: CURRENCY.USD,
    company: foundCompany.id,
  };

  const { data: createdAccount, error: createAccountError } = await providerHandler.accounts.createAccount(createAccountPayload);

  if (createAccountError) {
    log(Log.fg.red, `ERROR:Unable to create account ${JSON.stringify(createAccountError)}`);
    throw new ValidationError("Unable to create account");
  }

  return createdAccount?.data?.id;
}

async function createOrReturnBusiness({ foundCacDoc, foundCompany, providerHandler, personId, majorShareHolder, foundCacNumber }) {
  const businessId = foundCompany.graphIdentifier;

  if (businessId) return businessId;

  const countryIdLevelRemap = {
    NG: "secondary",
    US: "primary",
  };

  const documentAsset = await Asset.findOne({ where: { id: foundCacDoc.asset } });

  if (!documentAsset) {
    log(Log.fg.red, `ERROR: Unable to create business as no cac document found`);
    throw new ValidationError("Unable to create business as no CAC document found");
  }

  const { data: createdBusiness, error: createBusinessError } = await providerHandler.businesses.createBusiness({
    ownerId: personId,
    name: foundCompany.name,
    businessType: HelperService.businessTypeMapper(VIRTUAL_ACCOUNT_PROVIDERS.GRAPH)[foundCompany.businessType],
    industry: "otherProfessionalServices",
    idType: DOCUMENT_MAPPER.CAC,
    idNumber: foundCacNumber.number.replace("RC", ""),
    idCountry: foundCompany.Address.countryIso,
    idLevel: countryIdLevelRemap[foundCompany.Address.countryIso],
    idUpload: await getAssetDownloadURL(documentAsset),
    dateOfFormation: format(new Date(foundCompany.dateOfRegistration), "yyyy-MM-dd"),
    contactPhone:
      majorShareHolder?.PhoneNumber?.internationalFormat ||
      formatPhoneNumber(majorShareHolder?.PhoneNumber?.localFormat, majorShareHolder?.PhoneNumber?.countryCode),
    contactEmail: foundCompany.contact_email,
    address: {
      line1: foundCompany.RegisteredAddress?.street || foundCompany.Address?.street,
      city: foundCompany.RegisteredAddress?.city || foundCompany.Address?.city,
      state: foundCompany.RegisteredAddress?.state || foundCompany.Address?.state,
      country: foundCompany.RegisteredAddress?.countryIso || foundCompany.Address?.countryIso,
      postalCode: foundCompany.RegisteredAddress?.postalCode || foundCompany.Address?.postalCode,
    },
    company: foundCompany.id,
  });

  if (createBusinessError) {
    log(Log.fg.red, `ERROR:Unable to create business ${JSON.stringify(createBusinessError)}`);
    throw new ValidationError("Unable to create account as business not found");
  }

  await CompanyRepo.updateACompany({
    queryParams: {
      id: foundCompany.id,
    },
    updateFields: {
      graphIdentifier: createdBusiness.data.id,
    },
  });

  return createdBusiness.data.id;
}

async function createOrReturnPerson(majorShareHolder, providerHandler) {
  const personId = majorShareHolder.graphIdentifier;

  if (personId) return personId;

  // get docs for this individual
  const [bvnDoc] = majorShareHolder.DirectorDocuments.filter((doc) => doc.type === DOCUMENT_MAPPER.BVN);

  if (!bvnDoc) {
    log(Log.fg.red, `ERROR:BVN document not found ${bvnDoc}`);
    throw new ValidationError("Unable to create account as bvn document not found");
  }

  const [idTypeDoc] = majorShareHolder.DirectorDocuments.filter(
    (doc) => ![DOCUMENT_MAPPER.NIN, DOCUMENT_MAPPER.BVN, DOCUMENT_MAPPER.UTILITY_BILL].includes(doc.type)
  );

  if (!idTypeDoc) {
    log(Log.fg.red, `ERROR:ID type document not found ${idTypeDoc}`);
    throw new ValidationError("Unable to create account as ID type document not found");
  }

  let documentIssueDate = null;
  if (idTypeDoc.issuing_date) {
    const parsedIssueDate = parseISO(idTypeDoc.issuing_date);
    if (isValid(parsedIssueDate)) {
      documentIssueDate = format(parsedIssueDate, "yyyy-MM-dd");
    } else {
      documentIssueDate = format(idTypeDoc.issuing_date, "yyyy-MM-dd");
    }
  }

  let documentExpiryDate = null;
  if (idTypeDoc.expiryDate) {
    const parsedExpiryDate = parseISO(idTypeDoc.expiryDate);
    if (isValid(parsedExpiryDate)) {
      documentExpiryDate = format(parsedExpiryDate, "yyyy-MM-dd");
    } else {
      documentExpiryDate = format(idTypeDoc.expiryDate, "yyyy-MM-dd");
    }
  }

  const idNumber = idTypeDoc.number;
  const bvn = bvnDoc.number;

  if (!documentIssueDate || !documentExpiryDate) {
    log(Log.fg.red, `ERROR:ID type document issue or expiry date not found ${idTypeDoc.type}`);
    throw new ValidationError("Unable to create account as ID type document issue or expiry date not found");
  }

  const bujetiGraphIdTypeRemap = {
    [ID_TYPES.INTERNATIONAL_PASSPORT.value]: "passport",
  };

  const documentType = bujetiGraphIdTypeRemap[idTypeDoc.type];

  if (!documentType) {
    log(Log.fg.red, `ERROR:Unsupported ID type document ${idTypeDoc.type}`);
    throw new ValidationError("Major shareholdr must possess an international passport to create an account");
  }

  const documentAsset = await Asset.findOne({ where: { id: idTypeDoc.asset } });
  const documentUrl = await getAssetDownloadURL(documentAsset);

  const { data: createdPerson, error: createPersonError } = await providerHandler.people.createPerson({
    firstName: majorShareHolder.firstName,
    lastName: majorShareHolder.lastName,
    middleName: majorShareHolder.firstName,
    phone:
      majorShareHolder?.PhoneNumber?.internationalFormat ||
      formatPhoneNumber(majorShareHolder?.PhoneNumber?.localFormat, majorShareHolder?.PhoneNumber?.countryCode),
    email: majorShareHolder.email || Utils.parseJSON(majorShareHolder.metadata)?.directorsEmail,
    idType: documentType,
    idNumber,
    idCountry: majorShareHolder.Address?.countryIso || "NG",
    bvn,
    dob: majorShareHolder.dob,
    address: {
      line1: majorShareHolder.Address?.street,
      city: majorShareHolder.Address?.city,
      state: majorShareHolder.Address?.state,
      country: majorShareHolder.Address?.countryIso,
      postalCode: majorShareHolder.Address?.postalCode,
    },
    documentType,
    documentUrl,
    documentIssueDate,
    documentExpiryDate,
    company: majorShareHolder.company,
  });

  if (createPersonError) {
    log(Log.fg.red, `ERROR:Unable to create person ${JSON.stringify(createPersonError)}`);
    throw new ValidationError("Unable to create account as major shareholder not found");
  }

  await IndividualRepo.updateAIndividual({
    queryParams: {
      id: majorShareHolder.id,
    },
    updateFields: {
      graphIdentifier: createdPerson.data.id,
    },
  });

  return createdPerson.data.id;
}

async function ensureBankAccountDoesNotExist({ company: foundCompany, currency, redisKey, issuer }) {
  const foundBankAccount = await BankAccountRepo.getOneBankAccount({
    queryParams: {
      owner: foundCompany.id,
      ownerType: "company",
      status: {
        [Op.notIn]: [STATUSES.DELETED, STATUSES.FAILED, STATUSES.ARCHIVED],
      },
      issuer,
      type: "virtual",
      subType: "deposit",
      currency,
    },
  });

  if (foundBankAccount) {
    log(Log.fg.red, `ERROR:Bank account already exists ${foundBankAccount.code}`);
    if (redisKey) {
      await RedisService.delete(redisKey);
    }
    throw new ValidationError("Virtual account already generated");
  }
}

async function finalizeAccountCreation({ externalIdentifier, company, currency, purpose, name, foundBalanceType, membersPayload, issuer }) {
  const bankAccountPayload = {
    owner: company,
    ownerType: "company",
    company,
    type: "virtual",
    subType: "deposit",
    status: STATUSES.PENDING,
    issuer,
    currency,
    ...(purpose && { purpose }),
    name,
    ...(externalIdentifier && { externalBankAccountId: externalIdentifier }),
  };

  const createdBankAccount = await BankAccountRepo.createABankAccount({ queryParams: bankAccountPayload });

  let createdBalance;
  if (foundBalanceType) {
    createdBalance = await BalanceRepo.findOrCreate({
      currency,
      company,
      status: STATUSES.PENDING,
      type: foundBalanceType.id,
      bankAccount: createdBankAccount.id,
      name,
      ...(purpose && { purpose }),
    });

    if (membersPayload) {
      await Service.addMembers({ ...membersPayload, code: createdBalance.code });
    }
  }
  return { createdBalance, createdBankAccount };
}
