const { Op, literal } = require("sequelize");
const dateFns = require("date-fns");
const NotificationService = require("./notification");
const Sanitizer = require("../utils/sanitizer");
const ValidationError = require("../utils/validation-error");
const Utils = require("../utils");
const {
  Beneficiary,
  User,
  Status,
  UserBudget,
  Company,
  Budget,
  Vendor,
  sequelize,
  BankAccount,
  Role,
  Subscription,
  BillingHistory,
  PaymentPlan,
} = require("../models");
const { STATUSES } = require("../models/status");
const {
  BeneficiaryRepo,
  TransactionRepo,
  UserBudgetRepo,
  UserRepo,
  RoleRepo,
  BillingAddonRepo,
  TransferRepo,
} = require("../repositories/index.repo");
const { BeneficiaryValidator } = require("../validators");
const RedisService = require("./redis");
const BudgetService = require("./budget");
const Helper = require("./helper.service");
const responseUtils = require("../utils/response.utils");
const NotFoundError = require("../utils/not-found-error");
const teamRepo = require("../repositories/team.repo");
const { BILLING_ADDONS } = require("../mocks/constants.mock");
const SettingsService = require("./settings");

const Service = {
  async fetchBeneficiaries(owner, filters = {}) {
    if (!owner) throw new ValidationError("Specify the owner");
    let { page = 1, budget, search, status, perPage = 50, company, from, to, role } = filters;
    perPage = parseInt(perPage, 10);
    page = parseInt(page, 10);
    const skip = Math.max(page - 1, 0) * perPage;
    let beneficiaries = [];
    let budgetId;

    if (budget) {
      const { id } = await BudgetService.getBudget({ code: budget });
      budgetId = id;
    }
    const criteria = {
      company,
    };
    // if (search) {
    //     criteria[Op.or] = [
    //         ...criteria['Op.or'],
    //         { firstName: search },
    //         { lastName: search },
    //         { middleName: search }
    //     ]
    // }
    if (from && to) {
      criteria.created_at = {};
      if (from && Utils.isValidDate(from)) {
        criteria.created_at[Op.gte] = from;
      }
      if (to && Utils.isValidDate(to)) {
        criteria.created_at[Op.lte] = to;
      }
    }
    if (status) {
      if (Array.isArray(status)) {
        criteria.status = status.map((val) => STATUSES[val.toUpperCase()]);
      } else criteria.status = STATUSES[status.toUpperCase()];
    }

    const queryFilters = {
      ...criteria,
      ...(!status && {
        status: {
          [Op.ne]: STATUSES.DELETED,
        },
      }),
    };

    if (role) {
      const foundRoles = await RoleRepo.getAllRoles({
        queryParams: { [Op.or]: [{ name: role }, { code: role }], company },
        selectOptions: ["id"],
      });
      if (!foundRoles.length || foundRoles.length !== role.length) throw new NotFoundError("Roles");
      queryFilters.role = foundRoles.map((singleRole) => singleRole.id);
    }

    const includes = [
      {
        model: User,
        via: "user",
        required: true,
        where: {
          ...(search && {
            [Op.or]: {
              firstName: { [Op.like]: `%${search}%` },
              lastName: { [Op.like]: `%${search}%` },
              middleName: { [Op.like]: `%${search}%` },
              email: { [Op.like]: `%${search}%` },
            },
          }),
        },
        include: [
          Role,
          {
            model: Status,
            required: true,
          },
          {
            model: UserBudget,
            attributes: ["spent", "amount", "isBudgetOwner", "limit", "duration"],
            required: !!budget,
            where: {
              ...(budget && { budget: budgetId }),
              status: {
                [Op.notIn]: [STATUSES.INACTIVE, STATUSES.DELETED],
              },

              [Op.exists]: literal(
                "(SELECT 1 FROM Budgets b WHERE b.id = " +
                  "`User->UserBudgets`.`budget`" +
                  `AND b.status NOT IN (${STATUSES.CLOSED}, ${STATUSES.INACTIVE}, ${STATUSES.PAUSE}, ${STATUSES.DELETED}))`
              ),
            },
            include: [
              {
                model: Budget,
                attributes: ["code", "name", "status"],
                required: true,
                where: {
                  company,
                  ...(budget && { code: budget }),
                },
              },
            ],
          },
          {
            model: User,
            via: "manager",
            as: "Manager",
          },
        ],
      },
      {
        model: Status,
        required: true,
      },
      {
        model: Company,
        required: true,
      },
      {
        model: Role,
        required: false,
      },
    ];

    const [hasBeneficiary, total] = await Promise.all([
      Beneficiary.count({
        where: { user: owner, company },
      }),

      Beneficiary.count({
        subQuery: false,
        where: queryFilters,
        col: "id",
        distinct: true,
        include: includes,
      }),
    ]);

    if (hasBeneficiary === 0) {
      // create a beneficiary for first user that signed up for this company.
      await Service.createBeneficiaryForUser({ user: owner, company });
      return Service.fetchBeneficiaries(owner, filters);
    }
    beneficiaries = await Beneficiary.findAll({
      subQuery: false,
      col: "id",
      distinct: true,
      where: queryFilters,
      include: includes,
      order: [
        [sequelize.literal(`Beneficiary.status = ${STATUSES.ACTIVE}`), "DESC"],
        [sequelize.literal(`Beneficiary.status = ${STATUSES.INACTIVE}`), "DESC"],
        ["created_at", "DESC"],
      ],
      offset: skip,
      limit: perPage,
    });
    return {
      beneficiaries: Sanitizer.sanitizeBeneficiaries(beneficiaries),
      meta: {
        page,
        total,
        perPage,
        nextPage: page + 1,
        hasMore: total >= page * perPage,
      },
    };
  },
  async allBeneficiaries(payload) {
    if (!payload && !payload.company) throw new ValidationError("Specify the company");
    const { error } = BeneficiaryValidator.getAll.validate(payload);
    if (error) throw new ValidationError(error.message);

    const { page, perPage, from, to, search, owner, role, ...queryParams } = payload;
    const beneficiaries = await BeneficiaryRepo.fetchAllBeneficiariesAndRelationships({ queryParams, page, perPage, from, to, role, search });
    return { beneficiaries };
  },

  async sendBeneficiaryInvitation(budgetOwner, beneficiary, budgets, company = null) {
    const isNewUser = beneficiary.status === STATUSES.PENDING;
    let hash;

    if (isNewUser) {
      hash = Utils.generateUniqueId(Utils.alphabet.alphanumeric, 24);
      await RedisService.setex(hash, JSON.stringify({ userCode: beneficiary.code }), 604800);
    }

    const role = `${Utils.indefiniteArticle(beneficiary.role || "member")} ${beneficiary.role?.toLowerCase() || "member"}`;
    const payload = {
      name: beneficiary.firstName,
      firstName: beneficiary.firstName,
      dashboardUrl: isNewUser ? `${Utils.getDashboardURL()}/register?hash=${hash}` : `${Utils.getDashboardURL()}/expenses/budgets`,
      invitedBy: budgetOwner.firstName,
      ownerName: budgetOwner.firstName,
      registration_link: `${Utils.getDashboardURL()}/register?hash=${hash}`,
      role,
    };

    if (Array.isArray(budgets)) {
      budgets.map((budget) => {
        const notificationPayload = {
          company: beneficiary.company,
          user_id: beneficiary.id,
          type: `info`,
          badge: `info`,
          title: `Invitation to Budget`,
          message: `You have been added to budget (${Utils.toTitle(budget.name)}) by ${Utils.toTitle(budgetOwner.firstName)}`,
          table: {
            code: budget.code,
            entity: "Budget",
          },
          event: "budgetInvite",
        };
        NotificationService.saveNotification(notificationPayload);
      });
      payload.dashboardUrl = isNewUser ? `${Utils.getDashboardURL()}/register?hash=${hash}` : `${Utils.getDashboardURL()}/expenses/budgets`;
    } else if (budgets) {
      const notificationPayload = {
        company: beneficiary.company,
        user_id: beneficiary.id,
        type: `info`,
        badge: `info`,
        title: `Invitation to Budget`,
        message: `You have been added to budget (${Utils.toTitle(budgets.name)}) by ${Utils.toTitle(budgetOwner.firstName)}`,
        table: {
          code: budgets.code,
          entity: "Budget",
        },
        event: "budgetInvite",
      };
      NotificationService.saveNotification(notificationPayload);
      payload.budgetName = budgets.name;
      payload.dashboardUrl = isNewUser
        ? `${Utils.getDashboardURL()}/register?hash=${hash}`
        : `${Utils.getDashboardURL()}/expenses/budgets/${budgets.code}/overview`;
    }

    const sourceConfig = {
      subject: `Hello ${beneficiary.firstName}, ${budgetOwner.firstName} invited you to join ${
        payload.budgetName || (company && `${company.name} on`)
      } Bujeti`,
      from_name: "Bujeti",
    };
    return NotificationService.notifyUser(beneficiary, "budget-invitation-v2", payload, sourceConfig);
  },

  async sendBudgetOwnerNotification({ beneficiary, budget, company, budgetOwner, isBudgetOwnerAssigned }) {
    if (!isBudgetOwnerAssigned) return;

    const payload = {
      firstName: beneficiary.firstName,
      companyName: company.name,
      budgetName: budget.name,
      dashboardUrl: `${Utils.getDashboardURL()}/expenses/budgets/${budget.code}/overview`,
    };

    const notificationPayload = {
      company: beneficiary.company,
      user_id: beneficiary.id,
      type: `info`,
      badge: `info`,
      title: `Budget Ownership 🚀`,
      message: `You have been made a budget owner on ${Utils.toTitle(budget.name)} by ${Utils.toTitle(budgetOwner.firstName)}`,
      table: {
        code: budget.code,
        entity: "Budget",
      },
      event: "budgetOwner",
    };

    NotificationService.saveNotification(notificationPayload);

    const sourceConfig = {
      subject: `You have been made a budget owner on ${budget.name}`,
      from_name: "Bujeti",
    };

    NotificationService.notifyUser(beneficiary, "budget-owner", payload, sourceConfig);
  },

  validateBeneficiaryPayload(payload) {
    const { error } = BeneficiaryValidator.create.validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: false, message: "Valid payload" };
  },

  validateBeneficiaryStatus(status) {
    const statusesNames = Object.keys(STATUSES).map((key) => key.toLowerCase());
    const statusesValues = Object.values(STATUSES);
    if (!(statusesNames.includes(status) || statusesValues.includes(parseInt(status)))) throw new ValidationError(`${status} is not supported`);
    return { error: false, message: "Valid status" };
  },

  validateBulkBeneficiaryPayload(payload) {
    const { error } = BeneficiaryValidator.bulkCreate.validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: false, message: "Valid payload" };
  },

  /**
   * Add a beneficiary to an account
   * @param {*} owner
   * @param {*} beneficiary
   * @param {*} company
   * @param {*} role
   * @returns
   */
  async addBeneficiary(owner, beneficiary, company = null, role = null, status = STATUSES.PENDING) {
    return Beneficiary.create(
      {
        owner,
        company,
        role,
        user: beneficiary,
        status,
      },
      {
        include: [{ model: User, via: "user" }, { model: Status }],
      }
    );
  },

  async bulkCreateBeneficiary({ budgetOwner, beneficiaries, company, UserService, TeamMemberService, transaction, usersCoveredByReservationMap }) {
    const createdBeneficiaries = await Service.callService("createBulkBeneficiaryItem", beneficiaries, {
      budgetOwner,
      company,
      UserService,
      TeamMemberService,
      transaction,
      usersCoveredByReservationMap,
    });

    return createdBeneficiaries;
  },

  async createBulkBeneficiaryItem(data) {
    const { budgetOwner, company, role, email, manager, team, UserService, TeamMemberService, transaction, usersCoveredByReservationMap = {} } = data;

    const existingUser = await UserRepo.getOneUser({ queryParams: { email, status: { [Op.ne]: STATUSES.DELETED } } });

    // idempotent
    if (existingUser) return existingUser;

    const roleCriteria = {
      company: {
        [Op.or]: [null, company.id],
      },
    };

    if (role?.startsWith("rol_")) {
      roleCriteria.code = role;
    } else {
      roleCriteria.name = role;
    }

    let foundTeam;
    const foundRole = await RoleRepo.getRole({ queryParams: roleCriteria });

    if (team) {
      if (foundRole) {
        const teamCriteria = {
          company: company.id,
        };

        if (team.startsWith("tms_")) {
          teamCriteria.code = team;
        } else {
          teamCriteria.name = team;
        }

        foundTeam = await teamRepo.findOneTeam({ conditions: teamCriteria });

        // team not found and code was not passed
        if (!foundTeam && !teamCriteria.code) foundTeam = await teamRepo.createTeam({ data: teamCriteria });
      }
    }

    if (manager) {
      const foundManager = await UserRepo.getOneUser({ queryParams: { code: manager, company: company.id }, selectOptions: ["id"] });
      data.manager = foundManager && foundManager.id;
    }

    const usedCompanyCredit = transaction?.usedCompanyCredit;
    const isCoveredByReservation = usersCoveredByReservationMap[data.email];

    const { data: newUser } = await UserService.registerUser({
      ...data,
      role: foundRole && foundRole.name,
      role_id: foundRole && foundRole.id,
      company: company.id,
      status: transaction && !usedCompanyCredit && !isCoveredByReservation ? STATUSES.PROCESSING : STATUSES.PENDING,
    });

    if (foundTeam) {
      const formattedTeamMembers = await Helper.formatTeamMembers({ users: [newUser.code], role: "Employee", company: company.id });
      TeamMemberService.createTeamMembers(foundTeam.id, formattedTeamMembers);
    }

    const newBeneficiary = await Beneficiary.create(
      {
        owner: budgetOwner.id,
        company: company.id,
        user: newUser.id,
        role: foundRole?.id,
        status: transaction && !usedCompanyCredit ? STATUSES.PROCESSING : STATUSES.PENDING,
      },
      {
        include: [{ model: User, via: "user" }, { model: Status }],
      }
    );

    if (!transaction || usedCompanyCredit || isCoveredByReservation) {
      newUser.status = STATUSES.PENDING;
      Service.sendBeneficiaryInvitation(budgetOwner, newUser, null, company);
    }

    return newBeneficiary;
  },

  async finalizeAdditionalBeneficiaries({ transaction, transfer }) {
    let foundTransfer;
    let foundTransaction;

    if (transaction) {
      foundTransaction = await TransactionRepo.getTransaction({ queryParams: { code: transaction, status: STATUSES.SUCCESS }, company: true });
    } else if (transfer) {
      foundTransfer = await TransferRepo.getTransfer({
        filter: {
          code: transfer,
          status: STATUSES.SUCCESS,
        },
        includeCompany: true,
      });
    }
    if (!foundTransaction && !foundTransfer) return null;

    const getBillingAddons = await BillingAddonRepo.getMany({
      queryParams: {
        entity: BILLING_ADDONS.BENEFICIARIES,
        ...(foundTransaction && { transaction: foundTransaction.id }),
        ...(foundTransfer && {
          transfer: foundTransfer.id,
        }),
      },
      includeInviter: true,
      includeCompany: true,
    });

    // the safe assumption for budget owner is that, one person invites every beneficiary in one session
    const [{ inviter: budgetOwner = {}, Company } = {}] = getBillingAddons;
    const foundCompany = Company || foundTransaction?.Company || foundTransfer?.Company;

    const beneficiaryIds = getBillingAddons.map((addon) => addon.entityId);
    let beneficiaries = [];

    if (beneficiaryIds.length) {
      beneficiaries = await Beneficiary.findAll({
        where: {
          status: STATUSES.PROCESSING,
          id: beneficiaryIds,
        },
        include: [{ model: User, via: "user" }, { model: Status }],
      });
    }

    const extraSeatsKey = `extra_seats:${foundCompany?.code}`;
    const serializedExtraSeats = await RedisService.get(extraSeatsKey);
    const extraSeats = Utils.parseJSON(serializedExtraSeats || {});

    if (extraSeats.additionalUsersCount) {
      const remainingSeats = Math.max(0, extraSeats.additionalUsersCount - beneficiaries.length);

      if (remainingSeats) {
        await Subscription.increment("additionalSeats", {
          by: remainingSeats,
          where: {
            code: extraSeats.subscription,
          },
        });
      }
    }

    RedisService.delete(extraSeatsKey);

    if (beneficiaries.length) {
      await Beneficiary.update({ status: STATUSES.PENDING }, { where: { id: beneficiaryIds } });

      const userIds = beneficiaries.map((beneficiary) => beneficiary.user);

      const users = await UserRepo.getAllUsers({ queryParams: { id: userIds } });

      await User.update({ status: STATUSES.PENDING }, { where: { id: userIds } });

      await BillingAddonRepo.update({
        queryParams: {
          entity: BILLING_ADDONS.BENEFICIARIES,
          ...(foundTransaction && { transaction: foundTransaction.id }),
          ...(foundTransfer && { transfer: foundTransfer.id }),
        },
        payload: { status: STATUSES.SUCCESS },
      });

      await Promise.all(
        users.map((newUser) => {
          return Service.sendBeneficiaryInvitation(budgetOwner, { ...Sanitizer.jsonify(newUser), status: STATUSES.PENDING }, null, foundCompany);
        })
      );
    }

    const activeSubscription = await Subscription.findOne({
      where: {
        company: foundCompany.id,
        status: STATUSES.ACTIVE,
      },
      include: [
        PaymentPlan,
        {
          model: BillingHistory,
          required: true,
          where: {
            status: STATUSES.PAID,
            company: foundCompany.id,
          },
          order: [["created_at", "DESC"]],
          limit: 1,
        },
      ],
    });

    const [billingHistory] = activeSubscription?.BillingHistories || [];

    const notificationPayload = {
      amount: Utils.formatMoney(foundTransaction?.amount || Math.abs(foundTransfer?.amount)),
      currency: foundTransaction?.currency || foundTransfer?.currency,
      planType: activeSubscription?.PaymentPlan?.name,
      paymentDate: Utils.formatHumanReadableDate(foundTransaction?.paidOn || new Date()),
      companyName: foundCompany.name,
      seats: extraSeats.additionalUsersCount,
      expiryDate: Utils.formatHumanReadableDate(billingHistory?.dueDate),
    };

    return NotificationService.notifyUser({ email: SettingsService.get("notification_email") }, "additional-users-payment", notificationPayload);
  },

  async updateBeneficiary(criteria, status) {
    await Beneficiary.update({ status }, { where: criteria });
    return Beneficiary.findOne({ where: criteria });
  },
  async getByCode(code) {
    const beneficiary = await Beneficiary.findOne({
      where: { code },
      include: [{ model: User, via: "user" }, { model: Status }],
    });
    if (!beneficiary) throw new NotFoundError("Beneficiary");
    return beneficiary;
  },
  async getSingleBeneficiary(criteria) {
    if (!criteria || typeof criteria !== "object") return null;
    return await Beneficiary.findOne({
      where: criteria,
      include: [Status, Role],
    });
  },
  async checkExistence(user, company) {
    const beneficiary = await Beneficiary.findOne({
      where: { user, company },
    });
    if (beneficiary) throw new ValidationError("Beneficiary already added");
    return null;
  },

  async search(company, query) {
    if (!company) throw new ValidationError("Specify the owner");

    const { error } = BeneficiaryValidator.search.validate({ company, ...query });
    if (error) throw new ValidationError(error.message);

    const { q } = query;
    const criteria = { company };

    const rows = await Promise.all([
      User.findAll({
        where: {
          [Op.or]: {
            firstName: { [Op.like]: `%${q}%` },
            lastName: { [Op.like]: `%${q}%` },
            middleName: { [Op.like]: `%${q}%` },
          },
          company,
        },
        attributes: ["id", "code", [sequelize.fn("CONCAT", sequelize.col("firstName"), " ", sequelize.col("lastName")), "name"]],
        include: [
          {
            model: BankAccount,
            where: { ownerType: "user", type: "real" },
            limit: 1,
            required: false,
          },
          Status,
        ],
        limit: 5,
      }),
      Vendor.findAll({
        where: { name: { [Op.like]: `%${q}%` } },
        attributes: ["id", "code", "name"],
        include: [
          {
            model: BankAccount,
            limit: 1,
            required: false,
            where: { ownerType: "vendor", type: "real" },
          },
          Status,
        ],
        limit: 5,
      }),
    ]).then((modelReturn) => {
      return modelReturn.flat();
    });

    return {
      beneficiaries: Sanitizer.sanitizeBeneficiaries(rows),
    };
  },

  async getBeneficiariesByCode(company, beneficiaries) {
    const queryParams = { company, code: beneficiaries };
    const selectOptions = ["user"];
    return BeneficiaryRepo.getAllBeneficiaries({ queryParams, selectOptions });
  },

  async getBeneficiariesCodeWithUserCodes(company, userCodes) {
    const allBeneficiaries = await BeneficiaryRepo.getAllBeneficiariesWithUserCodes({ queryParams: { company, code: userCodes } });

    return Array.from(allBeneficiaries).map((beneficiary) => beneficiary.code);
  },

  /**
   * get statistics of transactions by beneficiaries
   * @param {Object} criteria
   * @param {Object} payload
   * @returns
   */
  async analyticsOfTransactionByBeneficiary(criteria) {
    const today = new Date();
    const { to = today, from = dateFns.subDays(today, 30), ...query } = criteria;

    const [beneficiaryTransactionAnalytics, allTransactions] = await Promise.all([
      TransactionRepo.analysisOfTransactionByBeneficiaries({
        queryParams: { ...query },
        ...(from && { from }),
        ...(to && { to }),
      }),
      TransactionRepo.analysisOfTransaction({
        queryParams: { ...query },
        ...(from && { from }),
        ...(to && { to }),
      }),
    ]);
    const message = "Beneficiary breakdown successfully retrieved";
    const invalidTransaction = !beneficiaryTransactionAnalytics.length || !allTransactions;
    if (invalidTransaction) {
      return responseUtils.sendObjectResponse(message, {
        highestTransactionCount: null,
        beneficiaryTransactionAnalytics: [],
        highestTransactionVolume: null,
        percentageSpentByBeneficiary: [],
      });
    }

    const highestTransactionVolume = beneficiaryTransactionAnalytics[0];
    const highestTransactionCount = Utils.maxElementFromAnArray(beneficiaryTransactionAnalytics, "transactionCount");
    const percentageSpentByBeneficiary = await Helper.percentageSpent(beneficiaryTransactionAnalytics, allTransactions);

    return responseUtils.sendObjectResponse(message, {
      highestTransactionCount,
      beneficiaryTransactionAnalytics,
      highestTransactionVolume,
      percentageSpentByBeneficiary,
    });
  },

  /**
   * Deletes a single beneficiary and removes him from all budgets and sets the user's account to deleted
   * @param {*} criteria // An object containing attributes used in finding beneficiary
   * <AUTHOR>
   */
  async deleteBeneficiary(criteria) {
    const foundBeneficiary = await BeneficiaryRepo.getOneBeneficiary({
      queryParams: criteria,
      selectOptions: ["user"],
    });
    if (!foundBeneficiary) throw new NotFoundError("Beneficiary");

    const UserBudgets = await UserBudgetRepo.getAllUserBudgets({ queryParams: { user: foundBeneficiary.user } });
    if (UserBudgets && UserBudgets.length > 0) {
      const userBudgets = Array.from(UserBudgets).map((userBudget) => userBudget.id);
      await UserBudgetRepo.updateAUserBudget({
        queryParams: { id: userBudgets },
        updateFields: { status: STATUSES.DELETED },
      });
    }
    return Promise.all([
      BeneficiaryRepo.updateABeneficiary({
        queryParams: criteria,
        updateFields: { status: STATUSES.DELETED },
      }),
      UserRepo.updateUser({
        queryParams: { id: foundBeneficiary.user },
        updateFields: { status: STATUSES.DELETED },
      }),
    ]);
  },

  /**
   * Creates a beneficiary account for the first user who signed up for a company
   * @param {*} criteria // An object containing attributes used in finding beneficiary
   * <AUTHOR>
   */
  async createBeneficiaryForUser(criteria) {
    try {
      const findBeneficiary = await Beneficiary.findOne({ where: criteria, raw: true });

      // if beneficiary exists, no-op
      if (findBeneficiary) {
        return;
      }

      // find first user for company
      const [findFirstUserForCompany] = await User.findAll({
        order: [["created_at", "ASC"]],
        limit: 1,
        attributes: ["id"],
        raw: true,
        where: {
          company: criteria.company,
        },
      });

      // if this company has no user(unlikely), no-op
      if (!findFirstUserForCompany) {
        return;
      }

      criteria.owner = findFirstUserForCompany.id;
      criteria.user = findFirstUserForCompany.id;

      return Beneficiary.findOrCreate({
        where: criteria,
        defaults: {
          ...criteria,
          owner: criteria.user,
          status: STATUSES.ACTIVE,
        },
      });
    } catch (err) {}
  },

  async checkIfExtraSeatsProcessing(additionalUsersCount, company) {
    const isNotProd = !Utils.isProd();

    if (isNotProd) {
      return null;
    }

    if (additionalUsersCount) {
      const extraSeatsKey = `extra_seats:${company.code}`;

      const serializedExtraSeats = await RedisService.get(extraSeatsKey);

      if (serializedExtraSeats) {
        throw new ValidationError("Still finalizing previously requested extra seats. Try again in some minutes..");
      }
    }

    return null;
  },

  async callService(service, payload, supportData) {
    if (Array.isArray(payload)) {
      return Promise.all(payload.map((item) => Service.runService(service, item, supportData)));
    }
    return Service.runService(service, payload, supportData);
  },

  async runService(service, item, supportData) {
    return Service[service]({
      ...(typeof item === "object" ? { ...Sanitizer.jsonify(item) } : { item }),
      ...supportData,
    });
  },
};

module.exports = Service;
