/* eslint-disable no-param-reassign */
const { Op } = require("sequelize");
const Utils = require("../utils/utils");
const {
  CategoryRepo,
  BillRepo,
  VendorRepo,
  BillInstallmentRepo,
  BillProductRepo,
  ProductRepo,
  ScheduledBillRepo,
  AuditLogsRepo,
  BudgetRepo,
  BalanceRepo,
  TransactionRepo,
  BillPaymentRepo,
  ScheduleRepo,
  CompanyRepo,
} = require("../repositories/index.repo");
const { NotFoundError, ValidationError } = require("../utils/error.utils");
const { DirectDebit } = require("../models");
const { STATUSES } = require("../models/status");
const ApprovalService = require("./approval.service");
const NotificationService = require("./notification");
const ResponseUtils = require("../utils/response.utils");
const ScheduledBillService = require("./scheduledBill.service");
const CronService = require("./cron.service");
const RedisService = require("./redis");
const SettingsService = require("./settings");
const BillProductService = require("./billProduct.service");
const { METHODS, SCHEDULED_ENTITY } = require("../mocks/constants.mock");
const billInstallmentRepo = require("../repositories/billInstallment.repo");
const Sanitizer = require("../utils/sanitizer");
const HelperService = require("./helper.service");
const BudgetService = require("./budget");
const BalanceService = require("./balance");
const BankService = require("./bank");
const VendorService = require("./vendorService");
const BillingService = require("./billing");
const { TRANSACTION_TYPES } = require("../models/transaction");
const scheduledBillRepo = require("../repositories/scheduledBill.repo");
const PaymentService = require("./paymentservice");
const ExistsError = require("../utils/exists-error");
const { syncBillToZoho, syncVendorPaymentToZoho } = require("../utils/zoho.utils");

const Service = {
  async createBill(payload) {
    const {
      company,
      user,
      products,
      scheduledBill,
      isDraft,
      method,
      budget,
      balance,
      directDebit,
      installments = [],
      minAmountDue,
      schedule,
      uploads,
      reference,
      ...rest
    } = payload;

    // check if reference exists
    await Service.checkIfReferenceExists(reference, company);

    await HelperService.deduceCategory(rest);

    await HelperService.deduceVendor(rest);

    let billAmount = Array.from(products).reduce((prev, current) => prev + Utils.calculateProductPrice(current), 0);

    Service.validateMinAmountDue(minAmountDue, billAmount);

    if (rest.discount) billAmount -= Utils.calculateDiscount({ discount: rest.discount, discountType: rest.discount_type, amount: billAmount });

    if (rest.vat) {
      const vatAmount = Utils.calculateVat({ vat: rest.vat, amount: billAmount });
      rest.vatAmount = vatAmount;
      billAmount += vatAmount;
    }

    if (parseInt(minAmountDue, 10) > parseInt(billAmount, 10)) {
      throw new ValidationError("min amount cannot be greater than total amount");
    }

    if (installments.length > 0) Utils.validateInstallmentsPayload({ installments, billAmount });

    const billPayload = {
      ...rest,
      ...(isDraft && { status: STATUSES.DRAFT }),
      company,
      user,
      amount: billAmount,
      balanceDue: billAmount,
      reference: reference || Utils.generateRandomString(17),
      minAmountDue,
      ...(schedule && { scheduledBill: schedule }),
    };

    if (method) {
      billPayload.paymentMethodId = await HelperService.deduceAccount({
        method,
        budget,
        company,
        balance,
        directDebit,
      });
      billPayload.paymentMethod = METHODS[method.toUpperCase()];
    }

    if (scheduledBill) {
      return Service.generateScheduledBill(billPayload, products, scheduledBill, installments, uploads);
    }

    const createdBill = await BillRepo.create({ payload: billPayload });

    let hasApproval = false;

    if (!isDraft) {
      const { success } = await ApprovalService.conditionDetector({
        id: createdBill.id,
        type: "bill",
        company: createdBill.company,
        user,
      });

      if (!success) {
        await BillRepo.update({ filter: { id: createdBill.id }, data: { status: STATUSES.VERIFYING } });
      }

      hasApproval = !success;
    }

    await Service.completeCreationOfBill({ bill: createdBill, installments, products });

    if (!(hasApproval || isDraft)) {
      Service.sendBillCreatedEmail({ bill: createdBill.code });
    }

    const foundBill = await BillRepo.getBill({
      filter: {
        code: createdBill.code,
      },
    });

    await Service.linkAssets({ uploads, foundEntity: foundBill, entityType: "bill" });

    return ResponseUtils.sendObjectResponse(!hasApproval ? "Bill created successfully" : "Bill created successfully and pending approval", {
      bill: createdBill,
      hasApproval,
    });
  },

  async completeCreationOfBill(payload) {
    const { bill, installments, products } = payload;
    const callsToMake = [];
    if (products) callsToMake.push(BillProductService.createBillProducts(bill, products));
    if (installments && Object.keys(installments).length > 0) {
      const { type, payments } = installments;
      const billInstallmentPayload = payments.map((payment) => {
        const { percentage, amount, due_date: dueDate, ...rest } = payment;
        const installmentAmount = amount || Utils.calculatePercentageAmount(percentage, bill.amount);
        return {
          ...rest,
          dueDate,
          type,
          bill: bill.id,
          percentage,
          amount: installmentAmount,
          company: bill.company,
          currency: bill.currency,
        };
      });
      callsToMake.push(BillInstallmentRepo.createBulkBillInstallments({ payload: billInstallmentPayload }));

      // Activate cron for reminders
      const reminderJobRedisKey = "billInstallmentReminder:running";
      const reminderJobIsRunning = await RedisService.get(reminderJobRedisKey);
      if (!reminderJobIsRunning) {
        await RedisService.set(reminderJobRedisKey, 1);
        const jobId = SettingsService.get("billInstallmentReminderJobId");
        CronService.updateCron(jobId, { activate: true });
      }
    }

    syncBillToZoho(bill.code, bill.company);

    return Promise.all([...callsToMake]);
  },

  async list(filter) {
    const { company, ...rest } = filter;
    const query = { company, ...rest };

    const [{ bills, meta }, summary] = await Promise.all([BillRepo.listBills({ filter: query }), BillRepo.getBillStats({ filter: query })]);

    return {
      bills,
      summary,
      meta,
    };
  },

  async buildBillTimeline(bill) {
    const activities = [];
    const {
      ApprovalRequest,
      created_at: createdAt,
      Company: { name: companyName },
      currency,
      ScheduledBill,
      dueDate,
    } = Sanitizer.jsonify(bill);

    const { firstName = "Bujeti", lastName = "Fetch", code } = bill.User || {};

    activities.push({
      title: "Created by",
      type: "creation",
      performedBy: {
        firstName,
        lastName,
        code,
      },
      performedAt: createdAt,
      status: "success",
    });

    if (ScheduledBill) {
      const formattedDueDate = Utils.formatDate(createdAt, "dd MMMM, yyyy");
      activities.push({
        title: "Scheduled",
        type: "schedule",
        description: `This bill was scheduled to be sent on the ${formattedDueDate}`,
        status: "success",
      });
    }

    if (ApprovalRequest) {
      const { Approvals, status: approvalStatus, ApprovalStages } = ApprovalRequest;
      // get Current stage
      if (approvalStatus === STATUSES.PENDING) {
        const pendingStage = ApprovalStages?.find((stage) => stage.status === STATUSES.PENDING);
        const threshold = SettingsService.get("APPROVERS_THRESHOLD");
        const condition = Utils.getKeyByValue(threshold, pendingStage.ApproverLevel.approvers_threshold);
        const approvers = pendingStage.ApproverLevel?.Approvers.map((approver) => {
          const { User } = approver;
          return {
            firstName: User?.firstName,
            lastName: User?.lastName,
          };
        });
        activities.push({
          title: `Pending approval`,
          type: "pending_approval",
          status: "pending",
          condition,
          approvers,
        });
      } else {
        // eslint-disable-next-line no-unused-expressions
        Approvals?.forEach((approval) => {
          const { Approver: { User } = {}, status, created_at: performedAt } = approval;
          let decision;
          if (status === STATUSES.APPROVED) decision = "Approved";
          else if (status === STATUSES.DECLINED) decision = "Declined";
          else decision = "Awaiting approval";

          activities.push({
            title: `${decision} by`,
            type: `approval_action`,
            status: Utils.getKeyByValue(STATUSES, status === STATUSES.APPROVED ? STATUSES.SUCCESS : status).toLowerCase(),
            performedBy: {
              firstName: User?.firstName,
              lastName: User?.lastName,
            },
            performedAt,
          });
        });
      }
    }

    if (bill.status !== STATUSES.PAID) {
      if (dueDate && Utils.firstDateIsAfterSecondDate(dueDate)) {
        const formattedDueDate = Utils.formatDate(dueDate, "MMMM dd, yyyy");
        activities.push({
          title: `Payment due on ${formattedDueDate}`,
          type: "pending_payment",
          description: `Reminder is scheduled to be sent ${Utils.formatDate(Utils.getFutureDate(-1, dueDate), "MMMM dd, yyyy")}`,
          performedBy: {
            name: companyName,
          },
          status: "pending",
        });
      } else if (dueDate) {
        const formattedDueDate = Utils.formatDate(dueDate, "MMMM dd, yyyy");
        activities.push({
          title: `Payment is over due since ${formattedDueDate}`,
          type: "overdue_payment",
          description: `Reminder was sent ${Utils.formatDate(Utils.getFutureDate(-1, dueDate), "MMMM dd, yyyy")}`,
          performedBy: {
            name: companyName,
          },
          status: "success",
        });
      }
    }

    const billPayments = await BillPaymentRepo.listBillPayments({
      filter: {
        bill: bill.id,
      },
    });

    // eslint-disable-next-line no-unused-expressions
    billPayments?.forEach(({ Transaction }) => {
      const {
        status,
        paidOn,
        created_at,
        User: { firstName, lastName },
      } = Transaction;
      const parsedAmount = HelperService.getActualTransactionAmount(Transaction);
      const installmentAmount = Utils.formatMoney(parsedAmount);
      const userName = `${firstName} ${lastName}`;
      if ([STATUSES.SUCCESS, STATUSES.FAILED, STATUSES.PROCESSING].includes(status)) {
        activities.push({
          title: `${Utils.getSymbolFromCurrency(currency)}${installmentAmount} paid by`,
          type: "paid_installment",
          performedBy: {
            name: userName,
          },
          status: Sanitizer.getStatusById(STATUSES, status),
          performedAt: (paidOn && Utils.formatDate(paidOn, "MMMM dd, yyyy")) || created_at,
        });
      }
    });
    return activities;
  },

  async view(filter) {
    const { includeTimeline = false, ...rest } = filter;
    const bill = await BillRepo.getBill({
      filter: { ...rest },
      includeProducts: true,
      addCompany: true,
      includePaymentMethod: true,
      includePayments: true,
      includeAssets: true,
      includeApproval: Boolean(includeTimeline),
      includeSchedule: Boolean(includeTimeline),
    });
    if (!bill) throw new NotFoundError(`Bill`);

    return bill;
  },

  async updateBillCalculation(bill) {
    const billFilter = { id: bill };
    const foundBill = await BillRepo.getBill({
      filter: billFilter,
      includeProducts: true,
    });

    if (!foundBill) ResponseUtils.BadRequestException("Bill not found");

    const { BillProducts, vat, discount, discount_type } = foundBill;

    let billAmount = Array.from(BillProducts).reduce((prev, billProduct) => {
      const {
        id,
        discount,
        discount_type,
        quantity,
        Product: { price: unitPrice },
      } = billProduct;

      const payload = { discount, discount_type, quantity, unitPrice };
      const billProductPrice = Utils.calculateProductPrice(payload);
      BillProductRepo.update({
        filter: { id },
        data: { amount: billProductPrice },
      });
      return prev + billProductPrice;
    }, 0);

    if (discount) billAmount -= discount_type === "amount" ? discount : billAmount * (discount / 100);

    if (vat) billAmount += billAmount * (vat / 100);

    BillRepo.update({
      filter: billFilter,
      data: { amount: billAmount },
    });
  },

  async updateBillProducts(bill, productsData) {
    const foundBill = await BillRepo.getBill({ filter: { id: bill }, includeProducts: true });
    if (!foundBill) throw new NotFoundError("Bill");
    const { company, BillProducts = [] } = foundBill;
    const previousBillProducts = BillProducts.map((billProduct) => billProduct.toJSON());
    const response = Utils.compareObjectArray({ previousItems: previousBillProducts, newItems: productsData, key: "code" });
    const { itemsToAdd, itemsToDelete, itemsToUpdate } = response;

    if (itemsToDelete.length > 0) {
      const billCodes = Utils.mapAnArray(itemsToDelete, "code");
      await BillProductRepo.update({
        filter: { code: billCodes },
        data: { status: STATUSES.DELETED },
      });
    }

    if (itemsToUpdate.length > 0) {
      await Promise.allSettled(
        // eslint-disable-next-line array-callback-return
        itemsToUpdate.map(async (item) => {
          const { code, name, currency, Product, unitPrice, ...rest } = item;
          // If product data exist to update
          if (name || unitPrice)
            ProductRepo.update({
              filter: { id: Product.id },
              payload: {
                ...(name && { name }),
                ...(unitPrice && { price: unitPrice }),
              },
            });

          // Update Bill product
          let amount = 0;
          if (unitPrice) amount = Utils.calculateProductPrice({ ...rest, unitPrice });
          if (Object.keys(rest).length > 0)
            await BillProductRepo.update({
              filter: { code },
              data: { ...rest, ...(amount && { amount }) },
            });
        })
      );
    }

    if (itemsToAdd.length > 0) {
      const billProductsItems = [];
      await Promise.allSettled(
        // eslint-disable-next-line array-callback-return
        itemsToAdd.map(async (item) => {
          const { name, currency, Product, unitPrice, ...rest } = item;
          const createdProduct = await ProductRepo.findOrCreateProduct({
            data: { name, currency, company, price: unitPrice },
          });
          const billProductAmount = Utils.calculateProductPrice({ ...rest, unitPrice });

          billProductsItems.push({ ...rest, company, amount: billProductAmount, bill, product: createdProduct.id });
        })
      );
      await BillProductRepo.createBulkBillProducts({ payload: billProductsItems });
    }
  },

  async completeBillEditing({ bill, payload }) {
    const shouldTriggerApprovalCheck = bill.status === STATUSES.DRAFT && Utils.getStatusValues(payload.status) === STATUSES.PENDING;
    if (shouldTriggerApprovalCheck) {
      const { success } = await ApprovalService.conditionDetector({
        id: bill.id,
        type: "bill",
        company: bill.company,
        user: bill.User,
      });
      if (!success) {
        // If there's approval rule
        await BillRepo.update({ filter: { id: bill.id }, data: { status: STATUSES.VERIFYING } });
        return ResponseUtils.sendObjectResponse("Approval triggered");
      }
    }

    const shouldNotifyCustomer = bill.status === STATUSES.DRAFT && payload?.status === STATUSES.PENDING && bill.User.email;
    if (shouldNotifyCustomer) {
      Service.sendBillCreatedEmail({ bill: bill.code });
    }
    return ResponseUtils.sendObjectResponse("Approval triggered");
  },

  async updateBill({ filter, payload }) {
    const foundBill = await BillRepo.getBill({ filter, includeProducts: true });
    if (!foundBill) throw new NotFoundError("Bill");

    if ([STATUSES.PARTIAL, STATUSES.PAID].includes(foundBill.status)) throw new ValidationError("Cannot update this bill");

    const { products = null, vendor, method, budget, balance, directDebit, installments, scheduledBill, isDraft, uploads, ...rest } = payload;
    if (products) await Service.updateBillProducts(foundBill.id, products);

    if (method) {
      rest.paymentMethodId = await HelperService.deduceAccount({
        method,
        budget,
        company: filter.company,
        balance,
        directDebit,
      });
      rest.paymentMethod = METHODS[method.toUpperCase()];
    }

    if (vendor) {
      const foundVendor = await VendorRepo.getVendor({
        queryParams: {
          code: vendor,
        },
      });

      if (!foundVendor) throw new NotFoundError("Vendor");
      rest.vendor = foundVendor.id;
    }

    if (rest.category) {
      const foundCategory = await CategoryRepo.getCategory({ queryParams: { code: rest.category } });
      if (!foundCategory) throw new NotFoundError("Category");
      rest.category = foundCategory.id;
    }

    if (installments) {
      const { BillInstallments } = foundBill;
      const previousItems = BillInstallments.map((installment) => installment.toJSON());
      const response = Utils.compareObjectArray({ previousItems, newItems: installments.payments, key: "code" });
      const { itemsToAdd, itemsToDelete, itemsToUpdate } = response;

      if (itemsToDelete.length > 0) {
        const billCodes = Utils.mapAnArray(itemsToDelete, "code");
        await billInstallmentRepo.updateBillInstallment({
          filter: { code: billCodes },
          payload: { status: STATUSES.DELETED },
        });
      }

      if (itemsToUpdate.length > 0) {
        await Promise.allSettled(
          // eslint-disable-next-line array-callback-return
          itemsToUpdate.map((item) => {
            const { id, code, due_date: dueDate, ...remainingData } = item;
            billInstallmentRepo.updateBillInstallment({
              filter: { code },
              payload: { ...remainingData, dueDate },
            });
          })
        );
      }

      if (itemsToAdd.length > 0) {
        const { type } = installments;
        const newInstallments = itemsToAdd.map((item) => ({ ...item, bill: foundBill.id, type, dueDate: item.due_date }));
        await billInstallmentRepo.createBulkBillInstallments({ payload: newInstallments });
      }
    }

    if (rest.status) rest.status = Utils.getStatusValues(rest.status);
    if (Object.keys(rest).length > 0) await BillRepo.update({ filter, data: rest });
    Service.updateBillCalculation(foundBill.id);

    const { scheduledBill: scheduledBillId } = foundBill;

    const updatedBill = await BillRepo.getBill({ filter, includeProducts: true });

    await Service.linkAssets({ uploads, foundEntity: updatedBill, entityType: "bill" });

    if (scheduledBill) {
      if (scheduledBillId) {
        const foundScheduledBill = await ScheduledBillRepo.getScheduledBill({
          id: scheduledBillId,
        });

        await Service.cancelPreviousScheduleIfFound(foundScheduledBill, foundBill);
      }
      const { id, BillInstallments, BillProducts, code, status, ...remainingPayload } = Sanitizer.jsonify(updatedBill);
      await Service.createNewScheduleBillAndLinkToBill({
        remainingPayload: {
          ...remainingPayload,
          ...(isDraft && {
            status: STATUSES.DRAFT,
          }),
        },
        BillProducts,
        scheduledBill,
        BillInstallments,
        foundBill: updatedBill,
        uploads,
      });
    } else if (scheduledBillId) {
      await ScheduledBillRepo.updateScheduledBill({ queryParams: filter, payload: rest });
    }

    return ResponseUtils.sendObjectResponse("Bill updated successfully", foundBill);
  },

  async removeBill(criteria) {
    const bill = await BillRepo.getBill({ filter: criteria });
    if (!bill) throw new NotFoundError("Bill");

    const payload = { status: STATUSES.DELETED };
    return BillRepo.update({ filter: criteria, data: payload });
  },

  async markBillAsPaid({ filter, payload, extras = {} }) {
    const foundBill = await BillRepo.getBill({
      filter: { ...filter, status: [STATUSES.PENDING, STATUSES.PARTIAL, STATUSES.OVERDUE, STATUSES.APPROVED] },
    });
    if (!foundBill) throw new NotFoundError("Bill");

    const { success } = await ApprovalService.conditionDetector(
      {
        id: foundBill.id,
        type: "bill",
        company: foundBill.company,
        user: foundBill.User,
      },
      true
    );
    if (!success) {
      // If there's approval rule
      throw new ValidationError("Awaiting approval");
    }

    const { installments = [] } = payload;
    if (installments && installments.length) {
      // Mark installments as paid
      const foundInstallments = await BillInstallmentRepo.listBillInstallment({
        filter: { code: installments, bill: foundBill.id },
      });

      if (!(foundInstallments && foundInstallments.length)) throw new ValidationError("One or more bill installment not found");
      const hasPaidInstallment = Array.from(foundInstallments).find((installment) => installment.status === STATUSES.PAID);
      if (hasPaidInstallment) throw new ValidationError("An already paid installment cannot be marked as paid");
      await BillInstallmentRepo.updateBillInstallment({
        filter: { code: installments },
        payload: { status: STATUSES.PAID, paid_on: new Date() },
      });
    }

    // Check if it has other pending installment
    if (installments && installments.length) {
      const hasUnpaidInstallment = await BillInstallmentRepo.listBillInstallment({
        filter: { bill: foundBill.id, status: [STATUSES.PENDING, STATUSES.OVERDUE] },
      });
      if (foundBill.status === STATUSES.PENDING) await BillRepo.update({ filter: { id: foundBill.id }, data: { status: STATUSES.PARTIAL } });
      if (hasUnpaidInstallment.length) return ResponseUtils.sendObjectResponse("Bill Installment marked as paid");
    }

    // Installment not sent, mark all pending has paid
    await Promise.all([
      BillInstallmentRepo.updateBillInstallment({
        filter: { bill: foundBill.id, status: [STATUSES.PENDING, STATUSES.OVERDUE] },
        payload: { status: STATUSES.PAID, paid_on: new Date() },
      }),

      BillRepo.update({ filter, data: { status: STATUSES.PAID, balanceDue: 0, paidOn: new Date() } }),
    ]);

    AuditLogsRepo.createAnAuditLogs({
      queryParams: {
        event: "mark-bill-as-paid",
        user: extras?.user.id || foundBill.user,
        table_type: "Bills",
        table_id: foundBill.id,
        initial_state: Sanitizer.sanitizeBill(foundBill),
        delta: { ...payload },
      },
    });

    return ResponseUtils.sendObjectResponse("Your bill has been marked as paid");
  },

  /**
   * complete bill approval
   * @param {*} bill bill code
   * @returns
   */
  async completeApproval(payload) {
    const { code: billCode } = payload;
    const foundBill = await BillRepo.getBill({
      filter: { code: billCode },
      addCompany: true,
    });

    if (!foundBill) throw new NotFoundError("Bill");

    syncBillToZoho(billCode, foundBill.company);

    if (foundBill.sendForApproval) return null;

    const foundCompany = await CompanyRepo.getOneCompany({
      queryParams: {
        id: foundBill.company,
      },
      addPaymentPlan: true,
    });

    const sanitizedCompany = Sanitizer.jsonify(foundCompany);

    return Service.payBill({
      code: billCode,
      company: { ...sanitizedCompany, paymentPlan: sanitizedCompany.PaymentPlan },
      user: foundBill.User,
    });
  },

  async disableScheduleBillCron(code) {
    const foundScheduledBill = await ScheduledBillRepo.getScheduledBill({ queryParams: { code } });
    if (!foundScheduledBill) throw new NotFoundError("Scheduled Bill");
    const jobId = foundScheduledBill.Schedule?.cron_id;
    if (!jobId) return true;
    return CronService.updateCron(jobId, { activate: false });
  },

  async sendBillCreatedEmail({ bill }) {
    if (!(bill && String(bill).startsWith("bil_"))) throw new ValidationError("Please specify bill code");

    const foundBill = await BillRepo.getBill({ filter: { code: bill }, addCompany: true });
    if (!foundBill) throw new NotFoundError("Bill");

    const {
      billInstallments,
      User: foundUser,
      currency,
      Vendor: foundVendor,
      amount,
      code,
      vat,
      discount,
      discount_type: discountType,
      dueDate,
      Company: { name: companyName },
    } = foundBill;

    const { firstName, lastName } = foundUser;

    const emailPayload = {};
    if (billInstallments) {
      const { installmentAmountResponse } = Utils.prepareInvoiceInstallmentPayloadForEmail(billInstallments);
      Object.assign(emailPayload, installmentAmountResponse);
    }

    if (foundVendor.address) {
      emailPayload.billingAddress = foundVendor.address;
    }

    const { subTotal, discount: calculatedDiscount } = Utils.getInvoiceAmountBreakdown({
      currency,
      amount,
      vat,
      discount,
      discountType,
    });

    NotificationService.notifyUser({ email: foundVendor.email }, "create-bill", {
      recipientName: foundVendor.name,
      creatorName: `${firstName} ${lastName}`,
      companyName,
      currency: Utils.getSymbolFromCurrency(currency),
      totalAmount: (parseInt(amount, 10) / 100).toLocaleString(),
      billUrl: `${Utils.getWebsiteURL()}/bills/${code}`,
      recipientEmail: foundVendor.email,
      year: new Date().getFullYear(),
      ...(vat && { vat }),
      ...(dueDate && { dueDate: Utils.formatHumanReadableDate(dueDate) }),
      discount: calculatedDiscount,
      subTotal,
      ...emailPayload,
    });
  },

  async getBillAnalytics(filter) {
    return BillRepo.getBillStats({ filter });
  },

  async voidBill(criteria) {
    const bill = await BillRepo.getBill({ filter: criteria });
    if (!bill) throw new NotFoundError("Bill");

    const payload = { status: STATUSES.INACTIVE };
    return BillRepo.update({ filter: criteria, data: payload });
  },

  async payBill(filter) {
    let { amount } = filter;
    const { company, code, user } = filter;

    const foundBill = await BillRepo.getBill({
      filter: {
        code,
        company: company.id,
      },
    });

    if (!foundBill) throw new NotFoundError("Bill");

    const { success } = await ApprovalService.conditionDetector(
      {
        id: foundBill.id,
        type: "bill",
        company: foundBill.company,
        user: foundBill.User,
      },
      true
    );
    if (!success) {
      // If there's approval rule
      throw new ValidationError("Awaiting approval");
    }

    if (foundBill.status === STATUSES.PAID) throw new ValidationError("Bill already paid");

    if ([STATUSES.INACTIVE, STATUSES.DRAFT].includes(foundBill.status)) {
      throw new ValidationError(`Bill in ${Sanitizer.getStatusById(STATUSES, foundBill.status)} state cannot be paid for`);
    }

    if (!foundBill.paymentMethodId) throw new ValidationError("Please specify source of funds");

    const hasAlreadyMadePayment = await BillPaymentRepo.getBillPayment({
      filter: {
        bill: foundBill.id,
      },
    });

    const totalPaidAndProcessing = await BillPaymentRepo.totalBillPaymentAmount({
      filter: {
        bill: foundBill.id,
        status: [STATUSES.PAID, STATUSES.PROCESSING],
      },
    });

    const outstandingAmount = foundBill.amount - totalPaidAndProcessing;

    if (!outstandingAmount) throw new ValidationError("You have no outstanding payments");

    if (!amount) {
      amount = foundBill.minAmountDue || foundBill.amount;
      if (hasAlreadyMadePayment) {
        amount = outstandingAmount;
      }
    }

    const nextAmountToPay = amount - outstandingAmount;

    if (nextAmountToPay < 0 && Math.abs(nextAmountToPay) < 10000 && foundBill.currency === "NGN") {
      throw new ValidationError(
        `Your next amount will be less than NGN 100 which is less than the minimum we can process so this payment cannot be processed, please adjust this installment amount based on your outstanding amount of ${
          foundBill.currency
        } ${Utils.formatAmount(outstandingAmount).toLocaleString()}`
      );
    }

    if (amount > outstandingAmount) {
      throw new ValidationError(`Outstanding amount to pay is ${foundBill.currency} ${Utils.formatAmount(outstandingAmount).toLocaleString()}`);
    }

    if (!hasAlreadyMadePayment && amount < foundBill.minAmountDue) {
      throw new ValidationError(
        `Minimum amount for first payment is ${foundBill.currency} ${Utils.formatAmount(foundBill.minAmountDue).toLocaleString()}`
      );
    }

    const isPartialPayment = amount < foundBill.amount || hasAlreadyMadePayment;
    const isDirectDebit = foundBill.paymentMethod === METHODS.DIRECTDEBIT;
    const narration = `Bill Payment of ${foundBill.currency}${(parseInt(amount, 10) / 100).toLocaleString()} to ${foundBill.Vendor.name}`;

    const { id: recipientId, BankAccounts } = await VendorService.getVendor(
      {
        id: foundBill.vendor,
        company: company.id,
      },
      { includeBankAccount: true }
    );

    const foundVendorBankAccount = Array.isArray(BankAccounts) && BankAccounts.length > 0 ? BankAccounts[0] : null;

    if (!foundVendorBankAccount) throw new NotFoundError("Recipient bank account");

    const payload = {
      amount,
      description: narration,
      narration,
      payer: user.id,
      company: company.id,
      status: STATUSES.PENDING,
      recipient: recipientId,
      recipientType: "vendor",
      cbnFee: 0,
      bujetiFee: 0,
      bankAccountId: foundVendorBankAccount.id,
      currency: foundBill.currency,
    };

    const { cbnFee, bujetiFee } = await BillingService.computeFees({
      amount: payload.amount,
      foundCompany: company.code,
      currency: payload.currency,
      plan: Utils.parseJSON(company.paymentPlan?.configuration),
      isDirectDebit,
    });

    payload.cbnFee = cbnFee || 0;
    payload.bujetiFee = bujetiFee || 0;

    const totalAmount = amount + payload.cbnFee + payload.bujetiFee;

    await Service.prepareToCharge({
      method: foundBill.paymentMethod,
      methodId: foundBill.paymentMethodId,
      amount,
      totalAmount,
      payload,
      company,
      BankAccount: foundVendorBankAccount,
      narration,
    });

    const transaction = await this.createTransaction(payload);

    if (!isPartialPayment) {
      // create bill payment
      await BillPaymentRepo.createBillPayment({
        payload: {
          bill: foundBill.id,
          company: company.id,
          status: STATUSES.PROCESSING,
          transaction: transaction.id,
        },
      });
    } else {
      // create installment
      const billInstallment = await BillInstallmentRepo.createBillInstallment({
        payload: {
          type: "amount",
          bill: foundBill.id,
          amount,
          company: foundBill.company,
          currency: foundBill.currency,
          dueDate: foundBill.dueDate,
        },
      });

      // create bill payment
      await BillPaymentRepo.createBillPayment({
        payload: {
          bill: foundBill.id,
          company: company.id,
          status: STATUSES.PROCESSING,
          installment: billInstallment.id,
          transaction: transaction.id,
        },
      });
    }
    transaction.status = STATUSES.PROCESSING;
    await transaction.save();

    return ApprovalService.addPaymentToQueue(transaction.code);
  },

  async finalizeBill(payload) {
    const { company, transaction, status = STATUSES.PAID } = payload;

    const billPayment = await BillPaymentRepo.getBillPayment({
      filter: {
        transaction,
        company,
      },
    });

    if (!billPayment) throw new NotFoundError("Bill payment");

    if (billPayment.status === status) return null;

    const isInstallmentPayment = !!billPayment.installment;
    const isPaid = status === STATUSES.PAID;
    const isFailed = status === STATUSES.FAILED;

    if (!isInstallmentPayment) {
      await BillRepo.update({
        filter: {
          id: billPayment.bill,
        },
        data: {
          status,
          balanceDue: 0,
        },
      });
    } else {
      await BillInstallmentRepo.updateBillInstallment({
        filter: {
          id: billPayment.installment,
        },
        payload: {
          status,
          paidOn: new Date(),
        },
      });

      const totalPaidSoFar = await BillPaymentRepo.totalBillPaymentAmount({
        filter: {
          bill: billPayment.bill,
          status: STATUSES.PAID,
        },
      });

      const actualAmount = HelperService.getActualTransactionAmount(billPayment.Transaction);
      const totalPaymentSoFar = parseInt(totalPaidSoFar, 10) + actualAmount;
      const hasCompletedPayment = totalPaymentSoFar >= billPayment.Bill?.amount;

      let appropriateStatus = status;

      if (isPaid) {
        appropriateStatus = hasCompletedPayment ? STATUSES.PAID : STATUSES.PARTIAL;
      } else {
        const hasOtherBillPayments = await BillPaymentRepo.getBillPayment({
          filter: {
            id: {
              [Op.ne]: billPayment.id,
            },
            status: {
              [Op.in]: [STATUSES.PAID, STATUSES.PROCESSING],
            },
          },
        });

        if (isInstallmentPayment) {
          appropriateStatus = hasOtherBillPayments ? STATUSES.PARTIAL : status;
        } else {
          appropriateStatus = status;
        }
      }

      let balanceDue = 0;
      const foundBill = await BillRepo.getBill({
        filter: {
          id: billPayment.bill,
        },
      });
      if (isPaid) {
        balanceDue = Math.max(foundBill.amount - totalPaymentSoFar, 0);
      } else if (isFailed) {
        balanceDue = (foundBill.balanceDue || 0) + actualAmount;
      }

      await BillRepo.update({
        filter: {
          id: billPayment.bill,
        },
        data: {
          status: appropriateStatus,
          balanceDue: !Number.isNaN(balanceDue) ? balanceDue : 0,
        },
      });
    }
    syncVendorPaymentToZoho(
      billPayment?.Bill?.code,
      billPayment?.Transaction?.code,
      billPayment?.company
    );
    return billPayment.update({ status });
  },

  async syncZoho({ codes, company }) {
    const foundBills = await BillRepo.fetchBills({
      filter: {
        code: codes,
        company,
        zohoIdentifier: {
          [Op.eq]: null,
        },
      },
    });

    if (!foundBills.length) return null;

    const billsIds = [];
    let zohoError = null;

    await Promise.allSettled(
      foundBills.map(async (bill) => {
        if (
          [STATUSES.ACTIVE, STATUSES.PAID, STATUSES.COMPLETED].includes(
            bill.status
          )
        ) {
          const result = await syncBillToZoho(bill.code, company);
          if (result) {
            console.log(result, "result");
            zohoError = result;
          }
          billsIds.push(bill.id);
        }
        return billsIds;
      })
    );

    if (zohoError) {
      console.log(zohoError, "zohoError");
      return zohoError;
    }

    const billPayments = await BillPaymentRepo.listBillPayments({
      filter: {
        bill: billsIds,
        status: STATUSES.PAID,
      },
    });

    return Promise.allSettled(
      billPayments.map(async (billPayment) => {
        const { Transaction } = billPayment;
        if (!Transaction) return null;
        if (
          Transaction.status === STATUSES.SUCCESS &&
          !Transaction.zohoIdentifier
        ) {
          return syncVendorPaymentToZoho(
            billPayment.code,
            Transaction?.code,
            company
          );
        }
        return null;
      })
    );
  },

  async createTransaction(payload) {
    return TransactionRepo.createTransaction({
      data: {
        amount: payload.amount,
        description: payload.description,
        payer: payload.payer,
        budget: payload.budget,
        company: payload.company,
        currency: payload.currency,
        bank_account: payload.bankAccountId,
        status: STATUSES.PENDING,
        narration: payload.narration,
        recipient: payload.recipient,
        recipient_type: payload.recipientType,
        category: payload.category,
        processor_fee: payload.cbnFee,
        bujeti_fee: payload.bujetiFee,
        team: payload.team,
        balance: payload.balance,
        type: TRANSACTION_TYPES.BILL,
        directDebitId: payload.directDebitId,
      },
    });
  },

  async prepareToCharge({ method, methodId, amount, totalAmount, payload, company, BankAccount, narration }) {
    switch (Number(method)) {
      case METHODS.BUDGET:
        {
          if (!methodId) throw new ValidationError("Please specify budget");
          const foundBudget = await BudgetRepo.getBudget({ id: methodId, company: company.id });
          if (!foundBudget) throw new NotFoundError("Budget");

          await BudgetService.canBudgetHandleTransaction({ budget: foundBudget.id, amount, totalAmount });

          payload.budget = foundBudget.id;
          payload.currency = foundBudget.currency;
        }
        break;
      case METHODS.BALANCE:
        {
          if (!methodId) throw new ValidationError("Please specify balance");

          const foundBalance = await BalanceRepo.getBalance({
            filter: {
              id: methodId,
            },
          });
          if (!foundBalance) throw new NotFoundError("Balance");

          await BalanceService.canBalanceHandleTransaction({ balance: foundBalance.id, amount, totalAmount, company: company.id });
          payload.balance = foundBalance.id;
          payload.currency = foundBalance.currency;
        }
        break;
      case METHODS.DIRECTDEBIT:
        {
          if (!methodId) throw new ValidationError("Please specify direct debit source");

          const {
            data: { existingMandate },
          } = await BankService.canAccountProcessPayment({ directDebit: { bankAccount: methodId }, amount });

          const { number: accountNumber, bankCode, currency } = BankAccount;

          const createdDirectDebit = await DirectDebit.create({
            mandate: existingMandate.id,
            company: company.id,
            amount,
            beneficiaryAccountNumber: accountNumber,
            beneficiaryBankCode: bankCode,
            narration,
            status: STATUSES.PENDING,
            reference: Utils.generateRandomString(14),
          });

          payload.directDebitId = createdDirectDebit.id;
          payload.currency = currency;
        }
        break;
      default: {
        return null;
      }
    }
    return null;
  },
  async cancelPreviousScheduleIfFound(foundScheduledBill, foundBill) {
    if (foundScheduledBill) {
      await Promise.all([
        scheduledBillRepo.updateScheduledBill({
          queryParams: { id: foundBill.scheduledBill },
          payload: { status: STATUSES.CANCELLED },
        }),
        ScheduleRepo.updateSchedule({
          queryParams: { id: foundScheduledBill.schedule },
          updateFields: {
            status: STATUSES.CANCELLED,
          },
        }),
      ]);
      const {
        Schedule: { cron_id: jobId },
      } = foundScheduledBill;

      if (jobId) {
        CronService.updateCron(jobId, {
          activate: false,
        });
      }
    }
  },
  async createNewScheduleBillAndLinkToBill({ remainingPayload, BillProducts, scheduledBill, BillInstallments, foundBill, uploads }) {
    const { data } = await ScheduledBillService.createScheduledBill({
      billPayload: remainingPayload,
      products: Sanitizer.sanitizeArray(
        BillProducts.map(({ Product, quantity, discount, discount_type }) => ({
          ...Product,
          unitPrice: Product.price,
          quantity,
          discount,
          discount_type,
        }))
      ),
      scheduledBill,
      installments: Sanitizer.sanitizeArray(BillInstallments).reduce((mappedValue, installment) => {
        const { type, percentage, dueDate } = installment;

        mappedValue.type = type;
        mappedValue.payments = [...(mappedValue.payments || [])];

        const mappedInstallment = {
          due_date: dueDate,
          percentage,
        };

        mappedValue.payments.push(mappedInstallment);

        return mappedValue;
      }, {}),
      entity: SCHEDULED_ENTITY.BILL,
    });

    const foundScheduledBill = await ScheduledBillRepo.getScheduledBill({
      queryParams: {
        code: data.code,
      },
    });
    foundBill.scheduledBill = foundScheduledBill.id;
    foundBill.status = STATUSES.SCHEDULED;
    await foundBill.save();

    await Service.linkAssets({ uploads, foundEntity: foundScheduledBill, entityType: "scheduledBill" });
  },

  async linkAssets({ uploads, entityType, foundEntity }) {
    if (Array.isArray(uploads) && uploads.length) {
      await PaymentService.callService("updateAssetWithEntity", uploads, { entityId: foundEntity.id, entityType });
    }
  },

  async generateScheduledBill(billPayload, products, scheduledBill, installments, uploads) {
    const { data } = await ScheduledBillService.createScheduledBill({
      billPayload,
      products,
      scheduledBill,
      installments,
      entity: SCHEDULED_ENTITY.BILL,
    });

    const foundScheduledBill = await ScheduledBillRepo.getScheduledBill({
      queryParams: {
        code: data.code,
      },
    });
    await Service.linkAssets({ uploads, foundEntity: foundScheduledBill, entityType: "scheduledBill" });
    return ResponseUtils.sendObjectResponse("Scheduled bill created successfully", { bill: data });
  },

  async checkIfReferenceExists(reference, company) {
    if (reference) {
      const referenceExists = await BillRepo.getBill({
        filter: {
          reference,
          company,
          status: {
            [Op.ne]: STATUSES.DELETED,
          },
        },
        throwErrorIfNotfound: false,
      });
      if (referenceExists) throw new ExistsError("Reference");
    }
  },

  validateMinAmountDue(minAmountDue, billAmount) {
    if (minAmountDue) {
      if (minAmountDue > billAmount) {
        throw new ValidationError("min amount cannot be greater than total amount");
      }
    }
  },
};

module.exports = Service;
