const ProductService = require("./product.service");
const Utils = require("../utils");
const { BillProductRepo } = require("../repositories/index.repo");

const Service = {
  async createBillProducts(bill, products) {
    const { id: billId, company } = bill;
    // Create products
    let productsResponse = [];
    const shouldCreateProducts = !!products[0]?.name;
    if (shouldCreateProducts) {
      const productData = products.map((product) => ({ name: product.name, company, currency: product.currency, price: product.unitPrice }));
      productsResponse = await ProductService.findOrCreate(productData);
    }
    // Create Bill Products
    const billProductsData = products.map((singleProduct) => ({
      currency: singleProduct.currency,
      company,
      bill: billId,
      quantity: singleProduct.quantity,
      ...(singleProduct.discount && { discount: singleProduct.discount }),
      ...(singleProduct.discount_type && { discount_type: singleProduct.discount_type }),
      amount: Utils.calculateProductPrice(singleProduct),
      product: !shouldCreateProducts ? singleProduct.product : productsResponse.find((data) => data.name === singleProduct.name)?.id,
    }));

    return Service.createBulkBillProducts(billProductsData);
  },

  async createBulkBillProducts(payload) {
    return BillProductRepo.createBulkBillProducts({ payload });
  },
};

module.exports = Service;
