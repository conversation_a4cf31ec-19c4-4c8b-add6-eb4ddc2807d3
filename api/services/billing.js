/* eslint-disable no-use-before-define */
const { Op } = require("sequelize");
const Utils = require("../utils");
const { Providers, Subscription, Mandate, BillingHistory, DirectDebit, PaymentPlan, Transaction, Company } = require("../models");
const { STATUSES } = require("../models/status");
const {
  MonoAccountHolderRepo,
  BalanceRepo,
  BankAccountRepo,
  CompanyRepo,
  TransactionRepo,
  UserRepo,
  TransferRepo,
  BudgetAccountRepo,
} = require("../repositories/index.repo");
const { ValidationError, NotFoundError } = require("../utils/error.utils");
const SettingsService = require("./settings");
const { TRANSACTION_TYPES } = require("../models/transaction");
const BillingValidator = require("../validators/billing.validator");
const NotificationService = require("./notification");
const { METHODS_INTERPRETER, METHODS, BILLING_PERIODS, PAYMENT_PLANS } = require("../mocks/constants.mock");
const { getNextDate } = require("../utils");
const { CARD_ISSUER } = require("../models/cardissuer");
const QueueService = require("./queue.service");

const Service = {
  validatePayload(payload, validatorEntity) {
    const { error } = BillingValidator[validatorEntity].validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: true, message: "Valid payload" };
  },

  /**
   * Compute a transaction fee.
   * When a company code is passed, the custom fees for that company is used.
   * @param {number} amount
   * @param {string} companyCode
   * @param {string} currency
   * @params {boolean} isDirectDebit
   * @returns {Object} {cbnFee, bujetiFee }
   */
  computeFees({ amount, companyCode, currency = "NGN", isDirectDebit = false, plan = { transactionFee: 1, customPricing: null } }) {
    const pricingSettings = SettingsService.get("pricing_config");
    const { maximumFee = 100000, directDebitFee = 30000 } = pricingSettings[currency];

    if (isDirectDebit) return { cbnFee: directDebitFee, bujetiFee: 0 };

    const { transactionFee, payables = {}, customPricing = null } = plan;
    const { payoutFees = {} } = payables;
    let plainFee = 0;
    const providerFees = Service.getCbnFee(amount);

    if (customPricing) {
      plainFee = Service.getCustomPricingForCompany(amount, companyCode, plan);
      if (Object.prototype.hasOwnProperty.call(customPricing, "transactionFee")) {
        return {
          cbnFee: Math.min(providerFees, plainFee),
          bujetiFee: Math.max(0, plainFee - providerFees),
        };
      }
    } else if (Object.prototype.hasOwnProperty.call(payoutFees, currency)) {
      plainFee = Utils.money(payoutFees[currency]).toNumber();
    } else {
      plainFee = Number(Number(amount * (transactionFee / 100)).toFixed(0)); // default 1%
    }
    // if plainFee is above maximum fee, cap it at the max;
    const realFee = Math.min(maximumFee, plainFee);
    let bujetiFee = 0;
    bujetiFee = realFee - providerFees;

    if (currency === "NGN") {
      const bujetiCut = bujetiFee > 0 ? bujetiFee : 500;
      return {
        cbnFee: providerFees,
        bujetiFee: bujetiCut,
      };
    }
    return { cbnFee: 0, bujetiFee: realFee };
  },
  /**
   * Bill a transaction of the fee passed
   * @param {*} transactionCode
   * @param {*} fee
   * @param {*} currency
   * @param {*} company
   * @param {*} provider
   * @returns
   */
  async billTransaction(transactionCode, fee, currency, company, provider) {
    const account =
      provider === "mono"
        ? await MonoAccountHolderRepo.getAccountHolder({
            queryParams: { company, status: STATUSES.ACTIVE },
          })
        : null;
    if (!account) return;
    return Providers[provider].topUpIssuingWallet(account.holderId, fee, company, { transaction_code: transactionCode });
  },
  billCompany(company) {},
  // eslint-disable-next-line no-unused-vars
  getCbnFee(amount) {
    return 2000;
  },

  getCustomPricingForCompany(
    amount,
    companyCode,
    plan = {
      transactionFee: 1,
      transactionFeeMultiplier: null,
      customPricing: {},
    }
  ) {
    const { customPricing: companySpecialPrices } = plan;
    // { 'cmp_fd93nf03Nld': { above: { 500000: 5000, 200000: 7500 } } } }
    if (companySpecialPrices) {
      // { above: { 500000: 5000, 200000: 7500 } }, below : null }
      const { flatFee, transactionFee: customTransactionFee } = companySpecialPrices;
      const aboveAmountPrices = companySpecialPrices.above;
      const belowAmountPrices = companySpecialPrices.below;
      let pricing = { default_fee: null }; // null so the CBN fees are applied
      if (flatFee) return flatFee;
      if (typeof customTransactionFee === "number") {
        return (amount * parseFloat(customTransactionFee, 10)) / 100;
      }
      if (belowAmountPrices) {
        const prices = Object.keys(belowAmountPrices);
        pricing = prices.reduce((previousPriceKey, nextPriceKey) => {
          if (amount <= parseInt(nextPriceKey, 10)) {
            return belowAmountPrices[nextPriceKey];
          }
          return previousPriceKey;
        }, null);
      }
      if (!pricing && aboveAmountPrices) {
        const prices = Object.keys(aboveAmountPrices);
        pricing = prices.reduce((previousPriceKey, nextPriceKey) => {
          if (amount > parseInt(nextPriceKey, 10)) {
            return aboveAmountPrices[nextPriceKey];
          }
          return previousPriceKey;
        }, null);
      }
      return Number(Number(pricing).toFixed(0));
    }
    return null;
  },

  async createCustomPlan(body) {
    const { company, ...rest } = body;

    const foundCompany = await CompanyRepo.getCompany({ queryParams: { code: company } });

    if (!foundCompany) throw new NotFoundError("Company");

    await doNameCheck(rest, foundCompany);

    return PaymentPlan.create({
      ...rest,
      company: foundCompany.id,
      status: STATUSES.ACTIVE,
    });
  },

  async updateCustomPlan(body) {
    const { code: plan, ...rest } = body;

    const existingPlan = await PaymentPlan.findOne({
      where: {
        code: plan,
      },
      include: [Company],
    });

    if (!existingPlan) throw new NotFoundError("Plan");

    const foundCompany = existingPlan.Company;

    if (rest.name) {
      await doNameCheck(rest, foundCompany);
    }

    if (rest.status) {
      rest.status = Utils.getStatusValues(rest.status);
    }

    return existingPlan.update({
      ...(rest.name && { name: rest.name }),
      ...(rest.description && { description: rest.description }),
      ...(rest.currency && { currency: rest.currency }),
      ...(rest.amount && { amount: rest.amount }),
      ...(rest.configuration && { configuration: rest.configuration }),
      ...(rest.status && { status: rest.status }),
    });
  },

  /**
   * List billing history
   * @param {*} filters
   * @returns
   */
  listBillingHistory(filters) {
    const { from, to, company, status } = filters;
    if (from && !Utils.isValidDate(from)) throw new ValidationError("Invalid starting date");
    if (to && !Utils.isValidDate(to)) throw new ValidationError("Invalid ending date");

    const criteria = {
      company,
      ...(from && { from }),
      ...(to && { to }),
    };

    if (status) {
      criteria.status = Utils.getStatusValues(status);
    } else {
      criteria.status = { [Op.not]: STATUSES.DELETED };
    }

    return BillingHistory.findAll({
      where: criteria,
      include: [{ model: Subscription, include: [PaymentPlan] }],
      order: [["created_at", "DESC"]],
    });
  },

  async charge({ company, user, paymentPlan, billingPeriod, BudgetService, BankService, subscription, ApprovalService }) {
    let { method } = subscription;
    const plan = await PaymentPlan.findOne({
      where: {
        id: paymentPlan,
      },
    });

    if (!plan) throw new NotFoundError("Payment plan");

    const subscriptionAmount = await calculateSubscriptionAmount({ plan, billingPeriod, company: company.id, existingPlan: plan });

    const narration = `${company.name} - Payment of ${plan.currency}${(
      parseInt(subscriptionAmount, 10) / 100
    ).toLocaleString()} for subscription plan (${plan.name})`;

    const payload = {
      amount: subscriptionAmount,
      description: narration,
      narration,
      payer: user?.id,
      company: company.id,
      status: STATUSES.PENDING,
    };

    // use company credits if available
    const canUseCompanyCredit = company?.credits >= subscriptionAmount;

    if (canUseCompanyCredit) {
      method = Utils.getBillingMethod(METHODS.CREDITS);
      payload.currency = "NGN";
    }

    let createdDirectDebit;
    let response = { error: false };
    let account = null;

    if (method === METHODS.BUDGET) {
      const foundBudget = await BudgetService.getBudget({ id: subscription.method_id, company: company.id });
      account = await prepareToChargeBudget({
        budget: foundBudget?.code,
        BudgetService,
        company,
        subscriptionAmount,
        payload,
        account,
        Budget: foundBudget,
      });
    }

    if (method === METHODS.BALANCE) {
      const foundBalance = await BalanceRepo.getBalance({
        filter: {
          id: subscription.method_id,
        },
        includeAccount: true,
      });
      account = await prepareToChargeBalance({ balance: foundBalance?.code, subscriptionAmount, payload, account, Balance: foundBalance });
    }

    if (method === METHODS.DIRECTDEBIT) {
      const bankAccount = await BankAccountRepo.getOneBankAccount({
        queryParams: {
          id: subscription.method_id,
        },
        selectOptions: ["currency", "number", "bankCode"],
      });
      const directDebit = {
        bankAccount: bankAccount.code,
      };

      ({ createdDirectDebit, account } = await prepareToInitDirectDebit({
        directDebit,
        BankService,
        subscriptionAmount,
        company,
        createdDirectDebit,
        narration,
        payload,
        account,
        BankAccount: bankAccount,
      }));
    }

    let transaction = null;
    let transfer;

    if (!canUseCompanyCredit) {
      const { isDirectDebit, foundAccount } = await prepareBujetiAccount(payload);

      if (!isDirectDebit) {
        payload.recipient = foundAccount.owner;
        payload.recipientType = foundAccount.ownerType;
      }

      if (payload.useBookTransfer) {
        transfer = await this.createTransfer(payload);
      } else {
        const { cbnFee, bujetiFee } = this.computeFees({
          amount: payload.amount,
          companyCode: company.code,
          currency: payload.currency,
          plan: Utils.parseJSON(plan?.configuration),
          isDirectDebit,
        });

        payload.cbnFee = cbnFee;
        payload.bujetiFee = bujetiFee;
        transaction = await this.createTransaction(payload);
      }

      response = await chargeForSubscription({ isDirectDebit, payload, BankService, company, transaction, response, ApprovalService, transfer });

      if (response.error) {
        return {
          error: true,
          message: response.message,
          next: "retry",
        };
      }
    }

    const billingHistory = await BillingHistory.create({
      transaction: transaction?.id,
      amount: subscriptionAmount,
      status: canUseCompanyCredit ? STATUSES.PAID : STATUSES.PROCESSING,
      company: company.id,
      currency: payload.currency,
      subscription: subscription.id,
      plan: plan.id,
      dueDate: getNextDate(billingPeriod),
      creditsUsed: canUseCompanyCredit ? subscriptionAmount : 0,
      created_at: new Date(),
      transfer: transfer?.id,
    });

    await finalizeCreditPoweredSubscription(canUseCompanyCredit, company, subscription, subscriptionAmount, plan, billingHistory);

    return response;
  },

  async subscribe({
    plan,
    company,
    method,
    budget,
    balance,
    directDebit,
    user,
    contactEmail,
    address,
    state,
    country,
    companyName,
    city,
    firstName,
    lastName,
    billingPeriod,
    BudgetService,
    BankService,
    ApprovalService,
  }) {
    const existingSubscriptionCriteria = {
      company: company.id,
    };

    const existingSubscription = await Subscription.findOne({
      where: existingSubscriptionCriteria,
      order: [["created_at", "DESC"]],
      include: [
        PaymentPlan,
        {
          model: BillingHistory,
          where: {
            status: STATUSES.PAID,
            company: company.id,
          },
          order: [["created_at", "DESC"]],
          limit: 1,
        },
      ],
    });

    const unSubscribeablePlans = [PAYMENT_PLANS.CUSTOM, PAYMENT_PLANS.START, PAYMENT_PLANS.ENTERPRISE];

    if (unSubscribeablePlans.includes(plan.name)) {
      throw new ValidationError("You can't subscribe to this plan");
    }

    const isFreeTrial = !!existingSubscription?.isFreeTrial && existingSubscription?.status === STATUSES.ACTIVE;
    const canSubscribeToPlan = ![STATUSES.CANCELLED, STATUSES.ACTIVE].includes(existingSubscription?.status);

    // Fetch the company current plan
    // if current plan is same as new one, noop.
    if (company.paymentPlan.code === plan.code && !isFreeTrial) {
      if (!canSubscribeToPlan) {
        throw new ValidationError("You are already on this plan");
      }
    }

    const isActiveSubscription = existingSubscription?.status === STATUSES.ACTIVE && !isFreeTrial;
    let isUpgradingSubscription = false;

    if (isActiveSubscription) {
      if (company.paymentPlan.amount > plan.amount) {
        throw new ValidationError("You can't downgrade your plan");
      }

      const isLegacyPlanUser = Utils.isNotOnNewPlan(company.paymentPlan);

      if (isLegacyPlanUser) {
        throw new ValidationError("You can't upgrade from a legacy plan");
      }

      const formerBillingPeriod = existingSubscription.billingPeriod;

      if (formerBillingPeriod === BILLING_PERIODS.ANNUALLY) {
        if (billingPeriod === BILLING_PERIODS.MONTHLY) {
          throw new ValidationError("You can't downgrade from an annual plan to a monthly plan");
        }
      }
      isUpgradingSubscription = true;
    }

    const subscriptionAmount = await calculateSubscriptionAmount({
      plan,
      billingPeriod,
      company: company.id,
      existingSubscription: isUpgradingSubscription ? existingSubscription : null,
      existingPlan: isUpgradingSubscription ? existingSubscription?.PaymentPlan : null,
    });

    const narration = `${company.name} - Payment of ${plan.currency}${(
      parseInt(subscriptionAmount, 10) / 100
    ).toLocaleString()} for subscription plan (${plan.name})`;

    const payload = {
      amount: subscriptionAmount,
      description: narration,
      narration,
      payer: user.id,
      company: company.id,
      status: STATUSES.PENDING,
    };

    // use company credits if available
    const canUseCompanyCredit = company?.credits >= subscriptionAmount;

    if (canUseCompanyCredit) {
      // eslint-disable-next-line no-param-reassign
      method = Utils.getBillingMethod(METHODS.CREDITS);
      payload.currency = "NGN";
    }

    if (!["budget", "directdebit", "balance", "credits"].includes(method)) {
      throw new ValidationError("Invalid payment method");
    }

    let createdDirectDebit;
    let response = { error: false };
    let account = null;

    if (method === "budget") {
      account = await prepareToChargeBudget({ budget, BudgetService, company, subscriptionAmount, payload, account });
    }

    if (method === "balance") {
      account = await prepareToChargeBalance({ balance, subscriptionAmount, payload, account });
    }

    if (method === "directdebit") {
      ({ createdDirectDebit, account } = await prepareToInitDirectDebit({
        directDebit,
        BankService,
        subscriptionAmount,
        company,
        createdDirectDebit,
        narration,
        payload,
        account,
      }));
    }

    let transaction = null;
    let transfer;

    if (!canUseCompanyCredit) {
      const { isDirectDebit = false, foundAccount = null } = await prepareBujetiAccount(payload);

      if (!isDirectDebit) {
        payload.recipient = foundAccount.owner;
        payload.recipientType = foundAccount.ownerType;
      }

      if (payload.useBookTransfer) {
        transfer = await this.createTransfer(payload);
      } else {
        const existingPlan = existingSubscription?.plan && (await PaymentPlan.findOne({ where: { id: existingSubscription.plan } }));
        const { cbnFee, bujetiFee } = this.computeFees({
          amount: payload.amount,
          companyCode: company.code,
          currency: payload.currency,
          plan: Utils.parseJSON(existingPlan?.configuration || plan?.configuration),
          isDirectDebit,
        });
        payload.cbnFee = cbnFee;
        payload.bujetiFee = bujetiFee;
        transaction = await this.createTransaction(payload);
      }

      response = await chargeForSubscription({ isDirectDebit, payload, BankService, company, transaction, response, ApprovalService, transfer });

      if (response.error) {
        return {
          error: true,
          message: response.message,
          next: "retry",
        };
      }
    }

    let subscription = null;

    if (isUpgradingSubscription) {
      subscription = existingSubscription;

      if (canUseCompanyCredit) {
        subscription.plan = plan.id;
        subscription.amount = subscriptionAmount;
        await subscription.save();
      }
    } else {
      const subscriptionPayload = {
        amount: subscriptionAmount,
        currency: plan.currency,
        company: company.id,
        method: METHODS[method?.toUpperCase()],
        method_id: account?.id,
        plan: plan.id,
        contactEmail: contactEmail || existingSubscription?.contactEmail,
        billingPeriod: billingPeriod || existingSubscription?.billingPeriod,
        address: address || existingSubscription?.address,
        state: state || existingSubscription?.state,
        country: country || existingSubscription?.country,
        companyName: companyName || existingSubscription?.companyName,
        city: city || existingSubscription?.city,
        firstName: firstName || existingSubscription?.firstName,
        lastName: lastName || existingSubscription?.lastName,
        status: canUseCompanyCredit ? STATUSES.ACTIVE : STATUSES.PENDING,
      };

      subscription = await Subscription.create(subscriptionPayload);
    }

    const billingHistory = await BillingHistory.create({
      transaction: transaction?.id,
      amount: subscriptionAmount,
      status: canUseCompanyCredit ? STATUSES.PAID : STATUSES.PROCESSING,
      company: company.id,
      currency: payload.currency,
      subscription: subscription.id,
      plan: plan.id,
      dueDate: getNextDate(billingPeriod),
      creditsUsed: canUseCompanyCredit ? subscriptionAmount : 0,
      created_at: new Date(),
      transfer: transfer?.id,
    });

    await finalizeCreditPoweredSubscription(canUseCompanyCredit, company, subscription, subscriptionAmount, plan, billingHistory);

    return {
      error: false,
      message: "Subscription successful",
      subscription: subscription.code,
      next: null,
    };
  },

  /**
   * List available active payment plans
   * @param {number} company ID of the company in case it has a custom plan
   * @returns {Array} plans
   */
  async listPaymentPlans(company = null, plan = null) {
    const criteria = {
      status: STATUSES.ACTIVE,
      name: {
        [Op.notIn]: [PAYMENT_PLANS.START, PAYMENT_PLANS.GROWTH, PAYMENT_PLANS.SCALE, PAYMENT_PLANS.YC_DEAL], // plans not to list
      },
    };
    if (company) {
      criteria.company = {
        [Op.or]: {
          [Op.eq]: company,
          [Op.is]: null,
        },
      };
    }

    let plans = await PaymentPlan.findAll({
      where: criteria,
      include: [
        {
          model: Subscription,
          where: {
            company,
            status: STATUSES.ACTIVE,
          },
          required: false,
        },
      ],
    });

    // Post-filter: remove CUSTOM if no active subscription
    if (company) {
      const hasActiveCustomSubscription = plans.some((paymentPlan) => {
        return paymentPlan.name === PAYMENT_PLANS.CUSTOM && paymentPlan.Subscriptions?.some((sub) => sub.status === STATUSES.ACTIVE);
      });

      if (!hasActiveCustomSubscription) {
        plans = plans.filter((paymentPlan) => paymentPlan.name !== PAYMENT_PLANS.CUSTOM);
      } else {
        // If company has an active custom plan subscription, exclude ENTERPRISE;
        plans = plans.filter((paymentPlan) => paymentPlan.name !== PAYMENT_PLANS.ENTERPRISE);
      }
    } else {
      // No company provided — always exclude CUSTOM
      plans = plans.filter((paymentPlan) => paymentPlan.name !== PAYMENT_PLANS.CUSTOM);
    }

    return plans;
  },

  /**
   * View a payment plan
   * @param {number} code the plan's code
   * @param {number} code the plan's code
   * @returns {Object} plan
   */
  async getPaymentPlan(code, company = null) {
    const criteria = {
      code,
      status: STATUSES.ACTIVE,
      company: {
        [Op.or]: {
          [Op.eq]: company,
          [Op.is]: null,
        },
      },
    };
    return PaymentPlan.findOne({
      where: criteria,
    });
  },

  async cancelSubscription(body, company) {
    const foundSubscription = await Subscription.findOne({
      where: {
        code: body.code,
        company,
      },
      attributes: ["id", "status"],
    });

    if (!foundSubscription || foundSubscription.status !== STATUSES.ACTIVE) {
      throw new ValidationError("No active subscription found");
    }

    foundSubscription.status = STATUSES.CANCELLED;
    foundSubscription.reason = body.reason;
    return foundSubscription.save();
  },

  async getSubscriptionDetails(company, { BudgetService }) {
    // fetch active subscription
    const foundSubscription = await Subscription.findOne({
      where: {
        company,
        status: [STATUSES.ACTIVE, STATUSES.CANCELLED],
      },
      include: [
        PaymentPlan,
        {
          model: BillingHistory,
          required: true,
          where: {
            status: STATUSES.PAID,
            company,
          },
          order: [["id", "DESC"]],
          limit: 1,
        },
      ],
      order: [["id", "DESC"]],
    });

    if (!foundSubscription) return null;

    const { method } = foundSubscription;
    const methodId = foundSubscription.method_id;

    const source = await getSource(method, BudgetService, methodId, company);
    const [billingHistory] = foundSubscription?.BillingHistories || [];
    const paymentPlan = foundSubscription?.PaymentPlan;

    const configuration = Utils.parseJSON(paymentPlan.configuration);
    const maxTransactionLimit = configuration?.payables?.maxTransactionLimit?.[paymentPlan.currency || "NGN"];

    return {
      ...foundSubscription.toJSON(),
      source,
      expiryDate: billingHistory?.dueDate || null,
      paidOn: billingHistory?.created_at,
      isFreeTrial: Boolean(foundSubscription?.isFreeTrial),
      isNonPayingCompany: Boolean(maxTransactionLimit) && (!foundSubscription || foundSubscription?.isFreeTrial),
      additionalSeats: foundSubscription?.additionalSeats || 0,
      isActiveSubscription: Boolean(foundSubscription),
    };
  },

  async updateSubscription(body, company, { BudgetService }) {
    const foundSubscription = await Subscription.findOne({
      where: {
        code: body.code,
        company,
      },
      attributes: ["id", "contactEmail", "method", "method_id"],
    });

    if (!foundSubscription) throw new NotFoundError("Subscription");

    const {
      directDebit = {},
      budget,
      balance,
      method,
      contactEmail,
      billingPeriod,
      address,
      state,
      country,
      companyName,
      city,
      firstName,
      lastName,
    } = body;

    const payload = {
      ...(contactEmail && { contactEmail }),
      ...(billingPeriod && { billingPeriod }),
      ...(address && { address }),
      ...(state && { state }),
      ...(country && { country }),
      ...(companyName && { companyName }),
      ...(city && { city }),
      ...(firstName && { firstName }),
      ...(lastName && { lastName }),
    };

    if (method) {
      let account;
      account = await deduceAccount({ method, budget, BudgetService, company, account, balance, directDebit });

      if (account) {
        payload.method_id = account.id;
        payload.method = METHODS[method.toUpperCase()];
      }
    }

    await foundSubscription.update(payload);

    return foundSubscription;
  },

  async finalizeBilling(payload) {
    const { transfer, transaction, company, status = STATUSES.PAID } = payload;

    const billingHistory = await BillingHistory.findOne({
      where: {
        ...(transfer && { transfer }),
        ...(transaction && { transaction }),
        company,
      },
      include: [{ model: Subscription, required: true }],
    });

    if (!billingHistory) throw new NotFoundError("Billing history");

    if (billingHistory.status === STATUSES.PAID) return;

    const subscription = billingHistory.Subscription;

    billingHistory.status = status;
    await billingHistory.save();

    if (status === STATUSES.PAID) {
      // activate subscription
      await Subscription.update(
        { status: STATUSES.ACTIVE, plan: billingHistory.plan, amount: billingHistory.amount },
        { where: { id: subscription.id } }
      );

      await Subscription.update(
        { status: STATUSES.INACTIVE },
        {
          where: {
            company,
            id: {
              [Op.ne]: subscription.id,
            },
          },
        }
      );

      // Put company on the plan
      await CompanyRepo.updateACompany({
        queryParams: {
          id: company,
        },
        updateFields: {
          paymentPlan: billingHistory.plan,
        },
      });

      // send an invoice email
      const foundCompany = await CompanyRepo.getOneCompany({ queryParams: { id: company }, selectOptions: ["contact_email", "name"] });

      const emails = [foundCompany.contact_email, subscription.contactEmail];

      const paymentPlan = await PaymentPlan.findOne({
        where: {
          id: billingHistory.plan,
        },
        attributes: ["name", "configuration"],
      });

      sendInvoiceNotification(foundCompany, paymentPlan, billingHistory, emails, company, subscription);
    }
  },

  async createTransaction(payload) {
    return TransactionRepo.createTransaction({
      data: {
        amount: payload.amount,
        description: payload.description,
        payer: payload.payer,
        budget: payload.budget,
        company: payload.company,
        currency: payload.currency,
        bank_account: payload.bankAccountId,
        status: STATUSES.PENDING,
        narration: payload.narration,
        recipient: payload.recipient,
        recipient_type: payload.recipientType,
        category: payload.category,
        processor_fee: payload.cbnFee,
        bujeti_fee: payload.bujetiFee,
        team: payload.team,
        balance: payload.balance,
        type: TRANSACTION_TYPES.SUBSCRIPTION,
        directDebitId: payload.directDebitId,
      },
    });
  },

  async createTransfer(payload) {
    return TransferRepo.createTransfer({
      data: {
        amount: -1 * payload.amount,
        currency: payload.currency,
        balance: payload.balance,
        description: payload.description,
        company: payload.company,
        status: STATUSES.PENDING,
        reference: Utils.generateRandomString(14).toLowerCase(),
        processor_fee: 0,
        bujeti_fee: 0,
        narration: payload.narration,
        budget: payload.budget,
      },
    });
  },

  async freeTrial(payload) {
    const {
      method,
      plan,
      company,
      budget,
      balance,
      directDebit,
      contactEmail,
      billingPeriod = "monthly",
      dueDate,
      BudgetService,
      address,
      state,
      country,
      companyName,
      city,
      firstName,
      lastName,
      isFreeTrial,
      trialDays,
    } = payload;

    let account = {};

    if (method) {
      account = await deduceAccount({ method, budget, BudgetService, company: company.id, account, balance, directDebit });
    }

    const existingSubscriptionCriteria = {
      company: company.id,
      plan: plan.id,
      status: STATUSES.ACTIVE,
    };

    const existingSubscription = await Subscription.findOne({
      where: existingSubscriptionCriteria,
      include: [PaymentPlan],
    });

    //  cancel old subscription if exists and create new one
    if (existingSubscription) {
      if (isFreeTrial) {
        return {
          error: true,
          message: `Company already has an active subscription on plan (${plan.name})`,
          subscription: existingSubscription.code,
          next: null,
        };
      }

      existingSubscription.status = STATUSES.PAUSE;
      await existingSubscription.save();
    }

    const subscriptionAmount = await calculateSubscriptionAmount({
      plan,
      billingPeriod,
      company: company.id,
      existingPlan: existingSubscription?.PaymentPlan,
    });

    const subscriptionPayload = {
      amount: subscriptionAmount,
      currency: plan.currency,
      company: company.id,
      method: METHODS[method?.toUpperCase()],
      method_id: account?.id,
      plan: plan.id,
      contactEmail: contactEmail || existingSubscription?.contactEmail,
      billingPeriod: billingPeriod || existingSubscription?.billingPeriod,
      address: address || existingSubscription?.address,
      state: state || existingSubscription?.state,
      country: country || existingSubscription?.country,
      companyName: companyName || existingSubscription?.companyName,
      city: city || existingSubscription?.city,
      firstName: firstName || existingSubscription?.firstName,
      lastName: lastName || existingSubscription?.lastName,
      status: STATUSES.ACTIVE,
      isFreeTrial,
    };

    const subscription = await Subscription.create(subscriptionPayload);

    await BillingHistory.create({
      amount: 0,
      status: STATUSES.PAID,
      company: company.id,
      currency: payload.currency || "NGN",
      subscription: subscription.id,
      plan: plan.id,
      dueDate: dueDate || getNextDate(trialDays || billingPeriod),
    });

    // Put company on the plan
    await CompanyRepo.updateACompany({
      queryParams: {
        id: company.id,
      },
      updateFields: {
        paymentPlan: plan.id,
      },
    });

    return {
      error: false,
      message: `${isFreeTrial ? "Free trial activated" : "Activated"} on plan (${plan.name})`,
      subscription: subscription.code,
      next: null,
    };
  },
};

module.exports = Service;

async function finalizeCreditPoweredSubscription(canUseCompanyCredit, company, subscription, subscriptionAmount, plan, billingHistory) {
  if (canUseCompanyCredit) {
    const emails = [company.contact_email, subscription.contactEmail];

    const creditsLeft = Utils.money(company.credits).minus(subscriptionAmount).toNumber();
    // put company on the subscribed plan
    await CompanyRepo.updateACompany({
      queryParams: {
        id: company.id,
      },
      updateFields: {
        paymentPlan: plan.id,
        credits: creditsLeft,
      },
    });

    sendInvoiceNotification(company, plan, billingHistory, emails, company.id, subscription);
  }
}

async function sendInvoiceNotification(foundCompany, paymentPlan, billingHistory, emails, company, subscription) {
  const userCount = await UserRepo.count({
    company: foundCompany.id,
    status: {
      [Op.ne]: STATUSES.DELETED,
    },
  });

  const serviceConfig = Utils.parseJSON(paymentPlan.configuration || {});
  const freeUsers = serviceConfig?.userManagement?.freeUsers || 0;

  const extraSeats = userCount - freeUsers;

  const emailTokens = {
    companyName: foundCompany.name,
    planType: `${Utils.toTitle(paymentPlan.name)} ${extraSeats > 0 ? ` ( ${extraSeats} extra seat(s) )` : ""}`,
    startDate: Utils.formatHumanReadableDate(billingHistory?.created_at),
    dueDate: Utils.formatHumanReadableDate(billingHistory?.dueDate),
    currency: billingHistory?.currency,
    amount: Utils.formatAmount(billingHistory?.amount).toLocaleString(),
    year: Utils.getYear(billingHistory?.created_at),
  };

  // send invoice email to admins
  notifyAdmins({ emails, paymentPlan, company, subscription, emailTokens });
}

async function chargeForSubscription({ isDirectDebit, payload, BankService, company, transaction, response, ApprovalService, transfer }) {
  if (isDirectDebit) {
    const directDebit = await DirectDebit.findOne({
      where: {
        id: payload.directDebitId,
      },
    });

    if (!directDebit) throw new ValidationError("Direct debit not initiated");

    await BankService.directDebit(company, {
      bankCode: directDebit.beneficiaryBankCode,
      accountNumber: directDebit.beneficiaryAccountNumber,
      narration: directDebit.narration,
      amount: directDebit.amount,
      createdDirectDebit: directDebit,
    });

    await Transaction.update(
      {
        status: STATUSES.PROCESSING,
      },
      { where: { id: transaction.id } }
    );
  } else if (payload.useBookTransfer) {
    // book transfer
    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[company] || payment.defaultProvider;

    const bookTransferPayload = {
      recipientId: payload.externalBankAccountId,
      recipientType: "DepositAccount",
      senderId: payload.senderId,
      senderType: payload.senderType,
      currency: payload.currency,
      amount: Math.abs(payload.amount),
      reason: payload.description,
      reference: transfer.reference,
      company,
      paidBy: payload.payer,
      transfer: transfer.code,
      providerToUse,
    };

    const SQSPayload = {
      data: bookTransferPayload,
      id: Utils.generateRandomString(17),
      path: `/transfers/bookTransfer/${transfer.code}/process`,
      key: process.env.INTRA_SERVICE_TOKEN,
    };

    QueueService.addDelayedJob({}, SQSPayload, `BookTransferReference:${bookTransferPayload.reference}`, 5);
  } else {
    await ApprovalService.addPaymentToQueue(transaction.code);
    await Transaction.update(
      {
        status: STATUSES.PROCESSING,
      },
      { where: { id: transaction.id } }
    );
  }

  response = { error: false, message: "payment processing" };
  return response;
}

async function prepareBujetiAccount(payload) {
  const accounts = SettingsService.get("subscriptionCollectionAccounts");

  const specificAccount = accounts[payload.currency];

  console.log("specificAccount", specificAccount.number);

  if (!specificAccount) throw new ValidationError("Unable to complete billing");

  const foundAccount = await BankAccountRepo.getOneBankAccount({
    queryParams: {
      number: specificAccount.number,
      ownerType: "company",
    },
    selectOptions: ["id", "number", "bankCode", "bankName", "owner", "ownerType", "currency", "externalBankAccountId"],
  });

  if (!foundAccount) throw new ValidationError("Unable to complete billing");

  payload.bankAccountId = foundAccount.id;
  payload.externalBankAccountId = foundAccount.externalBankAccountId;

  const isDirectDebit = !!payload.directDebitId;
  return { isDirectDebit, foundAccount };
}

async function prepareToInitDirectDebit({
  directDebit,
  BankService,
  subscriptionAmount,
  company,
  createdDirectDebit,
  narration,
  payload,
  account,
  BankAccount,
}) {
  if (!directDebit.bankAccount) throw new ValidationError("Please specify direct debit source");
  const foundBankAccount =
    BankAccount ||
    (await BankAccountRepo.getOneBankAccount({
      queryParams: {
        code: directDebit.bankAccount,
      },
      selectOptions: ["currency", "number", "bankCode"],
    }));

  if (!foundBankAccount) throw new NotFoundError("Bank account");

  // verify active mandate
  const existingMandate = await Mandate.findOne({
    where: {
      bankAccount: foundBankAccount.id,
      status: "granted",
    },
  });
  if (!existingMandate) throw new ValidationError("No active mandate found");

  // confirm funds availability
  await BankService.confirmFundsAvailability({ amount: subscriptionAmount }, existingMandate, company);

  payload.currency = foundBankAccount.currency;
  const { foundAccount } = await prepareBujetiAccount(payload);

  const { number: accountNumber, bankCode, currency } = foundAccount;

  createdDirectDebit = await DirectDebit.create({
    mandate: existingMandate.id,
    company: company.id,
    amount: subscriptionAmount,
    beneficiaryAccountNumber: accountNumber,
    beneficiaryBankCode: bankCode,
    narration,
    status: STATUSES.PENDING,
    reference: Utils.generateRandomString(14),
  });
  payload.directDebitId = createdDirectDebit.id;
  payload.currency = currency;
  account = foundBankAccount;
  return { createdDirectDebit, account };
}

async function prepareToChargeBalance({ balance, subscriptionAmount, payload, account, Balance }) {
  if (!balance) throw new ValidationError("Please specify balance");
  const foundBalance =
    Balance ||
    (await BalanceRepo.getBalance({
      filter: {
        code: balance,
      },
      includeAccount: true,
    }));
  if (!foundBalance) throw new NotFoundError("Balance");
  const availableBalance = await BalanceRepo.getAvailableBalanceById(foundBalance.id);
  if (availableBalance < subscriptionAmount) {
    throw new ValidationError(`Insufficient balance. Please top up to make the payment`);
  }
  const senderId = foundBalance.BankAccount?.externalBankAccountId || foundBalance.BankAccount?.externalIdentifier;
  payload.balance = foundBalance.id;
  payload.currency = foundBalance.currency;
  account = foundBalance;
  payload.useBookTransfer = foundBalance.BankAccount?.issuer === CARD_ISSUER.Anchor;
  payload.senderId = senderId;
  payload.senderType = Utils.getAccountType(senderId);
  return account;
}

async function prepareToChargeBudget({ budget, BudgetService, company, subscriptionAmount, payload, account, Budget }) {
  if (!budget) throw new ValidationError("Please specify budget");
  const foundBudget = Budget || (await BudgetService.getBudget({ code: budget, company: company.id }));
  if (!foundBudget) throw new NotFoundError("Budget");
  const budgetBalance = await BudgetService.getBudgetTransferableBalance(foundBudget);
  if ((foundBudget.isFunded && budgetBalance < subscriptionAmount) || foundBudget.spent + subscriptionAmount > budget.amount)
    throw new ValidationError("Insufficient budget, please top up to make the payment");
  const budgetAccount = await BudgetAccountRepo.getBudgetAccount({
    filter: {
      budget: foundBudget.id,
    },
  });
  payload.budget = foundBudget.id;
  payload.currency = foundBudget.currency;
  payload.useBookTransfer = budgetAccount?.provider === CARD_ISSUER.Anchor;
  payload.senderId = budgetAccount?.externalIdentifier;
  payload.senderType = budgetAccount?.type;
  account = foundBudget;
  return account;
}

async function calculateSubscriptionAmount({ plan, billingPeriod, company, existingSubscription, existingPlan }) {
  const serviceConfig = Utils.parseJSON(plan.configuration || {});
  const numberOfMonths = {
    monthly: 1,
    annually: 12,
  };

  const userCount = await UserRepo.count({
    company,
    status: {
      [Op.ne]: STATUSES.DELETED,
    },
  });

  let isLegacyPlanUser = false;

  if (existingPlan) {
    const isStartPlanUser = existingPlan.name === PAYMENT_PLANS.START;
    isLegacyPlanUser = Utils.isNotOnNewPlan(existingPlan) && !isStartPlanUser;
  }

  const planCurrency = plan.currency || "NGN";
  const freeUsers = serviceConfig.userManagement?.freeUsers || 0;
  const additionalUserCost = isLegacyPlanUser ? 0 : serviceConfig.userManagement?.additionalUsers?.[planCurrency] || 0;
  const extraSeats = userCount - freeUsers;

  const totalExtraUsersCost = userCount > freeUsers ? extraSeats * additionalUserCost : 0;

  let computedPlanAmount = Utils.money(plan.amount).plus(totalExtraUsersCost).times(numberOfMonths[billingPeriod]).toNumber();

  if (billingPeriod === BILLING_PERIODS.ANNUALLY) {
    const settings = SettingsService.get("annualSubscriptionDiscount");

    if (settings) {
      const foundDiscount = settings[plan.company] || settings.default || 0;
      const discount = Utils.money(foundDiscount / 100)
        .times(computedPlanAmount)
        .toNumber();
      computedPlanAmount = Utils.money(computedPlanAmount).minus(discount).toNumber();
    }
  }

  if (existingSubscription) {
    const [billingHistory] = existingSubscription?.BillingHistories || [];
    const dueDate = billingHistory?.dueDate;

    const amountWithoutVAT = await BillingHistory.sum("amount", {
      where: {
        company,
        status: STATUSES.PAID,
        plan: existingSubscription.plan,
        subscription: existingSubscription.id,
      },
    });
    const totalCostsIncurred = Utils.money(amountWithoutVAT).div(1.075).toNumber();

    const basicProratedCost = Utils.calculateProratedCost(totalCostsIncurred, dueDate);

    if (basicProratedCost <= computedPlanAmount) {
      computedPlanAmount = Utils.money(computedPlanAmount)
        .minus(basicProratedCost || 0)
        .toNumber();
    } else {
      // give company difference as credits
      const foundCompany = await CompanyRepo.getOneCompany({ queryParams: { id: company }, selectOptions: ["credits"] });
      const differenceOwed = Utils.money(basicProratedCost).minus(computedPlanAmount).toNumber();
      const creditsLeft = Utils.money(foundCompany.credits).plus(differenceOwed).toNumber();

      await CompanyRepo.updateACompany({
        queryParams: {
          id: foundCompany.id,
        },
        updateFields: {
          credits: creditsLeft,
        },
      });
      computedPlanAmount = 0;
    }
  }

  const vat = Utils.money(7.5 / 100)
    .times(computedPlanAmount)
    .toNumber();
  const subscriptionAmount = Utils.money(computedPlanAmount).plus(vat).toNumber();

  return subscriptionAmount;
}

function notifyAdmins({ emails, paymentPlan, company, subscription, emailTokens }) {
  const payload = emailTokens;
  Promise.all(
    emails.map((email) => {
      const sourceConfig = {
        subject: `Your bujeti ${paymentPlan.name} plan subscription details`,
        from_name: "Bujeti",
      };

      const notificationPayload = {
        company,
        type: `alert`,
        badge: `alert`,
        title: `Your bujeti ${paymentPlan.name} plan subscription details`,
        message: `Your bujeti ${paymentPlan.name} plan subscription details`,
        body: {
          code: subscription.code,
          entity: "Subscription",
        },
        table: {
          code: subscription.code,
          entity: "Subscription",
        },
        reference_code: subscription.code,
        event: "billingSuccessful",
      };
      NotificationService.saveNotification(notificationPayload);
      payload.firstName = emailTokens.companyName;
      NotificationService.notifyUser({ email }, "billing-payment-invoice", payload, sourceConfig);

      return payload;
    })
  );
  NotificationService.notifyUser(
    { email: "<EMAIL>" },
    "billing-subscription",
    {
      companyName: emailTokens.companyName,
      currency: subscription.currency,
      amount: emailTokens.amount,
      planType: emailTokens.planType,
      period: subscription.billingPeriod,
      date: emailTokens.startDate,
    },
    {
      subject: `New ${subscription.billingPeriod} ${emailTokens.planType} plan subscription from ${emailTokens.companyName}`,
      from_name: "Bujeti",
    }
  );
}

async function getSource(method, BudgetService, methodId, company) {
  let source;
  switch (method) {
    case METHODS.BUDGET:
      {
        const foundBudget = await BudgetService.getBudget({ id: methodId, company });
        if (!foundBudget) return null;
        source = { name: foundBudget.name, type: METHODS_INTERPRETER[method - 1], code: foundBudget.code };
      }
      break;

    case METHODS.BALANCE:
      {
        const foundBalance = await BalanceRepo.getBalance({
          filter: {
            id: methodId,
            company,
          },
        });
        if (!foundBalance) return null;
        source = { name: foundBalance.name, type: METHODS_INTERPRETER[method - 1], code: foundBalance.code };
      }
      break;

    case METHODS.DIRECTDEBIT:
      {
        const foundBankAccount = await BankAccountRepo.getOneBankAccount({
          queryParams: {
            id: methodId,
            owner: company,
            ownerType: "company",
          },
        });

        if (!foundBankAccount) return null;
        source = {
          name: `${foundBankAccount.bankName}(${foundBankAccount.accountName})`,
          type: METHODS_INTERPRETER[method - 1],
          code: foundBankAccount.code,
        };
      }
      break;

    default: {
      source = null;
    }
  }
  return source;
}

async function doNameCheck(rest, foundCompany) {
  const existingPlan = await PaymentPlan.findOne({
    where: {
      name: rest.name.toLowerCase(),
      status: STATUSES.ACTIVE,
      company: foundCompany.id,
    },
  });

  if (existingPlan) throw new ValidationError("An active payment plan already exists");
}

async function deduceAccount({ method, budget, BudgetService, company, account, balance, directDebit }) {
  switch (METHODS[method.toUpperCase()]) {
    case METHODS.BUDGET:
      {
        if (!budget) throw new ValidationError("Please specify budget");
        const foundBudget = await BudgetService.getBudget({ code: budget, company });
        if (!foundBudget) throw new NotFoundError("Budget");
        account = foundBudget;
      }
      break;

    case METHODS.BALANCE:
      {
        if (!balance) throw new ValidationError("Please specify balance");
        const foundBalance = await BalanceRepo.getBalance({
          filter: {
            code: balance,
            company,
          },
        });
        if (!foundBalance) throw new NotFoundError("Balance");
        account = foundBalance;
      }
      break;

    case METHODS.DIRECTDEBIT:
      {
        if (!directDebit.bankAccount) throw new ValidationError("Please specify direct debit source");
        const foundBankAccount = await BankAccountRepo.getOneBankAccount({
          queryParams: {
            code: directDebit.bankAccount,
          },
        });

        if (!foundBankAccount) throw new NotFoundError("Bank account");

        // verify active mandate
        const existingMandate = await Mandate.findOne({
          where: {
            bankAccount: foundBankAccount.id,
            status: "granted",
          },
        });
        if (!existingMandate) throw new ValidationError("No active mandate found");
        account = foundBankAccount;
      }
      break;

    default: {
      account = null;
    }
  }
  return account;
}
