const axios = require("axios");
const ThirdPartyLogService = require("./thirdPartyLog");
const { CARD_ISSUER } = require("../models/cardissuer");
const Utils = require("../utils/utils");

const httpClient = {};
axios.defaults.timeout = 300000;

const requestHandler = async (request) => {
  try {
    const { data, status = 200, config } = await request;

    return {
      error: false,
      message: data && data.message,
      body: data,
      data,
      status,
      config: {
        url: `${config?.baseURL}${config.url}`,
        method: config.method,
        data: config.data,
      },
    };
  } catch (error) {
    const { response, message, status, config } = error;
    const { status: statusCode = 400, data: errorData = {} } = response || {};

    return {
      message: errorData?.message || message,
      status: status || statusCode,
      data: errorData,
      body: errorData,
      error: true,
      config: {
        url: `${config.baseURL}${config.url}`,
        method: config.method,
        data: config.data,
      },
    };
  }
};

const Service = {
  httpClient() {
    const headers = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${process.env.BLNK_API_KEY}`,
    };

    if (!httpClient.client) {
      httpClient.client = axios.create({
        baseURL: process.env.BLNK_API_URL,
        headers,
      });
    }

    return httpClient.client;
  },

  ledgers: {
    async createLedger(payload, company = -1) {
      const request = Service.httpClient().post("/ledgers", payload);

      const { data, error, status, config, message } = await requestHandler(request);
      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Failed to create" : "Created"} ledger`,
        company,
        event: "blnk.ledger.created",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.blnk,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      return { data, status, message, error };
    },

    async viewLedgerDetails(ledgerId, company = -1) {
      const request = Service.httpClient().get(`/ledgers/${ledgerId}`);

      const { data, error, status, config, message } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Failed to get" : "Got"} ledger details`,
        company,
        event: "blnk.ledger.viewed",
        payload: JSON.stringify({ ledgerId }),
        provider: CARD_ISSUER.blnk,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      return { data, status, message, error };
    },
  },

  identities: {
    async createIdentity(payload, company) {
      const request = Service.httpClient().post("/identities", payload);

      const { data, error, status, config, message } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Failed to create" : "Created"} identity`,
        company,
        event: "blnk.identity.created",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.blnk,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      return { data, status, message, error };
    },

    async viewIdentityDetails(identityId, company = -1) {
      const request = Service.httpClient().get(`/identities/${identityId}`);

      const { data, error, status, config, message } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Failed to get" : "Got"} identity details`,
        company,
        event: "blnk.identity.viewed",
        payload: JSON.stringify({ identityId }),
        provider: CARD_ISSUER.blnk,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      return { data, status, message, error };
    },
  },

  balances: {
    async createBalance(payload, company = -1) {
      const request = Service.httpClient().post("/balances", payload);

      const { data, error, status, config, message } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Failed to create" : "Created"} balance`,
        company,
        event: "blnk.balance.created",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.blnk,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      return { data, status, message, error };
    },

    async viewBalanceDetails(balanceId, company = -1) {
      const request = Service.httpClient().get(`/balances/${balanceId}`);

      const { data, error, status, config, message } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Failed to get" : "Got"} balance details`,
        company,
        event: "blnk.balance.viewed",
        payload: JSON.stringify({ balanceId }),
        provider: CARD_ISSUER.blnk,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      return { data, status, message, error };
    },

    async getBalanceAtPointInTime({ balanceId, date, company = -1 }) {
      const request = Service.httpClient().get(`/balances/${balanceId}/at?timestamp=${date}`);

      const { data, error, status, config, message } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Failed to get" : "Got"} balance at point in time`,
        company,
        event: "blnk.balance.at.time.viewed",
        payload: JSON.stringify({ balanceId }),
        provider: CARD_ISSUER.blnk,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      return { data, status, message, error };
    },
  },

  transactions: {
    async recordTransaction(payload, company = -1) {
      const request = Service.httpClient().post("/transactions", payload);

      const { data, error, status, config, message } = await requestHandler(request);
      let { attempt = 5 } = payload;

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Failed to record" : "Recorded"} transaction`,
        company,
        event: "blnk.transaction.recorded",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.blnk,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      if (error && attempt > 0) {
        await Utils.sleep(1000); // wait for 1 second before retrying
        attempt -= 1;
        return Service.transactions.recordTransaction({ ...payload, attempt }, company);
      }

      return { data, status, message, error };
    },

    async updateInflightTransaction({ transactionId, payload, company = -1 }) {
      const request = Service.httpClient().put(`/transactions/inflight/${transactionId}`, payload);

      const { data, error, status, config, message } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Failed to update" : "Updated"} inflight transaction`,
        company,
        event: "blnk.transaction.inflight.updated",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.blnk,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      return { data, status, message, error };
    },

    async viewTransactionDetails({ transactionId, company = -1 }) {
      const request = Service.httpClient().get(`/transactions/${transactionId}`);

      const { data, error, status, config, message } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Failed to get" : "Got"} transaction details`,
        company,
        event: "blnk.transaction.viewed",
        payload: JSON.stringify({ transactionId }),
        provider: CARD_ISSUER.blnk,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      return { data, status, message, error };
    },

    async refundTransaction({ transactionId, payload, company = -1 }) {
      const request = Service.httpClient().post(`/refund-transaction/${transactionId}`, payload);

      const { data, error, status, config, message } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Failed to refund" : "Refunded"} transaction`,
        company,
        event: "blnk.transaction.refunded",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.blnk,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      return { data, status, message, error };
    },
  },
  // TODO: cook statements
  statements: {},
};
module.exports = Service;
