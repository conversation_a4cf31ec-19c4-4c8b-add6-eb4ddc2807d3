const axios = require("axios");
const format = require("date-fns/format");
const { CARD_ISSUER } = require("../models/cardissuer");
const { STATUSES } = require("../models/status");
const {
  UserRepo,
  CardHolderRepo,
  CardRepo,
  CompanyRepo,
  VirtualCardRepo,
  PhoneNumberRepo,
  BalanceLedgerRepo,
  BalanceRepo,
} = require("../repositories/index.repo");
const { encrypt, getEncryptionkeys, decrypt, getDashboardURL, cardType, getCardType } = require("../utils");
const ThirdPartyLogService = require("./thirdPartyLog");
const NotificationService = require("./notification");
const RedisService = require("./redis");
const TransactionService = require("./transaction");
const { NotFoundError } = require("../utils/error.utils");
const { BudgetLedger } = require("../models");

const httpClient = {};
class BridgeCard {
  static httpClient = (getCard = false) => {
    const headers = {
      token: `Bearer ${process.env.BRIDGE_CARD_AUTH_TOKEN}`,
      "Content-Type": "application/json",
    };
    if (!httpClient.client && !getCard)
      httpClient.client = axios.create({
        headers,
        baseURL: `${process.env.BRIDGE_CARD_URL}/v1`,
      });
    else {
      httpClient.client = axios.create({
        headers,
        baseURL: `${process.env.BRIDGE_CARD_VAULT_URL}/v1`,
      });
    }
    return httpClient.client;
  };

  static async apicall({ url, method, payload = {}, vault_url = false }) {
    try {
      let call;
      if (process.env.NODE_ENV !== "production") {
        url = url.replace("issuing/", "issuing/sandbox/");
      }
      if (method === "get") {
        call = await axios[method](`${!vault_url ? process.env.BRIDGE_CARD_URL : process.env.BRIDGE_CARD_VAULT_URL}/v1${url}`, {
          headers: {
            token: `Bearer ${process.env.BRIDGE_CARD_AUTH_TOKEN}`,
            "Content-Type": "application/json",
          },
        });
      } else {
        call = await axios[method](`${!vault_url ? process.env.BRIDGE_CARD_URL : process.env.BRIDGE_CARD_VAULT_URL}/v1${url}`, payload, {
          headers: {
            token: `Bearer ${process.env.BRIDGE_CARD_AUTH_TOKEN}`,
            "Content-Type": "application/json",
          },
        });
      }
      const {
        data: { status, message, data },
      } = call;
      return { status, message, data };
    } catch (error) {
      const {
        response: {
          data: { message },
        },
      } = error;
      return { error: true, message };
    }
  }

  // Used to update cardholders information... To be Implemented
  static async updateCardHolder({ externalIdentifier, payload, company }) {}

  static async createCardHolder(payload, company, user, holderStatus = STATUSES.ACTIVE) {
    // are we going with sychronous creation or Asynchronous creations
    const request = await this.apicall({
      url: "/issuing/cardholder/register_cardholder_synchronously",
      payload,
      method: "post",
    });

    // eslint-disable-next-line prefer-const
    let { error, message, status, data } = request;
    if (error) {
      // eslint-disable-next-line no-use-before-define
      message = bridgecardErrorHandler(message);
    }
    await ThirdPartyLogService.createLog({
      company,
      event: "creating card Holder",
      payload: JSON.stringify(payload),
      message: `Creating card holder`,
      provider: CARD_ISSUER.bridgecard,
      providerType: "issuer",
      response: JSON.stringify(request),
      statusCode: 200,
      endpoint: `${process.env.BRIDGE_CARD_URL}/issuing/cardholder/register_cardholder_synchronously`,
      method: "POST",
    });
    if (!error) {
      // create on the DB
      const cardHolder = await CardHolderRepo.create({
        provider: CARD_ISSUER.bridgecard,
        user,
        company,
        status: holderStatus,
        externalIdentifier: data.cardholder_id,
      });
    }
    return {
      message,
      error,
      data,
    };
  }

  static encryptCard = (cards) => {
    const hash = getEncryptionkeys(cards.issuer);
    return {
      ...cards,
      last_4: encrypt({ text: cards.last_4, hash }),
      cvv: encrypt({ text: cards.cvv, hash }),
      exp_year: encrypt({ text: cards.exp_year, hash }),
      exp_month: encrypt({ text: cards.exp_month, hash }),
      ...(cards?.number && {
        number: encrypt({ text: cards.number, hash }),
      }),
      ...(cards?.pin && {
        pin: encrypt({ text: cards.pin, hash }),
      }),
    };
  };

  static async cardHolderWrapper(holderInfo) {
    let { Address, PhoneNumber } = holderInfo;
    if (!Address) {
      Address = await AddressRepo.getOneAddress({
        queryParams: {
          company: holderInfo.company,
        },
        selectOptions: ["street", "city", "state", "country", "postalCode"],
      });
    }
    if (!PhoneNumber) {
      const company = await CompanyRepo.getOneCompany({
        queryParams: {
          id: holderInfo.company,
        },
        selectOptions: ["phoneNumber"],
      });
      PhoneNumber = await PhoneNumberRepo.getOnePhoneNumber({
        queryParams: {
          id: company.id,
        },
        selectOptions: ["localFormat"],
      });
    }
    const { street, city, state = "Lagos", country = "Nigeria", postalCode = "100211" } = Address;
    const { localFormat } = PhoneNumber;
    if (!street || !city || !state || !country || !localFormat) {
      return {
        message: "unable to create card holder, address or phonenumber details are missing.",
        status: "failed",
        error: true,
        result: null,
      };
    }

    const result = {
      first_name: holderInfo.firstName,
      last_name: holderInfo.lastName,
      address: {
        address: street,
        city,
        state,
        country,
        postal_code: "",
        house_no: "13",
      },
      phone: localFormat.replace(/\s/g, ""),
      email_address: holderInfo.email,
      identity: {
        id_type: "NIGERIAN_NIN",
        id_no: localFormat,
        id_image: "http://google.com",
        bvn: generateBVN(),
      },
      meta_data: { any_key: "any_value" },
    };
    return {
      message: "Wrapped successfully",
      status: "success",
      error: false,
      result,
    };
  }

  static async fundVirtualCard({ id, amount, currency, reference = null, company = null }) {
    let status;
    let data;
    let message;
    const card = await VirtualCardRepo.find({ externalIdentifier: id });
    const fundPayload = {
      card_id: id,
      amount: String(amount),
      transaction_reference: reference,
      currency,
    };
    if (currency.toLowerCase() === "usd") {
      const fundCall = await this.apicall({
        url: `/issuing/cards/fund_card`,
        payload: fundPayload,
        method: "patch",
      });
      (status = fundCall.status), (data = fundCall.data), (message = fundCall.message);
      // get company
      if (fundCall?.status !== "success") {
        message = bridgecardErrorHandler(message);
      }
    }
    await ThirdPartyLogService.createLog({
      company: company || card.company,
      event: "card.funding.initiated",
      payload: JSON.stringify(fundPayload),
      message: `Funding card with externalIdentifier => ${id}`,
      provider: CARD_ISSUER.bridgecard,
      providerType: "issuer",
      response: JSON.stringify({ status, data, message }),
      statusCode: 200,
      endpoint: ``,
      method: "PATCH",
    });
    return {
      status,
      data,
      error: status !== "success",
      message,
    };
  }

  static async getVirtualCard({ id }) {
    const { status, data, message } = await this.apicall({
      url: `/issuing/cards/get_card_details?card_id=${id}`,
      vault_url: true,
      method: "get",
    });
    return {
      status,
      data,
      error: status !== "success",
      message,
    };
  }

  static async createVirtualCard(payload) {
    const { currency, amount, first_name, last_name, date_of_birth, email, phone, title, gender, card_type, reference } = payload;
    // get user by email
    const userDetails = await UserRepo.getOneUser({
      queryParams: { email },
      selectOptions: ["firstName", "lastName", "id", "company", "email", "address", "phoneNumber"],
      includeAddress: true,
      includePhonenumber: true,
    });
    // create cardHolderRegistration Payload
    // check cardHolderTable
    const cardHolder = await CardHolderRepo.find({
      condition: {
        user: userDetails.id,
        provider: CARD_ISSUER.bridgecard,
        status: STATUSES.ACTIVE,
      },
    });
    const cardHolderId = cardHolder?.externalIdentifier;

    const cardBrandMap = {
      ngn: "Mastercard",
      usd: "Visa",
    };

    const cardSupportType =
      // eslint-disable-next-line camelcase
      card_type.toLowerCase() === "physical" ? "physical" : "virtual";

    const createCardPayload = {
      cardholder_id: cardHolderId,
      card_type: cardSupportType,
      card_brand: cardBrandMap[currency.toLowerCase()],
      card_currency: currency.toUpperCase(),
      meta_data: {
        amount,
        reference,
      },
    };
    // do we create cardHolder on every create card request??
    const endpointPath = "/issuing/cards/create_card";
    const createCard = await this.apicall({
      url: endpointPath,
      payload: createCardPayload,
      method: "post",
    });
    await ThirdPartyLogService.createLog({
      company: cardHolder.company,
      event: "card.creation.initiated",
      payload: JSON.stringify(createCardPayload),
      message: createCard.message,
      provider: CARD_ISSUER.bridgecard,
      providerType: "issuer",
      response: JSON.stringify(createCard),
      statusCode: 200,
      endpoint: endpointPath,
      method: "POST",
    });
    if (!createCard.status || createCard.status !== "success" || createCard.error) {
      // eslint-disable-next-line no-use-before-define
      const message = bridgecardErrorHandler(createCard.message);
      return { status: "failed", message };
    }

    const cardData = {
      externalIdentifier: createCard.data.card_id,
      currency: createCard.data.currency,
      card_pan: null,
      masked_pan: null,
      expiration: null,
      cvv: null,
      name: null,
      card_type: createCardPayload.card_brand.toLowerCase(),
    };
    return {
      data: cardData,
      status: createCard.status,
      message: "Card created successfully",
    };
  }

  static async getExchangeRate() {
    const { status, message, data } = await this.apicall({
      url: `/issuing/cards/fx-rate`,
      method: "get",
    });

    return {
      status,
      message,
      data,
    };
  }

  static async verifyOtp({ cardholder_id, otp }) {
    const { status, message, data } = await this.apicall({
      url: `/issuing/verify_cardholder_otp`,
      method: "post",
      payload: { cardholder_id, otp },
    });
    const card = await CardHolderRepo.find({
      condition: {
        externalIdentifier: cardholder_id,
      },
    });
    await ThirdPartyLogService.createLog({
      company: card.company,
      event: "Verify Otp BridgeCard CardHolder",
      payload: JSON.stringify({ cardholder_id, otp }),
      message: `Verify otp bridgecard`,
      provider: CARD_ISSUER.bridgecard,
      providerType: "issuer",
      response: JSON.stringify({ status, data, message }),
      statusCode: 200,
      endpoint: ``,
      method: "POST",
    });
    return {
      status,
      message,
      data,
    };
  }

  static async virtualAccount({ externalIdentifier }) {
    const { status, message, data } = await this.apicall({
      url: `/issuing/naira_cards/create_naira_virtual_account`,
      method: "post",
      payload: {
        cardholder_id: externalIdentifier,
      },
    });
    return { status, message, data };
  }

  static async webhook({ event, data }) {
    switch (event) {
      case "card_creation_event.successful":
        await cardCreatedSuccessfully(data);
        break;
      case "card_debit_event.successful":
        await cardDebitedSuccessfully(data);
        break;
      case "card_debit_event.failed":
        cardDebitedFailed(data);
        break;
      case "card_debit_event.declined":
        cardDeclined(data);
        break;
      case "card_reversal_event.successful":
        await reversal(data);
        break;
      case "3d_secure_otp_event.generated":
        await ThreeDsOtp(data);
        break;
      case "card_credit_event.successful":
        await cardCredited(data);
        break;
      case "card_blocked_due_to_insufficient_funds.activated":
        await cardTerminated(data);
        break;
      case "cardholder_otp_verification.sent":
        await cardHolderOtpVerification(data);
        break;
      default:
        break;
    }
  }

  static decryptCard(cards) {
    return {
      ...cards,
      last_4: null,
      cvv: null,
      exp_year: null,
      exp_month: null,
      number: null,
      pin: null,
    };
  }

  static cloneCardHolder = async (cardholder_id) => {
    const { status, message, data } = await this.apicall({
      url: `/issuing/cardholder/clone_cardholder`,
      method: "post",
      payload: {
        cardholder_id,
      },
    });
    return { status, message, data };
  };

  static fundVACard = async ({ cardholder_id, amount }) => {
    const { status, message, data } = await this.apicall({
      url: `/issuing/naira_cards/mock_credit`,
      method: "post",
      payload: {
        credit_amount: amount,
        cardholder_id,
      },
    });
    return { status, message, data };
  };

  static getCard = async (id) => {
    const cardDetails = await this.getVirtualCard({ id });
    return cardDetails;
  };

  static resendOtp = async (cardholderId) => {
    const { status, message, data } = await this.apicall({
      url: "/issuing/resend_cardholder_otp",
      method: "post",
      payload: {
        cardholder_id: cardholderId,
      },
    });
    return { status, message, data };
  };
}

const bridgecardErrorHandler = (message) => {
  switch (message) {
    case "Invalid phoneno.":
      return "Your bujeti phonenumber is invalid";

    case "A cardholder already exists with this phoneno":
      return "This Phonenumber has already been registered to create cards";

    case "Invalid ID url":
    case "Invalid ID number":
    case "A cardholder already exists with this BVN":
      return "This BVN has already been registered to create cards";

    case "Oops.. we couldn't verify your BVN, please confirm the BVN and try again.":
      return "We couldn't verify your BVN, please check and retry.";

    case "Invalid address, a valid address should have a minimum of 3 letters":
      return "Invalid address, address should have a minimum of 3 letters";

    case "Invalid BVN, a valid BVN is 12 digits long":
      return "This BVN is invalid";

    case "Invalid lastname, a valid name should have a minimum of 3 letters":
      return "This BVN has already been registered";

    case "Please top up your USD issuing wallet, you have insufficient balance to perform this operation":
    case "This card type is currently unavailable, but we're working on it.":
      return "We temporarily unable to create cards";

    case "This cardholder has not had their ID verified yet and so cannot be issued a card.":
      return "This BVN has already been registered";

    default:
      return message;
  }
};

const cardCreatedSuccessfully = async (payload) => {
  const { card_id: externalIdentifier, currency } = payload;

  // get carddetails decrypted.
  const cardDetails = await BridgeCard.getVirtualCard({
    id: externalIdentifier,
  });

  const { data } = cardDetails;

  const foundCard = await CardRepo.getCard({
    externalIdentifier,
  });

  if (!foundCard) throw new NotFoundError("Card");

  const cardData = {
    id: data.card_id,
    card_pan: data.card_number,
    masked_pan: `${data.card_number.substring(0, 6)}*******${data.last_4}`,
    expiration: `${data.expiry_year}-${data.expiry_month}`,
    cvv: data.cvv,
    name: data.card_name.split(" ")[0],
    card_type: data.brand.toLowerCase(),
  };
  // fund created card.
  if (currency.toLowerCase() === "usd" && foundCard.status === STATUSES.PROCESSING) {
    await BridgeCard.fundVirtualCard({
      id: cardData.id,
      amount: foundCard.amount,
      currency,
      reference: null,
      company: foundCard.company,
    });
  }

  const encryptedData = BridgeCard.encryptCard({
    number: cardData.card_pan,
    pan: cardData.masked_pan,
    exp_month: cardData.expiration.split("-")[1],
    exp_year: cardData.expiration.split("-")[0],
    last_4: cardData.masked_pan.split("*******")[1],
    cvv: cardData.cvv,
    status: STATUSES.ACTIVE,
    issuer: foundCard.issuer,
  });
  // update card instead
  const preSaveVirtualCard = await VirtualCardRepo.update({
    conditions: {
      code: foundCard.code,
    },
    changes: {
      ...encryptedData,
    },
  });
  let holder = null;
  try {
    holder = await foundCard.getUser();
  } catch (error) {
    holder = await foundCard.getOwner();
  }
  const { lastName, firstName, email } = holder;
  const emailPayload = {
    lastName,
    firstName,
    failed: false,
    dashboardUrl: `${getDashboardURL()}/cards`,
  };
  NotificationService.notifyUser({ email }, "success-card", emailPayload, {
    subject: `Hey ${firstName}! Your Bujeti Card is ready to be used 💳`,
  });

  await ThirdPartyLogService.createLog({
    company: foundCard.company,
    event: "card_creation_event.successful",
    payload: JSON.stringify({
      card_id: cardData.card_id,
      card_holder: cardData.cardholder_id,
    }),
    message: `Card created Successfully card:${foundCard.code}, amount: ${data.amount}`,
    provider: CARD_ISSUER.bridgecard,
    providerType: "issuer",
    response: JSON.stringify({ message: "OK" }),
    statusCode: 200,
    endpoint: ``,
    method: "POST",
  });
  return preSaveVirtualCard;
};

const cardDebitedSuccessfully = async (data) => {
  // get the Card
  const card = await VirtualCardRepo.find({
    externalIdentifier: data.card_id,
  });
  const get_card_type = cardType();
  const card_type = getCardType(get_card_type, card.type);
  if (card_type === "flash") {
    // deactivate card
    await card.update({ status: STATUSES.INACTIVE });
  } else {
    const spent = card.spent + Number(data.amount);
    // update card amount
    await card.update({ spent });
  }

  // create Transaction
  const transaction = await TransactionService.createTransaction({
    amount: Number(data.amount),
    currency: card.currency,
    company: card.company,
    card: card.id,
    budget: card.budget,
    payer: card.user,
    reference: data.transaction_reference,
    narration: data.description,
    description: data.description,
    processor_fee: 0,
    bujeti_fee: 0,
    status: STATUSES.SUCCESS,
  });
  // create BudgetLedger
  await BudgetLedger.create({
    currency: card.currency,
    amount: -Number(data.amount),
    budget: card.budget,
    transaction: transaction.id,
    description: data.description,
  });

  const balance = await BalanceRepo.findOrCreate({
    company: card.company,
    currency: card.currency,
  });

  await BalanceLedgerRepo.createLedger({
    amount: -Number(data.amount),
    description: data.description,
    currency: card.currency,
    balance: balance.id,
    status: STATUSES.SUCCESS,
    company: card.company,
    transaction: transaction.id,
  });

  await ThirdPartyLogService.createLog({
    company: card.company,
    event: "card.debit.successfully",
    payload: JSON.stringify(data),
    message: `Card Debit Successful card:${card.code}, amount: ${data.amount}`,
    provider: CARD_ISSUER.bridgecard,
    providerType: "issuer",
    response: JSON.stringify({ data }),
    statusCode: 200,
    endpoint: ``,
    method: "GET",
  });
};

const cardDebitedFailed = (data) => {};

const cardDeclined = (data) => {
  // Email to be sent
};

const reversal = async (data) => {
  // get Card
  const card = await VirtualCardRepo.find({
    externalIdentifier: data.card_id,
  });
  const amount = card.amount + Number(data.amount);
  // update card amount
  await card.update({ amount });
  // create Transaction
  const transaction = await TransactionService.createTransaction({
    amount: Number(data.amount),
    currency: card.currency,
    company: card.company,
    card: card.id,
    budget: card.budget,
    payer: card.user,
    reference: data.transaction_reference,
    narration: "card reversal",
    description: "card reversal",
    processor_fee: 0,
    bujeti_fee: 0,
    status: STATUSES.SUCCESS,
  });
  // create BudgetLedger
  await BudgetLedger.create({
    currency: card.currency,
    amount: Number(data.amount),
    budget: card.budget,
    transaction: transaction.id,
    description: data.description,
  });

  const balance = await BalanceRepo.findOrCreate({
    company: card.company,
    currency: card.currency,
  });

  await BalanceLedgerRepo.createLedger({
    amount: Number(data.amount),
    description: data.description,
    currency: card.currency,
    balance: balance.id,
    status: STATUSES.SUCCESS,
    company: card.company,
    transaction: transaction.id,
  });
};

const ThreeDsOtp = async (data) => {
  const card = await VirtualCardRepo.find({ externalIdentifier: data.card_id });
  const user = await UserRepo.getOneUser({
    queryParams: {
      id: card.user,
    },
    selectOptions: ["email", "firstName"],
  });
  const { email, firstName } = user;
  const emailPayload = {};
  NotificationService.notifyUser({ email }, "Otp", emailPayload, {
    subject: `Hey ${firstName}, your payment of ${card.currency}${emailPayload.amount.toLocaleString()} has been made`,
  });
};

const cardCredited = async (data) => {
  const card = await VirtualCardRepo.find({
    externalIdentifier: data.card_id,
  });
  let newAmount = card.amount;
  if (!data.transaction_reference.includes("cr8_crd:")) {
    newAmount += Number(data.amount);
  }
  // update card amount
  await VirtualCardRepo.update({
    conditions: { externalIdentifier: data.card_id },
    changes: { amount: newAmount },
  });
};

const cardTerminated = async (data) => {
  const card = await VirtualCardRepo.find({
    externalIdentifier: data.card_id,
  });
  card.update({ status: STATUSES.CANCELLED });
};

const cardHolderOtpVerification = async (data) => {
  const { cardholder_id, otp } = data;
  setTimeout(async () => {
    let user;
    const cardHolder = await CardHolderRepo.find({
      condition: {
        externalIdentifier: cardholder_id,
      },
    });

    if (cardHolder) {
      user = await UserRepo.getOneUser({
        queryParams: {
          id: cardHolder.user,
        },
        selectOptions: [],
      });
    }

    NotificationService.notifyUser(
      { email: user.email },
      "bvn-otp",
      {
        code: otp,
        firstName: cardHolder?.User?.firstName,
        sendingDate: format(new Date(), "MMMM dd, yyyy"),
        year: format(new Date(), "yyyy"),
      },
      {
        subject: `Your BVN verification OTP`,
      }
    );
  }, 5000);
  await RedisService.set(`validate-otp:${otp}`, JSON.stringify({ cardholder_id }));
};

module.exports = BridgeCard;
