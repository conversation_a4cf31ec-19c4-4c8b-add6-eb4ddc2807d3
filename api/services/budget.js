/* eslint-disable no-param-reassign */
const { Op, QueryTypes, literal } = require("sequelize");
const dateFns = require("date-fns");
const ValidationError = require("../utils/validation-error");
const NotFoundError = require("../utils/not-found-error");
const { STATUSES } = require("../models/status");
const Sanitizer = require("../utils/sanitizer");
const {
  User,
  Budget,
  UserBudget,
  Status,
  Beneficiary,
  sequelize,
  BudgetLedger,
  PhoneNumber,
  Company,
  CreditCard,
  TeamBudget,
  Team,
  Transaction,
  ScheduledBudget,
  Schedule,
  Balance,
  Mandate,
  DirectDebit,
} = require("../models");
// const BudgetValidator = require('../validators/budget.validator');
const Utils = require("../utils");
const TransactionService = require("./transaction");
const ScheduleService = require("./schedule.service");
const AuditLogsService = require("./auditLogs");
const CronService = require("./cron.service");
const HelperService = require("./helper.service");
const NotificationService = require("./notification");
const {
  BudgetAccountRepo,
  BudgetRepo,
  TransactionRepo,
  BudgetLedgerRepo,
  UserBudgetRepo,
  AccountHolderRepo,
  BankAccountRepo,
  TransferRepo,
  ScheduledBudgetRepo,
  ScheduleRepo,
  BalanceLedgerRepo,
  BalanceRepo,
  ScheduledTransactionRepo,
  ReimbursementRepo,
  UserRepo,
  VirtualCardRepo,
  CompanyRepo,
} = require("../repositories/index.repo");
const { BudgetValidator } = require("../validators");
const ExistsError = require("../utils/exists-error");
const responseUtils = require("../utils/response.utils");
const SettingsService = require("./settings");
const BalanceService = require("./balance");
const Providers = require("./providers");
const { CARD_ISSUER } = require("../models/cardissuer");
const QueueService = require("./queue.service");
const { log, Log } = require("../utils/logger.utils");
const { TRANSACTION_TYPES } = require("../models/transaction");
const BankService = require("./bank");
const { ONBOARDING_LEVEL } = require("../models/company");

const LIFECYCLE_ACTIONS = ["Decrease budget amount", "Increase budget amount"];

const Service = {
  async validateBudgetCreationPayload(payload) {
    const { error } = BudgetValidator.create.validate(payload);
    if (error) throw new ValidationError(error.message);
    if (payload.beneficiaries && payload.beneficiaries.length > 0) {
      const typeofBeneficiaries = typeof payload.beneficiaries[0];
      const beneficiariesCodes =
        typeofBeneficiaries === "object" ? payload.beneficiaries.map((singleBeneficiary) => singleBeneficiary.beneficiary) : payload.beneficiaries;

      const beneficiaries = await Beneficiary.findAll({
        where: { code: beneficiariesCodes },
        attributes: ["id", "user", "code"],
      });

      if (!beneficiaries.length) throw new ValidationError("Beneficiaries not found");
    }
    return { error: false, status: true, message: "Valid payload" };
  },
  validateMultipleBudgetsCreationPayload(payload) {
    const { error } = BudgetValidator.groupCreate.validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: true, message: "Valid payload" };
  },
  validateBudgetUploadPayload(payload) {
    const { error, value } = BudgetValidator.update.validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: true, message: "Valid payload", value };
  },

  validateAddBeneficiaryPayload(payload) {
    const { error } = BudgetValidator.addBeneficiary.validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: true, message: "Valid payload" };
  },

  validateAddBeneficiariesPayload(payload) {
    const { error } = BudgetValidator.addBeneficiaries.validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: true, message: "Valid payload" };
  },

  async validateBeneficiaryConfigUpdatePayload(payload) {
    const { amount, status, budget: budgetCode } = payload;
    if (amount) {
      Utils.validatePositiveInteger(amount);
      const { amount: budgetAmount } = await Budget.findOne({ where: { code: budgetCode } }, { attributes: ["amount"] });
      if (budgetAmount < amount) throw new ValidationError("Amount cannot be greater than the budget's amount");
    }
    if (status && typeof status === "string") {
      const statuses = Object.keys(STATUSES).map((key) => key.toLowerCase());
      if (!statuses.includes(status)) {
        throw new ValidationError("Status not recognized, please verify and retry again");
      }
    }
    return { error: false, status: true, message: "Valid payload" };
  },

  async viewBudget(criteria) {
    return Budget.findOne({
      where: {
        ...criteria,
        status: { [Op.notIn]: SettingsService.get("invalidBudgetStatus") },
      },
    });
  },

  async getGroupBudget(criteria) {
    return BudgetRepo.getGroupBudget(criteria);
  },

  async getBudget(criteria, { addLedgers = false } = {}) {
    const { user, ...rest } = criteria;
    const include = [
      {
        model: BudgetLedger,
        ...(addLedgers ? null : { limit: 1 }),
        as: "figures",
        order: [["created_at", "DESC"]],
        required: false,
        where: {
          status: [STATUSES.PENDING, STATUSES.PROCESSED],
        },
      },
      {
        model: User,
        via: "owner",
        include: [Beneficiary],
      },
      {
        model: ScheduledBudget,
        required: false,
        where: {
          status: STATUSES.ACTIVE,
        },
        order: [["created_at", "DESC"]],
        attributes: ["expiry_date", "code", "type", "start_date"],
        include: [{ model: Schedule, attributes: ["cron_expression", "nextExecutionDate"] }],
      },
      {
        model: Balance,
        attributes: ["code", "name"],
      },
    ];

    if (addLedgers) {
      const budgetCardCriteria = {
        model: CreditCard,
        required: false,
        where: {
          status: [STATUSES.ACTIVE, STATUSES.PENDING],
          ...(user && { user }),
        },
      };

      const userBudgetCriteria = {
        model: UserBudget,
        include: [{ model: User, include: [Beneficiary] }, Status],
        as: "beneficiaries",
        required: false,
        where: {
          status: [STATUSES.ACTIVE, STATUSES.INVITED],
          ...(user && { user }),
        },
      };

      include.push(budgetCardCriteria, userBudgetCriteria);
    }
    const budget = await Budget.findOne({
      where: {
        ...rest,
        status: { [Op.notIn]: SettingsService.get("invalidBudgetStatus") },
      },
      include,
    });
    if (budget) {
      if (budget.isNewBudget) {
        // if (budget.figures.length && budget.figures[budget.figures.length - 1].balanceAfter)
        //   budget.available = budget.available || budget.figures[budget.figures.length - 1].balanceAfter;
        // else budget.available = 0;
      } else {
        const budgetLedgersCriteria = {
          attributes: [
            [sequelize.literal(`sum(case when (amount<=0 and transaction is not null) then amount else 0 end)`), "debit"],
            [sequelize.literal(`sum(case when (amount > 0) or (amount < 0 and transaction is null) then amount else 0 end)`), "credit"],
          ],
          required: false,
          where: {
            status: [STATUSES.PENDING, STATUSES.PROCESSED],
            budget: budget.id,
          },
          group: ["currency"],
        };
        budget.figures = await BudgetLedger.findAll(budgetLedgersCriteria);
      }
      budget.beneficiaries = budget.beneficiaries?.filter((element) => !element.isBudgetOwner);
      budget.owners = budget.beneficiaries?.filter((element) => element.isBudgetOwner);
    }
    return budget;
  },

  async fetchBudgetsOnly(criteria) {
    return Budget.findAll({
      where: {
        ...criteria,
        status: { [Op.notIn]: SettingsService.get("invalidBudgetStatus") },
      },
      include: [
        {
          model: User,
          raw: true,
          via: "owner",
        },
        Status,
      ],
    });
  },

  /**
   * List budgets that fit the passed criteria
   * @param filters
   * @param filters.user the beneficiary attached to the budget
   * @param filters.owner the budget's owner
   * @param filters.company the company that owns the budgets
   * @param filters.name the budget's name
   * @param filters.from the budget's creation date starting date
   * @param filters.to the budget's creation date ending date
   * @param filters.currency the budget's currency
   * @returns {Promise<{budgets: ([]|*), meta: {total, perPage: number, nextPage: number, hasMore: boolean, page: number}}>}
   */
  async listBudgets(filters = {}, { UserService } = {}) {
    let { user, owner, company, page = 1, perPage = 50, from, to, team, status, search, ...rest } = filters;
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    const skip = Math.max((page - 1) * perPage, 0);
    if (rest.name) rest.name = { [Op.like]: `%${rest.name}%` };
    if (owner) {
      rest[Op.or] = [{ owner }, { company }];
    } else rest.company = company;

    if (String(user).startsWith("usr_")) {
      const foundUser = await UserService.fetchUser(user);
      if (!foundUser) throw new NotFoundError("User");
      user = foundUser.id;
    }

    // Search filters
    if (search)
      rest[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { amount: { [Op.like]: `%${search}%` } },
        { spent: { [Op.like]: `%${search}%` } },
        { sharingType: { [Op.like]: `%${search}%` } },
      ];
    if (status) {
      const statusArray = Array.isArray(status) ? status : [status];
      const statusIds = statusArray.map((value) => {
        if (value === "inactive") return [STATUSES.CLOSED, STATUSES.INACTIVE, STATUSES.PAUSE, STATUSES.DELETED];
        return STATUSES[String(value).toUpperCase()];
      });
      rest.status = { [Op.in]: statusIds.flat(), [Op.notIn]: SettingsService.get("invalidBudgetStatus") };
    }
    const teamBudgetCriteria = {
      model: TeamBudget,
      required: !!team,
      include: [
        {
          model: Team,
          required: !!team,
          where: {
            ...(team && { code: team }),
          },
        },
      ],
    };

    const userBudgetCriteria = {
      model: UserBudget,
      include: [User, Status],
      as: "beneficiaries",
      required: !!user,
      where: {
        status: [STATUSES.ACTIVE, STATUSES.INVITED],
        ...(user && { user }),
      },
    };

    const budgetCardCriteria = {
      model: CreditCard,
      required: false,
      where: {
        status: [STATUSES.ACTIVE, STATUSES.PENDING, STATUSES.INACTIVE],
      },
    };

    if (owner) {
      if (String(user).startsWith("usr_")) {
        rest[Op.or].push({ "$User.code$": owner });
      } else {
        userBudgetCriteria.where = {
          ...userBudgetCriteria.where,
          "$beneficiaries.user$": owner,
        };
      }
    }
    if (from || to) {
      rest.created_at = {};
      if (from && Utils.isValidDate(from)) {
        rest.created_at[Op.gte] = from;
      }
      if (to && Utils.isValidDate(to)) {
        rest.where = sequelize.where(sequelize.fn("date", sequelize.col("Budget.created_at")), "<=", to);
        // rest.created_at[Op.lte] = to;
      }
    }

    const shouldDisableSubQuery = user || team;

    const budgetQuery = {
      // distinct: "$beneficiaries.user$",
      where: {
        ...(user && { status: STATUSES.ACTIVE }),
        ...rest,
      },
      include: [
        {
          model: User,
          raw: true,
          via: "owner",
          include: [Status],
        },
        userBudgetCriteria,
        Status,
        teamBudgetCriteria,
        budgetCardCriteria,
        {
          model: ScheduledBudget,
          required: false,
          where: {
            status: STATUSES.ACTIVE,
          },
          order: [["created_at", "DESC"]],
          attributes: ["expiry_date", "code", "type", "start_date"],
          include: [{ model: Schedule, attributes: ["cron_expression", "nextExecutionDate"] }],
        },
      ],
      order: [
        [literal(`Budget.status = ${STATUSES.INACTIVE}`), "ASC"],
        ["created_at", "DESC"],
      ],
      ...(shouldDisableSubQuery && { subQuery: false }),
      distinct: true,
      col: "id",
      offset: skip,
      limit: perPage,
    };

    const { group, offset, limit, ...whatIsLeft } = budgetQuery;

    // eslint-disable-next-line prefer-const
    let [budgets, total] = await Promise.all([Budget.findAll(budgetQuery), Budget.count(whatIsLeft)]);
    // let t;
    if (user && budgets) {
      budgets = budgets.map((budget) => {
        budget.amount =
          budget.beneficiaries && budget.beneficiaries.length && budget.beneficiaries[0].amount ? budget.beneficiaries[0].amount : budget.amount;
        return budget;
      });
    }

    const figures = await BudgetLedger.findAll({
      attributes: [
        [sequelize.literal(`sum(case when (amount<=0 and transaction is not null) then amount else 0 end)`), "debit"],
        [sequelize.literal(`sum(case when (amount > 0) or (amount < 0 and transaction is null) then amount else 0 end)`), "credit"],
        "budget",
      ],
      where: {
        status: [STATUSES.PENDING, STATUSES.PROCESSED],
        budget: budgets.map((budget) => budget.id),
      },
      group: ["budget"],
    });
    budgets.forEach((budget) => {
      // if (budget.isNewBudget) {
      //   if (budget.figures.length && budget.figures[0].balanceAfter) budget.available = budget.figures[0].balanceAfter;
      //   else budget.available = 0;
      // }

      const figure = figures.find((element) => element.budget === budget.id);
      budget.figures = (figure && [figure]) || [];
    });

    return {
      budgets,
      meta: {
        page,
        total,
        perPage,
        nextPage: page + 1,
        hasMore: total > page * perPage,
      },
    };
  },

  /**
   * List the connected user's budgets
   * @param filters
   * @returns {Promise<{budgets: ([]|*), meta: {total, perPage: number, nextPage: number, hasMore: boolean, page: number}}>}
   */
  async listMyBudgets(filters = {}, { UserService } = {}) {
    let { user, owner, company, name, page = 1, perPage = 50 } = filters;
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    const skip = Math.max(page - 1, 0);

    if (String(user).startsWith("usr_")) {
      const foundUser = await UserService.fetchUser(user);
      if (!foundUser) throw new NotFoundError("User");
      user = foundUser.id;
    }

    const criteria = {
      [Op.or]: [{ user: user || owner }, { "$Budget.owner$": user || owner }],
      "$Budget.company$": company,
    };
    if (name) {
      criteria["$Budget.name$"] = { [Op.like]: `%${name}%` };
    }
    const { rows: userBudgets, count: total } = await UserBudget.findAndCountAll({
      where: {
        ...criteria,
        status: { [Op.notIn]: SettingsService.get("invalidBudgetStatus") },
      },
      include: [
        {
          model: Budget,
          include: Status,
        },
        Status,
      ],
      offset: skip,
      limit: perPage,
    });
    const budgets = userBudgets.map((userBudget) => userBudget.Budget);
    return {
      budgets,
      meta: {
        page,
        total,
        perPage,
        nextPage: page + 1,
        hasMore: total > page * perPage,
      },
    };
  },

  // fetchAllBudgetsAndRelationships
  async allBudgets(payload) {
    if (!payload && !payload.owner) throw new ValidationError("Specify the owner");
    const { error } = BudgetValidator.getAll.validate(payload);
    if (error) throw new ValidationError(error.message);

    const { page, perPage, from, to, search, ...queryParams } = payload;
    const budgets = await BudgetRepo.fetchAllBudgetsAndRelationships({
      queryParams,
      page,
      perPage,
      from,
      to,
      search,
    });
    return { budgets };
  },

  /**
   * Create a new budget with the passed payload
   * @param owner
   * @param payload
   * @returns {Promise<void>}
   */
  async createBudget(owner, payload) {
    try {
      const budget = await Budget.create(
        {
          owner,
          ...payload,
          amount: parseInt(payload.amount, 10),
        },
        { include: [Status] }
      );
      return budget;
    } catch (error) {
      // TODO Log Error
      console.log("Achille", error);
      throw new ValidationError("Budget could not be created");
    }
  },
  /**
   * Update the budgets that mathces the passed criteria
   * @param code
   * @param owner
   * @param payload
   * @returns {Promise<*>}
   */
  async updateBudget(code, owner, payload) {
    const updatedBudget = await Budget.update(payload, {
      where: { owner, code },
    });
    if (!updatedBudget.length) throw new NotFoundError("Budget");
    return updatedBudget;
  },
  /**
   * Add a
   * @param budget the budget ID
   * @param beneficiary the beneficiary user ID
   * @param amount
   * @returns {Promise<*>}
   */
  async addBeneficiary(budget, beneficiary, amount = null, meta = {}) {
    try {
      const payload = {
        budget,
        amount,
        user: beneficiary,
        status: STATUSES.ACTIVE, // default active
        ...meta,
      };
      return await UserBudget.create(payload);
    } catch (error) {
      // TODO Log error
      throw new ValidationError("Beneficiary could not be added to budget");
    }
  },
  async removeBeneficiary(budget, beneficiary) {
    const [removedBeneficiary] = await UserBudget.update({ status: STATUSES.INACTIVE }, { where: { budget, user: beneficiary } });
    if (!removedBeneficiary) {
      throw new NotFoundError("Beneficiary or Budget");
    }
    return [removedBeneficiary];
  },
  async updateBeneficiaryConfig(userBudget, payload) {
    const [updatedConfig] = await UserBudget.update(payload, {
      where: { id: userBudget },
    });
    if (!updatedConfig) {
      throw new ValidationError("No updates applied to budget");
    }
    return updatedConfig;
  },
  async getBudgetAndBeneficiaryIDs(budgetCode, beneficiaryCode) {
    const [budget, beneficiary] = await Promise.all([
      Budget.findOne({
        where: { code: budgetCode },
        attributes: ["id", "sharingType", "amount", "owner", "code", "currency", "name"],
      }),
      beneficiaryCode.startsWith("bnf_")
        ? Beneficiary.findOne({
            where: { code: beneficiaryCode },
            attributes: ["id", "user", "code", "owner", "company"],
            include: [
              {
                model: User,
                attributes: ["id", "firstName", "email", "company", "role", "role_id"],
                include: [PhoneNumber],
              },
              {
                model: User,
                as: "Owner",
                via: "owner",
                attributes: ["id", "firstName", "company", "email"],
              },
            ],
          })
        : UserBudget.findOne({
            where: {
              code: beneficiaryCode,
            },
            attributes: ["id", "user", "code"],
          }),
    ]);
    if (!budget) throw new NotFoundError("Budget");
    if (!beneficiary) throw new NotFoundError("Beneficiary");
    return [budget, beneficiary];
  },
  async getUserBudgetByBudgetCode(code, user) {
    const budget = await Budget.findOne({ where: { code } });
    if (!budget) throw new NotFoundError("Budget");
    const userBudget = await UserBudget.findOne({
      where: { budget: budget.id, user },
      include: [Budget, User],
    });
    if (!userBudget) throw new ValidationError("You are not part of this budget");
    return userBudget;
  },
  async getUserBudgetByCode(code) {
    const userBudget = await UserBudget.findOne({
      where: { code },
      include: [Budget, User],
    });
    if (!userBudget) throw new ValidationError("You are not part of this budget");
    return userBudget;
  },
  async computeAllocableAmount(budget) {
    if (budget.sharingType !== "even") return;
    const total = await UserBudget.count({
      where: { budget: budget.id, status: STATUSES.ACTIVE },
    });
    return budget.amount / ((total || 1) + 1);
  },
  async updateBudgetUsersAmount(budget, amount) {
    return UserBudget.update({ amount }, { where: { budget, status: STATUSES.ACTIVE } });
  },

  async addBeneficiaries(owner, budget, beneficiaryArray, amount = null, budgetOwnersMap = {}) {
    const typeofBeneficiaries = typeof beneficiaryArray[0];
    const beneficiaries =
      typeofBeneficiaries === "object" ? beneficiaryArray.map((singleBeneficiary) => singleBeneficiary.beneficiary) : beneficiaryArray;

    const beneficiariesCodes = beneficiaries.filter((code) => code.startsWith("bnf_"));
    const userBudgetCodes = beneficiaries.filter((code) => code.startsWith("ubg_"));

    const foundBeneficiaries =
      beneficiariesCodes.length &&
      (await Beneficiary.findAll({
        where: { code: beneficiariesCodes },
        attributes: ["id", "user", "code"],
      }));

    if (beneficiariesCodes.length && !foundBeneficiaries.length) throw new ValidationError("Beneficiaries not found");

    const foundUserBudgets =
      userBudgetCodes.length &&
      (await UserBudget.findAll({
        where: {
          code: userBudgetCodes,
        },
      }));

    if (userBudgetCodes.length && !foundUserBudgets.length) throw new ValidationError("Beneficiaries not found");

    const mergedUserBudgets = [...(foundBeneficiaries || []), ...(foundUserBudgets || [])];

    // let amount = null;
    const payload = mergedUserBudgets.map((beneficiary) => {
      if (typeofBeneficiaries === "object") {
        amount =
          budgetOwnersMap[beneficiary.code]?.amount ||
          beneficiaryArray.find((singleBeneficiary) => singleBeneficiary.beneficiary === beneficiary.code).amount;
      }
      return {
        budget: budget.id,
        amount,
        user: beneficiary.user,
        status: STATUSES.ACTIVE,
        ...(budgetOwnersMap[beneficiary.code] && { ...budgetOwnersMap[beneficiary.code] }),
      };
    });
    try {
      return UserBudget.bulkCreate(payload);
    } catch (error) {
      throw new ValidationError("Beneficiaries could not be added to budget-1");
    }
  },

  async removeBeneficiaryFromBudgets(budgets, user) {
    const budgetsIds = budgets.map((budget) => (budget && budget.id) || budget);
    await UserBudget.update({ status: STATUSES.INACTIVE }, { where: { budget: budgetsIds, user } });
  },
  async addBeneficiaryToBudgets(budgets, user, pending = true) {
    const userBudgets = await UserBudget.findAll({
      where: { user: (user && user.id) || user, status: STATUSES.ACTIVE },
      attributes: ["budget"],
    });
    const payload = budgets.map((budget) => {
      budget = (budget && budget.id) || budget;
      const isExistingBudget = userBudgets.find((userBudget) => userBudget.budget === budget);
      if (!isExistingBudget) {
        return {
          budget,
          amount: 0, // TODO Compute beneficiary amount @achille
          user: (user && user.id) || user,
          status: pending ? STATUSES.ACTIVE : STATUSES.PENDING,
        };
      }
    });
    try {
      return await UserBudget.bulkCreate(payload);
    } catch (error) {
      console.error(error);
      throw new ValidationError("Beneficiaries could not be added to budget-2");
    }
  },
  getTotalBudget(company, currency) {
    const query = `
            select sum(amount) as amount, sum(spent) as spent 
            from Budgets 
            where 
                  company = ${company} and currency = '${currency}'
                  and status in (${STATUSES.ACTIVE}, ${STATUSES.PAUSE})
        `;

    return sequelize.query(query, {
      type: QueryTypes.SELECT,
    });
  },
  getTotalBudgets(company) {
    const query = `
            select sum(amount) as amount, sum(spent) as spent, currency
            from Budgets 
            where 
                  company = ${company}
                  and status in (${STATUSES.ACTIVE}, ${STATUSES.PAUSE})
            group by currency
        `;

    return sequelize.query(query, {
      type: QueryTypes.SELECT,
    });
  },
  activateBeneficiaryBudgets(user) {
    return UserBudget.update({ status: STATUSES.ACTIVE }, { where: { user } });
  },
  async getStats(company, budget, filters) {
    if (!budget) {
      throw new NotFoundError("Budget");
    }
    const chartTransactions = await Service.getTransactionsChart(company, {
      ...filters,
      budget: budget.id,
    });
    const transactionCriteria = {
      company,
      budget: budget.id,
      ...filters,
    };
    const [{ transactions }, [spentByCurrency]] = await Promise.all([
      TransactionService.listTransactions(transactionCriteria),
      TransactionService.getTotalSpent({
        ...transactionCriteria,
        currency: budget.currency,
        status: STATUSES.SUCCESS,
      }),
    ]);

    const spent = (spentByCurrency && spentByCurrency[filters.currency]) || 0;
    let spendableAmount = budget.amount;
    let available = 0;

    let [{ total: disbursed }] = await UserBudget.findAll({
      raw: true,
      attributes: [[sequelize.fn("sum", sequelize.col("amount")), "total"]],
      where: {
        budget: budget.id,
        ...(filters.payer && { user: filters.payer }),
      },
    });

    disbursed = disbursed ? parseInt(disbursed, 10) : 0;

    if (filters.payer) {
      const userBudget = await UserBudget.findOne({
        where: { budget: budget.id, user: filters.payer },
      });
      if (!userBudget) throw new ValidationError("This user is not on this budget");
      spendableAmount = userBudget.amount;
      available = userBudget && (userBudget.amount === 0 || userBudget.amount === null) ? budget.amount - (spent + disbursed) : disbursed - spent;
    } else {
      available = spendableAmount - (spent + disbursed);
    }

    return {
      available,
      spent,
      chart: Utils.groupByKey(chartTransactions, "created_at"),
      transactions: Sanitizer.sanitizeTransactions(transactions),
      disbursed: parseInt(disbursed || 0, 10),
      summary: {
        reimbursements: await ReimbursementRepo.count({ filters: { budget: budget.id, status: STATUSES.PENDING } }),
        payments: await ScheduledTransactionRepo.count({ budget: budget.id, start_date: Utils.addDays(new Date(), 1) }),
        transactions: await TransactionRepo.count({ filters: { budget: budget.id, status: STATUSES.PENDING } }),
        uncategorizedTransactions: await TransactionRepo.count({ filters: { budget: budget.id, category: { [Op.is]: null } } }),
      },
      topSpenders: await UserBudgetRepo.getTopSpenders(budget.id),
      categories: (
        await TransactionRepo.analysisOfTransactionByCategory({
          queryParams: {
            budget: budget.id,
          },
        })
      ).map((element) => {
        const { Category = null, currency, transactionCount, transactionVolume } = element.toJSON();
        const { name = null, code = null } = Category || {};
        return {
          category: name
            ? {
                name,
                code,
              }
            : null,
          transactionVolume: Number(transactionVolume),
          transactionCount,
          currency,
        };
      }),
      vendors: (
        await TransactionRepo.analysisOfTransactionByVendor({
          queryParams: {
            budget: budget.id,
          },
        })
      ).map((element) => {
        const {
          Vendor: { name, code },
          currency,
          transactionVolume,
          transactionCount,
        } = element.toJSON();

        return {
          currency,
          vendor: {
            name,
            code,
          },
          transactionCount,
          transactionVolume: Number(transactionVolume),
        };
      }),
    };
  },

  async getAvailableBalanceOldWay(budget) {
    const [{ total: spent = 0 }] = await BudgetLedger.findAll({
      raw: true,
      attributes: [[sequelize.fn("sum", sequelize.col("amount")), "total"]],
      where: {
        budget: budget.id,
        amount: { [Op.lt]: 0 },
        transaction: { [Op.not]: null },
      },
    });
    return parseInt(budget.amount, 10) - Math.abs(parseInt(spent || 0, 10));
  },

  async getBudgetAvailableBalance(budget) {
    const budgetRevampDeploymentDate = new Date(SettingsService.get("budgetRevampDeploymentDate"));
    // if the budget Were Created Before the new logic, use the old way
    if (Utils.firstDateIsBeforeSecondDate(budget.created_at, budgetRevampDeploymentDate)) {
      return Service.getAvailableBalanceOldWay(budget);
    }

    // otherwise use the new way
    if (budget.isNewBudget) {
      if (budget.available) return parseInt(budget.available, 10);
      const { balanceAfter = 0 } =
        (await BudgetLedger.findOne({
          raw: true,
          attributes: ["balanceAfter"],
          where: {
            budget: budget.id,
            status: { [Op.in]: [STATUSES.PENDING, STATUSES.PROCESSED] },
            card: null,
          },
          order: [["id", "DESC"]],
        })) || {};
      return parseInt(balanceAfter || 0, 10);
    }

    const [{ total = 0 }] = await BudgetLedger.findAll({
      raw: true,
      attributes: [[sequelize.fn("sum", sequelize.col("amount")), "total"]],
      where: {
        budget: budget.id,
        status: [STATUSES.PENDING, STATUSES.PROCESSED],
      },
    });
    return parseInt(budget.amount, 10) + parseInt(total || 0, 10);
  },

  /**
   * Get available fund on budgets cards
   * @param budget
   * @returns {Promise<number>}
   */
  async getTotalCardAvailableFundForBudget(budget) {
    const [{ totalSpent, totalAmount }] = await CreditCard.findAll({
      raw: true,
      attributes: [
        [sequelize.fn("sum", sequelize.col("spent")), "totalSpent"],
        [sequelize.fn("sum", sequelize.col("amount")), "totalAmount"],
      ],
      where: {
        budget: budget.id,
        currency: budget.currency,
        status: { [Op.notIn]: [STATUSES.INACTIVE, STATUSES.DELETED] },
      },
    });
    return parseInt(totalAmount || 0, 10) - parseInt(totalSpent || 0, 10);
  },

  /**
   * Get total fund on budgets
   * @param budget
   * @returns {Promise<number>}
   */
  async getTotalCardsFundForBudget(budget) {
    const [{ totalAmount }] = await CreditCard.findAll({
      raw: true,
      attributes: [[sequelize.fn("sum", sequelize.col("amount")), "totalAmount"]],
      where: {
        budget: budget.id,
        currency: budget.currency,
        status: { [Op.notIn]: [STATUSES.INACTIVE, STATUSES.DELETED] },
      },
    });
    return parseInt(totalAmount || 0, 10);
  },

  async getBudgetTransferableBalance(budget) {
    // const cardsTotalFund = await Service.getTotalCardsFundForBudget(budget);
    const budgetAvailableBalance = await Service.getBudgetAvailableBalance(budget);
    // const transferableBalance = budgetAvailableBalance - cardsTotalFund;
    // return transferableBalance <= 0 ? 0 : transferableBalance;
    return budgetAvailableBalance;
  },

  async getTransactionsChart(company, filters) {
    const { from, to, groupBy = "days", currency, budget, payer } = Utils.buildAnalyticsFilters(filters);
    const transactions = await TransactionService.listTransactionsRaw(company, {
      from,
      to,
      groupBy,
      currency,
      budget,
      ...(payer && { payer }),
    });

    let datesRange = [];

    switch (groupBy) {
      case "days":
        datesRange = Utils.generateDaysInRange(from, to, "MMM do");
        break;
      case "weeks":
        datesRange = Utils.generateWeeksInRange(from, to, "io 'Week'");
        break;
      default:
        datesRange = Utils.generateDaysInRange(Utils.removeDays(new Date(), 30), new Date(), "MMM do");
    }
    const datesObject = [];
    // eslint-disable-next-line array-callback-return
    datesRange.forEach((element) => {
      datesObject[element] = { created_at: element, amount: 0, currency: filters.currency || "NGN" };
    });
    transactions.forEach((transaction) => {
      datesObject[transaction.created_at] = {
        ...transaction,
        amount: parseInt(transaction.amount, 10),
      };
    });
    return Object.values(datesObject);
  },

  async listBudgetBeneficiaries(filters = {}) {
    let { page = 1, budget, search, status, perPage = 50, company, from, to } = filters;
    perPage = parseInt(perPage, 10);
    page = parseInt(page, 10);
    const skip = (page - 1) * perPage;
    let beneficiaries = [];
    let total = 0;
    let budgetId;

    if (!company) throw new ValidationError('"company" is required');

    if (budget) {
      const { id } = await Service.getBudget({ code: budget });
      budgetId = id;
    }

    const criteria = { company };

    if (search) {
      criteria[Op.or] = [{ "$User.firstName$": search }, { "$User.lastName$": search }, { "$User.middleName$": search }];
    }

    if (from && to) {
      criteria.created_at = {};
      if (from && Utils.isValidDate(from)) {
        criteria.created_at[Op.gte] = from;
      }
      if (to && Utils.isValidDate(to)) {
        criteria.created_at[Op.lte] = to;
      }
    }
    if (status) {
      if (Array.isArray(status)) {
        criteria.status = status.map((val) => STATUSES[val.toUpperCase()]);
      } else criteria.status = STATUSES[status.toUpperCase()];
    }

    const [count = 0, rows = []] = await Promise.all([
      Beneficiary.count({
        where: criteria,
        include: [
          {
            model: User,
            via: "user",
            required: true,
            include: [
              {
                model: Status,
                required: true,
              },
              {
                model: UserBudget,
                required: true,
                where: {
                  ...(budget && { budget: budgetId }),
                  status: {
                    [Op.ne]: STATUSES.INACTIVE,
                  },
                },
                include: [
                  {
                    model: Budget,
                    attributes: ["code", "name", "status"],
                    required: true,
                    where: {
                      company,
                      ...(budget && { code: budget }),
                    },
                  },
                ],
              },
            ],
          },
          {
            model: Status,
            required: true,
          },
          {
            model: Company,
            required: true,
          },
        ],
      }),
      Beneficiary.findAll({
        subQuery: false,
        where: criteria,
        include: [
          {
            model: User,
            via: "user",
            required: true,
            include: [
              {
                model: Status,
                required: true,
              },
              {
                model: UserBudget,
                required: !!budget,
                where: {
                  ...(budget && { budget: budgetId }),
                  status: {
                    [Op.ne]: STATUSES.INACTIVE,
                  },
                },
                include: [
                  {
                    model: Budget,
                    attributes: ["code", "name", "status"],
                    required: true,
                    where: {
                      company,
                      ...(budget && { code: budget }),
                    },
                  },
                ],
              },
            ],
          },
          {
            model: Status,
            required: true,
          },
          {
            model: Company,
            required: true,
          },
        ],
        order: [["created_at", "DESC"]],
        limit: perPage,
      }),
    ]);
    beneficiaries = rows;
    total = count;
    return {
      beneficiaries: Sanitizer.sanitizeBeneficiaries(beneficiaries),
      meta: {
        page,
        total,
        perPage,
        nextPage: page + 1,
        hasMore: total >= page * perPage,
      },
    };
  },

  async updateBudgetSpentAmount(budget, amount) {
    const response = await Budget.increment("spent", {
      by: parseInt(amount, 10),
      where: { id: budget.id },
    });
    return response;
  },

  async createBudgetLedger(payload) {
    // TODO Atomic operation
    const existingLedger = await BudgetLedger.findOne({ where: payload });
    if (!existingLedger || (!payload.transaction && LIFECYCLE_ACTIONS.includes(payload.description))) return BudgetLedger.create(payload);
    return existingLedger;
  },

  async getAlreadyDisbursedAmount(budget) {
    const [{ total: disbursed }] = await UserBudget.findAll({
      raw: true,
      where: { budget: budget.id },
      attributes: [[sequelize.fn("sum", sequelize.col("amount")), "total"]],
    });
    return parseInt(disbursed, 10);
  },

  /**
   * log a user payment and increase their spent amount
   * @param payload
   * @returns {Promise<*>}
   */
  async logUserPayment(payload) {
    const { user, budget, amount } = payload;
    const transaction = await sequelize.transaction();
    try {
      await UserBudget.increment("spent", {
        by: parseInt(amount, 10),
        where: { user, budget },
      });
      return transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  /**
   * Log a successful transfer on a budget
   * @param criteria Budget criteria
   * @param criteria.code Budget code
   * @param criteria.code Budget company
   * @param transaction
   * @returns {Promise<*>}
   */
  async logSuccessfulTransfer(criteria, transaction) {
    const {
      amount,
      id: transactionId,
      currency,
      narration,
      description,
      payer: user,
      processor_fee: processorFee = 0,
      bujeti_fee: bujetiFee = 0,
    } = transaction;
    const { id: budgetId } = await Service.getBudget(criteria);
    const totalAmount = amount + processorFee + bujetiFee;
    return Promise.all([
      UserBudgetRepo.increaseUserBudgetSpent({
        filter: { user, budget: budgetId },
        amount: totalAmount,
      }),
      Service.createBudgetLedger({
        currency,
        amount: -1 * totalAmount,
        user,
        budget: budgetId,
        description: narration || description,
        transaction: transactionId,
        status: STATUSES.PENDING,
      }),
    ]);
  },

  /**
   * Checks if a user already exists on a budget
   * @param user
   * @param budget
   */
  async userBudgetExists(user, budget) {
    const userBudget = await UserBudget.findOne({
      where: { user, budget, status: STATUSES.ACTIVE },
    });
    if (userBudget) throw new ExistsError("Beneficiary already exists on this Budget");
  },

  /**
   * get statistics of transactions by category
   * @param {Object} criteria
   * @param {Object} payload
   * @returns
   */
  async budgetTransactionAnalytics(criteria) {
    const today = new Date();
    const { to = today, from = dateFns.subDays(today, 30), ...query } = criteria;
    const budgetTransactionAnalytics = await TransactionRepo.analysisOfTransactionByBudget({
      queryParams: {
        ...query,
      },
      ...(from && { from }),
      ...(to && { to }),
    });
    const allocatedBudgets = budgetTransactionAnalytics.map(({ Budget }) => Budget?.id).filter(Boolean);
    const analysisOfBudgetAllocation = await BudgetRepo.analysisOfBudgetAllocation({
      queryParams: { budget: allocatedBudgets },
      ...(from && { from }),
      ...(to && { to }),
    });

    const highestTransactionVolume = budgetTransactionAnalytics.length && budgetTransactionAnalytics[0];
    const highestTransactionCount = budgetTransactionAnalytics.length && Utils.maxElementFromAnArray(budgetTransactionAnalytics, "transactionCount");
    const highestAllocationCount = analysisOfBudgetAllocation.length && Utils.maxElementFromAnArray(analysisOfBudgetAllocation, "allocatedCount");
    const highestAllocationVolume = analysisOfBudgetAllocation.length && analysisOfBudgetAllocation[0];

    return responseUtils.sendObjectResponse("Budgets breakdown successfully retrieved", {
      highestTransactionCount,
      budgetTransactionAnalytics,
      highestTransactionVolume,
      highestAllocationCount,
      highestAllocationVolume,
      analysisOfBudgetAllocation,
    });
  },

  /**
   * Top up a budget by the passed amount
   * @param {*} id
   * @param {*} amount
   * @returns
   */
  async topUpBudget(id, amount) {
    const response = await Budget.increment("amount", {
      by: parseInt(amount, 10),
      where: { id },
    });
    return response;
  },

  /**
   * Pause a budget and do not liquidate the balance
   * @param {object} budget the budget to close
   * @returns
   */
  async pauseBudget(budget) {
    return Budget.update({ status: STATUSES.PAUSE }, { where: { id: budget.id } });
  },

  /**
   * Close a budget and liquidate it since FE calls this endpoint for deleting a budget
   * @param {object} budget the budget to close
   * @returns
   */
  async closeBudget({ budget, user }) {
    const cardCriteria = {
      company: budget.company,
      budget: budget.id,
      status: STATUSES.ACTIVE,
    };

    const hasACard = await VirtualCardRepo.getCard({
      filter: cardCriteria,
    });

    if (hasACard) throw new ValidationError("Budget cannot be deleted as there are still active cards tied to it");

    await Service.liquidateBudgetSubaccount({ criteria: { id: budget.id }, user, balance: budget.balance });
    if (budget.schedule_id)
      ScheduledBudgetRepo.updateScheduledBudget({ queryParams: { id: budget.schedule_id }, updateFields: { status: STATUSES.CLOSED } });
    return Budget.update({ status: STATUSES.CLOSED }, { where: { id: budget.id } });
  },

  async deactivateBudget(budget) {
    const [budgetAvailableBalance, budgetOwner] = await Promise.all([
      Service.getBudgetAvailableBalance(budget),
      UserRepo.getOneUser({ queryParams: { id: budget.owner }, selectOptions: ["firstName"] }),
    ]);
    if (budgetAvailableBalance) {
      await Service.fundBudgetSubAccount(budget.id, -1 * budgetAvailableBalance, budget.currency, {
        isUpdate: true,
        user: budgetOwner.code,
        doNotThrow: true, // absence of a budget account should not stop execution
      });
    }

    if (budget.schedule_id)
      ScheduledBudgetRepo.updateScheduledBudget({ queryParams: { id: budget.schedule_id }, updateFields: { status: STATUSES.CLOSED } });
    return Budget.update({ status: STATUSES.CLOSED }, { where: { id: budget.id } });
  },

  async createBudgetScehdule(data) {
    let { expiryDate, schedule } = data;
    if (expiryDate && !schedule) {
      const { minutes, hours, dayOfMonth, month, dayOfWeek } = ScheduleService.convertDateToSchedule(expiryDate);
      schedule = { minutes, hours, dayOfMonth, month, dayOfWeek };
      expiryDate = ScheduleService.addMinutes(expiryDate, 5);
    }
    const {
      data: { cronExpression, createdSchedule, isStartDateToday },
    } = await ScheduleService.validateAndInitiateScheduleRecord({ schedule, expiryDate });
    const {
      data: { createdCron, generateSchedule },
    } = await ScheduleService.generateAndRecordScheduleWithCronJob({
      createdSchedule,
      isStartDateToday,
      cronBody: {},
      cronExpression,
      expiryDate,
      title: `budget/${createdSchedule.code}/expire`,
    });
    return responseUtils.sendObjectResponse("Budget Schedule Initiated", {
      createdCron,
      cronExpression,
      createdSchedule,
      generateSchedule,
    });
  },

  async updateScheduledBudgetStatus(data) {
    const { code: scheduleCode, status: incomingStatus, reason } = data;

    const existingSchedule = await ScheduledBudgetRepo.getScheduledBudget({ queryParams: { code: scheduleCode } });
    if (!existingSchedule) throw new NotFoundError("Budget Schedule");

    const status = STATUSES[incomingStatus.toUpperCase()];
    if (existingSchedule.status === STATUSES.COMPLETED) throw new ValidationError("Budget schedule is already completed");
    if (status === STATUSES.PAUSE && existingSchedule.status === STATUSES.PAUSE) throw new ValidationError("Budget schedule already paused");
    if (status === STATUSES.ACTIVE && existingSchedule.status === STATUSES.ACTIVE) throw new ValidationError("Transaction schedule already active");
    const { Schedule } = existingSchedule;

    await Promise.all([
      ScheduleRepo.updateSchedule({
        queryParams: { id: Schedule.id },
        updateFields: { status: status === STATUSES.ACTIVE ? STATUSES.ACTIVE : STATUSES.INACTIVE },
      }),
      CronService.updateCron(Schedule.cron_id, {
        activate: status === STATUSES.ACTIVE,
      }),
      ScheduledBudgetRepo.updateScheduledBudget({
        queryParams: { code: scheduleCode },
        updateFields: { reason, status },
      }),
    ]);
    return responseUtils.sendObjectResponse("Scheduled Budget updated successfully");
  },

  async updateNextExecutionDate(data) {
    const { code: scheduleCode } = data;
    const existingSchedule = await ScheduledBudgetRepo.getScheduledBudget({ queryParams: { code: scheduleCode } });
    if (!existingSchedule) throw new NotFoundError("Budget schedule");
    const { Schedule } = existingSchedule;
    await ScheduleRepo.updateSchedule({
      queryParams: { id: Schedule.id },
      updateFields: { nextExecutionDate: ScheduleService.generateNextDateFromCronExpression(Schedule.cron_expression) },
    });
    return responseUtils.sendObjectResponse("Scheduled budget start date updated successfully");
  },

  async fundBudget(data) {
    const { code: scheduleCode } = data;
    const existingSchedule = await ScheduledBudgetRepo.getScheduledBudget({ queryParams: { code: scheduleCode } });
    if (!existingSchedule) throw new NotFoundError("Budget schedule");

    const foundBudget = existingSchedule.Budget;
    const { amount, currency } = foundBudget;

    const foundUser = await UserRepo.getOneUser({
      queryParams: { id: foundBudget.owner },
    });

    AuditLogsService.createLog({
      event: "toped-up-budget",
      user: foundBudget.owner,
      initial_state: Sanitizer.sanitizeBudget(foundBudget),
      delta: { amount },
      table_type: "Budget",
      table_id: foundBudget.id,
    });

    await Service.fundBudgetSubAccount(foundBudget.id, amount, currency, { user: foundUser.code });

    // reset budget
    await BudgetRepo.updateABudget({
      queryParams: {
        code: foundBudget.code,
      },
      updateFields: {
        spent: 0,
        disbursed: 0,
      },
    });

    return responseUtils.sendObjectResponse("Budget funded successfully");
  },

  getSharingType({ beneficiaries = [], sharingType = null } = {}) {
    return sharingType || (beneficiaries?.length && typeof beneficiaries[0] === "object") ? "custom" : "even";
  },
  getAppropriateStatus({ amount, forecast, availableBalance }) {
    if (forecast || availableBalance < amount) return STATUSES.INACTIVE;
    return STATUSES.ACTIVE;
  },
  async commitPendingBudgetLedgers(budget) {
    const [committed] = await BudgetLedger.update({ status: STATUSES.PROCESSED }, { where: { budget } });
    return committed;
  },
  async getSumOfPreviousMovementsBeforeToday(budgetId) {
    const [{ total = 0 }] = await BudgetLedger.findAll({
      raw: true,
      attributes: [[sequelize.fn("sum", sequelize.col("amount")), "total"]],
      where: {
        budget: budgetId,
        status: STATUSES.PENDING,
        created_at: {
          [Op.lt]: dateFns.startOfDay(),
        },
      },
    });
    return total || 0;
  },
  async reconcileBudgetLedgersWithTransactions(args) {
    const criteria = {
      budget: {
        [Op.not]: null,
      },
      status: STATUSES.SUCCESS,
    };
    const page = args.page || 1;
    const SIZE = 1000;

    if (args.company) {
      const foundCompany = await Company.findOne({
        where: { code: args.company },
      });
      if (!foundCompany) return;
      criteria.company = foundCompany.id;
    }
    const transactions = await Transaction.findAll({
      where: criteria,
      offset: (page - 1) * SIZE,
      limit: SIZE,
    });

    if (!transactions.length) return;
    await Promise.all(
      transactions.map(({ id, currency, budget, narration, description, amount, processor_fee: processorFee, bujeti_fee: bujetiFee }) =>
        Service.createBudgetLedger({
          currency,
          description: narration || description,
          amount: -1 * (amount + processorFee + bujetiFee),
          budget,
          transaction: id,
          status: STATUSES.PENDING,
        })
      )
    );
    Service.reconcileBudgetLedgersWithTransactions({
      ...args,
      page: page + 1,
    });
  },
  async getBudgetLedgers(criteria) {
    return BudgetLedgerRepo.getBudgetLedgerTransactions(criteria);
  },

  async createBudgetSubAccount({ budget, company }) {
    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[company] || payment.defaultProvider;
    let accountHolder = await AccountHolderRepo.getAccountHolder({
      filter: { company, provider: CARD_ISSUER[providerToUse] },
      selectOptions: ["externalIdentifier"],
    });

    const foundCompany = await CompanyRepo.getCompany({
      queryParams: { id: company },
    });
    // Check for level one onboarding as they cannot perform this operation
    if (!accountHolder && foundCompany.onboardingLevel !== ONBOARDING_LEVEL.LEVEL_1) throw new NotFoundError("Account Holder");

    if (!accountHolder) accountHolder = { externalIdentifier: SettingsService.get("BUJETI_ANCHOR_CUSTOMER_ID") };

    if (!accountHolder) throw new NotFoundError("Account Holder");

    const { error, data } = await Providers[providerToUse].virtualAccount.createSubAccount(accountHolder.externalIdentifier, company);
    // eslint-disable-next-line new-cap
    if (error) throw Providers[providerToUse].throwProviderError(data);

    const {
      id,
      type,
      relationships: { virtualNubans },
    } = data;

    const { error: virtualAccountError, data: virtualAccountData } = await Providers[providerToUse].virtualAccount.getVirtualAccount(
      virtualNubans.data[0].id
    );
    // eslint-disable-next-line new-cap
    if (virtualAccountError) throw Providers[providerToUse].throwProviderError(data);

    const payload = {
      budget,
      company,
      type,
      externalIdentifier: id,
      accountId: virtualNubans.data[0].id,
      provider: CARD_ISSUER[providerToUse],
      number: virtualAccountData.attributes.accountNumber,
      bankName: virtualAccountData.attributes.bank.name,
      accountName: virtualAccountData.attributes.accountName,
      bankCode: virtualAccountData.attributes.bank.nipCode,
    };

    return BudgetAccountRepo.createBudgetAccount({ payload });
  },

  async canCreateBudgetAccount(company, currency, virtualAccountsConfigurations) {
    if (!SettingsService.get("feature_flags").subaccount) return false;
    const totalBudgetAccount = await BudgetAccountRepo.countBudgetAccount({
      filter: { company },
      include: [
        {
          model: Budget,
          where: {
            currency,
            status: STATUSES.ACTIVE,
          },
        },
      ],
    });

    // eslint-disable-next-line camelcase
    if (virtualAccountsConfigurations[currency].max_number < totalBudgetAccount + 1)
      throw new ValidationError("You cannot create more budgets or subaccount");
    return true;
  },

  async fundBudgetSubAccount(budgetId, amount, currency = "NGN", options = {}) {
    let { description = null } = options;
    const { userId, isUpdate = false, user = null, settlementBalance, sourceBudget, sourceBalance } = options;

    const budgetAccount = await BudgetAccountRepo.getBudgetAccount({
      filter: { budget: budgetId },
    });
    if (!budgetAccount) {
      // if no budget account and doNotThrow is set to true, just return
      if (options.doNotThrow) return;

      throw new NotFoundError("Budget Account");
    }

    if ([STATUSES.MIGRATING, STATUSES.PROCESSING].includes(budgetAccount.status))
      throw new ValidationError(`We are still migrating this account, Please try again after some time`);

    if (budgetAccount.status === STATUSES.MIGRATED) throw new ValidationError(`This account has been migrated, Please reach out to support`);

    // Get Balance and check
    if (amount > 0) {
      // Funding Budget from Balance
      if (sourceBalance) {
        const {
          data: { bankAccount: directDebitAccount, isDirectDebit },
        } = await HelperService.isDirectDebitAccount({ balance: sourceBalance });
        if (!isDirectDebit) {
          await BalanceService.canBalanceHandleTransaction({ balance: sourceBalance, amount, totalAmount: amount, company: budgetAccount.company });
        } else {
          await BankService.canAccountProcessPayment({ directDebit: { bankAccount: directDebitAccount.code }, amount });
        }
      } else {
        // Funding budget from another budget
        await Service.canBudgetHandleTransaction({ budget: sourceBudget || budgetId, amount, totalAmount: amount });
      }
    }

    description = description || `Top up budget(${budgetAccount.Budget.name})`;
    let bankAccount;
    let foundBalance;
    let isBalanceDebited = false;

    if (sourceBalance || settlementBalance) {
      foundBalance = await BalanceRepo.getBalance({
        filter: {
          ...(settlementBalance && { code: settlementBalance }),
          ...(!settlementBalance && { id: sourceBalance }),
        },
        includeAccount: true,
      });
      if (!foundBalance) throw new NotFoundError("Balance");
      bankAccount = foundBalance.BankAccount;
      bankAccount.type = Utils.getAccountType(bankAccount?.externalBankAccountId);
      isBalanceDebited = true;
    } else if (sourceBudget) {
      // Get Budget account
      bankAccount = await BudgetAccountRepo.getBudgetAccount({
        filter: { budget: sourceBudget, status: STATUSES.ACTIVE, company: budgetAccount.company },
      });
    } else {
      bankAccount = await BankAccountRepo.getOneBankAccount({
        queryParams: {
          owner: budgetAccount.company,
          type: "virtual",
          ownerType: "company",
          status: STATUSES.ACTIVE,
          issuer: budgetAccount.provider,
        },
        selectOptions: ["externalBankAccountId", "currency", "number", "bankCode"],
        addBalance: true,
      });
      if (bankAccount) {
        bankAccount = bankAccount.get({ plain: true });
        bankAccount.type = Utils.getAccountType(bankAccount?.externalBankAccountId);
      }
      isBalanceDebited = true;
    }

    if (!bankAccount) throw new NotFoundError("Bank Account");

    const existingMandate = await Mandate.findOne({
      where: {
        bankAccount: bankAccount.id,
        status: "granted",
        isReadyForDebit: true,
      },
    });

    if (existingMandate) {
      // eslint-disable-next-line consistent-return
      return Service.processDirectDebitTopUp({ budgetAccount, existingMandate, amount, userId, currency, bankAccount, balance: foundBalance?.id });
    }

    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[budgetAccount.company] || payment.defaultProvider;

    if (isUpdate || amount < 0) {
      description = description || `${amount < 0 ? "Decrease" : "Increase"} budget amount`;
    }

    const transferPayload = {
      currency,
      description,
      amount: -1 * amount,
      company: budgetAccount.company,
      reference: Utils.generateRandomString(17).toLowerCase(),
      narration: description,
      ...(isBalanceDebited && { balance: bankAccount.Balance?.id || foundBalance.id }),
    };
    const transfer = await TransferRepo.createTransfer({
      data: transferPayload,
    });
    const recipientAndSender = {};
    if (amount < 0) {
      // Withdrawal from budget... Destination is balance

      // Send here will always be budget
      recipientAndSender.senderId = budgetAccount.externalIdentifier;
      recipientAndSender.senderType = budgetAccount.type;

      // Fund main wallet
      recipientAndSender.recipientId = bankAccount.externalBankAccountId;
      recipientAndSender.recipientType = bankAccount.type;
    } else {
      // Fund budget account
      recipientAndSender.senderId = bankAccount.externalBankAccountId || bankAccount.externalIdentifier;
      recipientAndSender.senderType = bankAccount.type;

      recipientAndSender.recipientId = budgetAccount.externalIdentifier;
      recipientAndSender.recipientType = budgetAccount.type;
    }

    const payload = {
      ...recipientAndSender,
      currency,
      amount: Math.abs(amount),
      reason: description,
      reference: transfer.reference,
      company: budgetAccount.company,
      ...(user && { user }),
      ...(isBalanceDebited && { balance: bankAccount.Balance?.code || foundBalance?.code || settlementBalance }),
      providerToUse,
    };

    const data = {
      data: payload,
      id: Utils.generateRandomString(17),
      path: `/transfers/bookTransfer/${transfer.code}/process`,
      key: process.env.INTRA_SERVICE_TOKEN,
    };

    // eslint-disable-next-line consistent-return
    return QueueService.addDelayedJob({}, data, `BookTransferReference:${payload.reference}`, 5);
  },

  async getBudgetAccount(criteria) {
    const { budget, ...rest } = criteria;
    const foundBudget = await Service.getBudget({ code: budget });
    const budgetAccount = await BudgetAccountRepo.getBudgetAccount({
      filter: { ...rest, budget: foundBudget.id },
    });
    if (!budgetAccount)
      return Service.createBudgetSubAccount({
        budget: foundBudget.id,
        company: foundBudget.company,
      });
    return budgetAccount;
  },

  async getBudgetPreviousBalances(budgetId) {
    const lastBudgetLedger = await BudgetLedgerRepo.getLastBudgetLedger(budgetId);
    if (lastBudgetLedger && lastBudgetLedger.balanceBefore)
      return {
        balanceBefore: lastBudgetLedger.balanceBefore,
        balanceAfter: lastBudgetLedger.balanceAfter,
      };

    const budget = await BudgetRepo.getBudget({ id: budgetId });
    return Service.getBudgetAvailableBalance(budget);
  },

  async getBudgetLedgersInternal(budgetCode) {
    const foundBudget = await BudgetRepo.getBudget({ code: budgetCode });
    if (!foundBudget) throw new NotFoundError("Budget");

    return BudgetLedgerRepo.listBudgetLedger({
      criteria: { budget: foundBudget.id },
      selectedOptions: ["code", "balanceBefore", "balanceAfter", "currency", "description", "amount", "status", "created_at"],
    });
  },

  async liquidateBudgetSubaccount({ criteria, transaction = null, user, balance = null }) {
    if (typeof criteria !== "object") throw new ValidationError("Criteria must be an object");
    if (!user) throw new ValidationError("Please specify user");
    const foundBudget = await BudgetRepo.getOneBudget({
      queryParams: criteria,
      selectOptions: ["isNewBudget", "available", "owner", "currency", "company", "amount", "balance"],
      transaction,
    });
    let foundUser;
    if (typeof user === "object" && user.id) foundUser = user;
    else {
      foundUser = await UserRepo.getOneUser({
        queryParams: {
          [Op.or]: {
            id: user,
            code: user,
          },
        },
      });
      if (!foundUser) throw new NotFoundError("User");
    }

    if (!foundBudget) throw new NotFoundError("Budget");
    const { currency, company, id: budgetId } = foundBudget;
    // Get budget account, this is to check if it has a budget accunt before calling the liquidate function
    const budgetAccount = await BudgetAccountRepo.getBudgetAccount({ filter: { budget: budgetId }, transaction });

    if (budgetAccount) {
      const { payment } = SettingsService.get("providers");
      const providerToUse = payment[foundBudget.company] || payment.defaultProvider;

      if ([STATUSES.MIGRATING, STATUSES.PROCESSING].includes(budgetAccount.status))
        throw new ValidationError(`We are still migrating this account, Please try again after some time`);

      if (budgetAccount.status === STATUSES.MIGRATED) throw new ValidationError(`This account has been migrated, Please reach out to support`);

      const { data, error } = await Providers[providerToUse].virtualAccount.getSubAccountBalance(budgetAccount.externalIdentifier);
      if (error) throw Providers[providerToUse].throwProviderError(data);
      const { availableBalance } = data;
      if (parseInt(availableBalance, 10) > 0)
        return Service.fundBudgetSubAccount(budgetId, -1 * availableBalance, currency, { description: "Liquidate budget", user: foundUser.code });
      return true;
    }
    // create ledger for the reversal
    const budgetAvailableBalance = await BudgetLedgerRepo.getBudgetAvailableBalance(foundBudget);
    const budgetLedger = await BudgetLedgerRepo.createBudgetLedger({
      payload: {
        currency,
        description: "Liquidate budget",
        budget: budgetId,
        amount: -1 * budgetAvailableBalance,
        user: foundUser.id,
        status: STATUSES.PROCESSED,
        balanceBefore: budgetAvailableBalance,
        balanceAfter: 0,
      },
      isSpent: false,
    });

    const foundBalance = await BalanceRepo.getBalance({
      filter: {
        currency,
        company,
        status: STATUSES.ACTIVE,
        ...(balance && typeof balance === "number" && { id: balance }),
        ...(balance && typeof balance === "string" && { code: balance }),
      },
    });

    if (!foundBalance) throw new ValidationError("Please select an account to liquidate budget to");

    const availableBalance = await BalanceRepo.getAvailableBalance({
      company,
      currency,
      ...(foundBalance && { id: foundBalance.id }),
    });

    return BalanceLedgerRepo.createLedger({
      company,
      currency,
      description: "Liquidate budget",
      amount: budgetAvailableBalance,
      status: STATUSES.PROCESSED,
      balance: foundBalance.id,
      balanceBefore: parseInt(availableBalance, 10),
      balanceAfter: parseInt(availableBalance, 10) + budgetAvailableBalance,
      budgetLedger: budgetLedger.id,
    });
  },

  async expireBudget(data) {
    const { code } = data;
    const scheduledBudget = await ScheduledBudgetRepo.getScheduledBudget({ queryParams: { code } });
    if (!scheduledBudget) return;
    if (scheduledBudget.status && scheduledBudget.status !== STATUSES.ACTIVE) return;
    if (scheduledBudget.type === "schedule") {
      // if startDate does not exist complete the Schedule
      if (!scheduledBudget.start_date) {
        await Service.updateScheduledBudgetStatus({ code, status: "completed" });
        return;
      }
    }
    await Service.deactivateBudget(scheduledBudget.Budget);
    if (scheduledBudget.type === "schedule") await Service.updateScheduledBudgetStatus({ code, status: "completed" });
  },

  async autoFundBudget(data) {
    const { code } = data;
    const scheduledBudget = await ScheduledBudgetRepo.getScheduledBudget({ queryParams: { code } });
    if (!scheduledBudget) return;
    if (scheduledBudget.status && scheduledBudget.status !== STATUSES.ACTIVE) return;

    // auto-fund budget
    await Service.fundBudget(data);

    await Service.updateNextExecutionDate({ code });
  },

  async initiateSchedule({ expiryDate, schedule, budgetExpirationCondition, cronExpressionArg, startDate, initiatedSchedule }) {
    if (expiryDate) {
      if (!Utils.firstDateIsAfterSecondDate(ScheduleService.combineDateTime(expiryDate.date, expiryDate.timestamp)))
        throw new ValidationError("Expiry Date has to be ahead of current date");
    }
    if (budgetExpirationCondition) {
      const { minutes, hours, dayOfMonth, month, dayOfWeek } = ScheduleService.convertDateToSchedule(expiryDate);
      schedule = { minutes, hours, dayOfMonth, month, dayOfWeek };
      expiryDate = ScheduleService.addMinutes(expiryDate, 0, "Africa/Lagos");
    } else {
      cronExpressionArg = ScheduleService.generateCronFromDuration(schedule);
      const nextScheduleDate = ScheduleService.generateNextDateFromCronExpression(cronExpressionArg.expression);
      startDate = ScheduleService.splitDateAndTimeStamp(nextScheduleDate);
    }
    const { data: createdBudgetSchedule } = await ScheduleService.validateAndInitiateScheduleRecord({
      recurring: !!schedule,
      schedule,
      startDate,
      cronExpressionArg,
    });
    initiatedSchedule = createdBudgetSchedule;

    return { expiryDate, schedule, cronExpressionArg, startDate, initiatedSchedule };
  },

  async createScheduledBudget({ expiryDate, initiatedSchedule, amount, id, company, budgetExpirationCondition, startDate }) {
    const { cronExpression, createdSchedule, isStartDateToday } = initiatedSchedule;
    // create schedule on Schedule Budget
    const createdBudgetSchedule = await ScheduledBudgetRepo.createScheduledBudget({
      queryParams: {
        amount,
        budget: id,
        company,
        type: budgetExpirationCondition ? "schedule" : "recurring",
        schedule_id: createdSchedule.id,
        start_date: startDate && new Date(ScheduleService.combineDateTime(startDate.date, startDate.timestamp)),
        expiry_date: expiryDate && new Date(ScheduleService.combineDateTime(expiryDate.date, expiryDate.timestamp)),
        status: STATUSES.PENDING,
      },
    });

    await BudgetRepo.updateABudget({
      queryParams: { id },
      updateFields: { schedule_id: createdBudgetSchedule.id },
    });

    if (budgetExpirationCondition) {
      await ScheduleService.createExpiryBudgetCron({
        createdSchedule,
        isStartDateToday,
        startDate,
        cronBody: {},
        cronExpression,
        expiryDate,
      });
    } else {
      await ScheduleService.createRecurringBudgetCron({
        createdSchedule,
        isStartDateToday,
        startDate,
        cronBody: {},
        cronExpression,
        expiryDate,
      });
    }
  },

  async updatedScheduledBudget({ expiryDate, initiatedSchedule, amount, id, company, budgetExpirationCondition, startDate }) {
    // this flow below deactivates an existing cron if it only existed for this schedule while taking care of current schedule
    const existingScheduledBudget = await ScheduledBudgetRepo.getScheduledBudget({
      queryParams: { budget: id },
    });

    let otherScheduleExists;

    if (existingScheduledBudget) {
      // check if other schedules exist.
      if (existingScheduledBudget.type === "schedule") {
        otherScheduleExists = await ScheduledBudgetRepo.scheduledBudgetExists({
          queryParams: {
            expiry_date: {
              [Op.between]: [
                dateFns.startOfDay(new Date(existingScheduledBudget.expiry_date)),
                dateFns.endOfDay(new Date(existingScheduledBudget.expiry_date)),
              ],
            },
            budget: {
              [Op.ne]: id,
            },
            status: STATUSES.ACTIVE,
            type: "schedule",
          },
        });
      } else {
        otherScheduleExists = await ScheduledBudgetRepo.scheduledBudgetExists({
          queryParams: {
            status: STATUSES.ACTIVE,
            type: "recurring",
            budget: {
              [Op.ne]: id,
            },
            expiry_date: {
              [Op.or]: [
                null,
                {
                  [Op.gt]: new Date(),
                },
              ],
            },
          },
          include: {
            model: Schedule,
            where: {
              cron_expression: existingScheduledBudget.Schedule && existingScheduledBudget.Schedule.cron_expression,
              status: STATUSES.ACTIVE,
            },
          },
        });
      }

      const promisesToExecute = [
        ScheduleRepo.updateSchedule({
          queryParams: { id: existingScheduledBudget.schedule_id },
          updateFields: { status: STATUSES.INACTIVE },
        }),
        ScheduledBudgetRepo.updateScheduledBudget({
          queryParams: { budget: id },
          updateFields: { reason: "Schedule updated", status: STATUSES.INACTIVE },
        }),
      ];

      // Disable cron if it only existed for this schedule
      if (!otherScheduleExists) {
        promisesToExecute.push(
          CronService.updateCron(existingScheduledBudget.Schedule && existingScheduledBudget.Schedule.cron_id, {
            activate: false,
          })
        );
      }

      await Promise.all(promisesToExecute);
    }

    return Service.createScheduledBudget({ expiryDate, initiatedSchedule, amount, id, company, budgetExpirationCondition, startDate });
  },

  async getBudgetDetails(criteria) {
    const foundBudget = await BudgetRepo.getBudgetDetailsInternal({ queryParams: criteria });
    if (!foundBudget) throw new NotFoundError("Budget");
    return foundBudget;
  },

  async getExpiredBudgets(filter = {}) {
    let { page = 1, perPage = 20 } = filter;
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    const skip = (page - 1) * perPage;

    const date = Utils.getUTCPlusOneTime();
    const endOfDayTime = dateFns.endOfDay(date);

    const include = [
      {
        model: ScheduledBudget,
        required: true,
        where: {
          expiry_date: {
            [Op.lte]: endOfDayTime,
          },
          status: STATUSES.ACTIVE,
          type: "schedule",
        },
        order: [["created_at", "DESC"]],
        attributes: ["expiry_date", "code", "type", "start_date"],
        include: [{ model: Schedule, attributes: ["cron_expression", "nextExecutionDate", "status"] }],
      },
    ];

    const [total, budgets] = await Promise.all([
      Budget.count({ include }),
      Budget.findAll({
        include,
        offset: skip,
        limit: parseInt(perPage, 10),
        order: [["created_at", "DESC"]],
      }),
    ]);

    return {
      budgets,
      meta: {
        page,
        perPage,
        total,
        nextPage: page + 1,
        hasMore: total > page * perPage,
      },
    };
  },

  async exportBudgetStatement(filter) {
    const { isManager, user, CompanyService, ...rest } = filter;
    const foundBudget = await BudgetRepo.getOneBudget({
      queryParams: {
        code: rest.source,
      },
    });
    if (!foundBudget) throw new NotFoundError("Budget");
    await HelperService.checkBudgetOwnerShip({ isManager, user }, foundBudget);
    return CompanyService.exportAccountStatement(rest);
  },

  async notifyBudgetCreator({ message, company, budget }) {
    const user = await User.findOne({
      where: {
        id: budget.owner,
      },
    });
    if (!user) throw new NotFoundError("User");
    const notificationPayload = {
      company: company.id,
      user_id: user.id,
      type: `info`,
      badge: `info`,
      title: `Budget Activity 📰`,
      message,
      table: {
        code: budget.code,
        entity: "Budget",
      },
      body: {
        code: budget.code,
        entity: "Budget",
      },
      event: "budgetActivity",
    };
    NotificationService.saveNotification(notificationPayload);
  },

  async canBudgetProcessTransaction(budget) {
    const foundBudget = await BudgetRepo.getBudget({ id: budget }, true);
    if (!foundBudget) throw new NotFoundError("Budget");
    const {
      Status: { value },
      BudgetAccount,
    } = foundBudget;
    if (foundBudget.status !== STATUSES.ACTIVE)
      throw new ValidationError(`Cannot make payment from this budget as it is ${String(value).toLowerCase()}`);
    if ([STATUSES.MIGRATING, STATUSES.PROCESSING].includes(BudgetAccount.status))
      throw new ValidationError(`We are still migrating this account, Please try again after some time`);
    if (BudgetAccount.status === STATUSES.MIGRATED) throw new ValidationError(`This account has been migrated, Please reach out to support`);
    return true;
  },

  async canBudgetHandleTransaction({ budget, amount, totalAmount, shouldCheckBalance = true }) {
    let foundBudget;
    if (!budget) throw new ValidationError("Please specify a budget to process this transaction");
    if (String(budget).startsWith("bdg_")) foundBudget = await Service.getBudget({ code: budget });
    else foundBudget = await Service.getBudget({ id: budget });

    if (!foundBudget) throw new NotFoundError("Budget");
    if (!foundBudget.isFunded) {
      if (foundBudget.spent + totalAmount <= foundBudget.amount + (foundBudget.buffer || 0))
        return responseUtils.sendObjectResponse("Budget has sufficient funds", { budget: foundBudget });
    }

    await Service.canBudgetProcessTransaction(foundBudget.id); // Checks if budget is ready to handle payment(Budget Account status)

    if (shouldCheckBalance) {
      const budgetBalance = await Service.getBudgetTransferableBalance(foundBudget);

      // Without adding fees, if budget balance is less than amount, throw error
      if (foundBudget.isFunded && budgetBalance < amount) {
        log(Log.bg.red, { message: "Insufficient Balance", meta: { budgetBalance, amount } });
        throw new ValidationError("Insufficient budget, please top up to make payment");
      }
      if (budgetBalance < totalAmount) {
        log(Log.bg.red, { message: "Insufficient Balance", meta: { budgetBalance, totalAmount } });
        // in the future check if the budget is the bearer first, if it the balance it goes to the next instruction
        throw new ValidationError("The budget cannot bear the fees of the transaction, please top up to complete your transaction");
      }
    }

    return responseUtils.sendObjectResponse("Budget has sufficient funds", { budget: foundBudget });
  },

  async processDirectDebitTopUp({ budgetAccount, existingMandate, amount, userId, currency, bankAccount, balance }) {
    const { bankCode, number: accountNumber } = budgetAccount;

    const directDebit = await DirectDebit.create({
      mandate: existingMandate.id,
      company: budgetAccount.company,
      amount: Math.abs(amount),
      beneficiaryAccountNumber: accountNumber,
      beneficiaryBankCode: bankCode,
      narration: `Direct debit top up to budget`,
      status: STATUSES.PENDING,
      reference: Utils.generateRandomString(14),
    });

    const transactionPayload = {
      amount: Math.abs(amount),
      description: directDebit.narration,
      payer: userId,
      company: budgetAccount.company,
      currency,
      bank_account: bankAccount.id,
      status: STATUSES.PENDING,
      narration: directDebit.narration,
      type: TRANSACTION_TYPES.DIRECT_DEBIT,
      directDebitId: directDebit.id,
      balance,
    };

    const { transaction } = await BankService.directDebit(
      { id: budgetAccount.company },
      {
        bankCode: directDebit.beneficiaryBankCode,
        accountNumber: directDebit.beneficiaryAccountNumber,
        narration: directDebit.narration,
        amount: directDebit.amount,
        createdDirectDebit: directDebit,
        transactionPayload,
      }
    );

    transaction.status = STATUSES.PROCESSING;
    await transaction.save();
  },
};

module.exports = Service;
