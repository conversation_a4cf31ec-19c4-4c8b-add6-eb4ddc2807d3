/* eslint-disable camelcase */
const FormData = require("form-data");
const axios = require("axios");
const { parsePhoneNumber } = require("libphonenumber-js");
const randomstring = require("randomstring");
const {
  UserRepo,
  CompanyRepo,
  AddressRepo,
  DocumentRepo,
  IndividualRepo,
  OnboardingInviteRepo,
  AccountHolderRepo,
} = require("../repositories/index.repo");
const { STATUSES } = require("../models/status");
const responseUtils = require("../utils/response.utils");
const { ValidationError, HttpException, HttpStatus, NotFoundError } = require("../utils/error.utils");
const Helper = require("./helper.service");
const { SmileIdIntegrator } = require("../integrations");
const CompanyService = require("./company");
const AssetService = require("./asset");
const { log, Log } = require("../utils/logger.utils");
const Sanitizer = require("../utils/sanitizer");
const Utils = require("../utils/utils");
const NotificationService = require("./notification");
const Providers = require("./providers");
const SettingsService = require("./settings");

const { BusinessOnboardingValidator, CompanyValidator } = require("../validators");
const { ONBOARDING_LEVEL } = require("../models/company");
const { DOCUMENTS } = require("../models/document");

const Service = {
  idTypes: "nin" || "vi" || "dl",
  idNameType: {
    nin: SmileIdIntegrator.smileIDType.NIN,
    vi: SmileIdIntegrator.smileIDType.VOTER,
    dl: SmileIdIntegrator.smileIDType.DRIVERS,
    TIN: SmileIdIntegrator.smileBusinessIDType.TIN,
    CAC: SmileIdIntegrator.smileBusinessIDType.CAC,
  },
  findDocByType(docsArray, docType) {
    return docsArray.find(({ type }) => type === docType);
  },

  async createDocsReference(existingCompany, documents) {
    const reference = existingCompany.document_reference || Utils.generateRandomString(6);
    let checkForDocs;
    if (existingCompany.document_reference) {
      checkForDocs = await Helper.DocumentChecker(
        {
          reference: existingCompany.document_reference,
          documents,
        },
        true
      );
      if (checkForDocs && !checkForDocs.data.checks) return responseUtils.sendObjectResponse("All Documents has been Submitted", existingCompany);
    }
    return { reference, checkForDocs };
  },

  async checkBvn(bvn) {
    const existingDirectorsBvn = await DocumentRepo.getOneDocument({
      queryParams: { table_type: "individual", type: "bvn", number: bvn },
    });
    if (existingDirectorsBvn) throw new HttpException("Your BVN exists for another account", HttpStatus.NOT_ACCEPTABLE);
  },

  async saveOnboardingDocument(type, data) {
    const { assetCode = null, processor = "anchor", number = null, entity, entityId, reference, issuingDate = null } = data;
    let asset;

    // Finds an Asset using an Asset Code
    if (assetCode) {
      const assetFound = await AssetService.getAsset(assetCode);
      if (!assetFound) throw new NotFoundError("Asset");
      asset = assetFound.id;
    }

    const basicDocumentQuery = {
      type,
      table_type: entity,
      table_id: entityId,
      status: STATUSES.UNVERIFIED,
      reference,
      processor,
    };
    await DocumentRepo.updateADocument({
      queryParams: basicDocumentQuery,
      updateFields: { status: STATUSES.DELETED },
    });

    const directorsUtilityBill = {
      number,
      ...(assetCode && { asset }),
      issuing_date: issuingDate,
      status: STATUSES.UNVERIFIED,
      ...basicDocumentQuery,
    };
    log(Log.fg.blue, `store ${type} docs`);

    return DocumentRepo.createADocument({ queryParams: directorsUtilityBill });
  },

  async createDirector({
    dateOfBirth = null,
    existingCompany,
    docsBVN,
    verifyDirectorsDocs,
    directorsEmail,
    phoneNumber,
    directorsName,
    reference,
    docsUtility,
    directorPercentage: percentageOwned = null,
  }) {
    log(Log.fg.blue, `create director`);
    const { firstName, lastName, phoneNumber: directorsPhone } = directorsName;
    const directorCriteria = {};
    if (existingCompany.director) directorCriteria.id = existingCompany.director;
    else {
      directorCriteria.document = docsBVN.id;
      directorCriteria.type = "director";
    }

    let director = await IndividualRepo.getOneIndividual({
      queryParams: directorCriteria,
      selectOptions: ["document", "phoneNumber", "address", "metadata"],
    });
    if (director && director.email === directorsEmail) {
      await IndividualRepo.updateAIndividual({
        queryParams: { code: director.code },
        updateFields: {
          document: docsBVN.id,
          address: existingCompany.address,
          ...(!director.phoneNumber && { phoneNumber: directorsPhone }),
          percentageOwned,
          dob: dateOfBirth,
          company: existingCompany.id,
        },
      });
    } else {
      director = await IndividualRepo.createAIndividual({
        queryParams: {
          type: "director",
          firstName,
          lastName,
          status: STATUSES.UNVERIFIED,
          document: docsBVN.id,
          phoneNumber,
          address: existingCompany.address,
          metadata: {
            directorsEmail,
          },
          percentageOwned,
          dob: dateOfBirth,
          company: existingCompany.id,
        },
      });
    }

    // Update Director Document
    await Promise.all([
      verifyDirectorsDocs &&
        DocumentRepo.updateADocument({
          queryParams: { code: verifyDirectorsDocs.code, reference },
          updateFields: { table_id: director.id },
        }),
      docsBVN &&
        DocumentRepo.updateADocument({
          queryParams: { code: docsBVN.code, reference },
          updateFields: { table_id: director.id },
        }),
      docsUtility &&
        DocumentRepo.updateADocument({
          queryParams: { code: docsUtility.code, reference },
          updateFields: { table_id: director.id },
        }),
    ]);

    return director;
  },

  /**
   * Requirements
   * @type {
   * [{
   *      name: string,
   *      description: string,
   *      url: string,
   *      business_size: string,
   *      company_code: string,
   *      industry: number,
   *      id: number
   *  }]
   * }
   * <AUTHOR> - created
   */
  async createBusinessInformation({ name, industry, description, url, business_size, id, company_code }) {
    const { error } = BusinessOnboardingValidator.businessInformation.validate({
      name,
      industry,
      description,
      url,
      business_size,
      id,
      company_code,
    });
    if (error) throw new ValidationError(error.message);

    const companyLastName = name.split(" ");
    if (companyLastName.length === 1) throw new ValidationError("Please provide your legal business name in full");

    const { data: user } = await Helper.UserChecker({ id });
    const { data: existingCompany } = await Helper.CompanyChecker({
      company_code,
    });

    let existingIndustry;
    if (String(industry).includes("idt_" || "ind_")) {
      const { data: existing } = await Helper.IndustryChecker({
        industry_code: industry,
      });
      existingIndustry = existing;
    }

    const companyQuery = { code: company_code, id: existingCompany.id };

    const [updatedCompany] = await CompanyRepo.updateACompany({
      queryParams: companyQuery,
      updateFields: {
        ...(name && { name }),
        ...(industry && { industry: existingIndustry.id || industry }),
        ...(description && { description }),
        ...(url && { website: url }),
        ...(business_size && { size: business_size }),
      },
    });

    if (!updatedCompany) return responseUtils.sendObjectResponse("Company Info already updated");
    // Get return Data
    const getCompany = await CompanyRepo.getOneCompany({
      queryParams: companyQuery,
      selectOptions: ["contact_email", "contact_phone"],
    });
    if (!getCompany) throw new HttpException("Company not found", HttpStatus.NOT_FOUND);

    return responseUtils.sendObjectResponse("Company Info successfully updated", getCompany);
  },

  /**
   * Requirements
   * @type {
   * [
   *  {
   *      country_code: string,
   *      state: string,
   *      city: string,
   *      address: string,
   *      contact_email: string,
   *      phone: string,
   *      company_code: string,
   *      contact_details: boolean,
   *      id: number
   *  }]
   * }
   * <AUTHOR> - created
   */
  async createBusinessAddress({
    country_code,
    state,
    city,
    address,
    contact_email,
    phone,
    contact_details,
    id,
    company_code,
    postalCode,
    utilityBill,
  }) {
    const { error } = BusinessOnboardingValidator.businessAddress.validate({
      country_code,
      state,
      city,
      address,
      contact_email,
      phone,
      contact_details,
      id,
      company_code,
      postalCode,
      utilityBill,
    });
    if (error) throw new ValidationError(error.message);

    // Checkers
    const { data: user } = await Helper.UserChecker({ id });
    const { data: existingCompany } = await Helper.CompanyChecker({
      company_code,
    });

    const companyQuery = { code: company_code };

    // Save to Address table
    const createdAddress = await Helper.findOrCreateAddress(
      {
        street: address,
        state,
        city,
        country: country_code,
        postalCode,
      },
      true
    );

    // phone number
    const phoneNumberObj = phone && (await Helper.findOrCreatePhoneNumber(phone));

    // Update Company table
    const { internationalFormat, localFormat, countryCode, id: phoneId } = phoneNumberObj;

    const existingDirector =
      existingCompany.director &&
      (await IndividualRepo.getOneIndividual({
        queryParams: { id: existingCompany.director, type: "director" },
      }));

    const reference = existingCompany.document_reference || Utils.generateRandomString(6);
    const docsUtility =
      utilityBill &&
      (await Service.saveOnboardingDocument("utility-Bill", {
        assetCode: utilityBill,
        entity: "business",
        entityId: existingCompany.id,
        reference,
      }));

    await CompanyRepo.updateACompany({
      queryParams: companyQuery,
      updateFields: {
        address: createdAddress.id,
        ...(docsUtility && { incorporation: docsUtility.id }),
        document_reference: reference,
        ...(existingDirector && { director: existingDirector.id }),
        ...(contact_email && { contactEmail: contact_email }),
        ...(phone && typeof phone === "string" && { contact_phone: phone }),
        ...(phone &&
          typeof phone === "object" && {
            phoneNumber: phoneId,
            contact_phone: internationalFormat || `${countryCode}${localFormat}`,
          }),
      },
    });

    // Get return Data
    const getCompany = await CompanyRepo.getOneCompany({
      queryParams: companyQuery,
      selectOptions: ["contact_email", "contact_phone"],
    });
    if (!getCompany) throw new HttpException("Company not found", HttpStatus.NOT_FOUND);

    delete getCompany.dataValues.id;
    return responseUtils.sendObjectResponse("Company Address successfully updated", getCompany);
  },

  async storeEnterpriseDocs(payload) {
    log(Log.fg.blue, `starting storeSoleProprietorshipDocs onboarding`);
    const { error } = CompanyValidator.storeDocs.validate(payload);
    if (error) throw new ValidationError(error.message);

    // expand payload
    const { id, company_code, document, type } = payload;
    const { companyRegistrationDate } = document;
    const companyQuery = { code: company_code };

    // Checkers
    const { data: user } = await Helper.UserChecker({ id });
    const { data: existingCompany } = await Helper.CompanyChecker({
      company_code,
    });

    // Update Company table
    let updatedCompany;
    updatedCompany = await CompanyRepo.updateACompany({
      queryParams: companyQuery,
      updateFields: {
        businessType: type,
        dateOfRegistration: companyRegistrationDate,
      },
    });

    // Get return Data
    const getCompany = await CompanyRepo.getOneCompany({
      queryParams: companyQuery,
      selectOptions: ["contact_email", "contact_phone"],
    });
    if (!getCompany) throw new NotFoundError("Company");

    log(Log.bg.cyan, ` Completed `);
    return responseUtils.sendObjectResponse("Company Docs successfully updated", getCompany);
  },

  async storeSoleProprietorshipDocs(payload) {
    log(Log.fg.blue, `starting storeSoleProprietorshipDocs onboarding`);
    const { error } = CompanyValidator.storeDocs.validate(payload);
    if (error) throw new ValidationError(error.message);

    // expand payload
    const { id, company_code, document, type } = payload;
    const { bnNumber, cac, cacBn1, companyRegistrationDate, utilityBill } = document;
    const companyQuery = { code: company_code };

    // Checkers
    const { data: user } = await Helper.UserChecker({ id });
    const { data: existingCompany } = await Helper.CompanyChecker({
      company_code,
    });

    const reference = existingCompany.document_reference || Utils.generateRandomString(6);

    const businessDocsData = {
      entity: "business",
      entityId: existingCompany.id,
      reference,
    };
    const [docsCAC, docsBnNumber] = await Promise.all([
      Service.saveOnboardingDocument("cac", {
        assetCode: cac,
        ...businessDocsData,
      }),
      Service.saveOnboardingDocument("bnNumber", {
        number: bnNumber,
        ...businessDocsData,
      }),
      Service.saveOnboardingDocument("cacBn1", {
        assetCode: cacBn1,
        ...businessDocsData,
      }),
      Service.saveOnboardingDocument("utility-Bill", {
        assetCode: utilityBill,
        ...businessDocsData,
      }),
    ]);

    // Update Company table
    let updatedCompany;
    updatedCompany = await CompanyRepo.updateACompany({
      queryParams: companyQuery,
      updateFields: {
        document_reference: reference,
        businessType: type,
        dateOfRegistration: companyRegistrationDate,
      },
    });

    // Get return Data
    const getCompany = await CompanyRepo.getOneCompany({
      queryParams: companyQuery,
      selectOptions: ["contact_email", "contact_phone"],
    });
    if (!getCompany) throw new NotFoundError("Company");

    log(Log.bg.cyan, ` Completed `);
    return responseUtils.sendObjectResponse("Company Docs successfully updated", getCompany);
  },

  async storeNgoDocs(payload) {
    log(Log.fg.blue, `starting storeNgoDocs onboarding`);
    const { error } = CompanyValidator.storeDocs.validate(payload);
    if (error) throw new ValidationError(error.message);

    // expand payload
    const { id, company_code, document, type } = payload;
    const { utilityBill, cacITNumber, companyRegistrationDate, cacITForm1, scumlCertificate, certificateOfTrustees } = document;
    const companyQuery = { code: company_code };

    // Checkers
    const { data: user } = await Helper.UserChecker({ id });
    const { data: existingCompany } = await Helper.CompanyChecker({
      company_code,
    });

    const reference = existingCompany.document_reference || Utils.generateRandomString(6);

    const businessDocsData = {
      entity: "business",
      entityId: existingCompany.id,
      reference,
    };
    const [docsRC, docsCacITForm1, docsScumlCertificate] = await Promise.all([
      Service.saveOnboardingDocument("cacITNumber", {
        number: cacITNumber,
        ...businessDocsData,
      }),
      Service.saveOnboardingDocument("cacITForm1", {
        assetCode: cacITForm1,
        ...businessDocsData,
      }),
      Service.saveOnboardingDocument("Scum_L_C", {
        assetCode: scumlCertificate,
        ...businessDocsData,
      }),
      Service.saveOnboardingDocument("C_of_T", {
        assetCode: certificateOfTrustees,
        ...businessDocsData,
      }),
      Service.saveOnboardingDocument("utility-Bill", {
        assetCode: utilityBill,
        ...businessDocsData,
      }),
    ]);

    // Update Company table
    const updatedCompany = await CompanyRepo.updateACompany({
      queryParams: companyQuery,
      updateFields: {
        document_reference: reference,
        businessType: type,
        dateOfRegistration: companyRegistrationDate,
      },
    });

    // Get return Data
    const getCompany = await CompanyRepo.getOneCompany({
      queryParams: companyQuery,
      selectOptions: ["contact_email", "contact_phone"],
    });
    if (!getCompany) throw new NotFoundError("Company");

    log(Log.bg.cyan, ` Completed `);
    return responseUtils.sendObjectResponse("Company Docs successfully updated", getCompany);
  },

  async storePartnershipDocs(payload) {
    log(Log.fg.blue, `starting storePartnershipDocs onboarding`);
    const { error } = CompanyValidator.storeDocs.validate(payload);
    if (error) throw new ValidationError(error.message);

    // expand payload
    const { id, company_code, document, type } = payload;
    const { bnNumber, bnDocument, cacBn1, companyRegistrationDate } = document;
    const companyQuery = { code: company_code };

    // Checkers
    const { data: existingCompany } = await Helper.CompanyChecker({
      company_code,
    });

    const reference = existingCompany.document_reference || Utils.generateRandomString(6);

    const businessDocsData = {
      entity: "business",
      entityId: existingCompany.id,
      reference,
    };

    const [docsBnNumber] = await Promise.all([
      Service.saveOnboardingDocument("bnNumber", {
        number: bnNumber,
        assetCode: bnDocument,
        ...businessDocsData,
      }),
      Service.saveOnboardingDocument("cacBn1", {
        assetCode: cacBn1,
        ...businessDocsData,
      }),
    ]);

    let updatedCompany;
    updatedCompany = await CompanyRepo.updateACompany({
      queryParams: companyQuery,
      updateFields: {
        document_reference: reference,
        businessType: type,
        dateOfRegistration: companyRegistrationDate,
      },
    });

    // Get return Data
    const getCompany = await CompanyRepo.getOneCompany({
      queryParams: companyQuery,
      selectOptions: ["contact_email", "contact_phone"],
    });
    if (!getCompany) throw new NotFoundError("Company");

    log(Log.bg.cyan, ` Completed `);
    return responseUtils.sendObjectResponse("Company Docs successfully updated", getCompany);
  },

  async storeLLCDocs(payload) {
    log(Log.fg.blue, `starting storeLLCDocs onboarding`);
    const { error } = CompanyValidator.storeDocs.validate(payload);

    if (error) throw new ValidationError(error.message);

    // expand payload
    const { id, company_code, document, type } = payload;
    const {
      rcNumber,
      incorporationCertificate,
      taxIdentificationNumber,
      companyRegistrationDate,
      memorandumOfAssociation,
      cacForm3,
      cacForm2,
      cacForm7,
      cacStatusReport,
      cacForm1,
      utilityBill,
    } = document;
    const companyQuery = { code: company_code };

    const docTypes = {
      rcNumberExist: "existingDocsRC",
      incorp_CExist: "existingDocsIncorporationCertificate",
      tinExist: "existingDocsTaxIdentificationNumber",
      moaExist: "existingDocsMemorandumOfAssociation",
      utilityBillExist: "existingUtilityBill",
    };

    const isBefore2017 = Utils.firstDateIsBeforeSecondDate(new Date(companyRegistrationDate).getTime(), new Date("2017-01-01").getTime());
    if (isBefore2017) {
      const { error } = CompanyValidator.cacForms.validate({
        cacForm3,
        cacForm2,
        cacForm7,
      });
      if (error) throw new ValidationError(error.message);

      docTypes.cacForm3Exist = "existingDocsCacForm3";
      docTypes.cacForm2Exist = "existingDocsCacForm2";
      docTypes.cacForm7Exist = "existingDocsCacForm7";
    }
    const isBetween2017And2020 = Utils.isWithinInterval(
      new Date(companyRegistrationDate).getTime(),
      new Date("2017-01-01").getTime(),
      new Date("2020-11-30").getTime()
    );
    if (isBetween2017And2020) {
      const { error } = CompanyValidator.cacForm1.validate({ cacForm1 });
      if (error) throw new ValidationError(error.message);

      docTypes.cacForm1Exist = "existingDocsCacForm1";
    }
    const isAfter2020 = Utils.firstDateIsAfterSecondDate(new Date(companyRegistrationDate).getTime(), new Date("2020-12-31").getTime());
    if (isAfter2020) {
      const { error } = CompanyValidator.cacStatus.validate({
        cacStatusReport,
      });
      if (error) throw new ValidationError(error.message);
      docTypes.cacStatusReportExist = "existingDocsCacStatusReport";
    }

    // Checkers
    const { data: user } = await Helper.UserChecker({ id });
    const { data: existingCompany } = await Helper.CompanyChecker({
      company_code,
    });

    // reference and docs Checkers
    const createdReference = await Service.createDocsReference(Sanitizer.sanitizeCompany(existingCompany), {
      rcNumber,
      incorporationCertificate,
      taxIdentificationNumber,
      memorandumOfAssociation,
      cacForm3,
      cacForm2,
      cacForm7,
      cacForm1,
      cacStatusReport,
      utilityBill,
    });

    const { success, reference, checkForDocs } = createdReference;
    if (success) return createdReference;

    const referenceDocs = {};
    if (checkForDocs) {
      for (const [key, value] of Object.entries(docTypes)) {
        const docExists = checkForDocs.data[key];
        if (docExists) referenceDocs[value] = docExists;
      }
    }

    log(Log.fg.blue, `store docs for Directors`);

    // directors store Individual Details
    log(Log.fg.blue, `store docs for Business`);
    let docsRC;
    let docsIncorporationCertificate;
    const requiredDocs = [
      {
        name: "utility-Bill",
        doc: "existingUtilityBill",
        value: utilityBill,
        number: false,
      },
      {
        name: "rcNumber",
        doc: "existingDocsRC",
        value: rcNumber,
        number: true,
      },
      {
        name: "incorp_C",
        doc: "existingDocsIncorporationCertificate",
        value: incorporationCertificate,
        number: false,
      },
      {
        name: "moa",
        doc: "existingDocsMemorandumOfAssociation",
        value: memorandumOfAssociation,
        number: false,
      },
      {
        name: "tin",
        doc: "existingDocsTaxIdentificationNumber",
        value: taxIdentificationNumber,
        number: true,
      },
    ];
    if (isBefore2017)
      requiredDocs.push(
        {
          name: "cacForm3",
          doc: "existingDocsCacForm3",
          value: cacForm3,
          number: false,
        },
        {
          name: "cacForm2",
          doc: "existingDocsCacForm2",
          value: cacForm2,
          number: false,
        },
        {
          name: "cacForm7",
          doc: "existingDocsCacForm7",
          value: cacForm7,
          number: false,
        }
      );

    if (isAfter2020)
      requiredDocs.push({
        name: "cacStatusReport",
        doc: "existingDocsCacStatusReport",
        value: cacStatusReport,
        number: false,
      });

    if (isBetween2017And2020)
      requiredDocs.push({
        name: "cacForm1",
        docs: "existingDocsCaCForm1",
        number: false,
        value: cacForm1,
      });
    const [RCdocs, IncorporationCertificateDocs] = await Promise.all(
      requiredDocs.map(
        ({ name, doc, value, number }) =>
          !referenceDocs[doc] &&
          Service.saveOnboardingDocument(name, {
            ...(number ? { number: value } : { assetCode: value }),
            entity: "business",
            entityId: existingCompany.id,
            reference,
          })
      )
    );

    docsRC = RCdocs;
    docsIncorporationCertificate = IncorporationCertificateDocs;

    if (!docsRC)
      docsRC = await DocumentRepo.getOneDocument({
        queryParams: {
          table_id: existingCompany.id,
          table_type: "business",
          type: "rcNumber",
          reference,
        },
      });
    if (!docsIncorporationCertificate)
      docsIncorporationCertificate = await DocumentRepo.getOneDocument({
        queryParams: {
          table_id: existingCompany.id,
          table_type: "business",
          type: "incorp_C",
          reference,
        },
      });

    await CompanyRepo.updateACompany({
      queryParams: companyQuery,
      updateFields: {
        document_reference: reference,
        businessType: type,
        dateOfRegistration: companyRegistrationDate,
      },
    });

    log(Log.fg.blue, `Mailing to Back-Office`);
    // Get return Data
    const getCompany = await CompanyRepo.getOneCompany({
      queryParams: companyQuery,
      selectOptions: ["contact_email", "contact_phone"],
    });
    if (!getCompany) throw new HttpException("Company not found", HttpStatus.NOT_FOUND);

    log(Log.bg.blue, `Completed`);
    delete getCompany.dataValues.id;
    return responseUtils.sendObjectResponse("Company Docs successfully updated", getCompany);
  },

  async getDocs({ id, addDocs, company_code, addAddress, docsCode = null }) {
    const { error } = CompanyValidator.getDetails.validate({
      id,
      addDocs,
      company_code,
      addAddress,
    });
    if (error) throw new ValidationError(error.message);

    await Helper.UserChecker({ id });
    const companyQuery = { code: company_code };
    const existingCompany = await CompanyRepo.getOneCompany({
      queryParams: companyQuery,
      selectOptions: ["code", "name", "name", "industry", "document_reference"],
      addIndustry: true,
      addPhoneNumber: true,
    });
    if (!existingCompany) throw new HttpException("Company not found", HttpStatus.NOT_FOUND);
    let docs;
    let address;
    if (addDocs) {
      docs = await DocumentRepo.getAllDocuments({
        queryParams: {
          ...(!existingCompany.document_reference && {
            table_id: existingCompany.id,
            table_type: "business",
          }),
          ...(existingCompany.document_reference && {
            reference: existingCompany.document_reference,
          }),
        },
        selectOptions: ["code", "type", "number", "url", "country", "status", "table_type", "table_id", "metadata", "reference"],
      });
    }
    if (docsCode) {
      docs = Sanitizer.sanitizeDocument(
        await DocumentRepo.getOneDocument({
          queryParams: { code: docsCode },
          selectOptions: ["code", "type", "number", "url", "country", "status", "table_type", "table_id", "metadata", "reference"],
        })
      );
    }
    if (addAddress)
      address = await AddressRepo.getAllAddress({
        queryParams: { company: existingCompany.id },
        selectOptions: ["code", "street", "city", "state", "company", "country"],
      });

    delete existingCompany.dataValues.id;
    const response = {
      ...(!docsCode && { existingCompany }),
      ...(addDocs && { docs }),
      ...(docsCode && { docs }),
      ...(addAddress && { address }),
    };
    return responseUtils.sendObjectResponse("Company Details successfully retrieved", response);
  },

  async reviewDocs({ id, company_code, docsCode }) {
    const { error } = CompanyValidator.reviewDocs.validate({
      id,
      company_code,
      docsCode,
    });
    if (error) throw new ValidationError(error.message);

    return Service.getDocs({ id, company_code, docsCode });
  },

  async rejectedDocs({ id, company_code }) {
    await Helper.UserChecker({ id });
    const companyQuery = { code: company_code };
    const existingCompany = await CompanyRepo.getOneCompany({
      queryParams: companyQuery,
      selectOptions: ["code", "name", "name", "industry", "document_reference"],
      addIndustry: true,
      addPhoneNumber: true,
    });
    if (!existingCompany) throw new HttpException("Company not found", HttpStatus.NOT_FOUND);

    const docs = await DocumentRepo.getAllDocuments({
      queryParams: {
        reference: existingCompany.document_reference,
        response: "Rejected",
        expiryDate: null,
        status: STATUSES.REJECTED,
      },
      selectOptions: ["code", "type", "number", "url", "country", "status", "table_type", "table_id", "metadata", "reference"],
    });

    if (!docs.length) return responseUtils.sendObjectResponse("No rejected documents found");

    const response = {
      existingCompany,
      docs,
    };
    return responseUtils.sendObjectResponse("Company Details successfully retrieved", response);
  },

  async docsResubmission({ id, company_code, docsCode, newDocument }) {
    const { error } = CompanyValidator.rejectedDocs.validate({
      id,
      company_code,
      docsCode,
      newDocument,
    });
    if (error) throw new ValidationError(error.message);

    await Helper.UserChecker({ id });
    const companyQuery = { code: company_code };
    const existingCompany = await CompanyRepo.getOneCompany({
      queryParams: companyQuery,
      selectOptions: ["code", "name", "name", "industry", "document_reference"],
      addIndustry: true,
      addPhoneNumber: true,
    });

    if (!existingCompany) throw new HttpException("Company not found", HttpStatus.NOT_FOUND);

    const submission = await Helper.DocumentReviewer({
      reference: existingCompany.document_reference,
      oldDocument: docsCode,
      newDocument,
    });

    if (!submission || !submission.success)
      return responseUtils.sendObjectResponse(submission.error || "Error while submitting document, please try again");

    return submission;
  },

  async inviteSubmission({ payload, company: existingCompany }) {
    log(Log.fg.blue, `Saving Invited Director Document`);
    // expand payload
    const { firstName, lastName, phoneNumber, email, invite, documents } = payload;
    const {
      bnNumber,
      cac,
      bvn,
      idType,
      idNumber,
      idCopy,
      utilityBill,
      issuingDate,
      rcNumber,
      cacITNumber,
      cacITForm1,
      certificateOfTrustees,
      scumlCertificate,
      memorandumOfAssociation,
      cacBn1,
      dateOfBirth,
      directorPercentage,
      cacForm3,
      cacForm2,
      cacForm7,
      cacStatusReport,
      cacForm1,
    } = documents;
    let { companyRegistrationDate } = documents;
    const companyQuery = { code: existingCompany.code };

    // Checkers
    const { dateOfRegistration } = await CompanyRepo.getOneCompany({ queryParams: { id: existingCompany.id } });
    const companyAdmin = await UserRepo.getOneUser({
      queryParams: { company: existingCompany.id, role: "admin" },
    });

    if (dateOfRegistration) companyRegistrationDate = companyRegistrationDate || dateOfRegistration;

    const { document_reference: reference, id: companyId, director: directorId, incorporation, businessType } = existingCompany;

    // Check BVN
    if (bvn) {
      const existingDirectorsBvn = await DocumentRepo.getOneDocument({
        queryParams: { table_type: "individual", type: "bvn", number: bvn },
        selectOptions: ["table_id", "table_type"],
      });
      if (existingDirectorsBvn) {
        const existingIndividual = await IndividualRepo.getOneIndividual({
          queryParams: { id: existingDirectorsBvn.table_id },
          selectOptions: ["firstName", "lastName"],
        });
        const existingNameArray = [existingIndividual.firstName, existingIndividual.lastName];
        if (!(existingNameArray.includes(firstName) && existingNameArray.includes(lastName))) throw new ValidationError("Kindly verify your BVN");
      }
    }

    const documentDefaultData = {
      reference,
      entity: "individual",
      entityId: companyAdmin.id,
    };

    const businessDocumentData = {
      reference,
      entity: "business",
      entityId: companyId,
    };
    // Store Docs on Details on Doc Table
    log(Log.fg.blue, `store docs`);
    const [docsBVN, docsIdCard, docsUtility, docsCAC, docsBnNumber, docsRC, docsCacITForm1, docsCertificateOfTrustees, docsScumlCertificate] =
      await Promise.all([
        bvn &&
          Service.saveOnboardingDocument("bvn", {
            number: bvn,
            ...documentDefaultData,
          }),
        idType &&
          Service.saveOnboardingDocument(idType, {
            number: idNumber,
            assetCode: idCopy,
            issuingDate,
            ...documentDefaultData,
          }),
        utilityBill &&
          Service.saveOnboardingDocument("utility-Bill", {
            assetCode: utilityBill,
            ...documentDefaultData,
          }),
        cac &&
          Service.saveOnboardingDocument("cac", {
            assetCode: cac,
            ...businessDocumentData,
          }),
        bnNumber &&
          Service.saveOnboardingDocument("bnNumber", {
            number: bnNumber,
            ...businessDocumentData,
          }),
        rcNumber &&
          Service.saveOnboardingDocument("rcNumber", {
            number: rcNumber,
            ...businessDocumentData,
          }),
        cacITNumber &&
          Service.saveOnboardingDocument("cacITNumber", {
            number: cacITNumber,
            ...businessDocumentData,
          }),
        cacITForm1 &&
          Service.saveOnboardingDocument("cacITForm1", {
            assetCode: cacITForm1,
            ...businessDocumentData,
          }),
        certificateOfTrustees &&
          Service.saveOnboardingDocument("C_of_T", {
            assetCode: certificateOfTrustees,
            ...businessDocumentData,
          }),
        scumlCertificate &&
          Service.saveOnboardingDocument("Scum_L_C", {
            assetCode: scumlCertificate,
            ...businessDocumentData,
          }),
        memorandumOfAssociation &&
          Service.saveOnboardingDocument("moa", {
            assetCode: memorandumOfAssociation,
            ...businessDocumentData,
          }),
        cacBn1 &&
          Service.saveOnboardingDocument("cacBn1", {
            assetCode: cacBn1,
            ...businessDocumentData,
          }),
      ]);

    if (
      businessType === "limited liability" &&
      Utils.firstDateIsBeforeSecondDate(new Date(companyRegistrationDate).getTime(), new Date("2017-01-01").getTime())
    ) {
      await Promise.all([
        cacForm3 &&
          Service.saveOnboardingDocument("cacForm3", {
            assetCode: cacForm3,
            ...businessDocumentData,
          }),
        cacForm2 &&
          Service.saveOnboardingDocument("cacForm2", {
            assetCode: cacForm2,
            ...businessDocumentData,
          }),
        cacForm7 &&
          Service.saveOnboardingDocument("cacForm7", {
            assetCode: cacForm7,
            ...businessDocumentData,
          }),
      ]);
    }

    if (
      businessType === "limited liability" &&
      Utils.firstDateIsAfterSecondDate(new Date(companyRegistrationDate).getTime(), new Date("2020-12-31").getTime())
    ) {
      await Promise.all([
        cacStatusReport &&
          Service.saveOnboardingDocument("cacStatusReport ", {
            assetCode: cacStatusReport,
            ...businessDocumentData,
          }),
      ]);
    }

    if (businessType === "limited liability" && Utils.isWithinInterval(new Date("2017-01-01").getTime(), new Date("2020-11-30").getTime())) {
      await Promise.all([
        cacStatusReport &&
          Service.saveOnboardingDocument("cacStatusReport ", {
            assetCode: cacStatusReport,
            ...businessDocumentData,
          }),
      ]);
    }

    // Create Director Phone Number
    const { id: directorPhone } = await Helper.findOrCreatePhoneNumber(phoneNumber);
    // directors store Individual Details
    const director = await Service.createDirector({
      dateOfBirth,
      directorPercentage,
      docsUtility,
      existingCompany,
      docsBVN,
      verifyDirectorsDocs: docsIdCard,
      directorsEmail: email,
      phoneNumber: directorPhone,
      directorsName: { firstName, lastName, phoneNumber },
      reference,
    });

    // Update Company table
    await CompanyRepo.updateACompany({
      queryParams: companyQuery,
      updateFields: {
        businessType,
        document_reference: reference,
        director: !directorId && director.id,
        incorporation: (!incorporation && docsUtility && docsUtility.id) || (docsCAC && docsCAC.id) || (docsRC && docsRC.id),
        dateOfRegistration: companyRegistrationDate,
      },
    });

    // DEACTIVATE INVITE LINK
    if (invite) {
      await OnboardingInviteRepo.update({
        queryParams: { code: invite },
        payload: { status: STATUSES.INACTIVE },
      });
      const notificationPayload = {
        firstName: companyAdmin.firstName,
        directorName: `${Utils.toTitle(firstName)} ${Utils.toTitle(lastName)}`,
        dashboardUrl: `${Utils.getDashboardURL()}/login`,
      };
      NotificationService.notifyUser(companyAdmin, "director-details-submited", notificationPayload);
    }

    log(Log.fg.blue, `Mailing to Back-Office`);
    // Mailing to Back-Office
    await CompanyService.sendNewActivationRequestMail({
      companyName: existingCompany.name,
      companyCode: existingCompany.code,
      description: existingCompany.description,
    });

    // Get return Data
    const getCompany = await CompanyRepo.getOneCompany({
      queryParams: companyQuery,
      selectOptions: ["contact_email", "contact_phone"],
    });
    if (!getCompany) throw new NotFoundError("Company");

    log(Log.bg.cyan, ` Completed `);
    return responseUtils.sendObjectResponse("Your company documents has been updated sucessfuly and submitted for review", getCompany);
  },

  async submitSingleDocument({ document }) {
    const foundDocument = await DocumentRepo.getOneDocument({
      queryParams: { code: document, status: STATUSES.VERIFIED, table_type: "business" },
      selectOptions: ["asset", "type", "number", "table_id"],
      includeAsset: true,
    });
    if (!foundDocument) throw new NotFoundError("Document");

    const [accountHolder, foundCompany] = await Promise.all([
      AccountHolderRepo.getAccountHolder({
        filter: { company: foundDocument.table_id },
        selectOptions: ["externalIdentifier"],
      }),
      CompanyRepo.getCompany({
        queryParams: { id: foundDocument.table_id },
      }),
    ]);

    if (!foundCompany) throw new NotFoundError("Company");

    if (foundCompany.onboardingLevel === ONBOARDING_LEVEL.LEVEL_1 && !accountHolder) return; // Level 1 companies don't submit docs to Anchor

    if (!accountHolder) throw new NotFoundError("Account Holder");

    const { payment } = SettingsService.get("providers");
    const providerToUse = payment.defaultProvider;
    const providerDocument = await Providers[providerToUse].customer.getSingleCustomerRequiredDocument({
      externalIdentifier: accountHolder.externalIdentifier,
      documentType: foundDocument.type,
      company: foundDocument.table_id,
    });

    const formData = new FormData();
    const { id: documentId } = providerDocument;
    const { Asset: assetObject = null, number } = foundDocument;
    if (assetObject) {
      const accessUrl = await AssetService.getAssetDownloadURL({
        key: assetObject.key,
      });
      const { data: fileStream } = await axios.get(accessUrl, {
        responseType: "stream",
      });
      formData.append("fileData", fileStream, assetObject.name);
    } else {
      formData.append("textData", number);
    }

    const payload = { customerId: accountHolder.externalIdentifier, documentId };

    Providers[providerToUse].customer.uploadCustomerDocument({ formData, payload });
  },

  async sendRejectedDocumentNotification({ documents, reason }) {
    const foundDocuments = await DocumentRepo.getAllDocuments({ queryParams: { code: documents } });
    if (!foundDocuments.length || foundDocuments.length !== documents.length) throw new NotFoundError("One or more documents");

    const foundBusinessDocument = foundDocuments.find((document) => document.table_type === "business");
    let companyId;
    if (!foundBusinessDocument) {
      // Then it's an individual docs
      const foundDirector = await IndividualRepo.getOneIndividual({
        queryParams: { id: foundDocuments[0].table_id },
        selectOptions: ["company"],
      });
      if (!foundDirector) throw new NotFoundError("Director");
      companyId = foundDirector.company;
    } else companyId = foundBusinessDocument.table_id;

    // Get company
    const foundCompany = await CompanyRepo.getCompanyWithAdmins({ id: companyId }, true);
    if (!foundCompany) throw new NotFoundError("Company");
    const { Users: admins } = foundCompany;

    if (!admins.length) return;

    const documentsHash = Utils.convertArrayOfObjectToHashMap(DOCUMENTS, "value");

    const documentNames = foundDocuments.map((document) => documentsHash[document?.type]?.name);

    const emailPayload = {
      documents: documentNames,
      reason,
      dashboardUrl: `${Utils.getDashboardURL()}/login`,
    };

    admins.forEach((admin) => {
      const { firstName, email } = admin;
      emailPayload.firstName = firstName;
      NotificationService.notifyUser({ email }, "rejected-documents", emailPayload);
    });
  },

  async reviewOnboardingDocument(payload) {
    const { documents, decision, reason = null } = payload;
    const foundDocuments = await DocumentRepo.getAllDocuments({ queryParams: { code: documents } });
    if (!foundDocuments.length || foundDocuments.length !== documents.length) throw new NotFoundError("One or more documents");
    const hasApprovedDocuments = foundDocuments.find((document) => document.status === STATUSES.APPROVED);
    if (hasApprovedDocuments) throw new ValidationError("Cannot review already approved document");
    const updatePayload = {};
    updatePayload.status = decision === "approved" ? STATUSES.VERIFIED : STATUSES.REJECTED;
    if (decision === "approved") updatePayload.response = null;
    await DocumentRepo.updateADocument({
      queryParams: { code: documents },
      updateFields: { ...updatePayload, ...(reason && { response: reason }) },
    });

    if (decision === "approved") {
      if (Array.isArray(documents)) documents.forEach((document) => Service.submitSingleDocument({ document }));
      else Service.submitSingleDocument({ document: documents });
    }

    // TODO: SEND NOTIFICATION IF REJECTED
    if (decision === "rejected") Service.sendRejectedDocumentNotification({ documents, reason });
    return responseUtils.sendObjectResponse("Onboarding document reviewed successfully");
  },
};

module.exports = Service;
