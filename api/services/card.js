const { Op } = require("sequelize");
const CardValidator = require("../validators/virtual-card.validator");
const ValidationError = require("../utils/validation-error");
const Providers = require("./providers");
const { CreditCard: Card, Budget, Status, User, MonoAccountHolder, BankAccount, CardIssuer: Issuer, Team, UserBudget } = require("../models");
const { STATUSES } = require("../models/status");
const NotFoundError = require("../utils/not-found-error");
const Utils = require("../utils/utils");
const sudoProcessor = require("./physical-card/sudo");
const { VirtualCardRepo, CardRequestRepo } = require("../repositories/index.repo");
const UserBudgetRepo = require("../repositories/userBudget.repo");
const ApprovalService = require("./approval.service");

const Service = {
  /**
   * @param {string} [cardPan]
   * @param {string} [bvn]
   * @param {object} [company = null]
   * @param {object} [user = null]
   */
  async activateCard({ cardPan, bvn, company = null, user = null, cardRequest = null } = {}) {
    const foundCardRequest = await CardRequestRepo.findOne({
      conditions: {
        code: cardRequest,
      },
    });

    const { success } = await ApprovalService.conditionDetector(
      {
        id: foundCardRequest.id,
        type: "cardRequest",
        company: company.id,
        user: user.id,
      },
      true
    );

    if (!success) {
      throw new ValidationError("Card request pending approval");
    }

    if (foundCardRequest.budget) {
      const userBudget = await UserBudget.findOne({
        where: {
          budget: foundCardRequest.budget,
          user: foundCardRequest.owner,
          status: STATUSES.ACTIVE,
        },
      });
      if (!userBudget) {
        // Add user to the budget since the card is tied to that budget
        await UserBudgetRepo.createAUserBudget({
          queryParams: {
            budget: foundCardRequest.budget,
            user: foundCardRequest.owner,
            status: STATUSES.ACTIVE,
          },
        });
      }
    }

    return sudoProcessor.process({ cardPan, bvn, company, user, cardRequest });
  },
  chargeCard(cardCode, amount, budgetToCredit = null) {},
  validateCardCreationPayload(payload, source = "dashboard") {
    const { error } = CardValidator[source].create.validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, message: "Valid payload" };
  },
  async validateCharge(token, amount) {},
  async validateFunding(code, amount, budget, source = "dashboard") {
    const { error } = CardValidator[source].topUp.validate({
      code,
      amount,
      budget,
    });
    if (error) throw new ValidationError(error.message);

    const card = await Card.findOne({
      where: { code },
      include: [
        {
          model: Budget,
          where: { code: budget },
        },
        Issuer,
      ],
    });
    if (!card) throw new NotFoundError("Card");
    return card;
  },
  async getCard(criteria) {
    if (typeof criteria !== "object") throw new ValidationError("Invalid criteria, Expecting object");
    const foundCard = await Card.findOne({
      where: criteria,
      include: [User, Budget, Status, Issuer],
    });
    if (!foundCard) {
      throw new NotFoundError("Card");
    }
    return foundCard;
  },
  async deleteCard({ id: user }, { id, code }) {
    const [deletedCard] = await Card.update({ status: STATUSES.DELETED }, { code, user });
    if (!deletedCard) throw new NotFoundError("Card");
    Service.deactivateCard(id);
    return deletedCard;
  },

  async liquidateCard(criteria, amount = 0, provider = "mono") {
    const [updatedCard] = await Card.update({ amount }, { where: criteria });
    if (!updatedCard) {
      throw new ValidationError("The card could not be liquidated");
    }
    return Providers[provider].liquidateCard(criteria.externalIdentifier);
  },
  /**
   *
   * @param user.firstName
   * @param user.lastName
   * @param user.middleName
   * @param {string} budget.currency
   * @param data
   * @param provider
   */
  async createVirtualCard(data, provider = "mono") {
    /* const payload = {
            user,
            currency,
            amount,
            billing_name: `${user.firstName}${user.middleName? ' ' + user.middleName:''} ${user.lastName}`,
            billing_state: (user.address || defaultAddress).state,
            billing_city: (user.address || defaultAddress).city,
            billing_postal_code: (user.address || defaultAddress).postal_code,
            billing_country: (user.address || defaultAddress).country,
            callback_url: callbackUrl,
        }; */
    return Providers[provider].createVirtualCard(data);
  },
  async createPhysicalCard(payload, provider = "mono") {
    /* const payload = {
            user,
            currency,
            amount,
            billing_name: `${user.firstName}${user.middleName? ' ' + user.middleName:''} ${user.lastName}`,
            billing_state: (user.address || defaultAddress).state,
            billing_city: (user.address || defaultAddress).city,
            billing_postal_code: (user.address || defaultAddress).postal_code,
            billing_country: (user.address || defaultAddress).country,
            callback_url: callbackUrl,
        }; */
    return Providers[provider].createVirtualCard(payload);
  },

  async getVirtualCardPayload({ company, amount, currency = "NGN", user = null, disposable = false, provider = "mono" }) {
    if (provider === "mono") {
      let accountHolder;
      if (user) {
        accountHolder = await MonoAccountHolder.findOne({
          where: {
            company,
            user,
          },
        });
      }
      if (!accountHolder) {
        accountHolder = await MonoAccountHolder.findOne({
          where: {
            company,
            type: "main",
          },
        });
      }
      if (!accountHolder) throw new ValidationError("Please contact the customer service");
      return {
        accountHolder: accountHolder.holderId,
        user,
        disposable,
        currency,
        amount,
        company,
      };
    }
  },

  async createVirtualAccount() {},

  freezeCard(id, provider = "mono") {
    return Providers[provider].freezeVirtualCard(id);
  },

  unfreezeCard(id, provider = "mono") {
    return Providers[provider].unfreezeVirtualCard(id);
  },

  deactivateCard(id, provider = "mono") {
    return Providers[provider].deactivateVirtualCard(id);
  },

  fundCard(id, amount, provider = "mono") {
    return Providers[provider].fundVirtualCard(id, amount);
  },

  async persistCard(card, user, budget) {},

  /**
   * Pre save a card while waiting for the webhook to hit
   * @param externalIdentifier
   * @param user
   * @param company
   * @param budget
   * @param type
   * @param currency
   * @param name
   * @returns {Promise<*>}
   */
  async preSaveCreditCard({ externalIdentifier, user, company, budget, type, currency, amount, name = null }) {
    // if card is virtual and naira then it is a verve card
    const brand = type === 0 && currency === "NGN" ? "verve" : "visa";
    return Card.create({
      externalIdentifier,
      currency,
      user,
      company,
      budget,
      type,
      name,
      brand,
      amount,
      status: STATUSES.PROCESSING,
    });
  },

  async fundBalance(amount, currency, provider = "mono") {
    return Providers[provider].fundBalance(amount, currency);
  },

  /**
   * Move money from VA to Issuing Wallet
   * @param {*} company
   * @param {*} amount
   * @param {*} provider
   * @returns
   */
  async topUpWallet(company, amount, currency, provider = "mono") {
    let account;
    if (provider === "mono") {
      account = await BankAccount.findOne({
        where: {
          owner: company,
          type: "virtual",
          ownerType: "company",
          currency,
        },
      });
    }

    if (!account) throw new ValidationError("Please reach out to the customer service");

    return Providers[provider].topUpIssuingWallet(account.externalIdentifier, amount, company);
  },

  async fetchCards(query) {
    let { search, company, user, team, budget, currency, holder, type, from, to, status, page = 1, perPage = 50 } = query;
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    const criteria = {
      company,
      isExternal: false,
    };

    const teamCriteria = {
      model: Team,
      required: !!team,
      where: {
        ...(team && { code: team }),
      },
    };

    if (user) {
      if (String(user).startsWith("usr_")) criteria["$User.code$"] = user;
      else criteria.user = user;
    }
    if (holder) {
      if (String(holder).startsWith("usr_")) criteria["$User.code$"] = holder;
      // else criteria.budget = budget;
    }
    if (budget) {
      if (String(budget).startsWith("bdg_")) criteria["$Budget.code$"] = budget;
      else criteria.budget = budget;
    }
    if (search) {
      criteria[Op.or] = [{ name: { [Op.like]: `%{search}` } }, { "$Budget.name$": { [Op.like]: `%{search}` } }];
    }

    if (currency) {
      if (Array.isArray(currency)) {
        criteria.currency = currency.map((val) => String(val).toUpperCase());
      } else criteria.currency = currency.toUpperCase();
    }

    if (type) {
      if (Array.isArray(type)) {
        criteria.type = type.map((val) => (val === "virtual" ? 1 : 0));
      } else criteria.type = type === "virtual" ? 0 : 1;
    }

    if (status) {
      if (Array.isArray(status)) {
        criteria.status = status.map((val) => STATUSES[val.toUpperCase()]);
      } else criteria.status = STATUSES[status.toUpperCase()];
    }
    if (from && to) {
      criteria.created_at = {};
      if (from && Utils.isValidDate(from)) {
        criteria.created_at[Op.gte] = from;
      }
      if (to && Utils.isValidDate(to)) {
        criteria.created_at[Op.lte] = to;
      }
    }
    const { rows: cards, count: total } = await Card.findAndCountAll({
      where: criteria,
      include: [Budget, Status, User, teamCriteria],
      limit: perPage,
      order: [
        ["type", "DESC"],
        ["created_at", "DESC"],
      ],
    });
    return {
      cards,
      meta: {
        page,
        perPage,
        total,
        nextPage: page + 1,
        hasMore: total > page * perPage,
      },
    };
  },
  /**
   * Decrypt a credit card data
   * @param cardInstance
   * @param flag bool to tell the function whether or not to send back
   * @returns {(*&{number: *, cvv: *, exp_month: *, exp_year: *})|(*&{number: null, cvv: null, exp_month: null, exp_year: null})}
   */
  decryptCard(cardInstance, flag) {
    const card = cardInstance.toJSON();
    if (!flag)
      return {
        ...card,
        number: null,
        cvv: null,
        exp_month: null,
        exp_year: null,
      };
    const issuer = card.CardIssuer;
    const issuerName = ((issuer && issuer.name) || "mono").toLowerCase();
    return {
      ...card,
      number: Providers[issuerName].decrypt.number(card.number),
      cvv: Providers[issuerName].decrypt.cvv(card.cvv),
      exp_month: Providers[issuerName].decrypt.month(card.exp_month),
      exp_year: Providers[issuerName].decrypt.year(card.exp_year),
      pin: card.pin && Providers[issuerName].decrypt.pin(card.pin),
    };
  },
  async updateCard(criteria, payload) {
    return Card.update(payload, criteria);
  },
  async createCard(payload) {
    return Card.create(payload);
  },
  async getCardDefaultPin({ criteria }) {
    const foundCard = await VirtualCardRepo.find(criteria);
    if (!foundCard) throw new NotFoundError("Card");
    const {
      company,
      externalIdentifier,
      CardIssuer: { name },
    } = foundCard;
    return Providers[String(name).toLowerCase()].getDefaultCardPin(externalIdentifier, company);
  },
};

module.exports = Service;
