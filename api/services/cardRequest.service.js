/* eslint-disable no-underscore-dangle */
/* eslint-disable camelcase */
const Utils = require("../utils");
const {
  BudgetRepo,
  UserRepo,
  CountryRepo,
  CategoryRepo,
  CardRequestRepo,
  BeneficiaryRepo,
  TeamMemberRepo,
  CompanyRepo,
  MccRepo,
  BalanceRepo,
  TransferRepo,
  BudgetAccountRepo,
  TransactionRepo,
} = require("../repositories/index.repo");
const { NotFoundError, ValidationError } = require("../utils/error.utils");
const NotificationService = require("./notification");
const { STATUSES } = require("../models/status");
const HelperService = require("./helper.service");
const ResponseUtils = require("../utils/response.utils");
const SettingService = require("./settings");
const QueueService = require("./queue.service");
const ChargeService = require("./charge");
const { METHODS, BUJETI_CHARGE_TYPES, TRANSFER_PURPOSES } = require("../mocks/constants.mock");
const { CARD_ISSUER } = require("../models/cardissuer");
const { TRANSACTION_TYPES } = require("../models/transaction");
const SlackUtils = require("../utils/slack.utils");
const { log, Log } = require("../utils/logger.utils");

class CardRequestService {
  /**
   * create Card
   * @param {*} param0
   * @returns
   */
  static async createCardRequest({
    beneficiary = null,
    budget: budgetCode,
    balance: balanceCode,
    paymentSource,
    user,
    firstName,
    lastName,
    address,
    company,
    settings,
    ApprovalService,
    BalanceService,
    BudgetService,
  }) {
    // eslint-disable-next-line camelcase,
    const computedPayload = { owner: user.id };
    // perform validations
    if (budgetCode) {
      const existingBudget = await BudgetRepo.getOneBudget({
        queryParams: { code: budgetCode },
      });
      if (!existingBudget) {
        throw new NotFoundError("Budget");
      }
      computedPayload.budget = existingBudget.id;
    }

    if (balanceCode) {
      const existingBalance = await BalanceRepo.getBalance({
        filter: {
          code: balanceCode,
          company: company.id,
        },
      });

      if (!existingBalance) {
        throw new NotFoundError("Account");
      }

      if (existingBalance.status !== STATUSES.ACTIVE) {
        throw new ValidationError("Account not active");
      }

      computedPayload.balance = existingBalance.id;
    }
    if (settings?.expenseCategory) {
      const category = await CategoryRepo.getCategory({
        queryParams: { code: settings.expenseCategory },
      });
      if (!category) {
        throw new NotFoundError("Expense Category");
      }
      computedPayload.expenseCategory = category.id;
    }

    const { country: countryCode, ...restOfAddress } = address;
    const country = await CountryRepo.getOneCountry({
      queryParams: { code: countryCode },
      selectOptions: ["id"],
    });
    if (!country) {
      throw new NotFoundError("Country");
    }

    if (beneficiary) {
      const foundBeneficiary = await BeneficiaryRepo.getOneBeneficiary({
        queryParams: { code: beneficiary },
        selectOptions: ["user"],
      });
      if (!foundBeneficiary) {
        throw new NotFoundError("Beneficiary");
      }
      computedPayload.owner = foundBeneficiary.user;
    }

    const cardChargeAmount = Utils.getPhysicalCardRequestCharge(company.PaymentPlan);

    // Check payment source balance
    if (paymentSource.startsWith("bdg_")) {
      // Budget check
      const {
        data: { budget: foundBudget },
      } = await BudgetService.canBudgetHandleTransaction({
        budget: paymentSource,
        amount: cardChargeAmount,
        totalAmount: cardChargeAmount,
        shouldCheckBalance: true,
      });
      computedPayload.payment_entity_type = Utils.getKeyByValue(METHODS, METHODS.BUDGET);
      computedPayload.payment_entity_id = foundBudget.id;
    } else {
      // Balance check
      const {
        data: { balance: foundBalance },
      } = await BalanceService.canBalanceHandleTransaction({
        balance: paymentSource,
        amount: cardChargeAmount,
        totalAmount: cardChargeAmount,
        shouldCheckBalance: true,
        company: company.id,
      });
      computedPayload.payment_entity_type = Utils.getKeyByValue(METHODS, METHODS.BALANCE);
      computedPayload.payment_entity_id = foundBalance.id;
    }

    const payload = {
      ...computedPayload,
      firstName,
      lastName,
      ...restOfAddress,
      country: country.id,
      onlineTransaction: settings?.onlineTransaction,
      atmWithdrawals: settings?.atmWithdrawals,
      posTransaction: settings?.posTransaction,
      contactlessTransaction: settings.contactlessTransaction,
      requestedBy: user.id,
      company: company.id,
    };
    const cardRequest = await CardRequestRepo.create({
      data: payload,
      transaction: null,
    });

    const foundCardRequest = await CardRequestRepo.findOne({
      conditions: {
        code: cardRequest.code,
      },
    });

    let mccs;
    let spendingLimit;

    if (settings?.allowedMerchants) {
      mccs = await HelperService.validateMccs(settings.allowedMerchants);
    }

    if (mccs) {
      await MccRepo.createMccCardRequests(foundCardRequest.id, mccs);
    }

    if (settings?.spendingLimitPolicy) {
      spendingLimit = await HelperService.validateSpendingPolicy(settings.spendingLimitPolicy);

      if (spendingLimit.frequency === "weekends") {
        throw new ValidationError("Spending controls do not apply to policies with 'weekends' frequency");
      }

      await CardRequestRepo.update({
        queryParams: {
          id: foundCardRequest.id,
        },
        payload: {
          policy: spendingLimit.id,
        },
      });
    }
    const { success } = await ApprovalService.conditionDetector({
      id: foundCardRequest.id,
      type: "cardRequest",
      company: company.id,
      user,
    });

    // If no approval, notify support
    if (success) {
      // Process debit before sending notification;
      const SQSPayload = {
        data: {
          trial: 0,
        },
        id: Utils.generateRandomString(17),
        path: `/card-request/${foundCardRequest.code}/process-charge`,
        key: process.env.INTRA_SERVICE_TOKEN,
      };

      QueueService.addDelayedJob({}, SQSPayload, `PhysicalCardRequestCharge:${foundCardRequest.code}`, 5);
    } else {
      foundCardRequest.status = STATUSES.PENDING;
      await foundCardRequest.save();
    }
    return foundCardRequest;
  }

  static async getAllCardRequestsByUser(requestData) {
    const { cardOwner, cardRequester, perPage, page, budget, ...rest } = requestData;
    // Initialize variables to store user objects
    let cardRequesterResult;
    let cardOwnerResult;
    let existingBudget;

    if (budget) {
      existingBudget = await BudgetRepo.getOneBudget({
        code: budget,
      });
      if (!existingBudget) {
        throw new NotFoundError("Budget");
      }
    }

    // Check if user is defined before fetching
    if (cardRequester) {
      cardRequesterResult = await UserRepo.fetchUser(cardRequester);
    }

    // Check if cardOwner is defined before fetching
    if (cardOwner) {
      if (Array.isArray(cardOwner)) {
        const foundUsers = await UserRepo.getAllUsers({ queryParams: { code: cardOwner } });
        if (foundUsers.length !== cardOwner.length) throw new ValidationError("One or more users not found");
        cardOwnerResult = foundUsers.map((user) => user.id);
      } else {
        const foundUser = await UserRepo.fetchUser(cardOwner);
        cardOwnerResult = foundUser.id;
      }
    }

    const filters = {
      ...(cardRequesterResult?.id && { requestedBy: cardRequesterResult.id }),
      ...(cardOwnerResult && { owner: cardOwnerResult }),
      ...(existingBudget?.id && { budget: existingBudget.id }),
      // ...(!cardOwner && !cardRequester && { owner: user.id }),
      ...rest,
      perPage,
      page,
    };

    const cardRequests = await CardRequestRepo.findAll({
      filters,
      transaction: null,
    });
    if (!cardRequests) {
      throw new NotFoundError("Card Request");
    }
    return cardRequests;
  }

  static async getOneCardRequestByCode({ cardCode }) {
    const cardRequest = await CardRequestRepo.findOne({
      conditions: { code: cardCode },
      transaction: null,
    });
    if (!cardRequest) {
      throw new NotFoundError("Card Request");
    }
    return cardRequest;
  }

  static async completeCardRequest({ id: cardRequestId, status }) {
    const foundCardRequest = await CardRequestRepo.findOne({
      conditions: { id: cardRequestId },
      addCompany: true,
      addCountry: true,
    });

    if (!foundCardRequest) {
      throw new NotFoundError("Card Request");
    }

    if (status === STATUSES.DECLINED) {
      foundCardRequest.status = STATUSES.DECLINED;
      return foundCardRequest.save();
    }

    const company = foundCardRequest.Company;
    const country = foundCardRequest.Country;
    const user = foundCardRequest.cardRequester;

    const address = {
      billingAddress: foundCardRequest?.billingAddress,
      city: foundCardRequest?.city,
      state: foundCardRequest?.state,
      postalCode: foundCardRequest?.postalCode,
    };

    const { Team: team } = (await TeamMemberRepo.getTeamMember({ user: foundCardRequest.requestedBy })) || {};

    SlackUtils.sendCardRequestNotification({
      requestId: foundCardRequest.code,
      user: {
        firstName: foundCardRequest.firstName,
        lastName: foundCardRequest.lastName,
        code: foundCardRequest.cardRequester.code,
      },
      currency: "NGN",
      cardType: "Standard",
    });

    foundCardRequest.status = STATUSES.PROCESSING;
    await foundCardRequest.save();

    // Process debit before sending notification;
    const SQSPayload = {
      data: {
        trial: 0,
      },
      id: Utils.generateRandomString(17),
      path: `/card-request/${foundCardRequest.code}/process-charge`,
      key: process.env.INTRA_SERVICE_TOKEN,
    };

    return QueueService.addDelayedJob({}, SQSPayload, `PhysicalCardRequestCharge:${foundCardRequest.code}`, 5);
  }

  static async notifySupport({ company, address, country, user, team, foundCardRequest }) {
    const foundCompany = await CompanyRepo.getOneCompany({
      queryParams: {
        id: company.id,
      },
      addAddress: true,
      addPhoneNumber: true,
    });

    const billingAddress = {
      street: address?.billingAddress || foundCompany?.Address?.street,
      city: address?.city || foundCompany?.Address?.city,
      state: address?.state || foundCompany?.Address?.state,
      country: Utils.toTitle(country?.name?.toLowerCase() || foundCompany.Address?.country?.toLowerCase()),
      postalCode: address?.postalCode || foundCompany?.Address?.postalCode,
    };

    // Notify Support
    const requesterName = `${foundCardRequest.firstName} ${foundCardRequest.lastName}`;
    const notificationPayload = {
      billingAddress: Object.values(billingAddress).join(", "),
      companyName: company.name,
      contactPhone: company.contact_phone || company?.PhoneNumber?.localFormat,
      userNameInitials: Utils.getInitials(requesterName),
      requesterName,
      team: team ? team.name : null,
      avatar: user.profilePictureUrl,
      time: Utils.formatHumanReadableDate(foundCardRequest.created_at),
    };

    NotificationService.notifyUser({ email: "<EMAIL>" }, "new-card-request-support", notificationPayload, {
      subject: `[Request] Card Request from ${notificationPayload.companyName} 💳`,
    });
  }

  static async deleteCardRequest({ filter }) {
    const foundCardRequest = await CardRequestRepo.findOne({ conditions: filter });
    if (!foundCardRequest) throw new NotFoundError("Card Request");

    if (foundCardRequest.status !== STATUSES.PROCESSING)
      throw new ValidationError(`Cannot delete this card request as it is currently ${foundCardRequest.Status?.value || "not available"}`);

    await CardRequestRepo.update({
      queryParams: filter,
      payload: { status: STATUSES.DELETED },
    });
    return ResponseUtils.sendObjectResponse("Card request deleted successfully");
  }

  static async processCardRequestCharge(payload) {
    const { code, BalanceService, BudgetService, BankService, BillingService, ApprovalService } = payload;
    const foundCardRequest = await CardRequestService.getOneCardRequestByCode({ cardCode: code });

    const foundCompany = await CompanyRepo.getOneCompany({
      queryParams: { id: foundCardRequest.company },
      selectOptions: ["name"],
      addPaymentPlan: true,
    });

    if (!foundCompany) throw new NotFoundError("Company");
    const { PaymentPlan: paymentPlan = {} } = foundCompany;
    const amount = Utils.getPhysicalCardRequestCharge(paymentPlan);

    const { payment_entity_id: paymentSource, payment_entity_type: paymentEntityType } = foundCardRequest;

    if (!(paymentSource && paymentEntityType)) throw new ValidationError("No payment source specified for this card request");

    let deducedMethod = METHODS[paymentEntityType.toUpperCase()];
    let isDirectDebit = deducedMethod === METHODS.DIRECTDEBIT;

    const narration = `${foundCompany.name} - Payment of NGN${(parseInt(amount, 10) / 100).toLocaleString()} for Physical Card Request`;

    const transferPayload = {
      amount: -1 * amount,
      description: narration,
      company: foundCompany.id,
      status: STATUSES.PENDING,
      reference: Utils.generateRandomString(14).toLowerCase(),
      processor_fee: 0,
      bujeti_fee: 0,
      narration,
    };

    let bankAccount;
    if (deducedMethod === METHODS.BALANCE) {
      // Check if it's direct debit
      const {
        data: { bankAccount: foundAccount, isDirectDebit: isBalanceDirectDebit },
      } = await HelperService.isDirectDebitAccount({ balance: paymentSource });

      bankAccount = foundAccount;
      isDirectDebit = isBalanceDirectDebit;
      if (isDirectDebit) deducedMethod = METHODS.DIRECTDEBIT;
    }

    let methodId = paymentSource;
    if (isDirectDebit) {
      methodId = bankAccount?.id || paymentSource;
    }

    await HelperService.prepareToCharge({
      method: deducedMethod,
      methodId,
      amount,
      totalAmount: amount,
      payload: transferPayload,
      company: foundCompany,
      BankAccount: bankAccount,
      narration,
      BudgetService,
      BalanceService,
      BankService,
    });

    const { payment } = SettingService.get("providers");
    const providerToUse = payment[foundCompany.id] || payment.defaultProvider;

    let createdTransfer;
    let createdTransaction;

    if (transferPayload.useBookTransfer) {
      createdTransfer = await TransferRepo.createTransfer({ data: transferPayload });

      // Do a book transfer
      const bookTransferPayload = {
        senderId: transferPayload.senderId,
        senderType: Utils.getAccountType(transferPayload.senderId),
        currency: createdTransfer.currency,
        amount: Math.abs(transferPayload.amount),
        reason: transferPayload.description,
        purpose: TRANSFER_PURPOSES.CARD_REQUEST_CHARGE,
        reference: createdTransfer.reference,
        company: foundCompany.id,
        cardRequest: code,
        user: foundCardRequest.requestedBy,
        transfer: createdTransfer.code,
        providerToUse,
      };
      await ChargeService.chargeCompanyViaBookTransfer({ payload: bookTransferPayload, chargeType: BUJETI_CHARGE_TYPES.CARD_REQUEST_CHARGE });
    } else {
      // Use NIP
      const isBalance = deducedMethod === METHODS.BALANCE;
      const { bankAccount: bujetiBankAccount } = await HelperService.getChargeRecipientAccount(TRANSACTION_TYPES.CARDREQUEST_CHARGE);

      const { cbnFee, bujetiFee } = BillingService.computeFees({
        amount,
        companyCode: foundCompany.code,
        currency: transferPayload.currency,
        plan: Utils.parseJSON(paymentPlan.configuration),
        isDirectDebit,
      });
      // Create Transaction
      createdTransaction = await TransactionRepo.createTransaction({
        data: {
          amount,
          description: transferPayload.description,
          payer: foundCardRequest.requestedBy,
          ...(isBalance && { balance: methodId }),
          ...(!isBalance && { budget: methodId }),
          company: foundCompany.id,
          currency: transferPayload.currency,
          status: STATUSES.PROCESSING,
          narration: transferPayload.description,
          bank_account: bujetiBankAccount.id,
          recipient: bujetiBankAccount.owner,
          recipient_type: bujetiBankAccount.ownerType,
          processor_fee: cbnFee,
          bujeti_fee: bujetiFee,
          type: TRANSACTION_TYPES.CARDREQUEST_CHARGE,
          directDebitId: transferPayload.directDebitId,
        },
      });

      await ApprovalService.addPaymentToQueue(createdTransaction.code);
    }

    await CardRequestRepo.update({
      queryParams: { id: foundCardRequest.id },
      payload: {
        ...(createdTransaction && { transaction: createdTransaction.id }),
        ...(createdTransfer && { transfer: createdTransfer.id }),
      },
    });
  }
}
module.exports = CardRequestService;
