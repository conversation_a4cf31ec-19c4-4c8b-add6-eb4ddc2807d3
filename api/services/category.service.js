/* eslint-disable dot-notation */
const dateFns = require("date-fns");
const { Op } = require("sequelize");
const { ValidationError, NotFoundError, ExistsError } = require("../utils/error.utils");
const responseUtils = require("../utils/response.utils");
const CategoryValidator = require("../validators/category");
const CategorizationRuleValidator = require("../validators/categorizationRule.validator");
const { CategoryRepo, TransactionRepo, CategorizationRuleRepo, CategorizationRuleConditionRepo } = require("../repositories/index.repo");
const Utils = require("../utils/utils");
const { STATUSES } = require("../models/status");
const { Category } = require("../models");
const Sanitizer = require("../utils/sanitizer");
const { RULES_EVALUATOR, BACKGROUND } = require("../mocks/constants.mock");
const BackgroundService = require("./backgroundService");

const Service = {
  validateCreationPayload(payload) {
    const { error } = CategoryValidator.create.validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: true, message: "Valid payload" };
  },
  validateUpdatePayload(payload) {
    const { error } = CategoryValidator.update.validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: true, message: "Valid payload" };
  },
  validateMassDeletePayload(payload) {
    const { error } = CategoryValidator.massDelete.validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: true, message: "Valid payload" };
  },
  validatePayload(payload, validatorEntity) {
    const { error } = CategoryValidator[validatorEntity].validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: true, message: "Valid payload" };
  },
  validateCategorizationRulePayload(payload, validatorEntity) {
    const { error, value } = CategorizationRuleValidator[validatorEntity].validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: true, message: "Valid payload", value };
  },
  /**
   * List categories
   * @param  {} payload
   */
  async listCategories(payload = {}) {
    const { search, to, from, page, perPage, paginate = false, shouldNotEnforceParentCheck = false, ...rest } = payload;

    if (rest.parent) {
      const parentCategory = await CategoryRepo.getCategory({
        queryParams: {
          code: rest.parent,
        },
      });

      if (!parentCategory) throw new NotFoundError("Category");

      rest.parent = parentCategory.id;
    }

    const shouldEnforceParentQuery = !rest.parent && !search && !shouldNotEnforceParentCheck;

    const criteria = {
      ...rest,
      ...(!rest.status && {
        status: {
          [Op.ne]: STATUSES.DELETED,
        },
      }),
      ...(shouldEnforceParentQuery && {
        parent: {
          [Op.eq]: null,
        },
      }),
    };

    if (from || to) {
      criteria.created_at = {};
      if (from && Utils.isValidDate(from)) {
        criteria.created_at[Op.gte] = from;
      }
      if (to && Utils.isValidDate(to)) {
        criteria.created_at[Op.lte] = to;
      }
    }

    if (search) {
      criteria[Op.or] = [
        { code: { [Op.like]: `%${search}%` } },
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } },
        { slug: { [Op.like]: `%${search}%` } },
      ];
    }

    return paginate
      ? CategoryRepo.paginateCategories({ queryParams: criteria, pagination: { page, perPage } })
      : CategoryRepo.listCategories({ queryParams: criteria });
  },
  /**
   * Create a new category
   * @param  {} payload
   */
  async createCategory(payload) {
    const { parent, limit } = payload;
    const createdCategory = await CategoryRepo.createCategory(payload);

    if (parent) await Service.updateParentCategoryLimitIfNeeded(parent, limit);

    return createdCategory;
  },
  /**
   * Get a category
   * @param  {} criteria
   */
  async getCategory(criteria) {
    if (criteria.code && !criteria.code.startsWith("ctg_")) throw new ValidationError("Invalid category code");

    const queryParams = {
      ...criteria,
      ...(criteria.company && {
        company: {
          [Op.or]: [null, criteria.company],
        },
      }),
      status: {
        [Op.ne]: STATUSES.DELETED,
      },
    };
    return CategoryRepo.getCategory({
      queryParams,
      include: [
        {
          model: Category,
          as: "ParentCategory",
          required: false,
          include: [
            {
              model: Category,
              as: "SubCategories",
              required: false,
            },
          ],
        },
        {
          model: Category,
          as: "SubCategories",
          required: false,
          include: [
            {
              model: Category,
              as: "SubCategories",
              required: false,
            },
          ],
        },
      ],
    });
  },
  /**
   * Update a category
   * @param {Object} criteria
   * @param {Object} payload
   * @returns
   */
  async updateCategory(criteria, payload, existingCategory) {
    if (payload.status === STATUSES.DELETED) {
      await Service.disableCategory(criteria);
    }
    const category = await CategoryRepo.updateCategory({
      queryParams: criteria,
      updateFields: payload,
    });

    if (existingCategory?.parent) await Service.updateParentCategoryLimitIfNeeded(existingCategory.parent, payload.limit || existingCategory.limit);

    if (payload.status) {
      await CategoryRepo.updateChildCategories(criteria.id, { status: payload.status });
    }

    return category;
  },

  async massDeleteCategories(criteria) {
    const categories = await CategoryRepo.listCategories({
      queryParams: {
        code: criteria.categories,
      },
      selectOptions: ["company"],
    });

    return Promise.allSettled(
      categories.map((category) => {
        return Service.updateCategory({ id: category.id, code: category.code, company: category.company }, { status: STATUSES.DELETED });
      })
    );
  },

  /**
   * get statistics of transactions by category
   * @param {Object} criteria
   * @param {Object} payload
   * @returns
   */
  async analyticsCategory(criteria) {
    const today = new Date();
    const { to = today, from = dateFns.subDays(today, 30), ...query } = criteria;

    const categoriesedAnalytics = await TransactionRepo.analysisOfTransactionByCategory({
      queryParams: {
        ...query,
      },
      ...(from && { from }),
      ...(to && { to }),
    });
    const message = "Categories breakdown retrieved";
    if (!categoriesedAnalytics.length) {
      return responseUtils.sendObjectResponse(message, {
        highestTransactionCount: null,
        categoriesedAnalytics: [],
        highestTransactionVolume: null,
      });
    }

    const highestTransactionVolume = categoriesedAnalytics[0];
    const highestTransactionCount = Utils.maxElementFromAnArray(categoriesedAnalytics, "transactionCount");

    return responseUtils.sendObjectResponse(message, {
      highestTransactionCount,
      categoriesedAnalytics,
      highestTransactionVolume,
    });
  },

  /**
   * disable a category
   * @param {Object} criteria
   * @param {Number} criteria.company company id
   * @param {String} criteria.code category code
   * @returns
   */

  async disableCategory(criteria) {
    return CategoryRepo.disableCategory({ queryParams: criteria });
  },

  async getCategoriesWithTransactions(filters = {}) {
    return CategoryRepo.getCategoryWithTransactions({ filters });
  },

  async analyticsBreakDownForCategory(criteria) {
    let topSubCategories = [];
    const { category, ...query } = criteria;

    const foundCategory = await CategoryRepo.getCategory({
      queryParams: {
        code: category,
      },
    });

    if (!foundCategory) throw new NotFoundError("Category");

    const subcategories = await CategoryRepo.listCategories({
      queryParams: {
        parent: foundCategory.id,
      },
      selectOptions: ["id"],
    });

    if (subcategories.length) {
      query.category = Utils.mapAnArray(subcategories, "id");

      const { data: { categoriesedAnalytics } = {} } = await Service.analyticsCategory({
        ...query,
        company: criteria.company,
      });

      topSubCategories = categoriesedAnalytics;
    }

    const topSpenders = await TransactionRepo.analysisOfTransactionByBeneficiaries({
      queryParams: {
        company: criteria.company,
        category: foundCategory.id,
      },
      from: criteria.from,
      to: criteria.to,
    });

    const topVendors = await TransactionRepo.analysisOfTransactionByVendor({
      queryParams: {
        company: criteria.company,
        category: foundCategory.id,
      },
      from: criteria.from,
      to: criteria.to,
    });

    const expensesBreakdown = await TransactionRepo.getExpensesBreakDown(criteria.company, {
      ...criteria,
      category: foundCategory.id,
      currency: criteria.currency || "NGN",
    });

    return {
      topSubCategories,
      topSpenders,
      topVendors,
      expensesBreakdown:
        expensesBreakdown.length > 0
          ? expensesBreakdown?.reduce((collection, element) => {
              const { month, year, weekday } = element;
              // eslint-disable-next-line no-param-reassign
              element.totalAmount = parseInt(element.totalAmount, 10);
              // eslint-disable-next-line no-param-reassign
              collection[weekday ? `${weekday}` : `${month}-${year}`] = element;
              return collection;
            }, {})
          : null,
    };
  },

  async createCategorizationRule(data) {
    let { name, category } = data;
    const { user, company, description = null, operator, conditions } = data;

    const foundCategory = await CategoryRepo.getCategory({
      queryParams: {
        code: category,
      },
    });

    if (!foundCategory) throw new NotFoundError("Category");

    category = foundCategory.id;

    if (!name) {
      name = `${foundCategory.name} rule`;
    }

    const existingCategorizationRule = await CategorizationRuleRepo.getCategorizationRule({
      queryParams: {
        name,
        status: STATUSES.ACTIVE,
      },
    });

    if (existingCategorizationRule) throw new ExistsError("Category rule");

    const conflicts = await Service.conflictDetector({
      conditions,
      company,
    });

    if (conflicts.length) {
      return responseUtils.BadRequestException("Conflicts with other rules detected.", conflicts);
    }

    const categorizationRule = await CategorizationRuleRepo.create({
      company,
      status: STATUSES.INACTIVE,
      name,
      created_by: user,
      description,
      operator,
      category,
    });

    BackgroundService.addToQueue(
      BACKGROUND.QUEUES.CATEGORIZATION_RULES_PROMPT,
      BACKGROUND.JOBS.CATEGORIZATION_RULES_PROMPT,
      categorizationRule,

      {
        delay: 30 * 1000, // 30 secs delay
      }
    );

    await Service.callService("createCategorizationRuleCondition", conditions, {
      rule: categorizationRule.id,
      company,
    });

    categorizationRule.status = STATUSES.ACTIVE;
    await categorizationRule.save();

    return responseUtils.sendObjectResponse("Category rule successfully created", categorizationRule);
  },

  async updateCategorizationRule(data) {
    let { name, category, status } = data;
    const { description = null, operator, rule, conditionsToAdd = [], conditionsToUpdate = [], conditionsToDelete = [] } = data;

    const existingCategorizationRule = await CategorizationRuleRepo.getCategorizationRule({
      queryParams: {
        code: rule,
        status: {
          [Op.ne]: STATUSES.DELETED,
        },
      },
    });

    if (!existingCategorizationRule) throw new NotFoundError("Category rule");

    const foundCategory = await CategoryRepo.getCategory({
      queryParams: {
        code: category,
      },
    });

    if (!foundCategory) throw new NotFoundError("Category");

    category = foundCategory.id;

    if (foundCategory.id !== existingCategorizationRule.category) {
      if (!name) {
        name = `${foundCategory.name} rule`;
      }
    }

    if (status) {
      status = Utils.getStatusValues(status);
    }

    await existingCategorizationRule.update({
      ...(name && { name }),
      ...(category && { category }),
      ...(operator && { operator }),
      ...(description && { description }),
      ...(status && { status }),
    });

    if (conditionsToAdd.length) {
      const conflicts = await Service.conflictDetector({
        conditions: conditionsToAdd,
        company: existingCategorizationRule.company,
      });
      if (conflicts.length) {
        return responseUtils.BadRequestException("Conflicts with other rules detected.", conflicts);
      }
      await Service.callService("createCategorizationRuleCondition", conditionsToAdd, {
        rule: existingCategorizationRule.id,
        company: existingCategorizationRule.company,
      });
    }

    if (conditionsToDelete.length) {
      await CategorizationRuleConditionRepo.validateCategorizationRuleConditions(conditionsToDelete);

      await CategorizationRuleConditionRepo.delete({
        queryParams: {
          code: {
            [Op.in]: conditionsToDelete,
          },
        },
      });
    }

    if (conditionsToUpdate.length) {
      const conditionCodes = Utils.mapAnArray(conditionsToUpdate, "condition");
      await CategorizationRuleConditionRepo.validateCategorizationRuleConditions(conditionCodes);

      const conflicts = await Service.conflictDetector({
        conditions: conditionsToUpdate,
        company: existingCategorizationRule.company,
        code: existingCategorizationRule.company,
      });
      if (conflicts.length) {
        return responseUtils.BadRequestException("Conflicts with other rules detected.", conflicts);
      }
      await Service.callService("updateCategorizationRuleCondition", conditionsToUpdate, {
        company: existingCategorizationRule.company,
        rule: existingCategorizationRule.id,
      });
    }
  },

  async deleteCategorizationRule(criteria) {
    const existingCategorizationRule = await CategorizationRuleRepo.getCategorizationRule({
      queryParams: {
        code: criteria.rule,
        company: criteria.company,
        status: {
          [Op.ne]: STATUSES.DELETED,
        },
      },
    });

    if (!existingCategorizationRule) throw new NotFoundError("Category rule");

    return CategorizationRuleRepo.delete({
      queryParams: {
        id: existingCategorizationRule.id,
      },
    });
  },

  async createCategorizationRuleCondition(data) {
    const { operator, rule, operand, company, transaction = null } = data;

    const existingCondition = await CategorizationRuleConditionRepo.getCategorizationRuleCondition({
      queryParams: {
        rule,
        operator,
        operand,
        company,
        status: STATUSES.ACTIVE,
      },
      transaction,
    });

    if (existingCondition) throw new ExistsError("Category rule condition");

    const condition = await CategorizationRuleConditionRepo.create(
      {
        rule,
        operator,
        operand,
        company,
      },
      transaction
    );

    return responseUtils.sendObjectResponse("Approval condition created successfully", condition);
  },

  async updateCategorizationRuleCondition(data) {
    const { condition, operator, rule, operand, company, transaction = null } = data;

    const existingCondition = await CategorizationRuleConditionRepo.getCategorizationRuleCondition({
      queryParams: {
        rule,
        code: condition,
        company,
      },
      transaction,
    });

    if (!existingCondition) throw new NotFoundError("Category rule condition");

    await CategorizationRuleConditionRepo.update({
      queryParams: {
        rule,
        code: condition,
        company,
      },
      updateFields: {
        ...(operator && { operator }),
        ...(operand && { operand }),
      },
      transaction,
    });

    return responseUtils.sendObjectResponse("Approval condition updated successfully");
  },

  async listCategorizationRules(data = {}) {
    let { page = 1, perPage = 50, status, category } = data;
    const { company, from, to, search } = data;
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    const skip = Math.max(page - 1, 0) * perPage;

    const criteria = {
      company,
    };

    const foundCategory = await CategoryRepo.getCategory({
      queryParams: {
        code: category,
      },
    });

    if (!foundCategory) throw new NotFoundError("Category");

    criteria.category = foundCategory.id;

    const queryParams = {
      ...criteria,
      status: {
        [Op.ne]: STATUSES.DELETED,
      },
    };

    if (from && to) {
      queryParams.created_at = {};
      if (from && Utils.isValidDate(from)) queryParams.created_at[Op.gte] = from;
      if (to && Utils.isValidDate(to)) queryParams.created_at[Op.lte] = to;
    }

    if (search) {
      queryParams[Op.or] = [{ name: { [Op.like]: `%${search}%` } }, { description: { [Op.like]: `%${search}%` } }];
    }

    if (status) {
      status = typeof status === "object" ? status.map((item) => STATUSES[item.toUpperCase()]) : STATUSES[status.toUpperCase()];
      queryParams.status = status;
    }

    const [categorizationRules, total] = await Promise.all([
      CategorizationRuleRepo.listCategorizationRules({
        queryParams,
        includeConditions: true,
        meta: {
          order: [["created_at", "DESC"]],
          offset: skip,
          limit: perPage,
        },
      }),
      CategorizationRuleRepo.count({
        queryParams,
        includeConditions: true,
      }),
    ]);

    return {
      categorizationRules,
      meta: {
        page,
        total,
        perPage,
        hasMore: total > page * perPage,
        nextPage: page + 1,
      },
    };
  },

  async getACategorizationRule(criteria) {
    const existingCategorizationRule = await CategorizationRuleRepo.getCategorizationRule({
      queryParams: {
        code: criteria.rule,
        company: criteria.company,
        status: {
          [Op.ne]: STATUSES.DELETED,
        },
      },
      includeConditions: true,
    });

    if (!existingCategorizationRule) throw new NotFoundError("Category rule");

    return existingCategorizationRule;
  },

  async bulkCreate(payload) {
    const { categories, company } = payload;

    // do a duplicate check [fan out]
    await checkForDuplicates(categories, company);

    return Promise.all(
      categories.map(async (category) => {
        const { subCategories = [], ...rest } = category;
        const createdCategory = await CategoryRepo.createCategory({
          ...rest,
          name: category.name?.toLowerCase(),
          company,
        });

        const prepareSubCategories = await Promise.all(
          subCategories.map((subCategory) => {
            return {
              ...subCategory,
              name: subCategory.name?.toLowerCase(),
              parent: createdCategory.id,
              company,
            };
          })
        );

        return CategoryRepo.bulkCreate(prepareSubCategories);
      })
    );
  },

  async conflictDetector({ conditions, company, code }) {
    const conditionsByRule = {};
    const conditionsByOperandsAndOperator = {};
    await Promise.all(
      conditions.map(async (condition) => {
        const { operator: operatorToCheck, operand } = condition;

        conditionsByOperandsAndOperator[`${operatorToCheck}-${operand}`] = true;

        const matchingRules = await CategorizationRuleRepo.listCategorizationRules({
          queryParams: {
            ...(code && {
              code: {
                [Op.ne]: code,
              },
            }),
            "$CategorizationRuleConditions.operator$": operatorToCheck,
            "$CategorizationRuleConditions.operand$": operand,
            status: STATUSES.ACTIVE,
            company,
          },
          includeConditions: true,
        });

        await Promise.all(
          matchingRules.map(async (rule) => {
            if (!conditionsByRule[rule.code]) {
              const allRuleConditions = await CategorizationRuleConditionRepo.listCategorizationRuleConditions({
                queryParams: {
                  rule: rule.id,
                  status: STATUSES.ACTIVE,
                  company,
                },
              });
              conditionsByRule[rule.code] = {
                rule,
                conditions: allRuleConditions,
              };
            }

            return conditionsByRule;
          })
        );
      })
    );

    const conflicts = [];

    Object.values(conditionsByRule).map((rule) => {
      const { conditions: conditionsToCheck = [], rule: ruleToCheck } = rule;

      const evaluator = {
        [RULES_EVALUATOR.OR]: "some",
        [RULES_EVALUATOR.AND]: "every",
      };

      let isConflicting = false;
      if (conditionsToCheck.length) {
        isConflicting = conditionsToCheck[evaluator[ruleToCheck.operator]]((condition) => {
          return !!conditionsByOperandsAndOperator[`${condition.operator}-${condition.operand}`];
        });
      }

      if (isConflicting) {
        conflicts.push(rule);
      }
    });

    return conflicts;
  },

  /**
   * * This function helps with handling most of the looping in the service
   * @param { object } data payload
   * @param { object[] } payload an array of objects
   * @param { string } service name of the service to be used
   * @param { object } supportData extra data to be sent to the service
   * @returns
   */
  async callService(service, payload, supportData) {
    if (payload?.length) {
      return Promise.all(payload.map((item) => Service.runService(service, item, supportData)));
    }
    return Service.runService(service, payload, supportData);
  },

  async runService(service, item, supportData) {
    if (!item) return null;
    return Service[service]({
      ...(typeof item === "object" ? { ...Sanitizer.jsonify(item) } : { item }),
      ...supportData,
    });
  },

  /**
   * Clone a category
   * @param {*} category
   * @param {*} company
   * @returns
   */
  async cloneCategory(code, company) {
    if (!(code && company)) throw new ValidationError("Category code or company id is missing");
    const foundCategory = await CategoryRepo.getCategory({
      queryParams: {
        ...(String(code).startsWith("ctg_") && { code }),
        ...(!String(code).startsWith("ctg_") && { id: code }),
      },
    });
    if (!foundCategory) return;
    const toBeClonedData = foundCategory.toJSON();
    toBeClonedData.id = undefined;
    toBeClonedData.code = undefined;
    toBeClonedData.company = company;
    toBeClonedData.spent = 0;
    toBeClonedData.limit = 0;
    const clonedCategory = await Service.createCategory(toBeClonedData);
    Service.migrateExistingTransactions(company, foundCategory.id, clonedCategory.id);
    return clonedCategory;
  },

  async migrateExistingTransactions(company, oldCategory, newCategory) {
    return TransactionRepo.updateTransaction({
      queryParams: {
        company,
        category: oldCategory,
      },
      updateFields: {
        category: newCategory,
      },
    });
  },

  async updateParentCategoryLimitIfNeeded(parent, limit) {
    if (!(parent && limit)) return null;

    const parentCategory = await CategoryRepo.getCategory({
      queryParams: {
        id: parent,
      },
    });

    const { limit: parentLimit } = parentCategory;

    const children = await CategoryRepo.listCategories({
      queryParams: { parent },
    });
    const totalChildrenLimit = children.reduce((total, subCategory) => {
      const { limit: childLimit } = subCategory;

      total += Number(childLimit) || 0;
      return total;
    }, 0);

    if (limit > parentLimit) {
      parentCategory.limit += totalChildrenLimit;
      return parentCategory.save();
    }

    if (totalChildrenLimit > parentLimit) {
      parentCategory.limit = totalChildrenLimit;
      return parentCategory.save();
    }

    return null;
  },

  async bulkUpdate(payload) {
    const { categories, company } = payload;

    await checkForDuplicates(categories, company);

    const categoryCodes = Utils.mapAnArray(categories, "code").filter(Boolean);

    const extractedSubCategories = Utils.mapAnArray(categories, "subCategories").flat();

    const subCategoryCodes = Utils.mapAnArray(extractedSubCategories.filter(Boolean), "code").filter(Boolean);

    const existingCategories = await CategoryRepo.listCategories({
      queryParams: {
        code: {
          [Op.in]: [...categoryCodes],
        },
        company,
      },
    });

    if (existingCategories.length !== categoryCodes.length) {
      throw new ValidationError("One or more categories do not exist");
    }

    const existingSubCategories = await CategoryRepo.listCategories({
      queryParams: {
        code: {
          [Op.in]: [...subCategoryCodes],
        },
        company,
      },
    });

    if (existingSubCategories.length !== subCategoryCodes.length) {
      throw new ValidationError("One or more sub-categories do not exist");
    }

    const existingParentCategoriesMap = existingCategories.reduce((acc, value) => {
      acc[value.code] = value;
      return acc;
    }, {});

    // Validate that subcategories to be updated or created or deleted:
    // 1. Exist in the database (for updates)
    // 2. Are actually linked to their respective parent categories
    // 3. Do not have duplicate names within the same parent category
    // This ensures data integrity and prevents orphaned or mislinked subcategories

    let preparedSubCategories = [];
    let validatedSubCategoriesToUpdate = [];
    let validatedSubCategoriesToDelete = [];

    // run validations and prepare subcategories to be added/updated/deleted
    await Promise.all(
      categories.map(async (category) => {
        const { code, subCategories = [] } = category;
        const parent = existingParentCategoriesMap[code]?.id;
        const subCategoriesToAdd = subCategories.filter((subCategory) => !subCategory.code);
        const subCategoriesToUpdate = subCategories.filter((subCategory) => subCategory.code && !subCategory.shouldDelete);
        const subCategoriesToDelete = subCategories.filter((subCategory) => subCategory.code && subCategory.shouldDelete);

        if (subCategoriesToUpdate.length) {
          if (!parent) throw new ValidationError("You can only update subcategories with a parent");
          const getSubcategoriesLinkedToThisParent = await CategoryRepo.listCategories({
            queryParams: {
              parent,
              code: Utils.mapAnArray(subCategoriesToUpdate, "code"),
              company,
            },
          });
          if (getSubcategoriesLinkedToThisParent.length !== subCategoriesToUpdate.length) {
            throw new ValidationError("One or more subcategories do not exist or are not linked to the parent category");
          }
          validatedSubCategoriesToUpdate = [...validatedSubCategoriesToUpdate, ...subCategoriesToUpdate];
        }

        if (subCategoriesToAdd.length) {
          const transformedSubCategories = subCategoriesToAdd
            .map((subCategory) => {
              // we will treat those without parents differently
              if (!parent) return null;
              return {
                ...subCategory,
                name: subCategory.name?.toLowerCase(),
                parent,
                company,
              };
            })
            .filter(Boolean);

          preparedSubCategories = [...preparedSubCategories, ...transformedSubCategories];
        }

        if (subCategoriesToDelete.length) {
          const getSubcategoriesLinkedToThisParent = await CategoryRepo.listCategories({
            queryParams: {
              parent,
              code: Utils.mapAnArray(subCategoriesToDelete, "code"),
              company,
            },
          });
          if (getSubcategoriesLinkedToThisParent.length !== subCategoriesToDelete.length) {
            throw new ValidationError("One or more subcategories do not exist or are not linked to the parent category");
          }
          validatedSubCategoriesToDelete = [...validatedSubCategoriesToDelete, ...subCategoriesToDelete];
        }
      })
    );

    const updateSubCategoriesPromises = validatedSubCategoriesToUpdate.map(async (subCategory) => {
      const { code, ...rest } = subCategory;
      return CategoryRepo.updateCategory({
        queryParams: {
          code,
          company,
        },
        updateFields: { ...rest, name: rest.name?.toLowerCase() },
      });
    });

    const parentCategoriesToDelete = categories.filter((category) => category.shouldDelete && category.code);

    const mergedCategoriesToDelete = [...parentCategoriesToDelete, ...validatedSubCategoriesToDelete];

    const deleteParentAndSubCategoriesPromises = mergedCategoriesToDelete.map(async (category) => {
      const parent = existingParentCategoriesMap[category.code]?.id;
      return Service.updateCategory({ code: category.code, company, ...(parent && { id: parent }) }, { status: STATUSES.DELETED });
    });

    const parentCategoriesToUpdate = categories.filter((category) => category.code && !category.shouldDelete);
    const parentCategoriesToAdd = categories.filter((category) => !category.code);

    const addParentCategoriesPromises = parentCategoriesToAdd.map(async (category) => {
      const { subCategories = [], ...rest } = category;
      const createdCategory = await CategoryRepo.createCategory({
        ...rest,
        name: category.name?.toLowerCase(),
        company,
      });
      const prepareSubCategories = subCategories.map((subCategory) => {
        return {
          ...subCategory,
          name: subCategory.name?.toLowerCase(),
          parent: createdCategory.id,
          company,
        };
      });
      return CategoryRepo.bulkCreate(prepareSubCategories);
    });

    const updateParentCategoriesPromises = parentCategoriesToUpdate.map(async (category) => {
      const { code, subCategories, shouldDelete, ...rest } = category;
      return CategoryRepo.updateCategory({
        queryParams: {
          code,
          company,
        },
        updateFields: { ...rest, name: rest.name?.toLowerCase() },
      });
    });
    return Promise.all([
      ...addParentCategoriesPromises,
      ...updateSubCategoriesPromises,
      ...updateParentCategoriesPromises,
      ...deleteParentAndSubCategoriesPromises,
      CategoryRepo.bulkCreate(preparedSubCategories),
    ]);
  },
};

module.exports = Service;
async function checkForDuplicates(categories, company) {
  const categoryNames = Utils.mapAnArray(
    categories.filter((category) => category && (!category.code || !category?.shouldDelete)),
    "name"
  );

  const extractedCategories = Utils.mapAnArray(categories, "subCategories").flat();

  const subCategoryNames = Utils.mapAnArray(
    extractedCategories.filter((category) => category && (!category.code || !category?.shouldDelete)),
    "name"
  );

  const existingCategories = await CategoryRepo.listCategories({
    queryParams: {
      name: {
        [Op.in]: [...categoryNames, ...subCategoryNames],
      },
      company,
    },
    selectOptions: ["name"],
  });

  if (existingCategories.length) {
    throw new ValidationError(
      `One or more categories already exist: ${[...new Set(existingCategories.map((category) => category.name))].join(", ")}`
    );
  }
}
