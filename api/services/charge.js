/* eslint-disable camelcase */
const redis = require("./redis");
const { STATUSES } = require("../models/status");
const { getAccountType, generateRandomString, toTitle } = require("../utils");
const Providers = require("./providers");
const BankRepo = require("../repositories/bankAccount.repo");
const QueueService = require("./queue.service");
const { TransactionRepo, BalanceRepo, BudgetRepo, VendorRepo, BankAccountRepo } = require("../repositories/index.repo");
const SettingsService = require("./settings");
const RedisService = require("./redis");
const { ValidationError, NotFoundError } = require("../utils/error.utils");
const { PAYMENT_PROVIDERS } = require("../mocks/constants.mock");
const { Log, log } = require("../utils/logger.utils");
const HelperService = require("./helper.service");

const ChargeService = {
  async getTransactionStatus(user, reference) {
    const key = `trx:${user}:${reference}`;
    const transaction = await redis.get(key);
    if (!transaction) return null;
    return JSON.parse(transaction);
  },
  /**
   *
   * @param company the company id or code
   * @param amount the amount to charge
   * @param currency the currency
   * @param recipient the recipient object
   * @param narration
   * @param budget
   */
  async chargeCompany(params) {
    const { provider } = params;
    const handler = ChargeService.getProviderHandler(provider);
    return handler(params);
  },

  getProviderHandler(provider) {
    switch (provider) {
      case PAYMENT_PROVIDERS.PAYSTACK:
        return ChargeService.handlePaystack;
      case PAYMENT_PROVIDERS.GRAPH:
        return ChargeService.handleGraph;
      default:
        return ChargeService.handleGenericProvider;
    }
  },

  async handlePaystack(params) {
    const { recipient, amount, narration, currency, transaction, balance, provider } = params;
    const transactionIdOrCode = transaction || recipient.transaction;
    const foundTransaction = await TransactionRepo.findByIdOrCode(transactionIdOrCode, ["code"]);
    const payload = {
      accountName: recipient.accountName,
      number: recipient.number,
      bankCode: recipient.bankCode,
      amount,
      reason: narration,
      currency,
      reference: foundTransaction.code,
      balance,
      isInitiatingTransfer: true,
    };
    try {
      const response = await Providers[provider].payments.initiateTransfer(payload, Providers);
      addToGarbageCollector(foundTransaction, PAYMENT_PROVIDERS.PAYSTACK);
      return response;
    } catch (error) {
      return ChargeService.handleTransferError(error, transaction || recipient.transaction, recipient.trial);
    }
  },

  async handleGraph(params) {
    const { recipient, narration, amount, balance, provider, transaction } = params;
    const transactionIdOrCode = transaction || recipient.transaction;
    const foundTransaction = await TransactionRepo.findByIdOrCode(transactionIdOrCode, ["code", "recipient", "company"]);
    try {
      const foundBalance = await BalanceRepo.getBalance({ filter: { id: balance }, includeAccount: true });
      if (!foundBalance) throw new ValidationError("Balance not found");
      const foundVendor = await VendorRepo.getVendor({ queryParams: { id: foundTransaction.recipient }, includeAccounts: true });
      if (!foundVendor) throw new ValidationError("Vendor not found");
      if (foundVendor.status !== STATUSES.ACTIVE) throw new Error("Vendor not active");

      const bankAccount = await BankAccountRepo.getOneBankAccount({
        queryParams: {
          id: foundTransaction.bank_account,
        },
      });

      if (!bankAccount) throw new NotFoundError("Bank account");

      let payoutDestinationId = bankAccount?.meta?.payoutDestinations?.[PAYMENT_PROVIDERS.GRAPH];
      if (!payoutDestinationId) {
        payoutDestinationId = await HelperService.createPayoutDestination({
          provider,
          bankAccount,
          company: foundTransaction.company,
          balance: foundBalance,
          Providers,
        });
      }

      const payload = {
        payoutDestinationId,
        amount,
        description: narration,
        company: foundTransaction.company,
      };
      const { error: createPayoutError, data: createPayoutResponse } = await Providers[provider].payouts.createPayout(payload, Providers);
      if (!createPayoutError) {
        await TransactionRepo.updateTransaction({
          queryParams: { code: foundTransaction.code },
          updateFields: {
            reference: createPayoutResponse?.data?.payout?.id,
          },
        });

        addToGarbageCollector(foundTransaction, PAYMENT_PROVIDERS.GRAPH);
      }
      return createPayoutResponse;
    } catch (error) {
      return ChargeService.handleTransferError(error, transaction || recipient.transaction, recipient.trial);
    }
  },

  async handleGenericProvider(params) {
    const {
      company,
      amount,
      currency,
      recipient,
      narration,
      budget,
      transaction,
      provider,
      accountId = null,
      accountType = null,
      reference = null,
      balance,
      debitAccountType = "Expenses",
    } = params;
    const transactionIdOrCode = transaction || recipient.transaction;

    let accountToDebitId = accountId;
    let accountToDebitType = accountType;
    if (!(accountToDebitId && accountToDebitType)) {
      ({ accountId: accountToDebitId, type: accountToDebitType } = await Providers[provider].getDebitAccountDetails(
        company,
        budget,
        balance,
        debitAccountType
      ));
    }

    const { number: account_number, bankName: bank_name, bankCode: bank_code, accountName: account_name, trial = 0, ...rest } = recipient;

    let foundBalance;
    let foundBudget;
    if (balance) {
      foundBalance = await BalanceRepo.getBalance({ filter: { id: balance } });
    }

    if (budget) {
      foundBudget = await BudgetRepo.getOneBudget({ queryParams: { id: budget } });
    }

    const payload = {
      accountId: accountToDebitId,
      type: accountToDebitType,
      company,
      amount,
      narration,
      currency,
      account_number,
      account_name,
      bank_name,
      bank_code,
      reference: reference || generateRandomString(17),
      meta: {
        ...rest,
        ...(foundBalance && { balance: foundBalance.code }),
        ...(foundBudget && { budget: foundBudget.code }),
      },
    };

    await RedisService.sAdd("initiated:transactions", recipient.transaction || recipient.transfer);

    try {
      const response = await Providers[provider].debitAccount(payload);
      if (!transactionIdOrCode) return response;
      if (response.error) {
        return ChargeService.markTransactionAsFailedOrRetry({
          transaction: transactionIdOrCode,
          trial,
          reason: response.data.message || response.message,
          data: response.data,
        });
      }

      const foundTransaction = await TransactionRepo.findByIdOrCode(transactionIdOrCode, ["code"]);
      addToGarbageCollector(foundTransaction);
      return response;
    } catch (error) {
      return ChargeService.handleTransferError(error, transaction || recipient.transaction, trial);
    }
  },

  handleTransferError(error, transactionCode, trial = 0) {
    log(Log.fg.red, { error: error.stack });
    if (!transactionCode) return null;
    return ChargeService.markTransactionAsFailedOrRetry({
      transaction: transactionCode,
      trial,
      reason: error.message,
    });
  },

  /**
   * Funds a company's virtual account
   */
  async creditCompanyVirtualAccount({ company: owner, amount, provider = "mono" }) {
    const criteria = {
      owner,
      ownerType: "company",
      type: "virtual",
      status: STATUSES.ACTIVE,
    };
    const bankAccount = await BankRepo.getOneBankAccount({
      queryParams: criteria,
      selectOptions: ["externalIdentifier"],
    });
    if (!bankAccount) return;
    const { externalIdentifier: accountId } = bankAccount;
    // eslint-disable-next-line consistent-return
    return Providers[provider].creditVirtualAccount(accountId, amount, owner, "Liquidated card funds reversed");
  },

  async autoQuerySingleTransaction(payload) {
    const { provider, ...rest } = payload;
    await Providers[provider]?.autoRequeryTransaction({ ...rest });
  },

  async processBulkTransfer({ transactions, company, provider, budget, balance }) {
    const bulkTransferPayload = await Providers[provider].prepareBulkTransferPayloadFromTransaction(transactions, {
      ...(balance && { balance: balance.code }),
      ...(budget && { budget: budget.code }),
    });
    const { data, message, status, error } = await Providers[provider].debitBulkTransfer(bulkTransferPayload, company);
    if (error) throw Providers.anchor.throwProviderError(data);
    return { data, message, status };
  },

  async retryTransaction({ transaction, provider }) {
    const { error, message, data, status } = await Providers[provider].retryTransaction(transaction);
    if (error) {
      await TransactionRepo.updateTransaction({
        queryParams: { code: transaction },
        updateFields: { status: STATUSES.FAILED, failure_reason: `Error retrying transaction. Please try again later` },
      });
      throw Providers[provider].throwProviderError(data);
    }
    return { error, message, data, status };
  },

  async markTransactionAsFailedOrRetry(payload) {
    const { transaction, trial, reason, data = null } = payload;
    // DELETE FROM REDIS
    await RedisService.removeSetMember("initiated:transactions", transaction);
    if (data) {
      const { attributes: { status = null, failureReason } = {} } = data;
      if (status === "FAILED" && failureReason === "INSUFFICIENT_BALANCE") {
        // Don't retry
        const cleanedFailureReason = toTitle(String(failureReason).replace(/_/gi, " ").toLowerCase());
        return TransactionRepo.updateTransaction({
          queryParams: { code: transaction },
          updateFields: { status: STATUSES.FAILED, failure_reason: cleanedFailureReason },
        });
      }
    }
    if (trial + 1 <= parseInt(SettingsService.get("MAXIMUM_TRANSACTION_RETRY_ATTEMPT"), 10)) {
      const SQSPayload = {
        id: transaction,
        idempotencyKey: transaction,
        path: `/payments/${transaction}/process`,
        key: process.env.INTRA_SERVICE_TOKEN,
        trial: trial + 1,
      };
      return QueueService.addDelayedJob({ tag: "payment" }, SQSPayload, `payment:${transaction}`);
    }

    // Mark as failed
    return TransactionRepo.updateTransaction({
      queryParams: { code: transaction },
      updateFields: { status: STATUSES.FAILED, failure_reason: reason },
    });
  },

  /**
   * Charges a company via book transfer
   * NOTE: PLEASE ENSURE YOU CHECK SOURCE BALANCE TO ENSURE THERE'S SUFFICIENT FUNDS BEFORE CALLING THIS FUNCTION
   * @param {*} param0
   */
  async chargeCompanyViaBookTransfer({ payload, chargeType }) {
    if (!chargeType) throw new ValidationError("Please specify a charge type");
    if (!payload.transfer) throw new ValidationError("Please specify Transfer Code");
    // Get Bujeti Account for receiving transfer
    const collectionAccounts = SettingsService.get("collectionAccounts") || {};
    let recipientId = collectionAccounts[chargeType];
    if (!recipientId) {
      // Use Subscription collection account
      const { subscriptionAccount = null } = collectionAccounts;
      if (!subscriptionAccount) {
        // TODO: MARK AS FAILED
        return false;
      }
      recipientId = subscriptionAccount;
    }

    const bookTransferPayload = {
      ...payload,
      recipientId,
      recipientType: getAccountType(recipientId),
    };

    const SQSPayload = {
      data: bookTransferPayload,
      id: generateRandomString(17),
      path: `/transfers/bookTransfer/${payload.transfer}/process`,
      key: process.env.INTRA_SERVICE_TOKEN,
    };

    return QueueService.addDelayedJob({}, SQSPayload, `BookTransferReference:${bookTransferPayload.reference}`, 5);
  },
};

module.exports = ChargeService;

function addToGarbageCollector(foundTransaction, preferredProvider) {
  if (foundTransaction) {
    const SQSPayload = {
      id: `gc:${foundTransaction.code}`,
      idempotencyKey: `gc:${foundTransaction.code}`,
      path: `/transactions/requery`,
      code: foundTransaction.code,
      preferredProvider,
      notify: true,
      key: process.env.INTRA_SERVICE_TOKEN,
    };
    QueueService.addDelayedJob({ tag: generateRandomString(20) }, SQSPayload, `Garbage collector(trx:${foundTransaction.code})`, 60);
  }
}
