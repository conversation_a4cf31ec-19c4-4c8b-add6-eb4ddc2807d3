const axios = require("axios");
const { QueryTypes, literal, Op, where, fn, col } = require("sequelize");
const FormData = require("form-data");
const { format, addDays } = require("date-fns");
const {
  Asset,
  Address,
  BalanceLedger,
  BudgetLedger,
  Company,
  Industry,
  Status,
  Individual,
  Document,
  PhoneNumber,
  User,
  PaymentPlan,
  sequelize,
  Subscription,
  BillingHistory,
  Budget,
} = require("../models");
const NotificationService = require("./notification");
const {
  DocumentRepo,
  IndividualRepo,
  BudgetRepo,
  BudgetLedgerRepo,
  BankAccountRepo,
  AccountHolderRepo,
  PaymentPlanRepo,
  CompanyRepo,
  CompanyLookupRepo,
  AddressRepo,
  BudgetAccountRepo,
  BalanceRepo,
  industryRepo: IndustryRepo,
  PhoneNumberRepo,
  BalanceTypeRepo,
  BalanceLedgerRepo,
  BeneficiaryRepo,
  CountryRepo,
  VirtualCardRepo,
} = require("../repositories/index.repo");
const responseUtils = require("../utils/response.utils");
const { SYSTEM_ROLES } = require("../models/role");

const NotFoundError = require("../utils/not-found-error");
const Utils = require("../utils/utils");
const { STATUSES } = require("../models/status");
const SettingsService = require("./settings");
const VerificationService = require("./verification");
const Support = require("./helper.service");
const { ValidationError, ExistsError } = require("../utils/error.utils");
const TransferService = require("./transfer");
const { CARD_ISSUER } = require("../models/cardissuer");
const Providers = require("./providers");
const AssetService = require("./asset");
const Sanitizer = require("../utils/sanitizer");
const QueueService = require("./queue.service");
const { ONBOARDING_LEVEL } = require("../models/company");
const RedisService = require("./redis");
const { DOCUMENT_MAPPER, DOCUMENT_ENTITY_TYPE } = require("../models/document");
const { COUNTRIES, PAYMENT_PLANS, PAYMENT_SOURCES, DEFAULTS } = require("../mocks/constants.mock");
const HelperService = require("./helper.service");

const REWRITTEN_STATUS = {
  ACCEPTED: "approved",
  REJECTED: "rejected",
};

const baseBudgetTransactionQuery = `SELECT 
      trx.id, 'transaction' AS transactionType, 0 AS "moneyIn", trx.code as code, trx.amount as amount, reference,
      processor_fee, bujeti_fee, failure_reason, trx.description as description,
      trx.currency as currency, trx.paidOn as created_at, trx.updated_at as updated_at, narration,
	    trx.externalIdentifier as externalIdentifier, bdg.name as budgetName, bdg.code as budgetCode,
	    bac.accountName, bac.number as accountNumber, ctg.name as category, 'null' as destinationName 
    FROM Transactions trx
	  LEFT JOIN Budgets AS bdg ON bdg.id = trx.budget
	  LEFT JOIN BankAccounts AS bac ON bac.id = trx.bank_account
    LEFT JOIN Categories AS ctg ON ctg.id = trx.category
	  WHERE trx.company = $1 AND trx.status = ${STATUSES.SUCCESS} AND trx.currency = $2`;

const baseBalanceTransactionQuery = `SELECT 
      trx.id, 'transaction' AS transactionType, 0 AS "moneyIn", trx.code as code, trx.amount as amount, reference,
      processor_fee, bujeti_fee, failure_reason, trx.description as description,
      trx.currency as currency, trx.paidOn as created_at, trx.updated_at as updated_at, narration,
	    trx.externalIdentifier as externalIdentifier, blc.name as balanceName, blc.code as balanceCode,
	    bac.accountName, bac.number as accountNumber, ctg.name as category, 'null' as destinationName 
    FROM Transactions trx
	  LEFT JOIN Balances blc ON blc.id = trx.balance
	  LEFT JOIN BankAccounts bac ON bac.id = trx.bank_account
    LEFT JOIN Categories AS ctg ON ctg.id = trx.category
	  WHERE trx.company = $1 AND trx.status = ${STATUSES.SUCCESS} AND trx.currency = $2`;

const baseBudgetLedgerQuery = `SELECT 
      'null' as id, 'transfer' AS transactionType, 
        CASE 
      		WHEN trf.amount > 0 AND (trf.description COLLATE utf8mb4_general_ci LIKE '%Top up Budget%' OR trf.description COLLATE utf8mb4_general_ci LIKE '%Increase budget amount%') THEN 1 ELSE 0 END AS "moneyIn",
      trf.code AS code, bdlg.amount AS amount, trf.reference AS reference,
	    trf.processor_fee AS processor_fee, trf.bujeti_fee AS bujeti_fee, trf.failure_reason AS failure_reason,
	    bdlg.description AS description, bdlg.currency AS currency, trf.created_at AS created_at, trf.updated_at as updated_at,
	    trf.narration AS narration, trf.externalIdentifier as externalIdentifier, bdg.name as budgetName, 
	    bdg.code as budgetCode, NULL AS accountName, NULL as accountNumber, ctg.name as category, blc.name as destinationName 
	  FROM BudgetLedgers AS bdlg
	  LEFT JOIN Transfers AS trf ON bdlg.transfer = trf.id 
	  LEFT JOIN Budgets AS bdg ON bdg.id = bdlg.budget
    LEFT JOIN Categories AS ctg ON ctg.id = trf.category
    LEFT JOIN BalanceLedgers AS bll ON bll.transfer = trf.id
    LEFT JOIN Balances AS blc ON bll.balance = blc.id
	  WHERE trf.status IN (${STATUSES.SUCCESS}, ${STATUSES.PENDING}) AND trf.company = $1 AND bdlg.currency = $2 AND bdlg.transaction IS NULL`;

const baseBalanceLedgerQuery = `SELECT 'null' AS id, 'transfer' AS transactionType, 
          CASE 
            WHEN trf.amount > 0 AND (trf.description COLLATE utf8mb4_general_ci LIKE '%Transfer received from%' OR trf.description COLLATE utf8mb4_general_ci LIKE '%Withdrawal from%' OR trf.description COLLATE utf8mb4_general_ci LIKE '%Top up from%') THEN 1 ELSE 0 END AS "moneyIn",
        trf.code AS code, blg.amount AS amount, trf.reference AS reference,
        trf.processor_fee AS processor_fee, trf.bujeti_fee AS bujeti_fee, trf.failure_reason AS failure_reason,
        blg.description AS description, blg.currency AS currency, trf.created_at AS created_at, trf.updated_at as updated_at,
        trf.narration AS narration, trf.externalIdentifier as externalIdentifier, blc.name as balanceName, 
        blc.code as balanceCode, cpt.accountName AS accountName, cpt.number as accountNumber, ctg.name as category, bdg.name as destinationName
      FROM BalanceLedgers AS blg
      LEFT JOIN Transfers AS trf ON blg.transfer = trf.id 
      LEFT JOIN Balances AS blc ON blc.id = blg.balance
      LEFT JOIN CounterParties as cpt ON trf.counterParty = cpt.id
      LEFT JOIN BudgetLedgers AS bdgl ON bdgl.transfer = trf.id
      LEFT JOIN Budgets AS bdg ON bdgl.budget = bdg.id
      LEFT JOIN Categories AS ctg ON ctg.id = trf.category
      WHERE trf.status IN (${STATUSES.SUCCESS}, ${STATUSES.PENDING}) AND trf.company = $1 AND blg.currency = $2`;

const Service = {
  async createCompany(payload) {
    const { parent, address, lookupCode, rcNumber, bnNumber, ...rest } = payload;
    const foundCompany = await CompanyRepo.getCompany({ queryParams: { name: payload.name } });
    if (foundCompany) throw new ExistsError("Company");
    if (parent) {
      const foundParent = await CompanyRepo.getCompany({ queryParams: { code: parent } });
      if (!foundParent) throw new NotFoundError("Parent company");
      rest.parent = foundParent.id;
    }
    const createdAddress = await Support.findOrCreateAddress(address, true);
    rest.address = createdAddress.id;

    rest.document_reference = Utils.generateRandomString(6); // Create's company reference
    if (rest.accounts) rest.accounts = STATUSES.PENDING;
    if (rest.cards) rest.cards = STATUSES.PENDING;
    if (rest.invoices) rest.invoices = STATUSES.PENDING;
    if (rest.bills) rest.bills = STATUSES.PENDING;
    if (rest.budgets) rest.budgets = STATUSES.PENDING;

    const defaultPlan = await PaymentPlanRepo.getPaymentPlan({
      queryParams: {
        name: PAYMENT_PLANS.BASIC,
        status: STATUSES.ACTIVE,
      },
    });

    const createdCompany = await CompanyRepo.createCompany({ data: { ...rest, ...(defaultPlan && { paymentPlan: defaultPlan.id }) } });

    await DocumentRepo.createADocument({
      queryParams: {
        type: rcNumber ? "rcNumber" : "bnNumber",
        number: rcNumber || bnNumber,
        status: STATUSES.VERIFYING,
        table_type: "business",
        table_id: createdCompany.id,
        reference: createdCompany.document_reference,
        processor: "anchor",
        country: createdAddress.country,
      },
    });

    if (lookupCode) {
      // Fetch Director and save
      Service.updateCompanyDataFromLookUp({ lookupCode, company: createdCompany.id });
    }
    return responseUtils.sendObjectResponse("Company created successfully", createdCompany);
  },

  async createCompanySubsidiary({ payload }) {
    const { parent, lookupCode, name, user } = payload;
    // Check Parent company
    const parentCompany = await CompanyRepo.getCompany({ queryParams: { code: parent } });
    if (!parentCompany) throw new NotFoundError(`Parent Company`);

    const existingCompany = await CompanyRepo.getCompany({
      queryParams: { name },
    });
    if (existingCompany) throw new ExistsError(`Company with this name`);

    const createdCompany = await CompanyRepo.createCompany({
      data: {
        name,
        admin: user?.id,
        contactEmail: user?.email,
        parent: parentCompany.id,
      },
    });

    await BeneficiaryRepo.createABeneficiary({
      queryParams: {
        owner: user?.id,
        company: createdCompany.id,
        role: user?.role_id,
        user: user?.id,
        status: STATUSES.ACTIVE,
      },
    });

    if (lookupCode) {
      // Update company data
      Service.updateCompanyDataFromLookUp({ lookupCode, company: createdCompany.id });
    }
    return responseUtils.sendObjectResponse("Company created successfully", createdCompany);
  },

  async sendExistingCompanyMail({ email, phoneNumber, firstName, lastName }, companyName) {
    const payload = {
      company: companyName,
      firstName: firstName || "there",
    };
    const template = "company-exists";
    return NotificationService.notifyUser({ email, phoneNumber }, template, payload);
  },

  async sendNewActivationRequestMail({ companyName, companyCode, description }) {
    const query = `
            SELECT u.email, u.firstName, u.lastName
            FROM Companies AS c 
            INNER JOIN Users u ON u.company = c.id AND u.role = 'admin' 
            WHERE c.name = '${companyName}' LIMIT 1`;

    const [response = undefined] = await sequelize.query(query, {
      type: QueryTypes.SELECT,
    });
    const template = "kyb-request";

    const payload = {
      companyName,
      firstName: response && response.firstName,
      companyCode,
      description,
    };

    const sourceConfig = {
      subject: `We are verifying your documents`,
      from_name: "Bujeti",
    };

    const adminConfig = {
      subject: `KYB submitted by ${companyName} 🎉`,
      from_name: "Bujeti",
    };

    // ****** Notify Admin ******
    NotificationService.notifyUser({ email: SettingsService.get("notification_email") }, template, payload, adminConfig);
    // ****** Notify User ******
    if (response && response.email) NotificationService.notifyUser({ email: response.email }, template, payload, sourceConfig);

    return true;
  },

  async update({ filter, payload }) {
    const foundCompany = await CompanyRepo.getOneCompany({
      queryParams: filter,
      selectOptions: ["registeredAddress", "address", "phoneNumber", "document_reference", "onboardingStatus", "onboardingLevel"],
    });
    if (!foundCompany) throw new NotFoundError("Company");
    const { rcNumber, bnNumber, registeredAddress, address, phoneNumber, logo, industry, email, name, ...rest } = payload;

    const isCompanyOnboarding = foundCompany.onboardingStatus === STATUSES.PENDING || foundCompany.onboardingLevel !== ONBOARDING_LEVEL.LEVEL_3;

    const updatePayload = { ...rest };

    if (name && isCompanyOnboarding) {
      const existingCompany = await CompanyRepo.getCompany({ queryParams: { name, id: { [Op.ne]: foundCompany.id } } });
      if (existingCompany) throw new ExistsError("Company");
      updatePayload.name = name;
    }

    if (email) updatePayload.contactEmail = email;

    if (registeredAddress) {
      const createdAddress = await Support.findOrCreateAddress(registeredAddress, true);
      if (foundCompany.registeredAddress && createdAddress.id !== foundCompany.registeredAddress)
        await AddressRepo.updateAnAddress({
          queryParams: { id: foundCompany.registeredAddress },
          updateFields: { status: STATUSES.INACTIVE }, // SETS THE PREVIOUS ADDRESS TO INACTIVE
        });
      updatePayload.registeredAddress = createdAddress.id;
    }

    if (address) {
      const createdAddress = await Support.findOrCreateAddress(address, true);
      if (foundCompany.address && createdAddress.id !== foundCompany.address)
        await AddressRepo.updateAnAddress({
          queryParams: { id: foundCompany.address },
          updateFields: { status: STATUSES.INACTIVE }, // SETS THE PREVIOUS ADDRESS TO INACTIVE
        });
      updatePayload.address = createdAddress.id;
    }

    if (phoneNumber) {
      const { id: newPhoneNumber } = await Support.findOrCreatePhoneNumber(phoneNumber);
      if (foundCompany.phoneNumber && foundCompany.phoneNumber !== newPhoneNumber)
        await PhoneNumberRepo.updateAPhoneNumber({
          queryParams: { id: foundCompany.phoneNumber },
          updateFields: { status: STATUSES.INACTIVE },
        });
      updatePayload.phoneNumber = newPhoneNumber;
    }

    if (logo) {
      const asset = await AssetService.getAsset(logo);
      if (!asset) throw new NotFoundError("Asset");
      updatePayload.logo = asset.id;
    }

    if (industry) {
      const foundIndustry = await IndustryRepo.getOneIndustry({ queryParams: { code: industry } });
      if (!foundIndustry) throw new NotFoundError("Industry");
      updatePayload.industry = foundIndustry.id;
    }

    if (rest.accounts) updatePayload.accounts = STATUSES.PENDING;
    if (rest.cards) updatePayload.cards = STATUSES.PENDING;
    if (rest.invoices) updatePayload.invoices = STATUSES.PENDING;
    if (rest.bills) updatePayload.bills = STATUSES.PENDING;
    if (rest.budgets) updatePayload.budgets = STATUSES.PENDING;

    await CompanyRepo.updateACompany({ queryParams: filter, updateFields: updatePayload });

    if (rcNumber || bnNumber) {
      Support.findOrCreateDocument({
        document: {
          type: rcNumber ? "rcNumber" : "bnNumber",
          number: rcNumber || bnNumber,
          status: STATUSES.VERIFYING,
          table_type: "business",
          table_id: foundCompany.id,
          reference: foundCompany.document_reference,
          processor: "anchor",
          country: "NIGERIA",
        },
      });
    }

    const updatedCompany = await CompanyRepo.getOneCompany({ queryParams: filter, addAddress: true, addPhoneNumber: true });

    return responseUtils.sendObjectResponse("Company updated sucessfully", updatedCompany);
  },

  async updateCompany({ code: company_code, id: companyId, from, ...criteria }, payload) {
    const { data: existingCompany } = await Support.CompanyChecker({
      company_code,
    });
    const { address, director, incorporation, phoneNumber, logo, ...data } = payload;

    if (address && companyId && typeof address === "object") {
      const { id: addressId, code, ...rest } = address;
      if (addressId)
        await Address.update(rest, {
          where: { id: addressId, company: companyId },
        });
      else {
        const newAddress = await Address.create({
          ...rest,
          company: companyId,
        });
        data.address = newAddress.id;
      }
    }

    if (director && typeof director === "object") {
      let document;
      const individualDocument = await DocumentRepo.getOneDocument({
        queryParams: {
          type: "bvn",
          table_type: "individual",
          reference: existingCompany.document_reference,
          table_id: existingCompany.director,
        },
      });
      if (director.document) {
        if (director.document.expiryDate && !Utils.isValidDate(director.document.expiryDate)) {
          director.document.expiryDate = null;
        }
        document = await Document.create(director.document);
        director.document = document.id;
      }
      if (director.address) {
        const newAddress = await Address.create({
          ...director.address,
          company: companyId,
        });
        director.address = newAddress.id;
      }
      director.type = "director";
      director.metadata = {
        bvn: director.bvn,
      };
      delete director.bvn;
      if (director.phoneNumber && typeof director.phoneNumber === "object") {
        const { id: newPhoneNumber } = await Support.findOrCreatePhoneNumber(director.phoneNumber);
        director.phoneNumber = newPhoneNumber;
      }
      let companyDirector;
      if (!existingCompany.director) companyDirector = await Individual.create({ ...director });
      else {
        await IndividualRepo.updateAIndividual({
          queryParams: { id: existingCompany.director },
          updateFields: {
            ...director,
            ...(individualDocument && { document: individualDocument.id }),
          },
        });
        companyDirector = existingCompany.director;
      }
      data.director = companyDirector.director;
      // VerificationService.startVerification({ document, ...director, company: companyId });
    }

    if (incorporation && typeof incorporation === "object") {
      if (incorporation.document.expiryDate && !Utils.isValidDate(incorporation.document.expiryDate)) {
        incorporation.document.expiryDate = null;
      }
      const document = await Document.create(incorporation.document);
      data.incorporation = document.id;
      // VerificationService.startVerification({ document, company: companyId });
    }

    // For Company Phone Update
    if (phoneNumber && typeof phoneNumber === "object") {
      const { id: newPhoneNumber } = await Support.findOrCreatePhoneNumber(phoneNumber);
      data.phoneNumber = newPhoneNumber;
    }

    if (payload.paymentPlan) {
      if (Utils.isProd()) throw new ValidationError("Cannot update payment plan");
      const foundPlan = await PaymentPlanRepo.getPaymentPlan({ queryParams: { name: payload.paymentPlan } });
      if (!foundPlan) throw new NotFoundError("Payment Plan");
      data.paymentPlan = foundPlan.id;
    }

    if (logo) {
      const asset = await AssetService.getAsset(logo);
      if (!asset) throw new NotFoundError("Asset");
      data.logo = asset.id;
    }

    const [updatedCompany] = await Company.update(data, {
      where: { id: companyId, ...criteria },
    });
    // if (!updatedCompany && from !== 'webhook') throw new NotFoundError('Company');
    return updatedCompany;
  },

  async getCompanyByCriteria({ criteria, includeDocuments = false }) {
    const company = await Company.findOne({
      where: criteria,
      include: [
        {
          model: Asset,
          required: false,
        },
        {
          model: Address,
        },
        {
          model: Address,
          as: "RegisteredAddress",
        },
        Industry,
        Document,
        Status,
        PhoneNumber,
        PaymentPlan,
      ],
    });
    if (!company) throw new NotFoundError("Company");

    if (includeDocuments) {
      let documents = await DocumentRepo.getCompanyDocuments({ companyCode: company.code });
      if (documents.length) {
        documents = await Promise.all(
          documents.map(async (document) => {
            const { asset, ...rest } = document;
            let sanitizedAsset = null;
            if (asset) {
              sanitizedAsset = {
                ...Sanitizer.sanitizeAsset(asset),
                url: await AssetService.getAssetDownloadURL({ key: asset.key }),
              };
            }
            return { ...rest, asset: sanitizedAsset };
          })
        );
      }
      company.documents = documents;
    }

    company.paymentPlan = company.PaymentPlan && company.PaymentPlan.toJSON();
    const { paymentPlan } = company;
    if (company.paymentPlan) {
      const activeSubscription = await Subscription.findOne({
        where: {
          plan: paymentPlan.id,
          status: STATUSES.ACTIVE,
          company: company.id,
        },
        attributes: ["isFreeTrial", "additionalSeats"],
        include: [
          {
            model: BillingHistory,
            required: true,
            where: {
              status: STATUSES.PAID,
              company: company.id,
            },
            order: [["created_at", "DESC"]],
            limit: 1,
          },
        ],
      });

      const billingAddress = await Subscription.findOne({
        where: {
          address: {
            [Op.ne]: null,
          },
        },
        attributes: ["address", "state", "country", "companyName", "city", "firstName", "lastName"],
        order: [["created_at", "DESC"]],
      });

      const billingHistory = activeSubscription?.BillingHistories?.[0];

      const configuration = Utils.parseJSON(paymentPlan.configuration);
      const maxTransactionLimit = configuration?.payables?.maxTransactionLimit?.[paymentPlan.currency || "NGN"];

      company.paymentPlan = {
        ...paymentPlan,
        configuration,
        expiryDate: billingHistory?.dueDate || null,
        paidOn: billingHistory?.created_at,
        billingAddress,
        isFreeTrial: Boolean(activeSubscription?.isFreeTrial),
        isNonPayingCompany: Boolean(maxTransactionLimit) && (!activeSubscription || activeSubscription?.isFreeTrial),
        additionalSeats: activeSubscription?.additionalSeats || 0,
        isActiveSubscription: Boolean(activeSubscription),
      };
    }
    return company;
  },

  async getCompany(criteria) {
    const company = await Company.findOne({ where: criteria });
    if (!company) throw new NotFoundError("Company");
    return company;
  },

  async getCompanyWithAdmins(criteria) {
    if (typeof criteria !== "object") throw Error("Invalid criteria");
    const company = await Company.findOne({
      where: criteria,
      include: [
        {
          model: User,
          required: true,
          where: { role_id: SYSTEM_ROLES.ADMIN, status: [STATUSES.ACTIVE, STATUSES.PENDING] },
        },
      ],
    });
    return company;
  },

  async getCompanyDirectorFullDetails(id) {
    const director = await Individual.findOne({
      where: { id, type: "director" },
      include: [Address, PhoneNumber, Document],
    });
    const parsed = director && director.toJSON();
    return {
      ...parsed,
      address: parsed.Address ? parsed.Address : null,
      phoneNumber: parsed.PhoneNumber ? parsed.PhoneNumber : null,
      document: parsed.Document,
      metadata: JSON.parse(parsed.metadata),
    };
  },

  async createDefaultBalance({ company }) {
    const { payment } = SettingsService.get("providers");
    const providerToUse = payment.defaultProvider;
    const externalIdentifier = SettingsService.get("BUJETI_ANCHOR_CUSTOMER_ID");

    const { error, data } = await Providers[providerToUse].virtualAccount.createSubAccount(externalIdentifier, company);
    // eslint-disable-next-line new-cap
    if (error) throw Providers[providerToUse].throwProviderError(data);

    const {
      id,
      type,
      relationships: { virtualNubans },
    } = data;

    const { error: virtualAccountError, data: virtualAccountData } = await Providers[providerToUse].virtualAccount.getVirtualAccount(
      virtualNubans.data[0].id
    );
    // eslint-disable-next-line new-cap
    if (virtualAccountError) throw Providers[providerToUse].throwProviderError(data);

    // create bank account
    const {
      attributes: {
        accountNumber,
        accountName,
        bank: { name: bankName, nipCode: bankCode },
      },
    } = virtualAccountData;

    const createdBankAccount = await BankAccountRepo.createABankAccount({
      queryParams: {
        accountName,
        bankName,
        currency: "NGN",
        bankCode,
        owner: company,
        externalIdentifier: virtualNubans?.data[0]?.id,
        number: accountNumber,
        type: "virtual",
        subtype: "deposit",
        ownerType: "company",
        issuer: CARD_ISSUER.Anchor,
        externalBankAccountId: id,
        company,
      },
    });
    // create balance
    const defaultBalanceType = await BalanceTypeRepo.getBalanceType({
      filter: { name: { [Op.like]: `%Expenses%` } },
    });

    if (!defaultBalanceType) throw new NotFoundError("Balance Type");

    await BalanceRepo.findOrCreate({
      company,
      currency: createdBankAccount.currency,
      bankAccount: createdBankAccount?.id,
      type: defaultBalanceType.id,
      name: "Main Account",
      purpose: defaultBalanceType.name,
    });
  },

  /**
   * This function declines a company's onboarding
   * @param {*} param0
   */
  async declineCompanyOnboarding({ foundCompany, reason = null }) {
    await CompanyRepo.updateACompany({
      queryParams: { id: foundCompany.id },
      updateFields: { status: STATUSES.REJECTED, onboardingStatus: STATUSES.DECLINED, ...(reason && { declineReason: reason }) },
    });
    return responseUtils.sendObjectResponse("Company declined successfully", foundCompany);
  },

  /**
   * This function approves a company for lowest KYB level
   * @param {*} company The company code
   */
  async approveCompanyOnboarding({ foundCompany }) {
    // Check that all document has been approved
    const businessType = Utils.businessTypeMapper()[foundCompany.businessType] || foundCompany.businessType;
    const basicDocs = DocumentRepo.getBasicRequiredDocument({ businessType });
    const documentTypes = Utils.mapAnArray(basicDocs, "value");

    const submittedDocs = await DocumentRepo.getAllDocuments({
      queryParams: { type: documentTypes, table_type: "business", table_id: foundCompany.id },
    });

    // Check if any docs has been submitted
    if (submittedDocs.length === 0) throw new ValidationError(`${foundCompany.name} hasn't submitted any onboarding document yet`);
    if (submittedDocs.length < basicDocs.length) throw new ValidationError(`${foundCompany.name} hasn't submitted all basic onboarding document yet`);

    // Check if document has been verified
    const foundUnverifiedDoc = submittedDocs.find((submittedDoc) => submittedDoc.status === STATUSES.VERIFYING);
    if (foundUnverifiedDoc) {
      const documentName = basicDocs.find((document) => document.value === foundUnverifiedDoc.type)?.name;
      throw new ValidationError(`Please verify ${foundCompany.name}'s ${documentName}`);
    }
    // Approve company
    await CompanyRepo.updateACompany({
      queryParams: { id: foundCompany.id },
      updateFields: {
        status: STATUSES.VERIFIED,
        onboardingStatus: STATUSES.APPROVED,
        onboardingLevel: ONBOARDING_LEVEL.LEVEL_1,
        maxTransactionAmount: Number(SettingsService.get("LEVEL_1_ONBOARDING_MAX_AMOUNT")),
      },
    });

    // Create Balance
    Service.createDefaultBalance({ company: foundCompany.id });
    return responseUtils.sendObjectResponse("Company approved successfully");
  },

  /**
   * This function triggers KYB with a provider
   * @param {*} providerToUse The provider to use for the verification
   *
   */
  async submitCompanyForKYB({ providerToUse, foundCompany }) {
    // Check that other details have been provided
    const sectionMap = {
      businessDetails: "Business Details",
      businessOwners: "Business Owners",
      additionalDetails: "Additional Details",
    };

    const onboardingSteps = await Support.buildCompanyOnboardingSteps({ company: foundCompany.id, isApproval: true });

    const pendingSection = Object.keys(onboardingSteps).find((key) => !onboardingSteps[key].completed);
    if (pendingSection) throw new ValidationError(`Company haven't completed ${sectionMap[pendingSection]} section`);

    // check that all docs have been verified
    const foundRequiredDocs = DocumentRepo.getCompanyTypeRequiredDocument({
      companyType: Utils.businessTypeMapper()[foundCompany.businessType] || foundCompany.businessType,
      registrationDate: foundCompany?.dateOfRegistration,
    });
    if (!foundRequiredDocs.length) throw new ValidationError("No document found for this company type");

    const documentTypes = Utils.mapAnArray(foundRequiredDocs, "value");

    const foundVerifiedDocs = await DocumentRepo.getAllDocuments({
      queryParams: { type: documentTypes, table_type: "business", table_id: foundCompany.id, status: [STATUSES.VERIFIED, STATUSES.VERIFYING] },
      selectOptions: ["type", "status", "number", "response"],
    });

    if (!foundVerifiedDocs.length) throw new ValidationError("Company cannot be approved as they don't have any verified document");

    const foundUnverifiedDoc = foundVerifiedDocs.find((submittedDoc) => submittedDoc.status === STATUSES.VERIFYING);
    if (foundUnverifiedDoc) {
      const documentName = foundRequiredDocs.find((document) => document.value === foundUnverifiedDoc.type)?.name;
      throw new ValidationError(`Please verify ${foundCompany.name}'s ${documentName}`);
    }

    // Get the type for all document submitted by the user
    const foundVerifiedDocsKeys = Utils.mapAnArray(foundVerifiedDocs, "type");

    // Check if provider compulsory document has been submitted and verified
    const providerCompulsoryDocument = documentTypes.filter((type) => ["cac", "incorp_C", "cacStatusReport", "utilityBill"].includes(type));
    const hasSubmittedAllCompulsoryDocs = providerCompulsoryDocument.every((compulsoryDocumet) => foundVerifiedDocsKeys.includes(compulsoryDocumet));
    // if (!hasSubmittedAllCompulsoryDocs) throw new ValidationError("This company haven't submitted all required docs");

    const { error, message, data } = await VerificationService.concludeVerification({
      company: foundCompany.code,
      provider: providerToUse,
    });

    if (error) {
      if (providerToUse === "anchor") throw new ValidationError(`Provider error: ${data.errors.map(({ detail }) => detail || "").join("\n")}`);
      throw new ValidationError(message);
    }

    await CompanyRepo.updateACompany({
      queryParams: { id: foundCompany.id },
      updateFields: { status: STATUSES.VERIFYING },
    });

    return responseUtils.sendObjectResponse("OK", {
      company: {
        code: foundCompany.code,
        name: foundCompany.name,
        admins: foundCompany.Users.map((user) => user.toJSON()),
      },
    });
  },

  /**
   *
   * @param {*} param0
   * @returns
   */
  async reviewCompany({ decision, reason, company }) {
    const foundCompany = await CompanyRepo.getCompanyWithAdmins({ code: company }, true);
    if (!foundCompany) throw new NotFoundError("Company");

    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[company] || payment.defaultProvider;
    let response;
    switch (decision) {
      case "REJECTED": // Declines a company's onboarding
        response = await Service.declineCompanyOnboarding({ foundCompany, reason });
        break;
      case "ACCEPTED": // Submits company to provider for KYB
        response = await Service.submitCompanyForKYB({ providerToUse, foundCompany });
        break;
      case "APPROVED": // Approves company onboarding
        response = await Service.approveCompanyOnboarding({ foundCompany });
        break;
      default:
        break;
    }

    if (!response) throw new ValidationError("Please specify a valid action");

    const { message } = response;

    return responseUtils.sendObjectResponse(message, {
      company: foundCompany,
      admins: foundCompany.Users.map((user) => user.toJSON()),
    });
  },

  async getBalanceHistory(company, currency) {
    return BalanceLedger.findAll({ where: { company, currency } });
  },
  async expenseAccountStatement({ start_date, end_date, account_type, status_type, paginate, companyId }) {
    const conditions = {
      created_at: {
        [Op.between]: [start_date, end_date],
      },
      amount: {
        [Op.lt]: 0,
      },
    };
    let result;
    if (status_type) {
      const getBudget = await BudgetRepo.getOneBudget({
        queryParams: { code: status_type },
      });
      conditions.budget = getBudget.id;
    }

    result = await BudgetLedgerRepo.findAndCountAllTransactions({
      conditions,
      inclusions: true,
      paginate,
      budgetCondition: { company: companyId },
    });
    return { result, type: "expense" };
  },

  async expenseDownloadAccountStatement({ start_date, end_date, account_type, status_type, companyId }) {
    const conditions = {
      created_at: {
        [Op.between]: [start_date, end_date],
      },
      amount: {
        [Op.lt]: 0,
      },
    };
    let result;
    if (status_type) {
      const getBudget = await BudgetRepo.getOneBudget({
        queryParams: { code: status_type },
      });
      conditions.budget = getBudget.id;
    }
    result = await BudgetLedgerRepo.findAllTransactions({
      conditions,
      inclusions: true,
      budgetCondition: { company: companyId },
    });
    return { result, type: "expense" };
  },

  async collectionAccountStatement({ start_date, end_date, account_type, paginate, companyId }) {
    const conditions = {
      status: STATUSES.SUCCESS,
      created_at: {
        [Op.between]: [start_date, end_date],
      },
      invoice: {
        [Op.ne]: null,
      },
      company: companyId,
    };
    let result;

    if (account_type === "debit") {
      conditions.amount = {
        [Op.lt]: 0,
      };
    }
    if (account_type === "credit") {
      conditions.amount = {
        [Op.gt]: 0,
      };
    }

    result = await TransferService.findAndCountAllTransfer({
      conditions,
      paginate,
      inclusions: true,
    });

    return { result, type: "collection" };
  },

  async collectionDownloadAccountStatement({ start_date, end_date, account_type, companyId }) {
    const conditions = {
      status: STATUSES.SUCCESS,
      created_at: {
        [Op.between]: [start_date, end_date],
      },
      company: companyId,
    };
    let result;

    if (account_type === "debit") {
      conditions.amount = {
        [Op.lt]: 0,
      };
    }
    if (account_type === "credit") {
      conditions.amount = {
        [Op.gt]: 0,
      };
    }

    result = await TransferService.findAllTransfer({
      conditions,
      inclusions: true,
    });

    return { result, type: "collection" };
  },

  getDetailsFromNarration(description) {
    const accountDetails = String(description).split("from ").pop();
    const splittedAccountDetails = accountDetails.replace(/[()]/g, "*").replace(/.$/, "").split("*");
    return {
      source: splittedAccountDetails[0],
      number: splittedAccountDetails[1],
    };
  },

  /**
   *
   * @param {*} companyCode The company Id
   */
  async documentSubmission(companyCode) {
    const foundCompany = await Service.getCompany({ code: companyCode });

    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[companyCode] || payment.defaultProvider;

    const accountHolder = await AccountHolderRepo.getAccountHolder({
      filter: {
        company: foundCompany.id,
        provider: CARD_ISSUER[providerToUse],
      },
    });

    if (!accountHolder) throw new NotFoundError("Account Holder");

    const { error, data } = await Providers[providerToUse].customer.getCustomerRequiredDocuments(accountHolder.externalIdentifier);
    if (error) Providers[providerToUse].throwProviderError(data);

    const requiredDocuments = Array.from(data).filter(({ attributes: { submitted, verified } }) => !submitted || !verified);
    if (requiredDocuments.length === 0) return responseUtils.sendObjectResponse("All documents submitted");
    const documentMapper = Utils.anchorDocumentMapper();
    const requiredDocsData = requiredDocuments.map((document) => {
      return {
        id: document.id,
        format: document.attributes.format,
        documentType: document.attributes.documentType,
        documentName: documentMapper[document.attributes.documentType],
      };
    });

    const documentNames = requiredDocsData.map((document) => document.documentName);

    const companyDoc = await DocumentRepo.getAllDocuments({
      queryParams: {
        reference: foundCompany.document_reference,
        type: documentNames,
      },
      selectOptions: ["asset", "type", "number"],
      includeAsset: true,
    });

    try {
      await Promise.all([
        companyDoc.map(async (doc) => {
          const jsonifiedDoc = doc.toJSON();
          const documentType = requiredDocsData.find(({ documentName }) => documentName === doc.type);
          const payload = {
            customerId: accountHolder.externalIdentifier,
            documentId: documentType.id,
          };

          if (!jsonifiedDoc.Asset) {
            // SUBMIT DOCUMENT THAT ARE TEXT
            const formData = new FormData();
            formData.append("textData", jsonifiedDoc.number);
            return Providers[providerToUse].customer.uploadCustomerDocument({
              formData,
              payload,
            });
          }

          const asset = jsonifiedDoc.Asset;
          const accessUrl = await AssetService.getAssetDownloadURL({
            key: asset.key,
          });
          const { data } = await axios.get(accessUrl);

          const formData = new FormData();
          formData.append("fileData", data, asset.name);
          return Providers[providerToUse].customer.uploadCustomerDocument({
            formData,
            payload,
          });
        }),
      ]);

      return responseUtils.sendObjectResponse("Documents have been submitted for approval");
    } catch (error) {
      throw new ValidationError(error.message);
    }
  },

  async generateStatement(filter) {
    const { company, startDate, endDate, currency = "NGN" } = filter;

    // Fetch and aggregate balances before the specified date range
    const openingBalances = await Promise.all([
      getOpeningBalance(BalanceLedger, {
        field: "balance",
        startDate,
        company,
        currency,
      }),
      getOpeningBalance(BudgetLedger, {
        field: "budget",
        company,
        currency,
        startDate,
        joinModel: Budget,
        joinCondition: `BudgetLedgers.budget = Budgets.id`,
      }),
    ]);

    let holdingBalance = openingBalances.flat().reduce((total, balance) => {
      if (balance.balanceAfter > 0) {
        total += balance.balanceAfter;
      }

      return total;
    }, 0);

    const bindings = [company, currency];

    const baseQuery = `
    SELECT 'transaction' AS type, trx.code as code, trx.amount as amount, reference,
    processor_fee, bujeti_fee, failure_reason, trx.description as description,
    trx.currency as currency, trx.created_at as created_at, trx.updated_at as updated_at, narration,
	trx.externalIdentifier as externalIdentifier, bdg.name as budgetName, bdg.code as budgetCode,
	bal.name as balanceName, bal.code as balanceCode, bac.accountName, bac.number as accountNumber FROM Transactions trx
	LEFT JOIN Budgets bdg ON bdg.id = trx.budget
	LEFT JOIN Balances as bal ON bal.id = trx.balance
	LEFT JOIN BankAccounts bac ON bac.id = trx.bank_account
	WHERE trx.company = $1 AND trx.status = ${STATUSES.SUCCESS} AND trx.currency = $2
	${startDate ? HelperService.addDateToBaseQuery({ bindings, date: startDate, table: "trx", timestamp: "00:00:00", direction: "start" }) : ""}
	${endDate ? HelperService.addDateToBaseQuery({ bindings, date: endDate, table: "trx", timestamp: "23:59:59" }) : ""}
    UNION ALL
    SELECT 'transfer' AS type, trf.code as code, trf.amount as amount, reference, 
	processor_fee, bujeti_fee, failure_reason, trf.description as description, 
	trf.currency as currency, trf.created_at as created_at, trf.updated_at as updated_at, narration, 
	trf.externalIdentifier as externalIdentifier, NULL as budgetName, 
	NULL as budgetCode, bal.name as balanceName, bal.code as balanceCode, NULL AS accountName, NULL as accountNumber FROM Transfers trf
	LEFT JOIN BalanceLedgers AS blg ON blg.transfer = trf.id
	LEFT JOIN Balances as bal ON bal.id = blg.balance
    WHERE trf.company = $1 AND reference LIKE '%anc_py'
    AND trf.status IN (${STATUSES.SUCCESS}, ${STATUSES.PENDING}) AND trf.currency = $2
	${startDate ? HelperService.addDateToBaseQuery({ bindings, date: startDate, table: "trf", timestamp: "00:00:00", direction: "start" }) : ""}
	${endDate ? HelperService.addDateToBaseQuery({ bindings, date: endDate, table: "trf", timestamp: "23:59:59" }) : ""}
    `;

    const totalTransactions = await sequelize.query(`${baseQuery} ORDER BY created_at ASC`, {
      bind: bindings,
      type: QueryTypes.SELECT,
    });

    const formattedTransactionWithFees = [];

    totalTransactions.forEach((transaction) => {
      const {
        amount,
        code,
        description,
        budgetName,
        budgetCode,
        balanceName,
        balanceCode,
        accountName,
        accountNumber,
        bujeti_fee: bujetiFee,
        processor_fee: processorFee,
        ...rest
      } = transaction;
      let balanceBefore;
      let balanceAfter;
      const isTransfer = rest.type === "transfer";
      const budget = budgetName ? { name: budgetName, code: budgetCode } : null;
      const to = (accountName && `${accountName}(${accountNumber})`) || budget?.name;
      const from = isTransfer ? description : budget?.name;
      const formattedAmount = isTransfer ? amount : -1 * amount;

      balanceBefore = holdingBalance;
      balanceAfter = balanceBefore + formattedAmount;
      holdingBalance = balanceAfter;

      let source = "balance";

      if (budgetName) {
        source = `Budget(${budgetName})`;
      } else if (balanceName) {
        source = `Balance(${balanceName})`;
      }

      const transactionWithoutFee = {
        ...rest,
        source,
        amount: formattedAmount,
        balanceBefore,
        balanceAfter,
        budget,
        description,
        to,
        from,
      };

      formattedTransactionWithFees.push(transactionWithoutFee);

      const transactionFee = Number(bujetiFee) + Number(processorFee);

      if (!transactionFee) {
        return formattedTransactionWithFees;
      }

      balanceBefore = holdingBalance;
      balanceAfter = balanceBefore - Number(transactionFee);
      holdingBalance = balanceAfter;

      const feeTransaction = {
        source,
        code,
        currency: transaction.currency,
        balanceBefore,
        balanceAfter,
        amount: -1 * transactionFee,
        description: `Transaction fee for ${description} (ID:${code})`,
        created_at: transaction.created_at,
        type: rest.type,
        budget,
        to: "Bujeti",
        from,
      };
      return formattedTransactionWithFees.push(feeTransaction);
    });
    return generateStatementMeta(formattedTransactionWithFees);
  },

  async generateBalanceStatementWithGroupedBatchTransaction(filter) {
    const { startDate, endDate, company, type = "all", source: sourceCode, perPage = 1000, page = 1 } = filter;

    const foundBalance = await BalanceRepo.getBalance({
      filter: { code: sourceCode, company },
    });
    if (!foundBalance) throw new NotFoundError("Balance");

    // Get holding Balance
    let holdingBalance = 0;
    if (startDate) {
      const lastTransactionBeforeStartDate = await BalanceLedgerRepo.getBalanceLedger({
        filter: {
          balance: foundBalance.id,
          currency: foundBalance.currency,
          status: [STATUSES.PROCESSED, STATUSES.PENDING, STATUSES.REVERSED],
          created_at: { [Op.lt]: `${startDate} 00:00:00` },
        },
      });
      holdingBalance = lastTransactionBeforeStartDate?.balanceAfter || 0;
    }

    const bindings = [company, foundBalance.currency, foundBalance.id];

    let baseQuery = "";

    if (["all", "debit"].includes(type)) {
      baseQuery = `
      SELECT 
      trx.id, 'transaction' AS transactionType, 0 AS "moneyIn", trx.code as code, trx.amount as amount, reference,
      processor_fee, bujeti_fee, failure_reason, trx.description as description,
      trx.currency as currency, trx.paidOn as created_at, trx.updated_at as updated_at, narration,
	    trx.externalIdentifier as externalIdentifier, blc.name as balanceName, blc.code as balanceCode,
	    bac.accountName, bac.number as accountNumber, trx.batch_id AS batch_id, COALESCE(batch_summary.total_batch_amount, 0) AS total_batch_amount, 
      COALESCE(batch_summary.transaction_count, 1) AS transaction_count, ctg.name as category, 'null' as destinationName 
    FROM Transactions trx
    LEFT JOIN (
      SELECT 
        batch_id, MIN(id) AS batch_trx_id, SUM(amount) AS total_batch_amount, count(id) as transaction_count
      FROM 
        Transactions 
      WHERE 
        batch_id IS NOT NULL AND status = ${STATUSES.SUCCESS} 
      GROUP BY batch_id) AS batch_summary ON trx.batch_id = batch_summary.batch_id
	  LEFT JOIN Balances blc ON blc.id = trx.balance
	  LEFT JOIN BankAccounts bac ON bac.id = trx.bank_account
    LEFT JOIN Categories AS ctg ON ctg.id = trx.category
	  WHERE trx.company = $1 AND trx.status = ${
      STATUSES.SUCCESS
    } AND trx.currency = $2 AND trx.balance = $3 AND trx.card IS NULL AND (trx.batch_id IS NULL OR trx.id = batch_summary.batch_trx_id)
      ${
        startDate
          ? HelperService.addDateToBaseQuery({ bindings, date: startDate, table: "trx", timestamp: "00:00:00", direction: "start", column: "paidOn" })
          : ""
      }
	    ${endDate ? HelperService.addDateToBaseQuery({ bindings, date: endDate, table: "trx", timestamp: "23:59:59", column: "paidOn" }) : ""}`;
    }

    if (baseQuery) baseQuery = `${baseQuery} UNION ALL`;

    baseQuery = `${baseQuery} SELECT 'null' as id, 'transfer' AS transactionType, 
          CASE 
            WHEN blg.amount > 0 AND (trf.description COLLATE utf8mb4_general_ci LIKE '%Transfer received from%' OR trf.description COLLATE utf8mb4_general_ci LIKE '%Withdrawal from%') THEN 1 ELSE 0 END AS "moneyIn",
        trf.code AS code, blg.amount AS amount, trf.reference AS reference,
        trf.processor_fee AS processor_fee, trf.bujeti_fee AS bujeti_fee, trf.failure_reason AS failure_reason,
        blg.description AS description, trf.currency AS currency, trf.created_at AS created_at, trf.updated_at as updated_at,
        trf.narration AS narration, trf.externalIdentifier as externalIdentifier, blc.name as balanceName, 
        blc.code as balanceCode, cpt.accountName AS accountName, cpt.number as accountNumber, NULL AS batch_id, 0 AS total_batch_amount, 1 as transaction_count, ctg.name as category, bdg.name as destinationName 
      FROM BalanceLedgers AS blg
      LEFT JOIN Transfers AS trf ON blg.transfer = trf.id 
      LEFT JOIN Balances AS blc ON blc.id = blg.balance
      LEFT JOIN CounterParties as cpt ON trf.counterParty = cpt.id
      LEFT JOIN BudgetLedgers AS bdgl ON bdgl.transfer = trf.id
      LEFT JOIN Budgets AS bdg ON bdgl.budget = bdg.id
      LEFT JOIN Categories AS ctg ON ctg.id = trf.category
      WHERE trf.status IN (${STATUSES.SUCCESS}, ${
      STATUSES.PENDING
    }) AND trf.company = $1 AND trf.currency = $2 AND blg.balance = $3 AND blg.card IS NULL AND blg.transaction IS NULL
        ${startDate ? HelperService.addDateToBaseQuery({ bindings, date: startDate, table: "trf", timestamp: "00:00:00", direction: "start" }) : ""}
        ${endDate ? HelperService.addDateToBaseQuery({ bindings, date: endDate, table: "trf", timestamp: "23:59:59" }) : ""}
        ${type === "debit" ? ` AND blg.amount < 0` : ""}
        ${type === "credit" ? ` AND blg.amount > 0` : ""}
      `;

    const transactionQuery = `SELECT 
      trx.id
    FROM Transactions trx
	  WHERE trx.company = $1 AND trx.status = ${STATUSES.SUCCESS} AND trx.currency = $2 AND trx.balance = $3 AND trx.card IS NULL 
      ${
        startDate
          ? HelperService.addDateToBaseQuery({ bindings, date: startDate, table: "trx", timestamp: "00:00:00", direction: "start", column: "paidOn" })
          : ""
      }
	    ${endDate ? HelperService.addDateToBaseQuery({ bindings, date: endDate, table: "trx", timestamp: "23:59:59", column: "paidOn" }) : ""}`;

    baseQuery = `${baseQuery} ORDER BY created_at ASC`;

    const skip = (page - 1) * perPage;
    baseQuery = `${baseQuery} LIMIT ${perPage} OFFSET ${skip}`;

    const [totalTransactions, transactionIdsResponse] = await Promise.all([
      sequelize.query(baseQuery, {
        bind: bindings,
        type: QueryTypes.SELECT,
      }),
      sequelize.query(transactionQuery, {
        bind: bindings,
        type: QueryTypes.SELECT,
      }),
    ]);

    const formattedTransactionWithFees = [];

    const transactionIds = transactionIdsResponse.map(({ id }) => +id);

    if (transactionIds.length) {
      const [{ amount: sumOfTransactionsThatWereNotDebited = 0 } = {}] = await BalanceLedgerRepo.sumLedgers({
        filters: {
          status: [STATUSES.PROCESSED, STATUSES.PENDING],
          company,
          excludedTransactions: transactionIds,
          from: startDate,
          to: endDate,
          balance: foundBalance.id,
          amount: {
            lt: 0,
          },
        },
      });

      // adjust holdingBalance to account for transactions that were not debited in the specified timeframe
      if (sumOfTransactionsThatWereNotDebited) {
        holdingBalance = Utils.money(holdingBalance)
          .minus(sumOfTransactionsThatWereNotDebited || 0)
          .toNumber();
      }
    }

    totalTransactions.forEach((transaction) => {
      const {
        amount,
        code,
        description,
        balanceName,
        balanceCode,
        accountName,
        accountNumber,
        bujeti_fee: bujetiFee = 0,
        processor_fee: processorFee = 0,
        moneyIn,
        id,
        transaction_count: transactionCount,
        total_batch_amount: totalSuccessfulBatchAmount = 0,
        batch_id: batchId,
        destinationName,
        ...rest
      } = transaction;
      let balanceBefore;
      let balanceAfter;
      const isTransfer = rest.transactionType === "transfer";
      const isDepositFee = Service.isDepositFee(description);
      const counterParty = accountName ? `${accountName}(${accountNumber})` : Utils.decodeString(description);
      const balance = balanceName ? { name: balanceName, code: balanceCode } : null;

      const isBudgetTopup = Service.isBudgetTopup(description);

      let to = counterParty;
      let from = balanceName;

      if (isTransfer) {
        if (isBudgetTopup) {
          to = destinationName;
          from = balanceName;
        } else {
          to = balanceName;
          from = counterParty;
        }
      }

      const paymentNarration = Utils.decodeString(description || rest.narration);

      let formattedAmount = Number(totalSuccessfulBatchAmount) || Number(amount);
      if (isTransfer && moneyIn === 1) {
        formattedAmount = Math.abs(amount); // Amount should be positive
      } else if (rest.transactionType === "transaction") {
        formattedAmount = -1 * amount;
      }

      if (isDepositFee) return;
      // if (isTransfer && Service.isCardTransaction(paymentNarration)) return; // This is meant to skip adding card

      balanceBefore = holdingBalance;
      balanceAfter = balanceBefore + formattedAmount;
      holdingBalance = balanceAfter;

      const source = `Balance(${balanceName})`;
      const isMoneyOut = rest.transactionType === "transaction" || Service.isCardTransaction(paymentNarration);

      const transactionWithoutFee = {
        ...rest,
        code,
        source,
        amount: formattedAmount,
        balanceBefore,
        balanceAfter,
        balance,
        description: paymentNarration,
        to,
        from,
        moneyIn,
        type: formattedAmount > 0 ? "credit" : "debit",
        moneyOut: isMoneyOut ? 1 : 0,
      };

      formattedTransactionWithFees.push(transactionWithoutFee);

      const transactionFee = Number(bujetiFee) + Number(processorFee);

      const hasNoExtraFees = Service.isCardTransaction(paymentNarration) || !transactionFee;

      if (hasNoExtraFees) {
        return formattedTransactionWithFees;
      }

      balanceBefore = holdingBalance;
      balanceAfter = balanceBefore - Number(transactionFee);
      holdingBalance = balanceAfter;

      const feeTransaction = {
        source,
        code,
        currency: transaction.currency,
        balanceBefore,
        balanceAfter,
        category: null,
        amount: Utils.money(transactionFee).times(transactionCount).times(-1).toNumber(),
        description: `${isTransfer ? "Deposit" : "Transaction"} fee for ${paymentNarration} (ID:${code})`,
        created_at: transaction.created_at,
        transactionType: rest.transactionType,
        balance,
        to: "Bujeti",
        from,
        moneyIn: 0,
        moneyOut: 1,
        type: "debit",
      };
      // eslint-disable-next-line consistent-return
      return formattedTransactionWithFees.push(feeTransaction);
    });

    return formattedTransactionWithFees;
  },

  async generateBalanceStatement(filter) {
    const companiesWithGroupedBatchStatement = Utils.parseJSON(SettingsService.get("GROUPED_BATCH_TRANSACTION_STATEMENT_COMPANIES")) || [];
    if (Array.from(companiesWithGroupedBatchStatement).includes(filter.companyCode))
      return Service.generateBalanceStatementWithGroupedBatchTransaction(filter); // For companies that want their batch transaction to show as one transaction on statement
    const { startDate, endDate, company, type = "all", source: sourceCode, perPage = 1000, page = 1, processAsync, reference = null } = filter;

    const foundBalance = await BalanceRepo.getBalance({
      filter: { code: sourceCode, company },
    });
    if (!foundBalance) throw new NotFoundError("Balance");

    // Get holding Balance
    let holdingBalance = 0;
    if (startDate) {
      const lastTransactionBeforeStartDate = await BalanceLedgerRepo.getBalanceLedger({
        filter: {
          balance: foundBalance.id,
          currency: foundBalance.currency,
          status: [STATUSES.PROCESSED, STATUSES.PENDING, STATUSES.REVERSED],
          created_at: { [Op.lt]: `${startDate} 00:00:00` },
        },
      });
      holdingBalance = lastTransactionBeforeStartDate?.balanceAfter || 0;
    }

    const bindings = [company, foundBalance.currency, foundBalance.id];

    let baseQuery = "";

    if (["all", "debit"].includes(type)) {
      baseQuery = `${baseBalanceTransactionQuery} AND trx.balance = $3 AND trx.card IS NULL 
      ${
        startDate
          ? HelperService.addDateToBaseQuery({ bindings, date: startDate, table: "trx", timestamp: "00:00:00", direction: "start", column: "paidOn" })
          : ""
      }
	    ${endDate ? HelperService.addDateToBaseQuery({ bindings, date: endDate, table: "trx", timestamp: "23:59:59", column: "paidOn" }) : ""}`;
    }

    if (baseQuery) baseQuery = `${baseQuery} UNION ALL`;

    baseQuery = `${baseQuery} ${baseBalanceLedgerQuery} AND blg.balance = $3 AND blg.card IS NULL AND blg.transaction IS NULL 
    ${startDate ? HelperService.addDateToBaseQuery({ bindings, date: startDate, table: "trf", timestamp: "00:00:00", direction: "start" }) : ""}
    ${endDate ? HelperService.addDateToBaseQuery({ bindings, date: endDate, table: "trf", timestamp: "23:59:59" }) : ""}
    ${type === "debit" ? ` AND blg.amount < 0` : ""}
    ${type === "credit" ? ` AND blg.amount > 0` : ""}`;

    baseQuery = `${baseQuery} ORDER BY created_at ASC`;

    const skip = (page - 1) * perPage;
    baseQuery = `${baseQuery} LIMIT ${perPage} OFFSET ${skip}`;

    const totalTransactions = await sequelize.query(baseQuery, {
      bind: bindings,
      type: QueryTypes.SELECT,
    });

    const formattedTransactionWithFees = [];

    const transactionIds = totalTransactions.filter(({ id }) => id !== "null").map(({ id }) => +id);

    if (transactionIds.length) {
      const [{ amount: sumOfTransactionsThatWereNotDebited = 0 } = {}] = await BalanceLedgerRepo.sumLedgers({
        filters: {
          status: [STATUSES.PROCESSED, STATUSES.PENDING],
          company,
          excludedTransactions: transactionIds,
          from: startDate,
          to: endDate,
          balance: foundBalance.id,
          amount: {
            lt: 0,
          },
        },
      });

      // adjust holdingBalance to account for transactions that were not debited in the specified timeframe
      if (sumOfTransactionsThatWereNotDebited) {
        holdingBalance = Utils.money(holdingBalance)
          .minus(sumOfTransactionsThatWereNotDebited || 0)
          .toNumber();
      }
    }

    totalTransactions.forEach((transaction) => {
      const {
        amount,
        code,
        description,
        balanceName,
        balanceCode,
        accountName,
        accountNumber,
        bujeti_fee: bujetiFee = 0,
        processor_fee: processorFee = 0,
        moneyIn,
        destinationName,
        id,
        ...rest
      } = transaction;
      let balanceBefore;
      let balanceAfter;
      const isTransfer = rest.transactionType === "transfer";
      const isDepositFee = Service.isDepositFee(description);
      const counterParty = accountName ? `${accountName}(${accountNumber})` : Utils.decodeString(description);
      const balance = balanceName ? { name: balanceName, code: balanceCode } : null;

      const isBudgetTopup = Service.isBudgetTopup(description);

      let to = counterParty;
      let from = balanceName;

      if (isTransfer) {
        if (isBudgetTopup) {
          to = destinationName;
          from = balanceName;
        } else {
          to = balanceName;
          from = counterParty;
        }
      }

      const paymentNarration = Utils.decodeString(description || rest.narration);

      let formattedAmount = amount;
      if (isTransfer && moneyIn === 1) {
        formattedAmount = Math.abs(amount); // Amount should be positive
      } else if (rest.transactionType === "transaction") {
        formattedAmount = -1 * amount;
      }
      if (isDepositFee) return;
      // if (isTransfer && Service.isCardTransaction(paymentNarration)) return; // This is meant to skip adding card

      balanceBefore = holdingBalance;
      balanceAfter = balanceBefore + formattedAmount;
      holdingBalance = balanceAfter;

      const source = `Balance(${balanceName})`;
      const isMoneyOut = rest.transactionType === "transaction" || Service.isCardTransaction(paymentNarration);

      const transactionWithoutFee = {
        ...rest,
        code,
        source,
        amount: formattedAmount,
        balanceBefore,
        balanceAfter,
        balance,
        description: paymentNarration,
        to,
        from,
        moneyIn,
        type: formattedAmount > 0 ? "credit" : "debit",
        moneyOut: isMoneyOut ? 1 : 0,
      };

      formattedTransactionWithFees.push(transactionWithoutFee);

      const transactionFee = Number(bujetiFee) + Number(processorFee);

      const hasNoExtraFees = Service.isCardTransaction(paymentNarration) || !transactionFee;

      if (hasNoExtraFees) {
        return formattedTransactionWithFees;
      }

      balanceBefore = holdingBalance;
      balanceAfter = balanceBefore - Number(transactionFee);
      holdingBalance = balanceAfter;

      const feeTransaction = {
        source,
        code,
        currency: transaction.currency,
        balanceBefore,
        balanceAfter,
        amount: -1 * transactionFee,
        description: `Transaction fee for ${paymentNarration} (ID:${code})`,
        created_at: transaction.created_at,
        transactionType: rest.transactionType,
        balance,
        to: "Bujeti",
        from,
        moneyIn,
        moneyOut: 1,
        type: "debit",
      };

      // eslint-disable-next-line consistent-return
      return formattedTransactionWithFees.push(feeTransaction);
    });

    if (processAsync) {
      return formattedTransactionWithFees;
    }

    return generateStatementMeta(formattedTransactionWithFees, { source: sourceCode, ...(reference && { reference }) });
  },

  async generateCompanyStatement(filter) {
    const { startDate, endDate, company, type, source, perPage = 50, page = 1 } = filter;
    let query = "";
    // const offset = (Number(page) - 1) * Number(perPage);
    // const limit = Number(perPage);

    if (String(source).includes("blc_")) return Service.generateBalanceStatement(filter);

    const bindings = [company, STATUSES.PENDING, STATUSES.PROCESSED, STATUSES.CANCELLED];

    if (source === "general" || String(source).includes("bdg_")) {
      query = `SELECT ba.accountName, ba.number as accountNumber, tr.amount as transactionAmount, tr.code as transactionCode, tr.description as transactionDescription, 'budget' as source, bdgl.currency, bdgl.code, bdgl.balanceBefore, bdgl.balanceAfter, bdgl.amount, bdgl.description, bdgl.created_at, bdg.name as budgetName, bdg.code as budgetCode, ca.name as categoryName,  
        CASE 
          WHEN bdgl.transfer AND bdgl.amount > 0 THEN 1 ELSE 0 END as "moneyIn", 
        CASE 
          WHEN bdgl.amount < 0 THEN "debit" ELSE "credit" END as "type" 
        FROM BudgetLedgers AS bdgl 
        INNER JOIN 
          Budgets bdg ON bdg.id = bdgl.budget 
        LEFT JOIN 
          Transactions tr ON bdgl.transaction = tr.id AND tr.status NOT IN ($4) 
        LEFT JOIN
          BankAccounts ba ON tr.bank_account = ba.id
		LEFT JOIN
		  Categories ca ON tr.category = ca.id
        WHERE 
          bdg.company = $1 AND bdgl.status IN ($2, $3)`;

      if (String(source).includes("bdg_")) {
        bindings.push(source);
        query = `${query} AND bdg.code = $${bindings.length}`;
      }

      if (type === "credit") query = `${query} AND bdgl.amount > 0`;
      if (type === "debit") query = `${query} AND bdgl.amount < 0`;

      if (startDate) {
        bindings.push(`${startDate} 00:00:00`);
        query = `${query} AND bdgl.created_at >= $${bindings.length}`;
      }
      if (endDate) {
        bindings.push(`${endDate} 23:59:59`);
        query = `${query} AND bdgl.created_at <= $${bindings.length}`;
      }
    }

    if (source === "general") query = `${query} UNION`;

    if (["general", "balance"].includes(source) || String(source).includes("blc_")) {
      query = `${query} SELECT bac.accountName, bac.number as accountNumber, trx.amount as transactionAmount, trx.code as transactionCode, trx.description as transactionDescription, 'balance' as source, trx.currency, blg.code, blg.balanceBefore, blg.balanceAfter, blg.amount, blg.description, blg.created_at, bdg.name as budgetName, bdg.code as budgetCode, ca.name as categoryName,
        CASE 
          WHEN blg.transfer AND blg.amount > 0 THEN 1 ELSE 0 END as "moneyIn",  
        CASE 
          WHEN blg.amount < 0 THEN "debit" ELSE "credit" END as "type" 
          FROM
            BalanceLedgers AS blg
          INNER JOIN
            Balances as bal ON blg.balance = bal.id 
          LEFT JOIN
            Transactions trx ON blg.transaction = trx.id AND trx.status NOT IN ($4) 
          LEFT JOIN
            BankAccounts bac ON trx.bank_account = bac.id
          LEFT JOIN
            BudgetLedgers bdlg ON blg.budgetLedger = bdlg.id
          LEFT JOIN
            Budgets bdg ON bdlg.budget = bdg.id
		  LEFT JOIN
		    Categories ca ON trx.category = ca.id
          WHERE 
            blg.company = $1 AND blg.status IN ($2, $3)`;

      if (String(source).includes("blc_")) {
        bindings.push(source);
        query = `${query} AND bal.code = $${bindings.length}`;
      }

      if (type === "credit") query = `${query} AND blg.amount > 0`;
      if (type === "debit") query = `${query} AND blg.amount < 0`;

      if (startDate) {
        bindings.push(`${startDate} 00:00:00`);
        query = `${query} AND blg.created_at >= $${bindings.length}`;
      }
      if (endDate) {
        bindings.push(`${endDate} 23:59:59`);
        query = `${query} AND blg.created_at <= $${bindings.length}`;
      }
    }

    const totalTransactions = await sequelize.query(`${query} ORDER BY created_at ASC`, {
      bind: bindings,
      type: QueryTypes.SELECT,
    });

    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[company] || payment.defaultProvider;

    const companyVirtualAccount = await BankAccountRepo.getOneBankAccount({
      queryParams: {
        owner: company,
        ownerType: "company",
        type: "virtual",
        status: STATUSES.ACTIVE,
        issuer: CARD_ISSUER[providerToUse],
      },
      selectOptions: ["number", "accountName"],
    });

    const formattedTransactionWithFees = [];
    Array.from(totalTransactions).forEach((transaction) => {
      const {
        amount,
        transactionAmount,
        transactionCode,
        transactionDescription,
        description,
        budgetName,
        budgetCode,
        accountName,
        accountNumber,
        ...rest
      } = transaction;
      const budget = budgetName ? { name: budgetName, code: budgetCode } : null;
      let to = (accountName && `${accountName}(${accountNumber})`) || (budget && budget.name) || companyVirtualAccount.accountName;
      let from = rest.source === "balance" ? companyVirtualAccount.accountName : (budget && budget.name) || companyVirtualAccount.accountName;
      if (rest.type === "credit" && rest.source === "budget") {
        to = budget.name;
        from = companyVirtualAccount.accountName;
      }
      if (!transactionAmount)
        return formattedTransactionWithFees.push({
          ...rest,
          source: rest.source === "balance" ? "Balance" : `Budget(${budgetName})`,
          amount,
          budget,
          description: transactionDescription || description,
          to,
          from,
        });
      const amountWithoutFee = amount > 0 ? transactionAmount : -1 * transactionAmount;
      const transactionWithoutFee = {
        ...rest,
        source: rest.source === "balance" ? "Balance" : `Budget(${budgetName})`,
        amount: amountWithoutFee,
        balanceAfter: transaction.balanceBefore + amountWithoutFee, // If amountWithoutFee is negative, it will subtract
        budget,
        description: transactionDescription || description,
        to,
        from,
      };
      formattedTransactionWithFees.push(transactionWithoutFee);
      const transactionFee = (amount > 0 ? 1 : -1) * (Math.abs(amount) - transactionAmount);
      const feeTransaction = {
        // source: transaction.source,
        source: rest.source === "balance" ? "Balance" : `Budget(${budgetName})`,
        code: transactionCode,
        currency: transaction.currency,
        balanceBefore: transaction.balanceBefore + amountWithoutFee,
        balanceAfter: transaction.balanceBefore + amountWithoutFee + transactionFee,
        amount: transactionFee,
        description: `Transaction fee for ${transactionDescription || description} (ID:${transactionCode})`,
        created_at: transaction.created_at,
        type: rest.type,
        budget,
        to: "Bujeti",
        from,
      };
      return formattedTransactionWithFees.push(feeTransaction);
    });

    return formattedTransactionWithFees;
  },

  async exportAccountStatement(filter) {
    const { reference, ...rest } = filter;
    const transactions = await Service.generateCompanyStatement({ ...filter });
    const firstTransaction = (transactions && transactions[0]) || {};
    const latestTransaction = (transactions && transactions[transactions.length - 1]) || {};

    let moneyIn = 0;
    let moneyOut = 0;

    transactions.map((transaction) => {
      const { amount, moneyIn: isMoneyIn } = transaction;
      if (isMoneyIn) {
        moneyIn += Math.abs(amount);
        return moneyIn;
      }
      moneyOut += amount;
      return moneyOut;
    });

    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[filter.company] || payment.defaultProvider;

    const callsToMake = [
      DocumentRepo.getOneDocument({
        queryParams: {
          reference,
          [Op.or]: [{ type: "rcNumber" }, { type: "bnNumber" }],
        },
        selectOptions: ["number"],
      }),
    ];

    if (rest.source.startsWith("bdg")) {
      const foundBuget = await BudgetRepo.getOneBudget({ queryParams: { company: filter.company, code: rest.source } });
      callsToMake.push(BudgetAccountRepo.getBudgetAccount({ filter: { company: filter.company, budget: foundBuget.id } }));
    }

    if (rest.source.startsWith("blc")) {
      callsToMake.push(BalanceRepo.getBalance({ filter: { company: filter.company, code: rest.source }, includeAccount: true }));
    }

    if (rest.source === "general") {
      callsToMake.push(BalanceRepo.find({ company: filter.company, status: STATUSES.ACTIVE }, true));
    }

    const [companyDocs, bankAccounts] = await Promise.all([...callsToMake]);

    const statementAccount = [];
    if (rest.source.startsWith("bdg") && bankAccounts) {
      statementAccount.push({ accountNumber: Utils.maskNumber(bankAccounts?.number), bankName: bankAccounts?.bankName });
    }

    if (rest.source.startsWith("blc") && bankAccounts) {
      statementAccount.push({ accountNumber: Utils.maskNumber(bankAccounts?.BankAccount.number), bankName: bankAccounts?.BankAccount.bankName });
    }

    if (rest.source === "general" && bankAccounts) {
      bankAccounts.forEach((balanceAccount) => {
        if (balanceAccount.BankAccount) {
          statementAccount.push({
            accountNumber: Utils.maskNumber(balanceAccount.BankAccount.number),
            bankName: balanceAccount.BankAccount.bankName,
          });
        }
      });
    }

    const data = {
      summary: {
        statementAccount,
        moneyIn,
        moneyOut: Math.abs(moneyOut),
        openingBalance: firstTransaction?.balanceBefore || 0,
        closingBalance: latestTransaction.closingBalance || latestTransaction?.balanceAfter || 0,
        RCNumber: companyDocs?.number || null,
      },
      transactions,
    };

    return data;
  },

  async updateCompanyDataFromLookUp(payload) {
    const { lookupCode, company } = payload;

    const [foundLookup, foundCompany] = await Promise.all([
      CompanyLookupRepo.getCompany({
        queryParams: { code: lookupCode },
      }),
      CompanyRepo.getCompany({ queryParams: { id: company } }),
    ]);

    if (!foundLookup) throw new NotFoundError("Lookup company");

    const { rcNumber, registrationDate, externalIdentifier } = foundLookup;

    const companyPayload = {
      document_reference: foundCompany?.document_reference || Utils.generateRandomString(6),
      dateOfRegistration: registrationDate,
    };

    await CompanyRepo.updateACompany({
      queryParams: { id: company },
      updateFields: companyPayload,
    });

    // Check if RC exist
    if (rcNumber) {
      const foundRCNumber = await DocumentRepo.getOneDocument({
        queryParams: { type: [DOCUMENT_MAPPER.RC_NUMBER] },
      });

      if (!foundRCNumber) {
        await DocumentRepo.createADocument({
          queryParams: {
            entity: "business",
            entityId: company,
            reference: companyPayload.document_reference,
            type: "rcNumber",
            number: `RC${rcNumber}`,
            status: STATUSES.UNVERIFIED,
            processor: "anchor",
            country: "NIGERIA",
          },
        });
      }
    }

    if (foundLookup && !foundLookup.shareholders) {
      // get share holders

      // Get default country
      const defaultCountry = await CountryRepo.getOneCountry({ queryParams: { name: DEFAULTS[COUNTRIES.NIGERIA].COUNTRY_NAME } });

      const shareholders = await Providers.mono.company.getCompanyShareholders(externalIdentifier);
      let createdDirector;
      await Promise.all(
        Array.from(shareholders).map(async (shareholder) => {
          const { idType, idNumber, percentageOwned, firstName, lastName, email, dob, address: shareholderAddress, phoneNumber } = shareholder;

          const createdAddress = await HelperService.findOrCreateAddress({ ...shareholderAddress, country: defaultCountry?.code }, true);
          const createdPhoneNumber = await HelperService.findOrCreatePhoneNumber({
            localFormat: phoneNumber,
            countryCode: DEFAULTS[COUNTRIES.NIGERIA].COUNTRY_CODE,
          });

          const directorPayload = {
            type: "director",
            firstName,
            lastName,
            ...(createdAddress && { address: createdAddress.id }),
            ...(createdPhoneNumber && { phoneNumber: createdPhoneNumber.id }),
            status: STATUSES.PENDING,
            dob,
            metadata: { directorsEmail: email },
            company,
            percentageOwned,
            ...(email && { email }),
          };
          createdDirector = await IndividualRepo.createAIndividual({ queryParams: directorPayload });
          if (idType && idNumber) {
            const createdDoc = await DocumentRepo.createADocument({
              queryParams: {
                type: idType,
                number: idNumber,
                status: STATUSES.UNVERIFIED,
                processor: "anchor",
                country: "NIGERIA",
                table_type: "individual",
                table_id: createdDirector.id,
                reference: companyPayload.document_reference,
              },
            });
            await IndividualRepo.updateAIndividual({
              queryParams: { id: createdDirector.id },
              updateFields: { document: createdDoc.id },
            });
          }
        })
      );
    }
  },

  /**
   *
   * @param {number} company the parent company ID
   * @param {Object} filters filters
   * @param {String} filters.search free text search
   * @returns
   */
  async getSubsidiaries(company, filters) {
    const query = `
    select company.code, company.name, count(beneficiaries.id) as members, company.created_at as createdAt, paymentPlan.name as paymentPlanName
    from Companies as company
    join PaymentPlans as paymentPlan on company.paymentPlan = paymentPlan.id
    left join Beneficiaries as beneficiaries on beneficiaries.company = company.id and beneficiaries.status = ${STATUSES.ACTIVE}
    where company.parent=$1
    ${
      (filters.search && `and company.name like %${filters.search}%`) || ""
    } GROUP BY company.code, company.name, beneficiaries.company, company.created_at, paymentPlan.name
    ORDER BY company.created_at ASC`;
    const subsidiaries = await sequelize.query(query, {
      bind: [company],
      type: QueryTypes.SELECT,
    });
    return subsidiaries || [];
  },
  /**
   * Get the companies the user belongs to(company or subsidiaries)
   * @param {Array} companies the companies IDs
   * @param {String} filters.search free text search
   * @returns
   */
  async getCompaniesUserBelongTo(companies) {
    // this is wrong, the function must handle the steps of getting the companies
    // and return the data to the caller as it can reused elsewhere(Refactor)
    const query = `
    select company.code, company.name, count(beneficiaries.id) as members, company.created_at as createdAt, paymentPlan.name as paymentPlanName
    from Companies as company
    join PaymentPlans as paymentPlan on company.paymentPlan = paymentPlan.id
    left join Beneficiaries as beneficiaries on beneficiaries.company = company.id and beneficiaries.status = ${STATUSES.ACTIVE} 
    where company.status IN (${STATUSES.ACTIVE},${STATUSES.VERIFIED},${STATUSES.UNVERIFIED}) and company.id IN (${companies.join(",")})
    GROUP BY company.code, company.name, beneficiaries.company, company.created_at, paymentPlan.name
    ORDER BY company.created_at ASC`;
    const subsidiaries = await sequelize.query(query, {
      type: QueryTypes.SELECT,
    });
    return subsidiaries || [];
  },

  async resendDirectorInvite({ filter }) {
    const { company, director } = filter;

    const foundCompany = await CompanyRepo.getCompanyWithAdmins({ code: company }, true);
    if (!foundCompany) throw new NotFoundError("Company");

    const foundIndividual = await IndividualRepo.getOneIndividual({
      queryParams: { code: director, company: foundCompany.id },
      selectOptions: ["firstName", "lastName", "email"],
    });
    if (!foundIndividual) throw new NotFoundError("Director");

    const { Users: admins } = foundCompany;

    const hash = Utils.generatePublicId(foundIndividual.code);
    const notificationPayload = {
      name: `${foundIndividual.firstName} ${foundIndividual.lastName}`,
      companyName: Utils.toTitle(foundCompany.name),
      adminName: Utils.toTitle(`${admins[0]?.firstName} ${admins[0]?.lastName}`),
      onboardingUrl: `${Utils.getDashboardURL()}/complete-onboarding/${hash}`,
    };
    NotificationService.notifyUser(foundIndividual, "invite-director", notificationPayload, {
      subject: `${Utils.toTitle(foundCompany.name)} onboarding process on Bujeti`,
    });
    const redisKey = String(foundIndividual.code).replace("idv_", "");
    RedisService.setex(redisKey, JSON.stringify({ prefix: "idv_", company: foundCompany.id }), 30 * 86400); // Kepts for 30 days
    return responseUtils.sendObjectResponse("Director invite sent successfully");
  },

  async updateDirector({ payload, filter }) {
    const { company, director } = filter;
    const foundCompany = await CompanyRepo.getOneCompany({
      queryParams: { code: company },
      selectOptions: ["document_reference", "status", "onboardingStatus", "onboardingLevel"],
    });
    if (!foundCompany) throw new NotFoundError("Company");

    const isCompanyOnboarding = foundCompany.onboardingStatus === STATUSES.PENDING || foundCompany.onboardingLevel !== ONBOARDING_LEVEL.LEVEL_3;
    if (!isCompanyOnboarding) throw new ValidationError("Cannot edit director for company that is not onboarding");

    const foundIndividual = await IndividualRepo.getOneIndividual({
      queryParams: { code: director, company: foundCompany.id },
      selectOptions: ["externalIdentifier", "status"],
    });
    if (!foundIndividual) throw new NotFoundError("Director");

    const { email, bvn, phoneNumber, address, documents, ...rest } = payload;

    if (email) {
      // Check email doesn't exist
      const foundEmail = await IndividualRepo.getOneIndividual({ queryParams: { email, company: foundCompany.id } });
      if (foundEmail) throw new ExistsError(`Director's email`);
      rest.email = email;
    }

    if (rest.dob && !Utils.isValidDate(rest.dob)) throw new ValidationError("Please enter a valid date of birth");

    if (rest.percentageOwned) {
      // Check Percentage Owned
      await HelperService.canAddMorePercentage({
        filter: { company: foundCompany.id, id: { [Op.ne]: foundIndividual.id } },
        percentage: rest.percentageOwned,
      });
    }

    if (bvn) {
      const existingBVN = await DocumentRepo.getOneDocument({
        queryParams: { type: DOCUMENT_MAPPER.BVN, number: bvn, reference: foundCompany.document_reference },
      });

      if (existingBVN) throw new ExistsError("BVN already exists");

      // Deactivate previous BVN for this director
      await DocumentRepo.updateADocument({
        queryParams: {
          type: DOCUMENT_MAPPER.BVN,
          status: STATUSES.UNVERIFIED,
          table_type: DOCUMENT_ENTITY_TYPE.INDIVIDUAL,
          table_id: foundIndividual.id,
        },
        updateFields: { status: STATUSES.INACTIVE },
      });
    }

    let foundIdCopy;
    let foundUtility;
    let didUpdateDocument = false;
    if (documents) {
      if (documents.idCopy) {
        foundIdCopy = await AssetService.getAsset(documents.idCopy);
        if (!foundIdCopy) throw new NotFoundError("Uploaded Document");
      }

      // Check utility
      if (documents.utilityBill) {
        foundUtility = await AssetService.getAsset(documents.utilityBill);
        if (!foundUtility) throw new NotFoundError("Utility Bill");
        // Check Existing utility
        const existingDocument = await DocumentRepo.getOneDocument({
          queryParams: {
            status: STATUSES.UNVERIFIED,
            type: DOCUMENT_MAPPER.UTILITY_BILL,
            table_type: "individual",
            table_id: foundIndividual.id,
          },
        });

        if (existingDocument) {
          // Deactivate previous document
          await DocumentRepo.updateADocument({
            queryParams: {
              id: existingDocument.id,
            },
            updateFields: { status: STATUSES.INACTIVE },
          });
        }

        // Create new Utility
        await DocumentRepo.createADocument({
          queryParams: {
            status: STATUSES.UNVERIFIED,
            processor: "anchor",
            country: "NIGERIA",
            table_type: "individual",
            table_id: foundIndividual.id,
            reference: foundCompany.document_reference,
            type: DOCUMENT_MAPPER.UTILITY_BILL,
            ...(foundUtility && { asset: foundUtility.id }),
          },
        });
      }

      if (documents.idType) {
        const existingDocument = await DocumentRepo.getOneDocument({
          queryParams: { status: STATUSES.UNVERIFIED, type: documents.idType, table_type: "individual", table_id: foundIndividual.id },
        });

        if (existingDocument) {
          didUpdateDocument = true;
          await DocumentRepo.updateADocument({
            queryParams: { id: existingDocument.id },
            updateFields: {
              ...(foundIdCopy && { asset: foundIdCopy.id }),
              ...(documents.idNumber && { number: documents.idNumber }),
            },
          });
        } else {
          await DocumentRepo.updateADocument({
            queryParams: {
              status: STATUSES.UNVERIFIED,
              table_type: "individual",
              table_id: foundIndividual.id,
              type: { [Op.notIn]: [DOCUMENT_MAPPER.BVN, DOCUMENT_MAPPER.UTILITY_BILL] },
            },
            updateFields: { status: STATUSES.INACTIVE },
          });
        }
      }
    }

    const [createdPhoneNumber, createdAddress] = await Promise.all([
      Support.findOrCreatePhoneNumber(phoneNumber),
      Support.findOrCreateAddress(address, true),
    ]);

    if (foundIndividual.status === STATUSES.INVITED && documents?.idType) rest.status = STATUSES.PENDING;

    // Update director
    await IndividualRepo.updateAIndividual({
      queryParams: { id: foundIndividual.id },
      updateFields: {
        ...rest,
        ...(createdPhoneNumber && { phoneNumber: createdPhoneNumber.id }),
        ...(createdAddress && { address: createdAddress.id }),
      },
    });

    const directorsDocumentBasicInfo = {
      status: STATUSES.UNVERIFIED,
      processor: "anchor",
      country: "NIGERIA",
      table_type: "individual",
      table_id: foundIndividual.id,
      reference: foundCompany.document_reference,
    };

    const documentPromise = [];

    if (bvn) {
      documentPromise.push(
        DocumentRepo.createADocument({
          queryParams: {
            ...directorsDocumentBasicInfo,
            type: "bvn",
            number: bvn,
          },
        })
      );
    }

    if (documents && !didUpdateDocument) {
      const { idType, idNumber, utilityBill } = documents;
      if (idType) {
        documentPromise.push(
          DocumentRepo.createADocument({
            queryParams: {
              ...directorsDocumentBasicInfo,
              type: idType,
              ...(idNumber && { number: idNumber }),
              ...(foundIdCopy && { asset: foundIdCopy.id }),
            },
          })
        );
      }
    }

    await Promise.all(documentPromise);

    const accountHolder = await AccountHolderRepo.getAccountHolder({
      filter: { company: foundCompany.id },
      selectOptions: ["externalIdentifier", "provider"],
    });

    if (accountHolder && foundIndividual.externalIdentifier) {
      const {
        CardIssuer: { name: provider },
        externalIdentifier,
      } = accountHolder;
      const { error, data } = await Providers[provider.toLowerCase()].customer.updateDirector({
        externalIdentifier,
        directorIdentifer: foundIndividual.externalIdentifier,
        payload: { ...payload, company: foundCompany.id },
      });
      if (error) Providers.anchor.throwProviderError(data);
    }

    return responseUtils.sendObjectResponse("Director updated successfully");
  },

  async createCompanyDirector({ payload }) {
    const { company, firstName, dob, lastName, bvn, phoneNumber, documents, address, ...rest } = payload;
    const foundCompany = await CompanyRepo.getOneCompany({
      queryParams: { code: company },
      selectOptions: ["document_reference", "onboardingStatus", "onboardingLevel"],
    });
    if (!foundCompany) throw new NotFoundError("Company");
    if (![STATUSES.PENDING, STATUSES.APPROVED].includes(foundCompany.onboardingStatus) || foundCompany.onboardingLevel === ONBOARDING_LEVEL.LEVEL_2)
      throw new ValidationError("Cannot upload director for company that isn't onboarding");

    // Check Director exists
    const existingDirector = await IndividualRepo.getOneIndividual({
      queryParams: { firstName, lastName, company: foundCompany.id },
    });
    if (existingDirector) throw new ExistsError("Director with this name");
    // Check BVN exists
    const existingBVN = await DocumentRepo.getOneDocument({
      queryParams: { type: "bvn", number: bvn, reference: foundCompany.document_reference },
    });

    if (existingBVN) throw new ExistsError("BVN already exists");

    if (dob && !Utils.isValidDate(dob)) throw new ValidationError("Please enter a valid date of birth");

    if (rest.percentageOwned) {
      // Check Percentage Owned
      await HelperService.canAddMorePercentage({ filter: { company: foundCompany.id }, percentage: rest.percentageOwned });
    }

    // Check that docs exist
    let foundIdCopy;
    let foundUtility;
    if (documents) {
      if (documents.idCopy) {
        foundIdCopy = await AssetService.getAsset(documents.idCopy);
        if (!foundIdCopy) throw new NotFoundError("Uploaded Document");
      }

      if (documents.utilityBill) {
        foundUtility = await AssetService.getAsset(documents.utilityBill);
        if (!foundUtility) throw new NotFoundError("Utility Bill");
      }
    }

    const [createdPhoneNumber, createdAddress] = await Promise.all([
      Support.findOrCreatePhoneNumber(phoneNumber),
      Support.findOrCreateAddress(address, true),
    ]);

    // Create director
    const createdDirector = await IndividualRepo.createAIndividual({
      queryParams: {
        ...rest,
        type: "director",
        firstName,
        lastName,
        status: STATUSES.PENDING,
        company: foundCompany.id,
        ...(dob && { dob }),
        ...(createdPhoneNumber && { phoneNumber: createdPhoneNumber.id }),
        ...(createdAddress && { address: createdAddress.id }),
      },
    });

    // Create Docs
    const directorsDocumentBasicInfo = {
      status: STATUSES.UNVERIFIED,
      processor: "anchor",
      country: "NIGERIA",
      table_type: "individual",
      table_id: createdDirector.id,
      reference: foundCompany.document_reference,
    };

    const documentPromise = [];

    documentPromise.push(
      DocumentRepo.createADocument({
        queryParams: {
          ...directorsDocumentBasicInfo,
          type: "bvn",
          number: bvn,
        },
      })
    );

    if (documents) {
      const { idType, idNumber, utilityBill } = documents;
      if (idType) {
        documentPromise.push(
          DocumentRepo.createADocument({
            queryParams: {
              ...directorsDocumentBasicInfo,
              type: idType,
              ...(idNumber && { number: idNumber }),
              ...(foundIdCopy && { asset: foundIdCopy.id }),
            },
          })
        );
      }

      if (utilityBill) {
        documentPromise.push(
          DocumentRepo.createADocument({
            queryParams: {
              ...directorsDocumentBasicInfo,
              type: DOCUMENT_MAPPER.UTILITY_BILL,
              ...(foundUtility && { asset: foundUtility.id }),
            },
          })
        );
      }
    }

    await Promise.all(documentPromise);

    return responseUtils.sendObjectResponse("Director created successfully", createdDirector);
  },

  async listCompanyDirectors({ queryParams }) {
    const foundCompany = await CompanyRepo.getOneCompany({ queryParams });
    if (!foundCompany) throw new NotFoundError("Company");

    const foundDirectors = await IndividualRepo.getAllIndividuals({
      queryParams: { company: foundCompany.id, status: { [Op.ne]: STATUSES.DELETED } },
      selectOptions: ["firstName", "lastName", "email", "dob", "percentageOwned", "email", "status"],
      includeAddress: true,
      includeDocument: true,
      includePhoneNumber: true,
    });

    return responseUtils.sendObjectResponse("Directors fetched successfully", foundDirectors);
  },

  async saveAndSendDirectorInvite({ payload }) {
    const { fullname, email, company, admin } = payload;
    const [firstName, lastName] = String(fullname).split(" ");

    let directorDetails = await IndividualRepo.getOneIndividual({
      queryParams: { email, company: company.id, status: [STATUSES.PENDING, STATUSES.INVITED, STATUSES.APPROVED] },
    });
    if (!directorDetails) {
      directorDetails = await IndividualRepo.createAIndividual({
        queryParams: {
          type: "director",
          firstName,
          lastName,
          company: company.id,
          email,
          status: STATUSES.INVITED,
          nextReminder: Utils.getFutureDate(4),
        },
      });
    }

    const hash = Utils.generatePublicId(directorDetails.code);
    const notificationPayload = {
      name: fullname,
      companyName: Utils.toTitle(company.name),
      adminName: Utils.toTitle(`${admin.firstName} ${admin.lastName}`),
      onboardingUrl: `${Utils.getDashboardURL()}/complete-onboarding/${hash}`,
    };
    NotificationService.notifyUser({ email }, "invite-director", notificationPayload, {
      subject: `${Utils.toTitle(company.name)} onboarding process on Bujeti`,
    });
    const redisKey = String(directorDetails.code).replace("idv_", "");
    RedisService.setex(redisKey, JSON.stringify({ prefix: "idv_", company: company.id }), 30 * 86400); // Kepts for 30 days
  },

  async deleteDirector({ filter }) {
    const { company, director } = filter;

    const foundCompany = await CompanyRepo.getOneCompany({
      queryParams: { code: company },
      selectOptions: ["document_reference", "status", "onboardingStatus"],
    });
    if (!foundCompany) throw new NotFoundError("Company");

    const foundIndividual = await IndividualRepo.getOneIndividual({
      queryParams: { code: director, company: foundCompany.id },
    });
    if (!foundIndividual) throw new NotFoundError("Director");

    // Delete any created documents and director
    await Promise.all([
      IndividualRepo.updateAIndividual({
        queryParams: { id: foundIndividual.id },
        updateFields: { status: STATUSES.DELETED },
      }),
      DocumentRepo.updateADocument({
        queryParams: { table_id: foundIndividual.id, table_type: "individual" },
        updateFields: { status: STATUSES.DELETED },
      }),
    ]);

    return responseUtils.sendObjectResponse("Director deleted successfully");
  },

  async getDirector({ filter }) {
    const { company, director } = filter;

    const foundCompany = await CompanyRepo.getOneCompany({
      queryParams: { code: company },
      selectOptions: ["document_reference", "status", "onboardingStatus"],
    });
    if (!foundCompany) throw new NotFoundError("Company");

    const foundIndividual = await IndividualRepo.getOneIndividual({
      queryParams: { code: director, company: foundCompany.id, status: [STATUSES.PENDING, STATUSES.INVITED] },
      selectOptions: ["firstName", "lastName", "email", "dob", "percentageOwned", "email"],
      includeDocument: true,
      includeAddress: true,
      includePhoneNumber: true,
    });
    if (!foundIndividual) throw new NotFoundError("Director");
    return responseUtils.sendObjectResponse("Director fetched successfully", foundIndividual);
  },

  async inviteDirector({ queryParams, payload }) {
    const foundCompany = await CompanyRepo.getCompanyWithAdmins(queryParams, true);
    if (!foundCompany) throw new NotFoundError("Company");

    if (foundCompany.onboardingLevel === ONBOARDING_LEVEL.LEVEL_2)
      throw new ValidationError("Cannot Invite director as company has already been approved");

    const { directors } = payload;

    const directorsEmails = Utils.mapAnArray(directors, "email");

    const foundDirectors = await IndividualRepo.getAllIndividuals({
      queryParams: { email: directorsEmails, company: foundCompany.id },
      selectOptions: ["email"],
    });

    if (foundDirectors.length) throw new ExistsError(`Director with email ${foundDirectors[0].email}`);

    const { Users: admins } = foundCompany;
    await Promise.all(
      directors.map((director) => Service.saveAndSendDirectorInvite({ payload: { ...director, company: foundCompany, admin: admins[0] } }))
    );

    return responseUtils.sendObjectResponse("Director Invited successfully");
  },

  async getInvitedDirector({ queryParams }) {
    const foundDirector = await IndividualRepo.getOneIndividual({ queryParams, selectOptions: ["firstName", "lastName", "email"] });
    if (!foundDirector) throw new NotFoundError("Director");
    return responseUtils.sendObjectResponse("Invited director fetched", foundDirector);
  },

  async invitedDirectorSubmission({ queryParams, payload }) {
    const foundDirector = await IndividualRepo.getOneIndividual({ queryParams, includeCompany: true });
    if (!foundDirector) throw new NotFoundError("Director");

    const { company, bvn, phoneNumber, documents, address, ...rest } = payload;

    const { Company: foundCompany } = foundDirector;
    if (![STATUSES.PENDING, STATUSES.APPROVED].includes(foundCompany.onboardingStatus) || foundCompany.onboardingLevel === ONBOARDING_LEVEL.LEVEL_2)
      throw new ValidationError("Cannot upload director for company that isn't onboarding");

    // Check BVN exists
    // Create Docs
    const directorsDocumentBasicInfo = {
      status: STATUSES.UNVERIFIED,
      processor: "anchor",
      country: "NIGERIA",
      table_type: "individual",
      table_id: foundDirector.id,
      reference: foundCompany.document_reference,
    };

    const promisesToExecute = [];
    if (bvn) {
      const existingBVN = await DocumentRepo.getOneDocument({
        queryParams: { type: "bvn", number: bvn, reference: foundCompany.document_reference },
      });

      if (existingBVN) throw new ExistsError("BVN already exists");

      promisesToExecute.push(
        DocumentRepo.createADocument({
          queryParams: {
            ...directorsDocumentBasicInfo,
            type: "bvn",
            number: bvn,
          },
        })
      );
    }

    if (rest.dob && !Utils.isValidDate(rest.dob)) throw new ValidationError("Please enter a valid date of birth");

    const { idType, idNumber } = documents || {};
    if (documents && documents.idType) {
      // get existing docs
      const foundIdCopy = await AssetService.getAsset(documents.idCopy);
      if (!foundIdCopy) throw new NotFoundError("Uploaded Document");

      const document = {
        status: STATUSES.VERIFYING,
        processor: "anchor",
        country: "NIGERIA",
        table_type: "individual",
        table_id: foundDirector.id,
        reference: foundCompany.document_reference,
        type: idType,
        ...(idNumber && { number: idNumber }),
        ...(foundIdCopy && { asset: foundIdCopy.id }),
      };

      promisesToExecute.push(Support.findOrCreateDocument({ document }));
    }

    if (documents && documents.utilityBill) {
      const foundUtility = await AssetService.getAsset(documents.utilityBill);
      if (!foundUtility) throw new NotFoundError("Utility Bill");

      const existingDocs = await DocumentRepo.getOneDocument({
        queryParams: { table_id: foundDirector.id, table_type: "individual", type: DOCUMENT_MAPPER.UTILITY_BILL },
        selectOptions: ["asset"],
      });

      if (!existingDocs) {
        promisesToExecute.push(
          DocumentRepo.createADocument({
            queryParams: {
              ...directorsDocumentBasicInfo,
              type: DOCUMENT_MAPPER.UTILITY_BILL,
              asset: foundUtility.id,
            },
          })
        );
      } else if (existingDocs.asset !== foundUtility.id) {
        promisesToExecute.push(
          DocumentRepo.updateADocument({
            queryParams: { id: existingDocs.id },
            updateFields: { status: STATUSES.INACTIVE },
          }),

          DocumentRepo.createADocument({
            queryParams: {
              ...directorsDocumentBasicInfo,
              type: DOCUMENT_MAPPER.UTILITY_BILL,
              asset: foundUtility.id,
            },
          })
        );
      }
    }

    if (phoneNumber) {
      const createdPhoneNumber = await Support.findOrCreatePhoneNumber(phoneNumber);
      rest.phoneNumber = createdPhoneNumber.id;
    }

    if (address) {
      const createdAddress = await Support.findOrCreateAddress(address, true);
      rest.address = createdAddress.id;
    }

    // Update director
    await IndividualRepo.updateAIndividual({
      queryParams: { id: foundDirector.id },
      updateFields: { ...rest, nextReminder: null, status: STATUSES.PENDING },
    });

    await Promise.all([...promisesToExecute]);

    if (documents && documents.idType) {
      const redisKey = String(foundDirector.code).replace("idv_", "");
      RedisService.delete(redisKey);
    }

    return responseUtils.sendObjectResponse("Director details updated successfully");
  },

  async verifySubmittedDocs({ submittedDocs, requiredDocs, company }) {
    const requiredDocumentKeysHash = Utils.convertArrayOfObjectToHashMap(requiredDocs, "value");
    const submittedDocsKey = Object.keys(submittedDocs);
    const isValidDocuments = submittedDocsKey.every((key) => requiredDocumentKeysHash[key]);
    if (!isValidDocuments) {
      const requiredDocumentNames = Utils.mapAnArray(requiredDocs, "name").join(", ");
      const errorMessage = `Only ${requiredDocumentNames} is required for your company`;
      throw new ValidationError(errorMessage);
    }

    if (!company.document_reference) return; // Haven't submitted any docs;

    submittedDocsKey.forEach((value) => {
      const submittedValue = submittedDocs[value];
      const foundRequiredDocs = requiredDocs.find(({ value: docValue }) => value === docValue);
      const isFileSubmitted = submittedValue.startsWith("ast_");
      if (foundRequiredDocs.type === "file" && !isFileSubmitted) throw new ValidationError("Please submit a file");
      else if (foundRequiredDocs.type === "text" && isFileSubmitted) throw new ValidationError("Please submit a text for this document");
    });

    // Check if any is approved
    const previousApprovedDoc = await DocumentRepo.getAllDocuments({
      queryParams: {
        status: STATUSES.APPROVED,
        type: submittedDocsKey,
        reference: company.document_reference,
        table_type: "business",
        table_id: company.id,
      },
    });
    if (previousApprovedDoc.length) throw new ValidationError("Cannot submit already approved document");
  },

  async uploadCompanyDocument({ type, document, extras = {} }) {
    const isFile = document.startsWith("ast_");
    let foundAsset;
    if (isFile) {
      foundAsset = await AssetService.getAsset(document);
      if (!foundAsset) throw new NotFoundError("Document");
    }

    const existingDocs = await DocumentRepo.getOneDocument({
      queryParams: { table_id: extras.table_id, table_type: "business", type },
      selectOptions: ["asset"],
    });

    if (!existingDocs) {
      await DocumentRepo.createADocument({
        queryParams: {
          ...extras,
          type,
          ...(!isFile && { number: document }),
          ...(isFile && { asset: foundAsset.id }),
        },
      });
    } else if (existingDocs || (foundAsset && foundAsset.id !== existingDocs.asset)) {
      await Promise.all([
        DocumentRepo.updateADocument({
          queryParams: { id: existingDocs.id },
          updateFields: { status: STATUSES.INACTIVE },
        }),

        DocumentRepo.createADocument({
          queryParams: {
            ...extras,
            type,
            ...(!isFile && { number: document }),
            ...(isFile && { asset: foundAsset.id }),
          },
        }),
      ]);
    }
  },

  async uploadCompanyDocuments({ filter, payload }) {
    const foundCompany = await CompanyRepo.getCompany({ queryParams: filter });
    if (!foundCompany) throw new NotFoundError("Company");
    const requiredDocs = DocumentRepo.getCompanyTypeRequiredDocument({
      companyType: Utils.businessTypeMapper()[foundCompany.businessType] || foundCompany.businessType,
      registrationDate: foundCompany?.dateOfRegistration,
    });

    await Service.verifySubmittedDocs({ submittedDocs: payload, requiredDocs, company: foundCompany });

    const extras = {
      status: STATUSES.VERIFYING,
      processor: "anchor",
      country: COUNTRIES.NIGERIA,
      table_type: "business",
      table_id: foundCompany.id,
      reference: foundCompany.document_reference,
    };

    await Promise.all([...Object.keys(payload).map((type) => Service.uploadCompanyDocument({ type, document: payload[type], extras }))]);

    return responseUtils.sendObjectResponse("Documents uploaded successfully");
  },

  /**
   *
   * @param {Object} director Director Object
   */
  async sendDirectorReminder({ director }) {
    const { company, firstName, lastName, email } = director;

    const foundCompany = await CompanyRepo.getCompanyWithAdmins({ id: director.company }, true);

    if (!foundCompany) throw new NotFoundError("Company");

    const { Users: admins, name: companyName } = foundCompany;

    const admin = admins[0];

    const hash = Utils.generatePublicId(director.code);
    const notificationPayload = {
      name: `${firstName} ${lastName}`,
      companyName: Utils.toTitle(companyName),
      adminName: Utils.toTitle(`${admin.firstName} ${admin.lastName}`),
      onboardingUrl: `${Utils.getDashboardURL()}/complete-onboarding/${hash}`,
    };
    NotificationService.notifyUser({ email }, "invite-director", notificationPayload, {
      subject: `[Reminder] ${Utils.toTitle(company.name)} onboarding process on Bujeti`,
    });
    const redisKey = String(director.code).replace("idv_", "");
    RedisService.setex(redisKey, JSON.stringify({ prefix: "idv_", company: foundCompany.id }), 30 * 86400); // Kepts for 30 days

    // Update next reminder date
    await IndividualRepo.updateAIndividual({
      queryParams: { id: director.id },
      updateFields: { nextReminder: Utils.getFutureDate(4) },
    });
  },

  async sendInvitedDirectorsReminder(payload = {}) {
    // GET DIRECTORS
    const { meta: { lastId = null } = {} } = payload;
    const today = format(new Date(), "yyyy-MM-dd");
    const foundDirectors = await IndividualRepo.getDirectorsToSendReminder({
      queryParams: { status: STATUSES.INVITED, [Op.and]: where(fn("DATE", col("nextReminder")), today), ...(lastId && { id: { [Op.gt]: lastId } }) },
      selectOptions: ["firstName", "lastName", "email", "nextReminder", "company"],
    });

    if (!foundDirectors.length) return true; // No director to send reminders to;

    await Promise.all([...foundDirectors.map((director) => Service.sendDirectorReminder({ director }))]);

    // Check if there's more directors to send
    const lastDirector = foundDirectors.pop(); // Get the last item in the array
    const hasMore = await IndividualRepo.getOneIndividual({
      queryParams: { status: STATUSES.INVITED, nextReminder: today, id: { [Op.gt]: lastDirector.id } },
    });
    if (!hasMore) return true; // There's none left

    // Add a call to SQS
    const SQSPayload = {
      id: `last_reminder_id:${lastDirector.id}`,
      idempotencyKey: `last_reminder_id:${lastDirector.id}`,
      path: `/companies/directors/send-reminder`,
      key: process.env.INTRA_SERVICE_TOKEN,
      meta: {
        lastId: lastDirector.id,
      },
    };
    return QueueService.addDelayedJob({}, SQSPayload, `invited_director_reminder:${lastDirector.id}`);
  },

  isCardTransaction(description) {
    const cardTest = "Card charges for card";
    // TODO: REFACTOR this to be smarter
    const fundingTest = /(Funding of Naira virtual card|Funding of Dollar virtual card|Funding of Naira physical card|Card creation funding)/i;
    const regexCardTest = new RegExp(cardTest, "g");
    const isCardTransaction = regexCardTest.test(description) || fundingTest.test(description);
    return isCardTransaction;
  },

  isBudgetTopup(description) {
    if (!description) return false;
    return String(description).toLowerCase().includes("top up budget");
  },

  isTopupTransaction(description) {
    if (!description) return false;
    const regex = /(Card topup|Card creation funding)/i;
    return regex.test(description);
  },

  isDepositFee(description) {
    if (!description) return false;
    const isDepositFee = description?.startsWith("Deposit fee for trf_");
    return isDepositFee;
  },

  isCardCharges(description) {
    const cardTest = "Card charges for card";
    const regexCardTest = new RegExp(cardTest, "g");
    const isCardCharge = regexCardTest.test(description);
    return isCardCharge;
  },

  async generateBudgetStatementWithGroupedBatchTransaction(filter) {
    const {
      page = 1,
      perPage = 1000,
      reference = null,
      source: selectedSource,
      company,
      budget: selectedBudgetCode,
      startDate,
      endDate,
      currency = "NGN",
      processAsync,
    } = filter;

    const statementSource = selectedSource || selectedBudgetCode;

    const foundBudget = await BudgetRepo.getBudget({ code: statementSource });
    if (!foundBudget) throw new NotFoundError("Budget");

    let holdingBalance = 0;
    if (startDate) {
      const lastTransactionBeforeStartDate = await BudgetLedger.findOne({
        where: {
          budget: foundBudget.id,
          currency,
          created_at: { [Op.lt]: `${startDate} 00:00:00` },
          status: [STATUSES.PROCESSED, STATUSES.PENDING, STATUSES.REVERSED],
        },
        order: [["created_at", "DESC"]],
      });
      holdingBalance = lastTransactionBeforeStartDate?.balanceAfter || 0;
    }

    const bindings = [company, currency, foundBudget.id];

    let baseQuery = `
    SELECT 
      'transaction' AS transactionType, 0 AS "moneyIn", trx.code as code, trx.amount as amount, reference,
      processor_fee, bujeti_fee, failure_reason, trx.description as description,
      trx.currency as currency, trx.paidOn as created_at, trx.updated_at as updated_at, narration,
	    trx.externalIdentifier as externalIdentifier, bdg.name as budgetName, bdg.code as budgetCode,
	    bac.accountName, bac.number as accountNumber, trx.batch_id AS batch_id, COALESCE(batch_summary.total_batch_amount, 0) AS total_batch_amount, 
      COALESCE(batch_summary.transaction_count, 1) AS transaction_count, 'null' as destinationName
    FROM Transactions trx
    LEFT JOIN (
      SELECT 
        batch_id, MIN(id) AS batch_trx_id, SUM(amount) AS total_batch_amount, count(id) as transaction_count
      FROM 
        Transactions 
      WHERE 
        batch_id IS NOT NULL AND status = ${STATUSES.SUCCESS} 
      GROUP BY batch_id) AS batch_summary ON trx.batch_id = batch_summary.batch_id
	  LEFT JOIN Budgets bdg ON bdg.id = trx.budget
	  LEFT JOIN BankAccounts bac ON bac.id = trx.bank_account
	  WHERE trx.company = $1 AND trx.status = ${
      STATUSES.SUCCESS
    } AND trx.currency = $2 AND trx.budget = $3 AND trx.card IS NULL AND (trx.batch_id IS NULL OR trx.id = batch_summary.batch_trx_id)
      ${
        startDate
          ? HelperService.addDateToBaseQuery({ bindings, date: startDate, table: "trx", timestamp: "00:00:00", direction: "start", column: "painOn" })
          : ""
      }
	    ${endDate ? HelperService.addDateToBaseQuery({ bindings, date: endDate, table: "trx", timestamp: "23:59:59", column: "paidOn" }) : ""} 
    UNION ALL
    SELECT 
      'transfer' AS transactionType, 
        CASE 
      		WHEN trf.amount < 0 AND (trf.description COLLATE utf8mb4_general_ci LIKE '%Top up Budget%' OR trf.description COLLATE utf8mb4_general_ci LIKE '%Increase budget amount%') THEN 1 ELSE 0 END AS "moneyIn",
      trf.code AS code, bdlg.amount AS amount, trf.reference AS reference,
	    trf.processor_fee AS processor_fee, trf.bujeti_fee AS bujeti_fee, trf.failure_reason AS failure_reason,
	    bdlg.description AS description, trf.currency AS currency, trf.created_at AS created_at, trf.updated_at as updated_at,
	    trf.narration AS narration, trf.externalIdentifier as externalIdentifier, bdg.name as budgetName, 
	    bdg.code as budgetCode, NULL AS accountName, NULL as accountNumber, NULL AS batch_id, 0 AS total_batch_amount, 0 as transaction_count, blc.name as destinationName 
	  FROM BudgetLedgers AS bdlg
	  LEFT JOIN Transfers AS trf ON bdlg.transfer = trf.id 
	  LEFT JOIN Budgets AS bdg ON bdg.id = bdlg.budget
    LEFT JOIN BalanceLedgers AS bll ON bll.transfer = trf.id
    LEFT JOIN Balances AS blc ON bll.balance = blc.id
	  WHERE trf.status IN (${STATUSES.SUCCESS}, ${
      STATUSES.PENDING
    }) AND trf.company = $1 AND trf.currency = $2 AND bdlg.budget = $3 AND bdlg.card IS NULL AND bdlg.transaction IS NULL
	    ${startDate ? HelperService.addDateToBaseQuery({ bindings, date: startDate, table: "trf", timestamp: "00:00:00", direction: "start" }) : ""}
	    ${endDate ? HelperService.addDateToBaseQuery({ bindings, date: endDate, table: "trf", timestamp: "23:59:59" }) : ""}
  `;

    baseQuery = `${baseQuery} ORDER BY created_at ASC`;

    const skip = (page - 1) * perPage;
    baseQuery = `${baseQuery} LIMIT ${perPage} OFFSET ${skip}`;

    const totalTransactions = await sequelize.query(baseQuery, {
      bind: bindings,
      type: QueryTypes.SELECT,
    });

    const formattedTransactionWithFees = [];

    totalTransactions.forEach((transaction) => {
      const {
        amount,
        code,
        description,
        budgetName,
        budgetCode,
        balanceName,
        balanceCode,
        accountName,
        accountNumber,
        bujeti_fee: bujetiFee,
        processor_fee: processorFee,
        moneyIn,
        transaction_count: transactionCount,
        total_batch_amount: totalSuccessfulBatchAmount = 0,
        batch_id: batchId,
        destinationName,
        ...rest
      } = transaction;
      let balanceBefore;
      let balanceAfter;
      const isTransfer = rest.transactionType === "transfer";
      const budget = budgetName ? { name: budgetName, code: budgetCode } : null;
      const counterParty = accountName ? `${accountName}(${accountNumber})` : Utils.decodeString(description);

      const isBudgetTopup = Service.isBudgetTopup(description);

      let to = counterParty;
      let from = budgetName;

      if (isTransfer) {
        if (isBudgetTopup) {
          to = destinationName;
          from = budgetName;
        } else {
          to = budgetName;
          from = counterParty;
        }
      }

      let formattedAmount = totalSuccessfulBatchAmount || amount;
      if (isTransfer && moneyIn === 1) {
        formattedAmount = Math.abs(amount); // Amount should be positive
      } else if (rest.transactionType === "transaction") {
        formattedAmount = -1 * (totalSuccessfulBatchAmount || amount);
      }

      const paymentNarration = Utils.decodeString(description || rest.narration);

      // if (isTransfer && Service.isCardTransaction(paymentNarration)) return; // This is meant to skip adding card

      balanceBefore = holdingBalance;
      balanceAfter = balanceBefore + formattedAmount;
      holdingBalance = balanceAfter;

      const source = `Budget(${budgetName})`;

      const transactionWithoutFee = {
        ...rest,
        code,
        source,
        amount: formattedAmount,
        balanceBefore,
        balanceAfter,
        budget,
        description: paymentNarration,
        to,
        from,
        moneyIn,
        type: formattedAmount > 0 ? "credit" : "debit",
        moneyOut: rest.transactionType === "transaction" || Service.isCardTransaction(paymentNarration) ? 1 : 0,
      };

      formattedTransactionWithFees.push(transactionWithoutFee);

      const transactionFee = Number(bujetiFee) + Number(processorFee);

      if (!transactionFee || Service.isCardTransaction(paymentNarration)) {
        // eslint-disable-next-line consistent-return
        return formattedTransactionWithFees;
      }

      balanceBefore = holdingBalance;
      balanceAfter = balanceBefore - Number(transactionFee);
      holdingBalance = balanceAfter;

      const feeTransaction = {
        source,
        code,
        currency: transaction.currency,
        balanceBefore,
        balanceAfter,
        amount: Utils.money(transactionFee).times(transactionCount).times(-1).toNumber(),
        description: `Transaction fee for ${paymentNarration} (ID:${code})`,
        created_at: transaction.created_at,
        transactionType: rest.transactionType,
        budget,
        to: "Bujeti",
        from,
        moneyIn,
        moneyOut: 1,
        type: "debit",
      };
      // eslint-disable-next-line consistent-return
      return formattedTransactionWithFees.push(feeTransaction);
    });

    if (processAsync) {
      return formattedTransactionWithFees;
    }

    return generateStatementMeta(formattedTransactionWithFees, { source: statementSource, ...(reference && { reference }) });
  },

  async initiateAccountStatement({ filter }) {
    const { company, source, ...rest } = filter;
    // Confirm source
    let foundSource;
    let statementSource;
    if (source.includes("bdg_")) {
      statementSource = PAYMENT_SOURCES.BUDGET;
      foundSource = await BudgetRepo.getOneBudget({ queryParams: { code: source, company } });
    } else if (source.includes("blc_")) {
      statementSource = PAYMENT_SOURCES.BALANCE;
      foundSource = await BalanceRepo.getBalance({ filter: { code: source, company } });
    } else {
      statementSource = PAYMENT_SOURCES.CARD;
      foundSource = await VirtualCardRepo.find({ code: source, company });
    }

    if (!foundSource) throw new NotFoundError("Statement source");

    // Confirm there's at least one transaction for the date range
    let foundTransactions = [];
    switch (statementSource) {
      case PAYMENT_SOURCES.BALANCE:
        foundTransactions = await Service.generateBalanceStatement({ ...rest, source, company, page: 1, perPage: 1, processAsync: true });
        break;
      case PAYMENT_SOURCES.BUDGET:
        foundTransactions = await Service.generateBudgetStatement({ ...rest, source, company, page: 1, perPage: 1, processAsync: true });
        break;
      case PAYMENT_SOURCES.CARD:
        foundTransactions = await Service.generateCardStatement({ ...rest, source, company, page: 1, perPage: 1, processAsync: true });
        break;
      default:
        break;
    }
    if (!foundTransactions.length) throw new ValidationError("No transactions found");

    // Add to queue
    const SQSPayload = {
      id: `statement_source:${foundSource.code}`,
      idempotencyKey: `statement_source:${foundSource.code}`,
      path: `/companies/transactions/statement-notification`,
      key: process.env.INTRA_SERVICE_TOKEN,
      data: filter,
    };
    await QueueService.addDelayedJob({}, SQSPayload, `statement_source:${foundSource.code}`);
    return responseUtils.sendObjectResponse("Statement is been generated and will be sent to your email");
  },

  async generateBudgetStatement(filter) {
    const companiesWithGroupedBatchStatement = Utils.parseJSON(SettingsService.get("GROUPED_BATCH_TRANSACTION_STATEMENT_COMPANIES")) || [];
    if (Array.from(companiesWithGroupedBatchStatement).includes(filter.companyCode))
      return Service.generateBudgetStatementWithGroupedBatchTransaction(filter); // For companies that want their batch transaction to show as one transaction on statement
    const {
      page = 1,
      perPage = 1000,
      type,
      reference = null,
      source: selectedSource,
      company,
      budget: selectedBudgetCode,
      startDate,
      endDate,
      currency = "NGN",
      processAsync,
    } = filter;

    const statementSource = selectedSource || selectedBudgetCode;

    const foundBudget = await BudgetRepo.getBudget({ code: statementSource });
    if (!foundBudget) throw new NotFoundError("Budget");

    let holdingBalance = 0;
    if (startDate) {
      const lastTransactionBeforeStartDate = await BudgetLedger.findOne({
        where: {
          budget: foundBudget.id,
          currency,
          created_at: { [Op.lt]: `${startDate} 00:00:00` },
          status: [STATUSES.PROCESSED, STATUSES.PENDING, STATUSES.REVERSED],
        },
        order: [["created_at", "DESC"]],
      });
      holdingBalance = lastTransactionBeforeStartDate?.balanceAfter || 0;
    }

    const bindings = [company, currency, foundBudget.id];
    let baseQuery = "";

    if (["all", "debit"].includes(type)) {
      baseQuery = `${baseBudgetTransactionQuery} AND trx.budget = $3 AND trx.card IS NULL 
      ${
        startDate
          ? HelperService.addDateToBaseQuery({ bindings, date: startDate, table: "trx", timestamp: "00:00:00", direction: "start", column: "paidOn" })
          : ""
      }
	    ${endDate ? HelperService.addDateToBaseQuery({ bindings, date: endDate, table: "trx", timestamp: "23:59:59", column: "paidOn" }) : ""} `;
    }

    if (baseQuery) baseQuery = `${baseQuery} UNION ALL`;

    baseQuery = `${baseQuery} ${baseBudgetLedgerQuery} AND bdlg.budget = $3 AND bdlg.card IS NULL AND bdlg.transaction IS NULL 
    ${startDate ? HelperService.addDateToBaseQuery({ bindings, date: startDate, table: "trf", timestamp: "00:00:00", direction: "start" }) : ""}
	  ${endDate ? HelperService.addDateToBaseQuery({ bindings, date: endDate, table: "trf", timestamp: "23:59:59" }) : ""}`;

    if (type === "debit") baseQuery = `${baseQuery} AND bdlg.amount < 0`;
    if (type === "credit") baseQuery = `${baseQuery} AND bdlg.amount > 0`;

    baseQuery = `${baseQuery} ORDER BY created_at ASC`;

    const skip = (page - 1) * perPage;
    baseQuery = `${baseQuery} LIMIT ${perPage} OFFSET ${skip}`;

    const totalTransactions = await sequelize.query(baseQuery, {
      bind: bindings,
      type: QueryTypes.SELECT,
    });

    const formattedTransactionWithFees = [];

    totalTransactions.forEach((transaction) => {
      const {
        amount,
        code,
        description,
        budgetName,
        budgetCode,
        balanceName,
        balanceCode,
        accountName,
        accountNumber,
        bujeti_fee: bujetiFee,
        processor_fee: processorFee,
        moneyIn,
        destinationName,
        ...rest
      } = transaction;
      let balanceBefore;
      let balanceAfter;
      const isTransfer = rest.transactionType === "transfer";
      const budget = budgetName ? { name: budgetName, code: budgetCode } : null;

      const counterParty = accountName ? `${accountName}(${accountNumber})` : Utils.decodeString(description);

      const isBudgetTopup = Service.isBudgetTopup(description);

      let to = counterParty;
      let from = budgetName;

      if (isTransfer) {
        if (isBudgetTopup) {
          to = destinationName;
          from = budgetName;
        } else {
          to = budgetName;
          from = counterParty;
        }
      }

      const paymentNarration = Utils.decodeString(description || rest.narration);

      let formattedAmount = 0;
      if (isTransfer && moneyIn === 1) {
        formattedAmount = Math.abs(amount); // Amount should be positive
      } else if (rest.transactionType === "transaction") {
        formattedAmount = -1 * amount;
      } else {
        formattedAmount = amount;
      }

      // if (isTransfer && Service.isCardTransaction(paymentNarration)) return; // This is meant to skip adding card

      balanceBefore = holdingBalance;
      balanceAfter = balanceBefore + formattedAmount;
      holdingBalance = balanceAfter;

      const source = `Budget(${budgetName})`;

      const transactionWithoutFee = {
        ...rest,
        code,
        source,
        amount: formattedAmount,
        balanceBefore,
        balanceAfter,
        budget,
        description: paymentNarration,
        to,
        from,
        moneyIn,
        type: formattedAmount > 0 ? "credit" : "debit",
        moneyOut: rest.transactionType === "transaction" || Service.isCardTransaction(paymentNarration) ? 1 : 0,
      };

      formattedTransactionWithFees.push(transactionWithoutFee);

      const transactionFee = Number(bujetiFee) + Number(processorFee);

      if (!transactionFee || Service.isCardTransaction(paymentNarration)) {
        return formattedTransactionWithFees;
      }

      balanceBefore = holdingBalance;
      balanceAfter = balanceBefore - Number(transactionFee);
      holdingBalance = balanceAfter;

      const feeTransaction = {
        source,
        code,
        currency: transaction.currency,
        balanceBefore,
        balanceAfter,
        amount: -1 * transactionFee,
        description: `Transaction fee for ${paymentNarration} (ID:${code})`,
        created_at: transaction.created_at,
        transactionType: rest.transactionType,
        budget,
        to: "Bujeti",
        from,
        moneyIn,
        moneyOut: 1,
        type: "debit",
      };
      return formattedTransactionWithFees.push(feeTransaction);
    });

    if (processAsync) {
      return formattedTransactionWithFees;
    }

    return generateStatementMeta(formattedTransactionWithFees, { source: statementSource, ...(reference && { reference }) });
  },

  async regenerateInviteCode(code) {
    const company = await CompanyRepo.getOneCompany({
      queryParams: { code },
      selectOptions: ["inviteCode", "name"],
    });
    await company.regenerateInviteCode();
    await company.reload();
    return company;
  },

  async verifyInviteCode(inviteCode) {
    if (!inviteCode || typeof inviteCode !== "string") {
      throw new ValidationError("Invitation code is required");
    }

    const company = await CompanyRepo.getOneCompany({
      queryParams: { inviteCode },
      selectOptions: ["id", "name", "code", "logo", "active", "status"],
      addAsset: true,
    });

    if (!company) {
      throw new ValidationError("Invalid or expired invitation code");
    }

    if (company.status === STATUSES.DELETED) {
      throw new ValidationError("This invitation code is no longer valid - company has been deleted");
    }

    return company;
  },

  async generateCardStatement(filter) {
    const { page = 1, perPage = 1000, type, reference = null, source: selectedSource, company, startDate, endDate, processAsync } = filter;

    const statementSource = selectedSource;

    const foundCard = await VirtualCardRepo.getCard({ filter: { code: statementSource } });
    if (!foundCard) throw new NotFoundError("Card");

    const { balance, budget } = foundCard;

    let holdingBalance = 0;
    if (startDate) {
      let lastTransactionBeforeStartDate;
      if (budget) {
        lastTransactionBeforeStartDate = await BudgetLedger.findOne({
          where: {
            budget,
            card: foundCard.id,
            currency: foundCard.currency,
            created_at: { [Op.lt]: `${startDate} 00:00:00` },
            status: [STATUSES.PROCESSED, STATUSES.PENDING, STATUSES.REVERSED],
          },
          order: [["created_at", "DESC"]],
        });
      } else {
        lastTransactionBeforeStartDate = await BalanceLedger.findOne({
          where: {
            balance,
            card: foundCard.id,
            currency: foundCard.currency,
            created_at: { [Op.lt]: `${startDate} 00:00:00` },
            status: [STATUSES.PROCESSED, STATUSES.PENDING, STATUSES.REVERSED],
          },
          order: [["created_at", "DESC"]],
        });
      }
      holdingBalance = lastTransactionBeforeStartDate?.balanceAfter || 0;
    }

    const bindings = [company, foundCard.currency, foundCard.id];

    if (balance) bindings.push(balance);
    if (budget) bindings.push(budget);

    let query = "";

    if (["all", "debit"].includes(type)) {
      query = `${balance ? baseBalanceTransactionQuery : baseBudgetTransactionQuery} AND trx.card = $3 AND ${
        balance ? "trx.balance = $4" : "trx.budget = $4"
      }
      ${
        startDate
          ? HelperService.addDateToBaseQuery({ bindings, date: startDate, table: "trx", timestamp: "00:00:00", direction: "start", column: "paidOn" })
          : ""
      }
	    ${endDate ? HelperService.addDateToBaseQuery({ bindings, date: endDate, table: "trx", timestamp: "23:59:59", column: "paidOn" }) : ""}`;
    }

    if (query) query = `${query} UNION ALL`;

    if (balance) {
      // For Cards whose source is balance
      query = `${query} ${baseBalanceLedgerQuery} AND blg.card = $3 AND blg.balance = $4 
      ${startDate ? HelperService.addDateToBaseQuery({ bindings, date: startDate, table: "trf", timestamp: "00:00:00", direction: "start" }) : ""}
        ${endDate ? HelperService.addDateToBaseQuery({ bindings, date: endDate, table: "trf", timestamp: "23:59:59" }) : ""}`;

      if (type === "debit") query = `${query} AND blg.amount < 0`;
      if (type === "credit") query = `${query} AND blg.amount > 0`;
    } else {
      // For Cards whose source is budget
      query = `${query} ${baseBudgetLedgerQuery} AND bdlg.card = $3 AND bdlg.budget = $4 
      ${startDate ? HelperService.addDateToBaseQuery({ bindings, date: startDate, table: "trf", timestamp: "00:00:00", direction: "start" }) : ""}
        ${endDate ? HelperService.addDateToBaseQuery({ bindings, date: endDate, table: "trf", timestamp: "23:59:59" }) : ""}`;

      if (type === "debit") query = `${query} AND bdlg.amount < 0`;
      if (type === "credit") query = `${query} AND bdlg.amount > 0`;
    }

    query = `${query} ORDER BY created_at ASC`;

    const skip = (page - 1) * perPage;
    query = `${query} LIMIT ${perPage} OFFSET ${skip}`;

    const totalTransactions = await sequelize.query(query, {
      bind: bindings,
      type: QueryTypes.SELECT,
    });

    const formattedTransactionWithFees = [];

    totalTransactions.forEach((transaction) => {
      const {
        amount,
        code,
        description,
        budgetName = null,
        balanceName = null,
        budgetCode,
        balanceCode,
        accountName,
        accountNumber,
        bujeti_fee: bujetiFee,
        processor_fee: processorFee,
        moneyIn,
        destinationName,
        ...rest
      } = transaction;
      let balanceBefore;
      let balanceAfter;
      const isTransfer = rest.transactionType === "transfer";
      let sourceObject = null;

      if (budgetName || balanceName) sourceObject = { name: budgetName || balanceName, code: budgetCode || balanceCode };

      const counterParty = accountName ? `${accountName}(${accountNumber})` : Utils.decodeString(description);

      const isTopup = Service.isTopupTransaction(description);

      let to = counterParty;
      let from = foundCard.name;

      if (isTransfer) {
        if (isTopup) {
          to = foundCard.name;
          from = budgetName || balanceName;
        } else {
          to = budgetName || balanceName;
          from = foundCard.name;
        }
      }

      const paymentNarration = Utils.decodeString(description || rest.narration);

      let formattedAmount = amount;
      if (isTransfer && moneyIn === 1) {
        formattedAmount = Math.abs(amount); // Amount should be positive
      } else if (rest.transactionType === "transaction") {
        formattedAmount = -1 * amount;
      }

      balanceBefore = holdingBalance;
      balanceAfter = balanceBefore + formattedAmount;
      holdingBalance = balanceAfter;

      const source = foundCard.name;

      const transactionWithoutFee = {
        ...rest,
        code,
        source,
        amount: formattedAmount,
        balanceBefore,
        balanceAfter,
        ...(budgetName && { budget: sourceObject }),
        ...(balanceName && { balance: sourceObject }),
        description: paymentNarration,
        to,
        from,
        moneyIn: formattedAmount > 0 ? 1 : 0,
        type: formattedAmount > 0 ? "credit" : "debit",
        moneyOut: rest.transactionType === "transaction" || Service.isCardTransaction(paymentNarration) ? 1 : 0,
      };

      formattedTransactionWithFees.push(transactionWithoutFee);

      if (rest.transactionType === "transfer") {
        return formattedTransactionWithFees;
      }

      const transactionFee = Number(bujetiFee) + Number(processorFee);

      balanceBefore = holdingBalance;
      balanceAfter = balanceBefore - Number(transactionFee);
      holdingBalance = balanceAfter;

      const feeTransaction = {
        source,
        code,
        currency: transaction.currency,
        balanceBefore,
        balanceAfter,
        amount: -1 * transactionFee,
        description: `Transaction fee for ${paymentNarration} (ID:${code})`,
        created_at: transaction.created_at,
        transactionType: rest.transactionType,
        ...(budgetName && { budget: sourceObject }),
        ...(balanceName && { balance: sourceObject }),
        to: "Bujeti",
        from,
        moneyIn: 0,
        moneyOut: 1,
        type: "debit",
      };
      return formattedTransactionWithFees.push(feeTransaction);
    });

    if (processAsync) {
      return formattedTransactionWithFees;
    }

    return generateStatementMeta(formattedTransactionWithFees, { source: statementSource, ...(reference && { reference }) });
  },
};

module.exports = Service;

const getOpeningBalance = async (Model, { field, company, startDate, currency, joinModel, joinCondition }) => {
  if (!startDate) return 0;
  const subQuery = `
        SELECT MAX(${Model.getTableName()}.id) as id
        FROM ${Model.getTableName()}
        ${joinModel ? `INNER JOIN ${joinModel.getTableName()} ON ${joinCondition}` : ""}
        WHERE ${Model.getTableName()}.created_at < :startDate AND ${Model.getTableName()}.currency = '${currency}'
        ${joinModel ? `AND ${joinModel.getTableName()}.company = :company` : `AND company = :company`}
		AND ${Model.getTableName()}.status IN (${STATUSES.PENDING}, ${STATUSES.PROCESSED}) 
		AND ${Model.getTableName()}.card IS NULL
        GROUP BY ${Model.getTableName()}.${field}
      `;
  return Model.findAll({
    where: {
      id: {
        [Op.in]: literal(`(${subQuery})`),
      },
    },
    attributes: ["id", "balanceAfter"],
    replacements: { company, startDate },
    raw: true,
  });
};
async function generateStatementMeta(formattedTransactionWithFees, extras = {}) {
  const transactions = formattedTransactionWithFees;
  const firstTransaction = (transactions && transactions[0]) || {};
  const latestTransaction = (transactions && transactions[transactions.length - 1]) || {};

  let moneyIn = 0;
  let moneyOut = 0;

  const extraSummary = {};
  if (Object.keys(extras).length) {
    const { reference, source } = extras;
    const callsToMake = [
      reference &&
        DocumentRepo.getOneDocument({
          queryParams: {
            reference,
            [Op.or]: [{ type: "rcNumber" }, { type: "bnNumber" }],
          },
          selectOptions: ["number"],
        }),
    ];

    if (source.startsWith("bdg")) {
      const foundBuget = await BudgetRepo.getOneBudget({ queryParams: { code: source } });
      callsToMake.push(BudgetAccountRepo.getBudgetAccount({ filter: { status: STATUSES.ACTIVE, budget: foundBuget.id } }));
    }

    const [companyDocs, bankAccounts] = await Promise.all([...callsToMake]);

    if (companyDocs?.number) extraSummary.RCNumber = companyDocs.number;
    const statementAccount = [];

    if (source.startsWith("bdg") && bankAccounts) {
      statementAccount.push({ accountNumber: Utils.maskNumber(bankAccounts?.number), bankName: bankAccounts?.bankName });
    }
    extraSummary.statementAccount = statementAccount;
  }

  transactions.map((transaction) => {
    const { amount, moneyIn: isTransferIn = null, moneyOut: isTransferOut = null } = transaction;
    const isMoneyIn = isTransferIn === 1;
    if (isMoneyIn) {
      moneyIn += Math.abs(amount);
      return moneyIn;
    }
    if (isTransferOut) {
      moneyOut += Math.abs(amount);
      return moneyOut;
    }
  });

  const data = {
    summary: {
      accountNumber: "general",
      moneyIn,
      moneyOut: Math.abs(moneyOut),
      openingBalance: firstTransaction?.balanceBefore || 0,
      closingBalance: latestTransaction?.balanceAfter || 0,
      ...extraSummary,
    },
    transactions,
  };

  return data;
}
