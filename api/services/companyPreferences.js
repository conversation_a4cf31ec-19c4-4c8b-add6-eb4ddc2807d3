const { DEFAULT_PREFERENCES, FEATURES } = require("../models/companyPreference");
const { CompanyPreferencesRepo, InvoiceTemplateRepo } = require("../repositories/index.repo");
const { parseJSON } = require("../utils");
const { NotFoundError } = require("../utils/error.utils");

module.exports = {
  async fetchAssociatedEntities(preferences) {
    return Promise.all(
      preferences.map(async (preference) => {
        if (
          (Number.isInteger(preference.feature) && Number(preference.feature) === FEATURES.INVOICES) ||
          FEATURES[preference.feature.toUpperCase()] === FEATURES.INVOICES
        ) {
          const templateCode = parseJSON(preference.value);
          if (preference.key === "template" && templateCode) {
            const templateFound = await InvoiceTemplateRepo.getTemplateByCode(templateCode);
            // eslint-disable-next-line no-param-reassign
            preference.value = {
              code: templateFound.code,
              name: templateFound.name,
              icon: templateFound.icon,
            };
          }
        }
        return preference;
      })
    );
  },
  async listPreferences(criteria) {
    // eslint-disable-next-line no-param-reassign
    const preferences = await CompanyPreferencesRepo.listPreferences(criteria);

    if (preferences.length !== DEFAULT_PREFERENCES.length) {
      await CompanyPreferencesRepo.generateDefaultPreferences(criteria.company, preferences);
      return CompanyPreferencesRepo.listPreferences(criteria);
    }
    return preferences;
  },
  async updatePreference(code, payload) {
    // clean the keys; only allowed keys should be passed to the repo
    // eslint-disable-next-line no-param-reassign
    let finalPayload = payload;
    if (payload.feature === FEATURES.INVOICES) {
      // eslint-disable-next-line no-use-before-define
      finalPayload = await invoicePrefences(finalPayload);
    }
    await CompanyPreferencesRepo.update({ code }, finalPayload);
  },

  async getPreference(code) {
    return CompanyPreferencesRepo.getPreference(code);
  },
};

async function invoicePrefences(payload) {
  const finalPayload = {
    ...payload,
  };
  if (payload.key === "template") {
    const foundTemplate = await InvoiceTemplateRepo.getTemplateByCode(payload.value);
    if (!foundTemplate) throw new NotFoundError("Template");
    finalPayload.value = foundTemplate.code;
  }
  return finalPayload;
}
