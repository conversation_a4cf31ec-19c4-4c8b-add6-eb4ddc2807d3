const { CronIntegrator } = require("../integrations");
const { NotFoundError } = require("../utils/error.utils");
const responseUtils = require("../utils/response.utils");

const Service = {
  async addCron(title, schedule, method, body) {
    const { url = null, ...rest } = schedule;
    const createdCron = await CronIntegrator.createAJob({
      title: title.toLowerCase(),
      url: url || title.toLowerCase(),
      method: CronIntegrator.METHODS[method.toUpperCase()],
      body,
      schedule: rest,
    });

    return responseUtils.sendObjectResponse("Crons created Successfully", createdCron.data);
  },

  async listCron() {
    const gottenJobs = await CronIntegrator.listJobs();
    return responseUtils.sendObjectResponse("Crons list retrieved Successfully", gottenJobs.data);
  },

  async getCron(jobId) {
    const gottenJob = await CronIntegrator.fetchJob(jobId);
    return responseUtils.sendObjectResponse("Cron retrieved Successfully", gottenJob.data.jobDetails);
  },

  async updateCron(jobId, data) {
    if (!jobId) throw new NotFoundError('Job ID')
    const { activate, ...rest } = data;
    await CronIntegrator.updateJob(jobId, {
      job: {
        enabled: activate,
        ...rest,
      },
    });
    return responseUtils.sendObjectResponse("Cron updated Successfully");
  },

  async deleteCron(jobId) {
    await CronIntegrator.deleteJob(jobId);
    return responseUtils.sendObjectResponse("Cron deleted Successfully");
  },

  async cronHistory(jobId) {
    const gottenJob = await CronIntegrator.fetchJobHistory(jobId);
    return responseUtils.sendObjectResponse("Cron History retrieved Successfully", gottenJob.data);
  },
};

module.exports = Service;
