const { Op } = require("sequelize");
const {
  PhoneNumberRepo,
  CustomerRepo,
  InvoiceRepo,
  AddressRepo,
  AccountHolderRepo,
  BankAccountRepo,
  CategoryRepo,
  CompanyRepo,
} = require("../repositories/index.repo");
const { STATUSES } = require("../models/status");

const { NotFoundError, ValidationError } = require("../utils/error.utils");
const customerRepo = require("../repositories/customer.repo");
const SettingsService = require("./settings");
const Providers = require("./providers");
const { CARD_ISSUER } = require("../models/cardissuer");
const ResponseUtils = require("../utils/response.utils");
const Sanitizer = require("../utils/sanitizer");
const { ONBOARDING_LEVEL } = require("../models/company");
const { syncCustomer } = require("../utils/zoho.utils");

const Service = {
  async createCustomer(payload) {
    let createdCustomer;
    const { phoneNumber, address, ...rest } = payload;
    if (rest.email) {
      createdCustomer = await CustomerRepo.getCustomer({
        filter: { email: rest.email, company: rest.company },
        addAccount: true,
      });
      if (createdCustomer && (!createdCustomer.BankAccounts || !createdCustomer.BankAccounts.length))
        Service.createCustomerAccount({ payload: { company: rest.company, customer: createdCustomer.id } });
      if (createdCustomer) return createdCustomer;
    }
    const createdPhone = phoneNumber && (await PhoneNumberRepo.createAPhoneNumber({ queryParams: phoneNumber }));
    const createdAddress = address && (await AddressRepo.createAnAddress({ queryParams: address }));
    createdCustomer = await CustomerRepo.createCustomer({
      data: {
        ...rest,
        phoneNumber: createdPhone?.id,
        address: createdAddress?.id,
      },
    });
    // Create customer account
    await Service.createCustomerAccount({ payload: { company: rest.company, customer: createdCustomer.id } });
    syncCustomer(createdCustomer.code, createdCustomer.company);
    return createdCustomer;
  },

  async updateCustomer(payload) {
    const { phoneNumber, address, code, company, ...rest } = payload;

    const customer = await customerRepo.getCustomer({ filter: { code, company } });
    if (!customer) throw new NotFoundError("Customer");

    if (phoneNumber) {
      const foundPhone = await PhoneNumberRepo.getOnePhoneNumber({ queryParams: { id: customer.phoneNumber } });
      if (foundPhone) {
        await PhoneNumberRepo.updateAPhoneNumber({
          queryParams: { id: customer.phoneNumber },
          updateFields: phoneNumber,
        });
      } else {
        const createdPhone = await PhoneNumberRepo.createAPhoneNumber({ queryParams: phoneNumber });
        rest.phoneNumber = createdPhone.id;
      }
    }

    if (address) {
      const foundAddress = await AddressRepo.getOneAddress({ queryParams: { id: customer.address } });
      if (foundAddress) {
        await AddressRepo.updateAnAddress({
          queryParams: { id: customer.address },
          updateFields: address,
        });
      } else {
        const createdAddress = await AddressRepo.createAnAddress({ queryParams: address });
        rest.address = createdAddress.id;
      }
    }

    await CustomerRepo.updateCustomer({
      queryParams: {
        code,
        company,
      },
      updateFields: rest,
    });

    return CustomerRepo.getCustomer({
      filter: { code, company },
      addAccount: true,
    });
  },

  async massCreate(payload) {
    const { customers, company } = payload;
    const categoryCache = {};

    const promises = customers.map(async (item) => {
      let customer = item;
      if (customer.firstName && customer.lastName) {
        const { lastName, firstName, ...rest } = customer;
        customer = { ...rest, name: `${firstName} ${lastName}` };
      }

      if (customer.category) {
        const foundCategory = categoryCache[customer.category] || (await CategoryRepo.getCategory({ queryParams: { code: customer.category } }));
        if (!foundCategory) throw new NotFoundError("Category");
        categoryCache[customer.category] = foundCategory;
        customer.category = foundCategory.id;
      }

      return {
        ...customer,
        taxIdentificationNumber: undefined,
        tin: customer.taxIdentificationNumber,
        company,
      };
    });

    const transformedCustomers = await Promise.all(promises);

    return Service.callService("createCustomer", transformedCustomers, {
      company,
    });
  },

  async deleteCustomer(payload) {
    const { code, company } = payload;

    const customer = await customerRepo.getCustomer({ filter: { code, company } });
    if (!customer) throw new NotFoundError("Customer");

    return CustomerRepo.updateCustomer({
      queryParams: {
        code,
        company,
      },
      updateFields: {
        status: STATUSES.DELETED,
      },
    });
  },

  async search(filter) {
    return CustomerRepo.getCustomers({ filter });
  },

  async getAll({ filter, transaction = null }) {
    const customers = await CustomerRepo.listCustomers({
      filter,
      transaction,
    });

    return customers;
  },

  async export({ filter, transaction = null }) {
    const { customers } = await CustomerRepo.listCustomers({
      filter: {
        ...filter,
        perPage: 10000,
      },
      transaction,
    });

    return customers;
  },

  async getOne({ company, code }) {
    const user = await customerRepo.getCustomer({ filter: { code, company } });
    if (!user) throw new NotFoundError("Customer");
    const list = await CustomerRepo.getCustomersTransactions({
      conditions: { customer: user.id, company },
    });
    return { customer: user, transactions_volume: list };
  },

  async getCustomerTransactions({ queryParams }) {
    const { customer, company } = queryParams;
    const foundCustomer = await CustomerRepo.getCustomer({ filter: { code: customer, company } });

    if (!foundCustomer) throw new NotFoundError("Customer");

    const [customerTransactions, chartData] = await Promise.all([
      CustomerRepo.getCustomerTransactions({ queryParams: { ...queryParams, customer: foundCustomer.id } }),
      CustomerRepo.getSpentOverDuration({ queryParams: { ...queryParams, customer: foundCustomer.id } }),
    ]);

    const { transactions, meta } = customerTransactions;
    let totalAmount = 0;
    const spent = chartData.reduce((collection, element) => {
      const { month, year, amount } = element;
      totalAmount += parseInt(amount, 10);
      element.amount = parseInt(amount, 10);
      collection[`${month}-${year}`] = element;
      return collection;
    }, {});

    return ResponseUtils.sendObjectResponse("Customer transactions fetched successfully", {
      transactions,
      meta,
      chart: { total: totalAmount, data: spent },
    });
  },

  async createCustomerAccount({ payload, transaction = null }) {
    const { customer, company } = payload;

    const [foundCustomerAccount, foundCompany] = await Promise.all([
      BankAccountRepo.getOneBankAccount({
        queryParams: {
          type: "virtual",
          currency: "NGN",
          ownerType: "customer",
          status: STATUSES.ACTIVE,
          owner: customer,
        },
      }),
      CompanyRepo.getOneCompany({
        queryParams: { id: company },
        selectOptions: ["onboardingLevel", "status"],
      }),
    ]);

    if (!foundCompany) throw new NotFoundError("Company");

    if (foundCustomerAccount) return foundCustomerAccount;

    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[company] || payment.defaultProvider;
    let accountHolder = await AccountHolderRepo.getAccountHolder({
      filter: { company, provider: CARD_ISSUER[providerToUse] },
      selectOptions: ["externalIdentifier"],
    });

    // Check for level one onboarding as they cannot perform this operation
    if (!accountHolder && foundCompany.onboardingLevel === ONBOARDING_LEVEL.LEVEL_1) throw new NotFoundError("Account Holder");

    if (!accountHolder) accountHolder = { externalIdentifier: SettingsService.get("BUJETI_ANCHOR_CUSTOMER_ID") };

    const { error, data } = await Providers[providerToUse].virtualAccount.createSubAccount(accountHolder.externalIdentifier, company);
    // eslint-disable-next-line new-cap
    if (error) throw Providers[providerToUse].throwProviderError(data);

    const {
      id,
      relationships: { virtualNubans },
    } = data;

    if (!virtualNubans || !virtualNubans.data || !virtualNubans.data.length)
      throw new ValidationError("Provider error: Couldn't get Virtual Account");

    const { error: virtualAccountError, data: virtualAccountData } = await Providers[providerToUse].virtualAccount.getVirtualAccount(
      virtualNubans.data[0].id
    );
    // eslint-disable-next-line new-cap
    if (virtualAccountError) throw Providers[providerToUse].throwProviderError(data);

    const bankAccountPayload = {
      status: STATUSES.ACTIVE,
      owner: customer,
      externalIdentifier: virtualAccountData.id,
      number: virtualAccountData.attributes.accountNumber,
      type: "virtual",
      currency: "NGN",
      subType: "deposit",
      accountName: virtualAccountData.attributes.accountName,
      bankName: virtualAccountData.attributes.bank.name,
      bankCode: virtualAccountData.attributes.bank.nipCode,
      ownerType: "customer",
      issuer: CARD_ISSUER[providerToUse],
      externalBankAccountId: id,
      isVerified: true,
      company,
    };

    return BankAccountRepo.createABankAccount({ queryParams: bankAccountPayload, transaction });
  },

  async runService(service, item, supportData) {
    return Service[service]({
      ...(typeof item === "object" ? { ...Sanitizer.jsonify(item) } : { item }),
      ...supportData,
    });
  },

  async callService(service, payload, supportData) {
    if (Array.isArray(payload)) {
      return Promise.all(payload.map((item) => Service.runService(service, item, supportData)));
    }
    return Service.runService(service, payload, supportData);
  },

  async syncCustomers({ filter }) {
    const { customers: foundCustomers } = await CustomerRepo.listCustomers({
      filter,
    });

    if (!foundCustomers.length) return [];

    return Promise.all(foundCustomers.map((customer) => syncCustomer(customer.code, filter.company)));
  },
};

module.exports = Service;
