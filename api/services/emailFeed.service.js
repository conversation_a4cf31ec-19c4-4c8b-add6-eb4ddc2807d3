const { authorize } = require("../../gmail/quickstart");
const { uploadFileBuffer } = require("./FileService");
const { Configuration, OpenAIApi } = require("openai");

// Set up OpenAI client
const configuration = new Configuration({
  apiKey: process.env.OPENAI_API_KEY,
});
const openai = new OpenAIApi(configuration);

/**
 * Reads the top 10 emails in the user's inbox.
 *
 * @param {google.auth.OAuth2} auth An authorized OAuth2 client.
 */
async function readTopEmails(auth) {
  const gmail = google.gmail({ version: "v1", auth });
  const res = await gmail.users.messages.list({
    userId: "me",
    maxResults: 10, // Retrieve only the top 10 emails
    labelIds: ["INBOX"], // Optionally specify label IDs if you want emails from a specific label
  });

  const messages = res.data.messages;
  if (!messages || messages.length === 0) {
    console.log("No emails found.");
    return;
  }

  const emails = [];
  for (const message of messages) {
    const msg = await gmail.users.messages.get({
      userId: "me",
      id: message.id,
    });
    const headers = {};
    msg.data.payload.headers.array.forEach((element) => (headers[element.name] = element.value));

    emails.push({
      senderEmail: headers.From,
      subject: headers.Subject,
      ...parseEmail(msg.data),
    });
  }
  /* emails.forEach(({ senderEmail, subject, textContent, attachments }) => {
    const emailContent = scanData({ textContent, attachments });
  }); */
  return emails;
}

function identifyEmail({ senderEmail }) {}
async function parseEmail(email) {
  let body = "";
  let attachments = [];

  // Extract body text
  if (email.payload.parts) {
    for (const part of email.payload.parts) {
      if (part.mimeType === "text/plain" && part.body.data) {
        body = Buffer.from(part.body.data, "base64").toString("utf-8");
      }

      // Scan for attachments
      if (part.filename && part.body.attachmentId) {
        const attachment = await downloadAttachment(email.id, part);
        attachments.push(attachment);
      }
    }
  }
  uploadFilesToAWS(attachments);

  return {
    textContent: body,
    attachments,
  };
}

async function scanData({ textContent, attachments }) {
  const openaiResponse = await sendToOpenAI(subject, from, textContent, attachments);
  return openaiResponse;
}

async function downloadAttachment(messageId, part) {
  const attachment = await gmail.users.messages.attachments.get({
    userId: "me",
    messageId: messageId,
    id: part.body.attachmentId,
  });

  const data = Buffer.from(attachment.data.data, "base64");
  const filePath = path.join(__dirname, part.filename);
  fs.writeFileSync(filePath, data);
  return { filename: part.filename, path: filePath, data };
}

async function uploadFilesToAWS(attachments, folder = "/invoices") {
  attachments.forEach(async (file) => {
    await uploadFileBuffer(`${folder}/${file.path}}`, file.data);
    fs.unlink(file.path);
  });
}

async function sendToOpenAI(subject, from, body, attachments) {
  const prompt = `
      Email Subject: ${subject}
      Body: ${body}
      \n\n${attachments.map((attachment) => readPdfContent(attachment.path)).join(", ")}
  
      Summarize the email content and identify any important information or action items. 
      Invoice recipient email, invoice due date, invoice amount, VAT, taxes, sub total, total amount, line items
      sender email, sender name, sender address, recipient name
    `;

  const response = await openai.createCompletion({
    model: "text-davinci-003",
    prompt: prompt,
    max_tokens: 150,
  });

  return response.data.choices[0].text.trim();
}

async function readPdfContent(filePath) {
  const dataBuffer = fs.readFileSync(filePath);
  const data = await pdf(dataBuffer);
  return data.text; // Extracted text from PDF
}

function saveData({ attachments }) {}
function notifyReceiver({ recipient, email }) {}

// authorize().then(readTopEmails).catch(console.error);

const imaps = require("imap-simple");

const config = {
  imap: {
    user: process.env.AP_EMAIL,
    password: process.env.AP_EMAIL_PASSWORD,
    host: process.env.AP_IMAP_HOST,
    port: 993,
    tls: true,
    authTimeout: 3000,
  },
};

export async function fetchEmails() {
  const emails = [];
  const connection = await imaps.connect(config);
  await connection.openBox("INBOX");
  const searchCriteria = ["UNSEEN"];
  const fetchOptions = { bodies: ["HEADER", "TEXT"], markSeen: true };

  const messages = await connection.search(searchCriteria, fetchOptions);
  messages.forEach((message) => {
    const all = message.parts.find((part) => part.which === "TEXT");
    const id = message.attributes.uid;
    const idHeader = "Imap-Id: " + id + "\r\n";
    const emailText = all.body;

    const parsedData = parseEmail(emailText);
    emails.push(parsedData);
  });
  return emails;
}
