const { ExternalIntegrationRepo, CategoryMappingRepo, CategoryRepo, SynchronizationRepo } = require("../repositories/index.repo");
const ValidationError = require("../utils/validation-error");
const { RutterIntegrator } = require("../integrations");
const { STATUSES } = require("../models/status");
const { NotFoundError, ExistsError } = require("../utils/error.utils");
const TransactionService = require("./transaction");
const CompanyService = require("./company");
const SanitizerService = require("../utils/sanitizer");
const Utils = require("../utils/utils");
const platformIdentityRepo = require("../repositories/platformIdentity.repo");
const { log, Log } = require("../utils/logger.utils");
const QueueService = require("./queue.service");
const { PLATFORM } = require("../constants/platforms");


const Service = {
  //  external integration through RutterIntegrator to generate the access token for company and user.
  async createAccessToken(user, company, token) {
    const { error, body } = await RutterIntegrator.createAccessTokenIntegration(token);
    if (error) throw new ValidationError(body.error_message);
    const { access_token, platform, is_ready, ...rest } = body;
    // Check if there's an existing integration with the same access token, company, and platform that is active.
    const integrationData = await ExternalIntegrationRepo.getIntegration({
      filter: { company, platform, access_token, status: STATUSES.ACTIVE },
      selectedOptions: ["access_token"],
    });
    if (integrationData) throw new ExistsError("Access Token");
    //  If the integration is not ready, throw a ValidationError indicating that the connection is not yet ready.
    if (!is_ready) throw new ValidationError("Connection not ready. Please try again in a few minutes");
    // Create a new user integration record with the access token and other relevant information.
    const { code, status } = await ExternalIntegrationRepo.createUserIntegration({
      payload: {
        access_token,
        status: STATUSES.ACTIVE,
        user,
        company,
        metadata: { ...rest },
        platform,
      },
    });
    return { code, platform, status };
  },

  // It retrieves the classes by utilizing an active QuickBooks integration.
  async fetchQuickBookClass(company) {
    const integrationData = await ExternalIntegrationRepo.getIntegration({
      filter: { company, platform: "QUICKBOOKS", status: STATUSES.ACTIVE },
      selectedOptions: ["access_token"],
    });
    if (!integrationData) throw new NotFoundError("Access Token");
    const { error, body } = await RutterIntegrator.getAllCategory(integrationData.access_token);
    if (error) throw new ValidationError(body.error_message);
    return body.classes;
  },

  // It uses an active QuickBooks integration to retrieve the account categories.
  async fetchQuickBookAccountCategories(company) {
    const categories = [];
    const categoriesFilter = ["expense", "other_expense", "credit_card", "accounts_payable", "accounts_receivable", "cost_of_goods_sold"];

    let nextCursor = null;
    const integrationData = await ExternalIntegrationRepo.getIntegration({
      filter: { company, platform: "QUICKBOOKS", status: STATUSES.ACTIVE },
      selectedOptions: ["access_token"],
    });

    if (!integrationData) throw new NotFoundError("Access Token");

    do {
      const { error, body } = await RutterIntegrator.getAllAccounts(integrationData.access_token, nextCursor);

      if (error) throw new ValidationError(body.error_message);

      const nextPageAccounts = body.accounts
        .filter((item) => categoriesFilter.includes(item.category) && item.status === "active")
        .map(({ id, category, name, status }) => ({
          id,
          category,
          name,
          status,
        }));

      categories.push(...nextPageAccounts);
      nextCursor = body.next_cursor;
    } while (nextCursor);

    if (!categories.length) throw new ValidationError("It appears you do not have categories on your QuickBook Account");
    return categories;
  },
  // It utilizes an active QuickBooks integration to retrieve the account information.
  async fetchQuickBookAccounts(company) {
    const integrationData = await ExternalIntegrationRepo.getIntegration({
      filter: { company, platform: "QUICKBOOKS", status: STATUSES.ACTIVE },
      selectedOptions: ["access_token"],
    });
    if (!integrationData) throw new NotFoundError("Access Token");
    let nextCursor = null;
    const accounts = [];
    do {
      const { error, body } = await RutterIntegrator.getAllAccounts(integrationData.access_token, nextCursor);
      if (error) throw new ValidationError(body.error_message);
      accounts.push(...body.accounts);
      nextCursor = body.next_cursor;
    } while (nextCursor);
    if (!accounts.length) return [];
    return accounts;
  },

  // It utilizes an active QuickBooks integration to retrieve vendor information.
  async fetchQuickBookVendors(company) {
    const integrationData = await ExternalIntegrationRepo.getIntegration({
      filter: { company, platform: "QUICKBOOKS", status: STATUSES.ACTIVE },
      selectedOptions: ["access_token"],
    });
    if (!integrationData) throw new NotFoundError("Access Token");
    let nextCursor = null;
    const vendors = [];
    do {
      const { error, body } = await RutterIntegrator.getAllVendors(integrationData.access_token, nextCursor);
      if (error) throw new ValidationError(body.error_message);
      vendors.push(...body.vendors);
      nextCursor = body.next_cursor;
    } while (nextCursor);
    if (!vendors.length) return [];
    return vendors;
  },

  // It utilizes an active QuickBooks integration to create the account
  async createQuickBookAccount(company, payload) {
    const integrationData = await ExternalIntegrationRepo.getIntegration({
      filter: { company, platform: "QUICKBOOKS", status: STATUSES.ACTIVE },
      selectedOptions: ["access_token"],
    });
    if (!integrationData) throw new NotFoundError("Access Token");
    const { error, body } = await RutterIntegrator.createAccount(integrationData.access_token, payload);
    if (error) throw new ValidationError(body.error_message);
    return body.account;
  },

  // It utilizes an active QuickBooks integration to create the vendor.
  async createQuickBookVendor(company, payload) {
    const integrationData = await ExternalIntegrationRepo.getIntegration({
      filter: { company, platform: "QUICKBOOKS", status: STATUSES.ACTIVE },
      selectedOptions: ["access_token"],
    });
    if (!integrationData) throw new NotFoundError("Access Token");
    const { error, body } = await RutterIntegrator.createVendor(integrationData.access_token, payload);
    if (error) throw new ValidationError(body.error_message);
    return body.vendor;
  },

  // checking and updating the mapping between Bujeti and QuickBooks categories.
  async createQuickBookBujetiMapping(category, company, user) {
    const bujetiCategory = await CategoryRepo.getCategory({
      queryParams: { code: category.bujetiCategory },
    });
    if (!bujetiCategory) throw new NotFoundError("Category");

    const filter = {
      company,
      category: bujetiCategory.id,
      user,
    };

    // Check if an existing mapping for the specified conditions already exists.
    const existingMapping = await CategoryMappingRepo.getCategoryMapping({
      filter,
    });

    if (existingMapping) {
      // If an existing mapping is found, update the mapping payload.
      const updatePayload = {
        category: bujetiCategory.id,
      };
      // Update QuickBooks category code if available.
      if (category.quickBooksCategory.code) {
        updatePayload.quickbook_category = category.quickBooksCategory.code;
      }

      if (category.quickBooksCategory.name) {
        updatePayload.quickbook_category_name = category.quickBooksCategory.name;
      }
      // Update the existing category mapping with the updated payload.
      await CategoryMappingRepo.updateCategoryMapping({
        id: existingMapping.id,
        payload: updatePayload,
      });
    } else {
      // If no existing mapping is found, create a new mapping payload.
      const createPayload = {
        company,
        category: bujetiCategory.id,
        user,
      };
      // Add QuickBooks category code if available.
      if (category.quickBooksCategory.code) {
        createPayload.quickbook_category = category.quickBooksCategory.code;
      }
      // Add QuickBooks category name if available.
      if (category.quickBooksCategory.name) {
        createPayload.quickbook_category_name = category.quickBooksCategory.name;
      }
      // Create a new category mapping with the created payload.
      await CategoryMappingRepo.createCategoryMapping({
        payload: createPayload,
      });
    }
  },

  // To create or update mappings for each category.
  async createCategoryMapping(transacationcategories, company, user) {
    await Promise.all(
      Array.from(transacationcategories).map((category) => {
        Service.createQuickBookBujetiMapping(category, company, user);
      })
    );
  },

  // checking the connection status, updating it to inactive, and deleting the connection.
  async disconnectQuickBookConnection(company) {
    const isConnectionExist = await ExternalIntegrationRepo.getIntegration({
      filter: { company, platform: "QUICKBOOKS", status: STATUSES.ACTIVE },
      selectedOptions: ["access_token", "metadata"],
    });
    if (!isConnectionExist) throw new ValidationError("Connection not found or already disconnected");
    const metadata = Utils.parseJSON(isConnectionExist.metadata);
    const { connection_id: connectionId } = metadata;
    await ExternalIntegrationRepo.updateIntegration({
      queryParams: { id: isConnectionExist.id },
      updateFields: {
        status: STATUSES.INACTIVE,
      },
    });
    const { error, body } = await RutterIntegrator.deleteConnection(isConnectionExist.access_token, connectionId);
    if (error) log(Log.fg.red, `An error occurred processing your request: ${body.error_message}`);
    return body;
  },

  async disconnectIntegration(company, platform) {
    return ExternalIntegrationRepo.updateIntegration({
      queryParams: {
        company,
        platform: platform.toUpperCase(),
      },
      updateFields: {
        status: STATUSES.INACTIVE,
        access_token: "",
        expirationDate: new Date(),
      },
    });
  },

  // It utilizes an active QuickBooks integration to create the transaction.
  async createQuickBookTransaction(company, payload) {
    const integrationData = await ExternalIntegrationRepo.getIntegration({
      filter: { company, platform: "QUICKBOOKS", status: STATUSES.ACTIVE },
      selectedOptions: ["access_token"],
    });
    if (!integrationData) throw new NotFoundError("Access Token");
    const { error, body } = await RutterIntegrator.createTransaction(integrationData.access_token, payload);
    if (error) throw new ValidationError(body.error_message);
    return body.expense;
  },

  async createQuickBooksTransactions(sanitizedQuickBooksTransaction, company) {
    console.log(`Synching ${sanitizedQuickBooksTransaction.length} transactions to QuickBooks`);
    const result = await Promise.all(
      Array.from(sanitizedQuickBooksTransaction).map((transaction) => {
        const SQSPayload = {
          idempotencyKey: transaction.code,
          path: `/integrations/quickbooks/process/transactions`,
          key: process.env.INTRA_SERVICE_TOKEN,
          transaction,
          company,
        };
        QueueService.addJob({ tag: "ProcessQuickBooksTransactions" }, SQSPayload, `processTransactions:${transaction.code}`);
      })
    );
    return result;
  },

  async findQuickBooksCategoryId(quickBooksCategories, quickBooksCategory) {
    const quickBooksAccountCategory = quickBooksCategories.find((qbAccountCategory) => qbAccountCategory.name === quickBooksCategory);
    return quickBooksAccountCategory ? quickBooksAccountCategory.id : null;
  },

  async getPayerAccountId(company, payer, currency_code) {
    const { id: payerId, ...restPayerItem } = payer;

    // Fetch QuickBooks accounts for the company
    const quickBookAccounts = await Service.fetchQuickBookAccounts(company);

    // Find the payer account in QuickBooks accounts by name
    const quickBookAccountPayerAccount = quickBookAccounts.find((qbAccount) => qbAccount.name === restPayerItem.name);

    if (quickBookAccountPayerAccount) {
      // Check if there is synchronization data for the payer account
      const synchronizationData = await SynchronizationRepo.getSynchronization({
        filter: {
          company,
          platform: "QUICKBOOKS",
          entity: "account",
          entity_id: payerId,
        },
        selectedOptions: ["platform_id"],
      });

      if (synchronizationData) {
        // If synchronization data exists, return the QuickBooks account ID
        return quickBookAccountPayerAccount.id;
      }
      // If QuickBooks account exists, create synchronization data and return the ID
      await SynchronizationRepo.createSynchronization({
        payload: {
          company,
          entity: "account",
          entity_id: payerId,
          platform_id: quickBookAccountPayerAccount.id,
          platform: "QUICKBOOKS",
        },
      });
      return quickBookAccountPayerAccount.id;
    }

    // If neither synchronization data nor QuickBooks account exists, create a new QuickBooks account
    const createPayerAccount = await Service.createQuickBookAccount(company, {
      account: { ...restPayerItem, currency_code },
    });

    // Create synchronization data for the new QuickBooks account and return the ID
    await SynchronizationRepo.createSynchronization({
      payload: {
        company,
        entity: "account",
        entity_id: payerId,
        platform_id: createPayerAccount.id,
        platform: "QUICKBOOKS",
      },
    });

    return createPayerAccount.id;
  },

  async getVendorRecipientId(company, recipient, currency_code) {
    const { companyId, id: vendorId, ...restRecipientItems } = recipient;

    // Fetch QuickBooks vendors for the company
    const quickBookVendors = await Service.fetchQuickBookVendors(company);

    // Find the vendor by vendor_name
    const quickBookAccountVendorAccount = quickBookVendors.find((vendor) => vendor.vendor_name === restRecipientItems.vendor_name);

    if (quickBookAccountVendorAccount) {
      // If QuickBooks vendor account exists, create synchronization data and return the ID

      // Check if there is synchronization data for the vendor
      const synchronizationData = await SynchronizationRepo.getSynchronization({
        filter: {
          company,
          platform: "QUICKBOOKS",
          entity: "vendor",
          entity_id: vendorId,
        },
        selectedOptions: ["platform_id"],
      });

      if (synchronizationData) {
        // If synchronization data exists, return the QuickBooks vendor account ID
        return quickBookAccountVendorAccount.id;
      }

      await SynchronizationRepo.createSynchronization({
        payload: {
          company,
          entity: "vendor",
          entity_id: vendorId,
          platform_id: quickBookAccountVendorAccount.id,
          platform: "QUICKBOOKS",
        },
      });
      return quickBookAccountVendorAccount.id;
    }

    // If neither synchronization data nor QuickBooks vendor account exists, create a new QuickBooks vendor
    const foundCompany = await CompanyService.getCompanyByCriteria({ criteria: { id: companyId } });
    const createVendor = await Service.createQuickBookVendor(company, {
      vendor: { ...restRecipientItems, currency: currency_code, contact_name: foundCompany.name },
    });

    // Create synchronization data for the new QuickBooks vendor and return the ID
    await SynchronizationRepo.createSynchronization({
      payload: {
        company,
        entity: "vendor",
        entity_id: vendorId,
        platform_id: createVendor.id,
        platform: "QUICKBOOKS",
      },
    });
    return createVendor.id;
  },

  async createQuickBooksExpense(company, payerAccountId, vendorRecipientId, transaction) {
    const { id, expense_type, amount, memo, description, currency_code, transaction_date, quickBooksCategoryId } = transaction;
    const synchronizationData = await SynchronizationRepo.getSynchronization({
      filter: {
        company,
        platform: "QUICKBOOKS",
        entity: "transaction",
        entity_id: id,
      },
      selectedOptions: ["platform_id"],
    });

    if (!synchronizationData) {
      const expense = {
        expense: {
          account_id: payerAccountId,
          vendor_id: vendorRecipientId,
          transaction_date,
          currency_code,
          expense_type,
          memo,
          line_items: [
            {
              account_id: quickBooksCategoryId,
              amount,
              description,
            },
          ],
        },
      };

      const createUserQuickBookTransaction = await Service.createQuickBookTransaction(company, expense);
      await SynchronizationRepo.createSynchronization({
        payload: {
          company,
          entity: "transaction",
          entity_id: id,
          platform_id: createUserQuickBookTransaction.id,
          platform: "QUICKBOOKS",
        },
      });
    }
  },

  // It involves handling the various components of the transaction and creating it in QuickBooks.
  async processTransaction(transaction, company) {
    const { payer, recipient, currency_code } = transaction;

    const quickBooksAccountCategories = await Service.fetchQuickBookAccountCategories(company);
    transaction.quickBooksCategoryId = await Service.findQuickBooksCategoryId(quickBooksAccountCategories, transaction.quickBooksCategory);

    const payerAccountId = await Service.getPayerAccountId(company, payer, currency_code);
    const vendorRecipientId = await Service.getVendorRecipientId(company, recipient, currency_code);

    if (vendorRecipientId && payerAccountId) {
      await Service.createQuickBooksExpense(company, payerAccountId, vendorRecipientId, transaction);
    } else {
      throw new ValidationError("Transactions process not complete, recipient and payer is required");
    }
  },

  /**
   *
   * @param {*} company
   * @param {Array} categories[]
   * @param {String} categories.0.bujetiCategory
   * @param {Object} categories.0.quickBooksCategory
   */

  async createQuickBookCategoryTransactions(company, categories) {
    const transactions = await TransactionService.listCategoriesTransactions({
      company,
      categories,
    });
    if (!transactions) throw new NotFoundError("Transactions");
    const sanitizedQuickBooksTransactions = SanitizerService.sanitizeForQuickBooksTransactions(transactions);
    if (!sanitizedQuickBooksTransactions.quickBooksCategory) throw ValidationError("Please provide a QuickBook Category for this Synchronization");
    await Service.createQuickBooksTransactions(sanitizedQuickBooksTransactions, company);
    return sanitizedQuickBooksTransactions.length;
  },

  async getAllCategoryMapping(company) {
    return CategoryMappingRepo.getMappingCategories({
      filter: { company, status: STATUSES.ACTIVE },
    });
  },

  async getAllCategoryMapped(company) {
    return CategoryMappingRepo.getMappedCategories({
      filter: { company, status: STATUSES.ACTIVE },
    });
  },

  async getIntegrations(company) {
    return platformIdentityRepo.getIntegrations(company);
  },

  async findIntegrations(criteria) {
    return ExternalIntegrationRepo.getIntegration({
      filter: criteria,
      selectedOptions: ["access_token", "refresh_token", "metadata", "expirationDate", "created_at", "platform", "code", "company"],
    });
  },

  async getSlackIntegrationData(company) {
    const integrationData = await ExternalIntegrationRepo.getIntegration({
      filter: { company, platform: PLATFORM.SLACK.toUpperCase(), status: STATUSES.ACTIVE },
      selectedOptions: ["access_token"],
    });
    if (!integrationData) throw new NotFoundError("Access Token");
    return integrationData;
  },

  //  external integration through RutterIntegrator to generate the access token for company and user.
  async createIntegrationForZoho(user, company, payload) {
    const { code, status } = await ExternalIntegrationRepo.createUserIntegration({
      payload: {
        ...payload,
        status: STATUSES.ACTIVE,
        user,
        company,
        metadata: { ...(payload.metadata || {}) },
        platform: PLATFORM.ZOHO.toUpperCase(),
      },
    });
    return { code, platform: PLATFORM.ZOHO.toUpperCase(), status };
  },

  async createIntegrationForSlack(user, company, payload) {
    const { code, status } = await ExternalIntegrationRepo.createUserIntegration({
      payload: {
        ...payload,
        status: STATUSES.ACTIVE,
        user,
        company,
        metadata: { ...(payload.metadata || {}) },
        platform: PLATFORM.SLACK.toUpperCase(),
      },
    });
    return { code, platform: PLATFORM.SLACK.toUpperCase(), status };
  },

  async list(criteria) {
    return ExternalIntegrationRepo.list(criteria);
  },
  async updateIntegration(criteria, payload) {
    return ExternalIntegrationRepo.updateIntegration({ queryParams: criteria, updateFields: payload });
  },
};
module.exports = Service;
