const Flutterwave = require("flutterwave-node-v3");
const ValidationError = require("../utils/validation-error");
const Utils = require("../utils/utils");
const { VirtualCardRepo } = require("../repositories/index.repo");
const TransactionService = require("./transaction");
const { BudgetLedger } = require("../models");
const { getApiURL } = require("../utils/utils");
const { STATUSES } = require("../models/status");

const { FLW_API_KEY, FLW_SECRET_KEY } = process.env;
let FLW = null;

module.exports = {
  init() {
    if (!FLW) FLW = new Flutterwave(FLW_API_KEY, FLW_SECRET_KEY);
  },
  async createVirtualCard(payload) {
    try {
      this.init();
      const url = getApiURL();
      const { card_type, reference, ...rest } = payload;
      payload = {
        ...rest,
        callback_url: `${url}/webhook/flw`,
      };
      return await FLW.VirtualCard.create(payload);
    } catch (error) {
      console.log(error);
      // TODO Log error
      throw new ValidationError("Fail creating virtual card");
    }
  },

  async getVirtualCard({ id }) {
    try {
      this.init();
      return await FLW.VirtualCard.fetch({ id });
    } catch (e) {
      // TODO Log Error
      throw new ValidationError("Error fetching card");
    }
  },
  async fundVirtualCard(id, amount, currency) {
    try {
      this.init();
      return FLW.VirtualCard.fund({
        id,
        amount,
        debit_currency: currency,
      });
    } catch (e) {
      // TODO Log Error
      throw new ValidationError("Error fetching card");
    }
  },
  async freezeVirtualCard(id) {
    try {
      this.init();
      return await FLW.VirtualCard.block({ id, status_action: "block" });
    } catch (e) {
      // TODO Log Error
      throw new ValidationError("Error freezing card");
    }
  },
  async unfreezeVirtualCard(id) {
    try {
      this.init();
      return await FLW.VirtualCard.unblock({ id, status_action: "unblock" });
    } catch (e) {
      // TODO Log Error
      throw new ValidationError("Error freezing card");
    }
  },
  async deactivateVirtualCard(id) {
    try {
      this.init();
      return await FLW.VirtualCard.terminate({ id });
    } catch (e) {
      // TODO Log Error
      throw new ValidationError("Error terminating card");
    }
  },

  async bankTransfer({ bankDetails, currency, amount, reference, narration, callback_url = null }) {
    try {
      //   const FLW = new Flutterwave(FLW_API_KEY, FLW_SECRET_KEY);
      return await FLW.Transfer.initiate({
        account_bank: bankDetails.bankCode,
        account_number: bankDetails.accountNumber,
        amount,
        narration,
        currency,
        reference,
        callback_url,
        debit_currency: currency,
      });
    } catch (e) {
      // TODO Log Error
      throw new ValidationError("Error initiateing transfer");
    }
  },

  async liquidateVirtualCard({ id, amount }) {
    try {
      this.init();
      return await FLW.VirtualCard.withdraw_funds({ id, amount });
    } catch (e) {
      // TODO Log Error
      throw new ValidationError("Error terminating card");
    }
  },
  decrypt: {
    card() {},
    number() {},
    cvv() {},
    month() {},
    year() {},
  },

  decryptCard(cards) {
    const hash = Utils.getEncryptionkeys("flutterwave");
    return {
      ...cards,
      last_4: Utils.decrypt({ text: cards.last_4, hash, card: cards }) || cards.last_4,
      cvv: Utils.decrypt({ text: cards.cvv, hash, card: cards }) || cards.cvv,
      exp_year: Utils.decrypt({ text: cards.exp_year, hash, card: cards }) || cards.exp_year,
      exp_month: Utils.decrypt({ text: cards.exp_month, hash, card: cards }) || cards.exp_month,
      ...(cards?.number && {
        number: Utils.decrypt({ text: cards.number, hash, card: cards }) || cards.number,
      }),
      ...(cards?.pin && {
        pin: Utils.decrypt({ text: cards.pin, hash, card: cards }) || cards.pin,
      }),
    };
  },

  async webhook(payload) {
    const { event, data } = payload;
    switch (event) {
      case "charge.completed":
        handleAwaitingDocumentUpload({ attributes, relationships, included });
        break;
      case undefined:
        specialWebhookCase(payload);
        break;
      default:
        break;
    }
  },

  async generateVirtualAccount(payload) {},
};

const specialWebhookCase = async (payload) => {
  if (payload.CardId && payload.Type === "Funding") {
    //get the said card
    const card = await VirtualCardRepo.find({
      externalIdentifier: payload.CardId,
    });
    //update card amount
    await VirtualCardRepo.update({
      conditions: { externalIdentifier: payload.CardId },
      changes: { amount: payload.Balance * 100 },
    });
    //
  }
  if (payload.CardId && payload.Type === "Debit") {
    //get the said card
    const card = await VirtualCardRepo.find({
      externalIdentifier: payload.CardId,
    });
    //update card amount
    await VirtualCardRepo.update({
      conditions: { externalIdentifier: payload.CardId },
      changes: { amount: payload.Balance * 100 },
    });
    //
    const transaction = await TransactionService.createTransaction({
      amount: payload.Amount * 100,
      currency: card.currency,
      company: card.company,
      card: card.id,
      budget: card.budget,
      payer: card.user,
      reference: payload.TransactionId,
      narration: payload.Description,
      description: payload.Description,
      processor_fee: 0,
      bujeti_fee: 0,
      status: STATUSES.SUCCESS,
    });

    await BudgetLedger.create({
      currency: card.currency,
      amount: -100 * payload.Amount,
      budget: card.budget,
      transaction: transaction.id,
      description: payload.Description,
    });
  }
};
