const { Op, fn, col } = require("sequelize");

const FundRequestValidator = require("../validators/fundrequest.validator");
const ValidationError = require("../utils/validation-error");
const { STATUSES } = require("../models/status");
const { FUND_REQUEST_TYPES } = require("../models/fundrequest");
const NotFoundError = require("../utils/not-found-error");
const {
  User,
  Beneficiary,
  Company,
  FundRequest,
  Status,
  Asset,
  Vendor,
  Budget,
  BankAccount,
  Transaction,
  Transfer,
  sequelize,
  ApprovalRequest,
  Approval,
  Approver,
  ApprovalStage,
  ApproverLevel,
  ApprovalRule,
  PaymentPlan,
  Category,
  CreditCard: Card,
  MoreInfoLog,
  Balance,
  UserBudget,
} = require("../models");
const NotificationService = require("./notification");
const BudgetService = require("./budget");
const CompanyService = require("./company");
const TransactionService = require("./transaction");
const Utils = require("../utils");
const UserService = require("./user");
const BillingService = require("./billing");
const UserRepo = require("../repositories/user.repo");
const FundRequestRepo = require("../repositories/fundRequest.repo");
const BudgetAccountRepo = require("../repositories/budgetAccount.repo");
const ApprovalService = require("./approval.service");
const SettingsService = require("./settings");
const VirtualCardService = require("./virtualcards.service");
const VirtualCardRepo = require("../repositories/virtualCard.repo");
const SanitizerService = require("../utils/sanitizer");
const BudgetRepo = require("../repositories/budget.repo");
const PolicyService = require("./policy.service");
const VendorService = require("./vendorService");
const BankAccountService = require("./bank");
const CategoryService = require("./category.service");
const PaymentService = require("./paymentservice");
const BalanceRepo = require("../repositories/balance.repo");
const AuditLogsService = require("./auditLogs");
const Sanitizer = require("../utils/sanitizer");
const HelperService = require("./helper.service");
const ResponseService = require("../utils/response.utils");
const BudgetLedgerRepo = require("../repositories/budgetLedger.repo");
const ScheduleService = require("./schedule.service");
const { CategorizationRuleRepo, TransferRepo, BalanceLedgerRepo } = require("../repositories/index.repo");
const QueueService = require("./queue.service");
const { sendSlackNotification, notifyUserOnSlackForFundRequestApproval } = require("../utils/slack.utils");
const { NOTIFICATION_TYPE } = require("../constants/notifications");

const Service = {
  validateCreationPayload(payload) {
    const { error } = FundRequestValidator.create.validate(payload);
    if (error) throw new ValidationError(error.message);
    const type = FUND_REQUEST_TYPES[payload.type.toUpperCase()];
    if (!type) {
      throw new ValidationError("Invalid request type");
    } else {
      payload.type = type;
    }
    return { error: false, status: true, message: "Valid payload" };
  },

  validateUpdatePayload(payload) {
    const { error } = FundRequestValidator.update.validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: true, message: "Valid payload" };
  },

  validateMoreInfoPayload(payload) {
    const { error } = FundRequestValidator.moreInfo.validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: true, message: "Valid payload" };
  },

  validateGetListPayload(payload) {
    const { error } = FundRequestValidator.listFundRequests.validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: true, message: "Valid payload" };
  },

  validatePayload(payload, name) {
    const { error } = FundRequestValidator[name].validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: true, message: "Valid payload" };
  },

  async createFundRequest({
    company,
    currency,
    user,
    budget = null,
    amount,
    meta,
    vendor = null,
    description,
    bankAccount = null,
    receipt = null,
    type,
    category,
    card,
    balance = null,
    source = null,
    plan,
    deadLine = null,
  }) {
    const payload = {
      company,
      user,
      budget,
      vendor,
      bankAccount,
      amount,
      currency,
      receipt,
      description,
      meta,
      type,
      category,
      card,
      balance,
      source,
      ...(deadLine && { deadLine: new Date(deadLine) }),
    };

    // check plan payout limit
    await Utils.checkPlanPayoutLimit({
      company: { id: company },
      amount,
      currency,
      Transaction,
    });

    const configurations = Utils.parseJSON(plan);

    if (card) {
      const existingCard = await VirtualCardRepo.find({ code: card });

      if (!existingCard) throw new NotFoundError("Card");

      if (existingCard.status !== STATUSES.ACTIVE) throw new ValidationError("Card is not active");

      payload.card = existingCard.id;
      payload.sourceBudget = existingCard.budget;
    }

    if (budget) {
      const { id: budgetId, status } =
        (await BudgetRepo.getOneBudget({
          queryParams: { code: budget, company },
          selectOptions: ["status"],
        })) || {};

      if (!budgetId) throw new NotFoundError("Budget");

      if (status !== STATUSES.ACTIVE) throw new ValidationError("Budget is not active");

      payload.budget = budgetId;
    }

    if (vendor) {
      const foundVendor = await VendorService.getVendor({ code: vendor }, { includeBankAccount: true });
      payload.vendor = foundVendor.id;

      const foundVendorBankAccount =
        foundVendor && Array.isArray(foundVendor.BankAccounts) && foundVendor.BankAccounts.length > 0 ? foundVendor.BankAccounts[0] : null;

      if (!foundVendorBankAccount) throw new NotFoundError("Recipient bank account");
    }

    if (bankAccount) {
      if (bankAccount.startsWith("blc_")) {
        const { bankAccount: bankAccountId } = (await BalanceRepo.getBalance({ filter: { code: bankAccount } })) || {};
        if (!bankAccountId) throw new NotFoundError("Bank Account");
        payload.bankAccount = bankAccountId;
      } else {
        const { id: bankAccountId } = await BankAccountService.getBankAccount({
          code: bankAccount,
        });
        payload.bankAccount = bankAccountId;
      }
    }

    if (balance || source) {
      let foundBalance;

      if (source?.startsWith("bdg_")) {
        const foundBudget = await BudgetRepo.getOneBudget({
          queryParams: { code: source },
          selectOptions: ["balance"],
        });

        if (!foundBudget) throw new NotFoundError("Budget");

        if (foundBudget.id === payload.budget) {
          throw new ValidationError("source budget must be different from budget to top up");
        }

        payload.sourceBudget = foundBudget.id;

        if (foundBudget.balance) {
          foundBalance = await BalanceRepo.getBalance({
            filter: { id: foundBudget.balance, company },
            includeAccount: true,
          });
        }
      } else {
        foundBalance = await BalanceRepo.getBalance({
          filter: { code: balance || source, company },
          includeAccount: true,
        });
      }

      if (!foundBalance) throw new NotFoundError("Balance");
      payload.balance = foundBalance.id;

      if (bankAccount) {
        if (payload.bankAccount === foundBalance?.BankAccount?.id) {
          throw new ValidationError("source and destination accounts cannot be the same");
        }
      }
    } else if (payload.bankAccount) {
      // auto select parent account as source for sub accounts
      const foundBankAccount = await BankAccountService.getBankAccount({
        id: payload.bankAccount,
      });
      const hasParent = !!foundBankAccount?.parent;

      if (hasParent) {
        const parentBalance = await BalanceRepo.getBalance({
          filter: { bankAccount: foundBankAccount?.parent },
        });
        if (parentBalance) {
          payload.balance = parentBalance.id;
        }
      }
    }

    if (meta) {
      // check if company can create more budgets
      await BudgetService.canCreateBudgetAccount(company, currency, configurations.virtual_accounts);
      const existingBudget = await BudgetService.getBudget({
        name: meta.budget,
        company,
      });
      if (existingBudget && [STATUSES.ACTIVE, STATUSES.PAUSE].includes(existingBudget.status))
        throw new ValidationError("A budget exists with the same name. Please rename your budget");
    }

    if (category) {
      const foundCategory = await CategoryService.getCategory({
        code: category,
      });
      if (!foundCategory) throw new NotFoundError("Category");
      payload.category = foundCategory.id;
    } else {
      const autoCategory = await CategorizationRuleRepo.autoCategorizer(payload.description, company);
      if (autoCategory) {
        payload.category = autoCategory.id;
      }
    }

    await PolicyService.policyEnforcer({
      company,
      entity: {
        receipt: payload.receipt,
        description: payload.description,
        category: payload.category,
        budget: payload.budget,
        amount,
        user,
        team: payload.team,
        account: payload.balance,
        currency,
        vendor: payload.vendor,
        type: "fundRequest",
        requestType: Utils.getKeyByValue(FUND_REQUEST_TYPES, payload.type).toLowerCase(),
      },
    });
    const fundRequest = await FundRequestRepo.createFundRequest(payload);

    if (Array.isArray(receipt) && receipt.length)
      await PaymentService.callService("updateAssetWithEntity", receipt, {
        entityId: fundRequest.id,
        entityType: "fundRequest",
      });

    const { success } = await ApprovalService.conditionDetector({
      id: fundRequest.id,
      type: "fundRequest",
      company,
      user,
    });
    // If no approval, notify admins
    if (success) {
      Service.notifyAdmins({
        company,
        fundRequest,
      });
    }

    // get user details
    const fundRequestUser = await UserRepo.getOneUser({
      queryParams: { id: user },
    });

    // get company details
    const companyDetails = await CompanyService.getCompany({ id: company });

    const notificationPayload = {
      amount,
      requesterName: `${fundRequestUser.firstName} ${fundRequestUser.lastName}`,
      requestId: fundRequest.code,
      status: fundRequest.status,
      companyName: companyDetails.name,
      company,
      description,
      workspaceId: companyDetails.id,
      currency,
      user: {
        code: fundRequestUser.code,
        firstName: fundRequestUser.firstName,
        lastName: fundRequestUser.lastName,
      },
      vendor,
      category,
      source,
      budget,
      type: Utils.getKeyByValue(FUND_REQUEST_TYPES, type).toLowerCase(),
      meta: meta || {},
      deadLine,
      date: fundRequest.created_at,
      success,
    };

    sendSlackNotification(NOTIFICATION_TYPE.REQUEST, notificationPayload);

    return fundRequest;
  },

  async listFundRequests(query = {}) {
    let { status, category, user, deadLine } = query;
    const {
      min_amount: minAmount,
      max_amount: maxAmount,
      company,
      currency,
      card,
      from,
      owner,
      to,
      search,
      loggedInUser,
      perPage = 50,
      page = 1,
    } = query;

    const skip = (parseInt(page, 10) - 1) * parseInt(perPage, 10);
    const { view_as: viewAs, isMobile } = query;
    const isReviewerView = viewAs === "reviewer";

    if (category) {
      const foundCategory = await CategoryService.getCategory({
        code: category,
      });
      if (!foundCategory) throw new NotFoundError("Category");
      category = foundCategory.id;
    }

    if (String(user).startsWith("usr_")) {
      const foundUser = await UserService.fetchUser(user);
      if (!foundUser) throw new NotFoundError("User");
      user = foundUser.id;
    }

    const isCardSearch = String(search).startsWith("crd_");
    const isBudgetSearch = String(search).startsWith("bdg_");
    const isUserSearch = String(search).startsWith("usr_");

    const userCriteria = {
      model: User,
      as: "User",
      required: !!isUserSearch,
      include: [
        {
          model: Beneficiary,
          required: false,
          attributes: ["id", "code"],
        },
      ],
    };

    const cardCriteria = {
      model: Card,
      required: !!card || !!isCardSearch,
      ...(card && { where: { code: card } }),
    };

    const budgetCriteria = {
      model: Budget,
      required: !!isBudgetSearch,
    };

    if (owner) {
      userCriteria.where = {
        "$User.code$": owner,
      };
    }

    const criteria = {
      company,
      ...(category && { category }),
      ...(status
        ? {
            status: String(status)
              .split(",")
              .map((value) => STATUSES[value.toUpperCase()]),
          }
        : {
            status: {
              [Op.ne]: STATUSES.DELETED,
            },
          }),
      ...(currency && { currency: String(currency).split(",") }),
    };

    const isPendingStatus = Array.isArray(criteria.status)
      ? criteria.status.length === 1 && criteria.status.includes(STATUSES.PENDING) && (!user || isReviewerView)
      : criteria.status === STATUSES.PENDING && (!user || isReviewerView);

    const shouldFilterByUser = !isPendingStatus && user;

    if (shouldFilterByUser) {
      criteria.user = user;
    }

    if (search) {
      if (isUserSearch) {
        userCriteria.where = { code: search };
      } else if (isBudgetSearch) {
        budgetCriteria.where = { code: search };
      } else if (isCardSearch) {
        cardCriteria.where = { code: search };
      } else {
        criteria[Op.or] = [
          { code: { [Op.like]: `%${search}%` } },
          { description: { [Op.like]: `%${search}%` } },
          { note: { [Op.like]: `%${search}%` } },
          { moreInfoDescription: { [Op.like]: `%${search}%` } },
          sequelize.literal(`EXISTS (SELECT * FROM Users WHERE Users.id = FundRequest.user AND Users.firstName LIKE '%${search}%')`),
          sequelize.literal(`EXISTS (SELECT * FROM Users WHERE Users.id = FundRequest.user AND Users.lastName LIKE '%${search}%')`),
          sequelize.literal(`EXISTS (SELECT * FROM Users WHERE Users.id = FundRequest.reviewer AND Users.firstName LIKE '%${search}%')`),
          sequelize.literal(`EXISTS (SELECT * FROM Users WHERE Users.id = FundRequest.reviewer AND Users.lastName LIKE '%${search}%')`),
          sequelize.literal(`EXISTS (SELECT * FROM Vendors WHERE Vendors.id = FundRequest.vendor AND Vendors.name LIKE '%${search}%')`),
        ];
      }
    }

    if (minAmount || maxAmount) {
      criteria.amount = {};
      if (minAmount) criteria.amount[Op.gte] = Number(minAmount);
      if (maxAmount) criteria.amount[Op.lte] = Number(maxAmount);
    }

    if (from || to) {
      criteria.created_at = {};
      if (from && Utils.isValidDate(from)) {
        criteria.created_at[Op.gte] = from;
      }
      if (to && Utils.isValidDate(to)) {
        criteria.where = sequelize.where(sequelize.fn("date", sequelize.col("FundRequest.created_at")), "<=", to);
      }
    }
    if (deadLine) {
      criteria.deadLine = {
        [Op.gte]: deadLine,
        [Op.lte]: deadLine,
      };
    }

    const include = [
      userCriteria,
      cardCriteria,
      budgetCriteria,
      {
        model: Asset,
        via: "receipt",
        as: "FundRequestAssets",
        where: {
          $entityType$: "fundRequest",
        },
        required: false,
      },
      { model: Status, required: false },
      { model: Category, required: false },
      Balance,
      {
        model: Budget,
        required: false,
        as: "SourceBudget",
      },
    ];

    if (isMobile) {
      include.push(
        {
          model: Vendor,
          required: false,
          include: [
            {
              model: BankAccount,
              attributes: ["id", "code", "bankCode", "number", "bankName", "currency"],
              where: {
                type: "real",
                ownerType: "vendor",
                status: STATUSES.ACTIVE,
              },
              required: false,
              limit: 1,
            },
          ],
        },
        {
          model: BankAccount,
          required: false,
          include: [Balance],
        }
      );
    } else if (isReviewerView) {
      include.push({
        model: Vendor,
        required: false,
        include: [
          {
            model: BankAccount,
            attributes: ["id", "code", "bankCode", "number", "bankName", "currency"],
            where: {
              type: "real",
              ownerType: "vendor",
              status: STATUSES.ACTIVE,
            },
            required: false,
            limit: 1,
          },
        ],
      });
    }

    if (isPendingStatus) {
      const approvalRequests = {
        model: ApprovalRequest,
        attributes: ["id", "status"],
        required: false,
        as: "FundRequestApprovalRequests",
        where: { entity: "fundRequest" },
        include: [
          {
            model: ApprovalRule,
            attributes: ["id"],
          },
          {
            model: ApprovalStage,
            attributes: ["id", "status"],
            order: [["created_at", "ASC"]],
            include: [
              {
                model: ApproverLevel,
                attributes: ["id"],
                include: [
                  {
                    model: Approver,
                    attributes: ["id", "status"],
                    include: [
                      {
                        model: User,
                        attributes: ["id", "code"],
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            model: Approval,
            attributes: ["id"],
            include: [
              {
                model: Approver,
                attributes: ["id", "status"],
                include: [
                  {
                    model: User,
                    attributes: ["id", "code"],
                  },
                ],
              },
            ],
          },
        ],
      };

      include.push(approvalRequests);
    }
    const [total = 0, fundRequests] = await Promise.all([
      FundRequest.count({
        where: {
          ...criteria,
          ...(isPendingStatus && {
            ...HelperService.returnRelevantRequests("FundRequestApprovalRequests", loggedInUser, !!user),
          }),
        },
        include,
        distinct: true,
        subQuery: false,
        col: "id",
      }),
      FundRequest.findAll({
        where: {
          ...criteria,
          ...(isPendingStatus && {
            ...HelperService.returnRelevantRequests("FundRequestApprovalRequests", loggedInUser, !!user),
          }),
        },
        distinct: true,
        subQuery: false,
        col: "id",
        include,
        limit: Number(perPage),
        offset: skip,
        order: [["created_at", "DESC"]],
      }),
    ]);
    return {
      meta: {
        total,
        perPage: Number(perPage),
        page: Number(page),
        nextPage: Number(page) + 1,
        hasMore: total >= page * perPage,
      },
      fundRequests: Service.transformFundRequests(fundRequests),
    };
  },
  async getFundRequest(criteria) {
    const approvalRequests = {
      model: ApprovalRequest,
      required: false,
      as: "FundRequestApprovalRequests",
      where: {
        entity: "fundRequest",
      },
      include: [
        ApprovalRule,
        {
          model: ApprovalStage,
          order: [["created_at", "ASC"]],
          include: [
            {
              model: ApproverLevel,
              include: [
                {
                  model: Approver,
                  include: [User],
                },
              ],
            },
          ],
        },
        { model: Approval, include: [{ model: Approver, include: [User] }] },
      ],
    };
    const fundRequest = await FundRequest.findOne({
      where: {
        ...criteria,
        status: {
          [Op.ne]: STATUSES.DELETED,
        },
      },
      include: [
        {
          model: User,
          via: "user",
          as: "User",
          include: [
            {
              model: Beneficiary,
              required: false,
              attributes: ["id", "code"],
            },
          ],
        },
        {
          model: User,
          via: "reviewer",
          as: "Reviewer",
          required: false,
        },
        {
          model: Asset,
          via: "receipt",
          as: "FundRequestAssets",
          where: {
            $entityType$: "fundRequest",
          },
          required: false,
        },
        {
          model: Budget,
          required: false,
        },
        {
          model: Budget,
          required: false,
          as: "SourceBudget",
        },
        {
          model: Vendor,
          required: false,
          include: [
            {
              model: BankAccount,
              attributes: ["id", "code", "bankCode", "number", "bankName", "currency"],
              where: {
                type: "real",
                ownerType: "vendor",
                status: STATUSES.ACTIVE,
              },
              required: false,
              limit: 1,
            },
          ],
        },
        {
          model: BankAccount,
          required: false,
          include: [Balance],
        },
        Status,
        approvalRequests,
        Transaction,
        Transfer,
        Company,
        Category,
        Card,
        {
          model: MoreInfoLog,
          required: false,
          include: [User],
          where: {
            entityType: "fundRequest",
          },
          order: [["created_at", "DESC"]],
        },
        Balance,
      ],
    });
    if (!fundRequest) throw new NotFoundError("FundRequest");
    return fundRequest;
  },
  async updateFundRequest(code, payload) {
    const { status, company, receipt, user, ...rest } = payload;

    const fundRequest = await Service.getFundRequest({ code, company });

    if (!fundRequest) throw new NotFoundError("Fund request");

    if (user && fundRequest.user !== user) {
      throw new ValidationError("You cannot edit this fund request");
    }

    if (rest.vendor) {
      const foundVendor = await VendorService.getVendor({ code: rest.vendor });
      rest.vendor = foundVendor.id;

      const foundVendorBankAccount =
        foundVendor && Array.isArray(foundVendor.BankAccounts) && foundVendor.BankAccounts.length > 0 ? foundVendor.BankAccounts[0] : null;

      if (!foundVendorBankAccount) throw new NotFoundError("Recipient bank account");
    }

    if (rest.meta) {
      const existingBudget = await BudgetService.getBudget({
        name: rest.meta.budget,
        company,
      });
      if (existingBudget && [STATUSES.ACTIVE, STATUSES.PAUSE].includes(existingBudget.status))
        throw new ValidationError("A budget exists with the same name. Please rename your budget");
    }

    if (rest.budget) {
      const { id: budget } = await BudgetService.getBudget({
        code: rest.budget,
      });
      rest.budget = budget;
    }

    if (rest.bankAccount) {
      if (rest.bankAccount.startsWith("blc_")) {
        const { bankAccount: bankAccountId } =
          (await BalanceRepo.getBalance({
            filter: { code: rest.bankAccount },
          })) || {};
        if (!bankAccountId) throw new NotFoundError("Bank Account");
        rest.bankAccount = bankAccountId;
      } else {
        const { id: bankAccountId } = await BankAccountService.getBankAccount({
          code: rest.bankAccount,
        });
        rest.bankAccount = bankAccountId;
      }
    }

    if (rest.category) {
      const foundCategory = await CategoryService.getCategory({
        code: rest.category,
      });
      if (!foundCategory) throw new NotFoundError("Category");
      rest.category = foundCategory.id;
    }

    if (rest.card) {
      const existingCard = await VirtualCardRepo.find({ code: rest.card });

      if (!existingCard) throw new NotFoundError("Card");

      rest.card = existingCard.id;
    }

    if (rest.balance || rest.source) {
      let foundBalance;

      if (rest.source?.startsWith("bdg_")) {
        const foundBudget = await BudgetRepo.getOneBudget({
          queryParams: { code: rest.source },
          selectOptions: ["balance"],
        });

        if (!foundBudget) throw new NotFoundError("Budget");

        if (foundBudget.id === fundRequest.budget) {
          throw new ValidationError("source budget must be different from budget to top up");
        }

        rest.sourceBudget = foundBudget.id;
        rest.balance = null;
      } else {
        foundBalance = await BalanceRepo.getBalance({
          filter: { code: rest.balance || rest.source, company },
          includeAccount: true,
        });
        if (!foundBalance) throw new NotFoundError("Balance");
        rest.balance = foundBalance.id;
        rest.sourceBudget = null;
      }

      if (rest.bankAccount || fundRequest.bankAccount) {
        if ((rest.bankAccount || fundRequest.bankAccount) === foundBalance?.BankAccount?.id) {
          throw new ValidationError("source and destination accounts cannot be the same");
        }
      }
    }

    if (Array.isArray(receipt) && receipt.length)
      await PaymentService.callService("updateAssetWithEntity", receipt, {
        entityId: fundRequest.id,
        entityType: "fundRequest",
      });

    if (fundRequest.status !== STATUSES.PENDING)
      throw new ValidationError(`Action cannot be performed, Fund Request already ${fundRequest.Status.value.toLowerCase()}`);
    if (status === "approved") {
      if (fundRequest.type === FUND_REQUEST_TYPES.BUDGET && !fundRequest.meta.budget) {
        throw new ValidationError(`Fund budget name is missing`);
      }
      if (fundRequest.type === FUND_REQUEST_TYPES.PAYMENT && !fundRequest.vendor) {
        throw new ValidationError(`The vendor to pay to is missing`);
      }
      if (fundRequest.type === FUND_REQUEST_TYPES.TOP_UP && !(fundRequest.bankAccount || fundRequest.budget || fundRequest.card)) {
        throw new ValidationError(`The budget or bank account to top up is missing`);
      }
      const { data } = await Service.approveFundRequest({
        code,
        ...payload,
        ...SanitizerService.jsonify(fundRequest),
        budget: fundRequest.sourceBudget,
      });
      return data;
    }
    if (status === "declined") {
      return Service.declineFundRequest({ code, ...payload });
    }

    if (status === STATUSES.DELETED) {
      await ApprovalRequest.update({ status }, { where: { entity: "fundRequest", entity_id: fundRequest.id } });
    }

    FundRequest.update({ status, ...rest }, { where: { code } });

    if (Array.isArray(receipt) && receipt.length) {
      await PaymentService.callService("updateAssetWithEntity", receipt, {
        entityId: fundRequest.id,
        entityType: "fundRequest",
      });
    }

    return FundRequest.findOne({ where: { code } });
  },
  async approveFundRequest(payload) {
    const {
      code,
      company,
      reviewer,
      amount,
      currency,
      isSQSRequest,
      status = STATUSES.APPROVED,
      plan = SettingsService.get("pricing_config"),
      actionLater,
      schedule,
    } = payload;

    const shouldProcessPaymentImmediately = !actionLater && !schedule;

    const fundRequest = await FundRequest.findOne({
      where: { code, company },
      include: [
        {
          model: Vendor,
          required: false,
          include: [
            {
              model: BankAccount,
              attributes: ["id", "code", "bankCode", "number", "bankName", "currency", "balance"],
              where: { type: "real", ownerType: "vendor" },
              required: false,
              limit: 1,
            },
          ],
        },
        {
          model: Budget,
          required: false,
        },
        {
          model: BankAccount,
          required: false,
        },
        {
          model: User,
          as: "User",
        },
        {
          model: Company,
          include: [
            PaymentPlan,
            {
              model: Status,
            },
          ],
        },
        Balance,
      ],
    });

    if (!fundRequest) throw new NotFoundError("Fund request");

    // make an exception for cards because their only source of funding is the budget they are tied to
    if (!(fundRequest.sourceBudget || fundRequest.Balance || fundRequest.card)) {
      if (!actionLater) throw new ValidationError("Please specify a source account to debit for this request");
    }

    const configurations = Utils.parseJSON(fundRequest.Company.PaymentPlan.configuration);
    const recipient = fundRequest.User;
    const approver = await UserRepo.getOneUser({
      queryParams: { id: reviewer },
    });

    await Utils.checkPlanPayoutLimit({
      company: fundRequest.Company,
      amount,
      currency,
      Transaction,
    });

    if (!isSQSRequest) {
      // if condition detected stop here
      const { success, data } = await ApprovalService.conditionDetector(
        {
          id: fundRequest.id,
          type: "fundRequest",
          company,
        },
        true
      );

      if (!success) {
        return ApprovalService.reviewApprovalRequest({
          code: data.code,
          user: reviewer,
          company: fundRequest.company,
          status: "approved",
          actionLater,
          schedule,
        });
      }
    }

    const responseObject = {
      error: false,
      message: "Fund request approved successfully",
    };
    if (fundRequest.type === FUND_REQUEST_TYPES.BUDGET && fundRequest?.meta.budget) {
      const existingBudget = await BudgetService.getBudget({
        name: fundRequest.meta.budget,
        company,
      });

      if (existingBudget && [STATUSES.ACTIVE, STATUSES.PAUSE].includes(existingBudget.status))
        throw new ValidationError("A budget exists with the same name. Please rename your budget");

      // the configuration colunm is saved as 'longtext' instead of 'json' therefore comes as a string this is to solve that

      const canCreateSubAccount = await BudgetService.canCreateBudgetAccount(company, currency, configurations.virtual_accounts);
      // Create the budget
      const budgetPayload = {
        amount: fundRequest.amount,
        sharingType: BudgetService.getSharingType(),
        name: fundRequest.meta.budget,
        currency,
        company,
        status: STATUSES.ACTIVE,
      };

      const newBudget = await BudgetService.createBudget(fundRequest.user, budgetPayload);

      // make user budget owner
      await UserBudget.create({
        budget: newBudget.id,
        user: fundRequest.user,
        status: STATUSES.ACTIVE,
        isBudgetOwner: true,
      });

      const { id: budgetId } = newBudget;

      AuditLogsService.createLog({
        event: "created-a-budget",
        initial_state: null,
        delta: Sanitizer.sanitizeBudget(newBudget),
        user: approver.id,
        table_type: "Budget",
        table_id: budgetId,
      });

      let budgetAccount;

      if (canCreateSubAccount) {
        budgetAccount = await BudgetService.createBudgetSubAccount({
          budget: budgetId,
          company,
        });
      } else {
        throw new ValidationError(`Unable to create Sub account for your budget`);
      }

      AuditLogsService.createLog({
        event: "topped-up-budget",
        user: approver.id,
        initial_state: Sanitizer.sanitizeBudget(newBudget),
        delta: { amount },
        table_type: "Budget",
        table_id: newBudget.id,
      });

      // top-up budget
      if (budgetAccount) {
        let hasSufficientBalance = true;
        if (fundRequest.sourceBudget) {
          // Check budget Balance
          hasSufficientBalance = await BudgetLedgerRepo.isBudgetSufficient({
            budget: { id: fundRequest.sourceBudget },
            amount: fundRequest.amount,
          });
        } else
          hasSufficientBalance = await BalanceRepo.isBalanceSufficient({
            balance: { id: fundRequest.balance },
            amount: fundRequest.amount,
          });

        if (!hasSufficientBalance) {
          responseObject.error = true;
          responseObject.message = "Budget created, but top up failed due to insufficient funds. Please add fund to the account or budget";
        } else {
          // Proceed to fund
          const fundingSource = {};
          if (fundRequest.sourceBudget) fundingSource.sourceBudget = fundRequest.sourceBudget;
          else fundingSource.sourceBalance = fundRequest.balance;
          const { transferId } =
            (await BudgetService.fundBudgetSubAccount(newBudget.id, amount, currency, {
              ...fundingSource,
              user: approver.code,
              userId: approver.id,
            })) || {};

          if (transferId) {
            fundRequest.transfer = transferId;
            await fundRequest.save();
          }
        }
      }
    }

    const { data: { isDirectDebit } = {} } =
      !fundRequest.sourceBudget && fundRequest.balance ? await HelperService.isDirectDebitAccount({ balance: fundRequest.balance }) : {};

    const { cbnFee, bujetiFee } = BillingService.computeFees({
      amount,
      companyCode: fundRequest.Company && fundRequest.Company.code,
      currency,
      plan: configurations || plan,
      isDirectDebit,
    });

    if (fundRequest.type === FUND_REQUEST_TYPES.PAYMENT && fundRequest.vendor) {
      // if it is payment pay the vendor
      const bankAccount =
        fundRequest.Vendor && Array.isArray(fundRequest.Vendor.BankAccounts) && fundRequest.Vendor.BankAccounts.length > 0
          ? fundRequest.Vendor.BankAccounts[0]
          : null;

      if (!bankAccount) throw new NotFoundError("Recipient bank account");

      const transaction = await TransactionService.createTransaction({
        amount,
        description: fundRequest.description,
        payer: approver.id,
        user: fundRequest.user,
        company,
        currency,
        bank_account: bankAccount?.id,
        status: actionLater ? STATUSES.APPROVED : STATUSES.PROCESSING,
        narration: fundRequest.description,
        recipient: fundRequest.vendor,
        category: fundRequest.category,
        recipient_type: "vendor",
        processor_fee: cbnFee,
        bujeti_fee: bujetiFee,
        ...(fundRequest.sourceBudget && { budget: fundRequest.sourceBudget }),
        ...(!fundRequest.sourceBudget && { balance: fundRequest.balance }),
      });

      if (transaction) {
        const totalAmount = amount + cbnFee + bujetiFee;
        let hasSufficientBalance = true;

        if (shouldProcessPaymentImmediately) {
          if (transaction.budget) {
            // Check budget Balance
            hasSufficientBalance = await BudgetLedgerRepo.isBudgetSufficient({
              budget: { id: transaction.budget },
              amount: totalAmount,
            });
          } else
            hasSufficientBalance = await BalanceRepo.isBalanceSufficient({
              balance: { id: transaction.balance },
              amount: totalAmount,
            });
        }

        if (!hasSufficientBalance && !actionLater) {
          await TransactionService.updateTransaction({
            criteria: { id: transaction.id },
            payload: {
              status: STATUSES.FAILED,
              failure_reason: "Insufficient Balance",
            },
          });
          responseObject.error = true;
          responseObject.message = "Fund request approved, but payout failed due to insufficient fund. Please add funds to the account or budget";
        }

        if (shouldProcessPaymentImmediately && hasSufficientBalance) {
          ApprovalService.addPaymentToQueue(transaction.code);
        }

        if (schedule) {
          await createScheduledTransaction({
            schedule,
            currency,
            amount,
            fundRequest,
            company,
            bankAccount,
            transaction,
            bujetiFee,
            cbnFee,
          });
        }

        fundRequest.transaction = transaction.id;
        await fundRequest.save();
      }
    }

    if (fundRequest.type === FUND_REQUEST_TYPES.TOP_UP) {
      // if it is top up
      if (fundRequest.budget) {
        // if the top up is on a budget

        const foundBudget = await BudgetService.getBudget({
          id: fundRequest.budget,
          company,
        });
        if (!foundBudget) throw new NotFoundError("Budget");

        // Checks if this budget has Budget Account and handle Budget ledger in Webhook
        let budgetAccount = await BudgetAccountRepo.getBudgetAccount({
          filter: { budget: foundBudget.id },
        });

        if (!budgetAccount) {
          const canCreateSubAccount = await BudgetService.canCreateBudgetAccount(company, currency, configurations.virtual_accounts);

          if (!canCreateSubAccount) throw new ValidationError(`Unable to create Sub account for your budget`);

          budgetAccount = await BudgetService.createBudgetSubAccount({
            company,
            budget: foundBudget.id,
          });
        }

        if (budgetAccount) {
          const fundingSource = {};
          if (fundRequest.sourceBudget) fundingSource.sourceBudget = fundRequest.sourceBudget;
          else fundingSource.sourceBalance = fundRequest.balance;

          const hasSource = Object.values(fundingSource).filter(Boolean).length;

          if (!hasSource) throw new ValidationError("Please specify a source account to debit for this request");

          await BudgetService.fundBudgetSubAccount(foundBudget.id, amount, currency, {
            ...fundingSource,
            user: approver.code,
            userId: approver.id,
          });
        }
      }

      if (fundRequest.bankAccount) {
        const destinationAccount = await BalanceRepo.getBalance({
          filter: { bankAccount: fundRequest.bankAccount },
          includeAccount: true,
        });
        let sourceAccount = {};
        if (fundRequest.sourceBudget) {
          const foundBudget = await BudgetRepo.getBudget({ id: fundRequest.sourceBudget }, true);
          const {
            BudgetAccount: { externalIdentifier: externalBankAccountId, type },
          } = foundBudget;

          await BudgetLedgerRepo.canBudgetHandleTransaction({
            budget: fundRequest.sourceBudget,
            amount,
            totalAmount: amount,
          });

          sourceAccount.BankAccount = { externalBankAccountId, type };
        } else {
          await BalanceLedgerRepo.canBalanceHandleTransaction({
            balance: fundRequest.balance,
            company,
            amount,
            totalAmount: amount,
          });
          sourceAccount = await BalanceRepo.getBalance({
            filter: { id: fundRequest.balance },
            includeAccount: true,
          });
        }

        if (!destinationAccount || !destinationAccount.BankAccount) {
          throw new NotFoundError("Destination account");
        }

        if (!sourceAccount || !sourceAccount.BankAccount) {
          throw new NotFoundError("Source account");
        }

        if (destinationAccount.BankAccount.externalBankAccountId === sourceAccount.BankAccount.externalBankAccountId) {
          throw new ValidationError("Source account cannot be equal to destination account");
        }

        // Create Transfer
        const reference = Utils.generateRandomString(17).toLowerCase();
        const transferPayload = {
          amount: -1 * amount,
          currency,
          balance: sourceAccount?.id,
          description: `Top up to ${destinationAccount.name}`,
          company,
          status: STATUSES.PENDING,
          reference,
          processor_fee: 0,
          bujeti_fee: 0,
          narration: `Balance Top up ref: ${reference}`,
        };

        const createdTransfer = await TransferRepo.createTransfer({
          data: transferPayload,
        });

        const { payment } = SettingsService.get("providers");
        const providerToUse = payment[company] || payment.defaultProvider;

        const bookTransferPayload = {
          recipientId: destinationAccount.BankAccount.externalBankAccountId,
          recipientType: "DepositAccount",
          senderId: sourceAccount.BankAccount.externalBankAccountId,
          senderType: fundRequest.sourceBudget ? sourceAccount.BankAccount.type : "DepositAccount",
          currency,
          amount: Math.abs(amount),
          reason: `Top up to ${destinationAccount.name}`,
          reference,
          company,
          purpose: "BALANCE_TOP_UP",
          source: sourceAccount.code,
          destination: destinationAccount.code,
          paidBy: reviewer,
          fundRequest: fundRequest.code,
          transfer: createdTransfer.code,
          providerToUse,
        };

        const SQSPayload = {
          data: bookTransferPayload,
          id: Utils.generateRandomString(17),
          path: `/transfers/bookTransfer/${createdTransfer.code}/process`,
          key: process.env.INTRA_SERVICE_TOKEN,
        };

        QueueService.addDelayedJob({}, SQSPayload, `BookTransferReference:${bookTransferPayload.reference}`, 5);

        await fundRequest.save();
      }

      if (fundRequest.card) {
        const card = await VirtualCardRepo.find({
          id: fundRequest.card,
          company,
          status: STATUSES.ACTIVE,
        });
        if (!card) throw new NotFoundError("Card");

        let cardSource;
        if (card.budget) {
          cardSource = await BudgetService.getBudget({ id: card.budget });
        } else {
          cardSource = await BalanceRepo.getBalance({
            filter: { id: card.balance },
          });
        }

        await VirtualCardService.fundCard({
          cardCode: card.code,
          user: fundRequest.user,
          company,
          amount,
          ...(card.budget && { budget: cardSource.code }),
          ...(card.balance && { balance: cardSource.code }),
          paymentPlan: configurations,
        });
      }
    }

    await FundRequest.update(
      {
        status,
        reviewer,
        reviewed_on: new Date(),
      },
      { where: { code, company } }
    );

    // notify beneficiary of pending payment
    const adminUser = await UserRepo.getOneUser({
      queryParams: { id: reviewer },
      selectOptions: ["firstName", "lastName"],
    });

    const {
      Company: foundCompany,
      Budget: foundBudget,
      description,
    } = await FundRequestRepo.getFundRequest({
      criteria: { code: fundRequest.code },
    });
    NotificationService.notifyUser(
      {
        email: recipient.email,
      },
      "fund-request-approved",
      {
        firstName: recipient.firstName,
        amount: (parseInt(amount, 10) / 100).toLocaleString(),
        currency,
        code,
        description,
        approvedBy: `${adminUser.firstName} ${adminUser.lastName}`,
        userFullname: `${recipient.firstName} ${recipient.lastName}`,
        userNameInitials: Utils.getInitials(`${recipient.firstName} ${recipient.lastName}`),
        companyName: foundCompany.name,
        budget: foundBudget?.name || null,
        companyNameInitials: Utils.getInitials(foundCompany.name),
        ...(fundRequest.vendor && {
          vendor: { name: fundRequest.Vendor.name },
        }), // add vendor bank details, address, etc...
        ...(fundRequest.budget && {
          budget: { name: fundRequest.meta.budget },
        }), // add budget new total amount
        ...(fundRequest.bankAccount && {
          bankAccount: { name: fundRequest.BankAccount.accountName },
        }), // add bank details
      }
    );

    // UPDATE PREVIOUS ACTION NOTIFICATION TO INFO
    NotificationService.updateNotification({ reference_code: fundRequest.code }, { type: "info", badge: "info" });

    const notificationPayload = {
      company,
      user_id: recipient.id,
      type: `info`,
      badge: `info`,
      title: `Fund Request Update`,
      message: `${Utils.toTitle(adminUser.firstName)} has approved your fund request of ${currency}${Utils.formatAmount(
        amount
      ).toLocaleString()} on ${Utils.getCurrentDate()}`,
      table: {
        code: fundRequest.code,
        entity: "FundRequest",
      },
      event: "fundRequestUpdate",
    };
    NotificationService.saveNotification(notificationPayload);

    try {
      await notifyUserOnSlackForFundRequestApproval({
        requestId: fundRequest.id,
        amount: fundRequest.amount,
        currency: fundRequest.currency,
        approverName: `${adminUser.firstName} ${adminUser.lastName}`,
        requestType: "fund request",
        company: fundRequest.company,
      });
    } catch (error) {
      console.error("Error sending Slack notification:", error.message);
    }

    return ResponseService[responseObject.error ? "BadRequestException" : "sendObjectResponse"](responseObject.message, fundRequest);
  },
  async declineFundRequest(payload) {
    const { code, company, reviewer, recipient, amount, currency, note, status = STATUSES.DECLINED } = payload;
    await FundRequest.update(
      {
        status,
        reviewer,
        note,
        reviewed_on: new Date(),
      },
      { where: { code, company } }
    );
    const isDeactivatedUser = recipient.status === STATUSES.DELETED;

    if (isDeactivatedUser) return;

    const { Company: foundCompany, Budget: foundBudget, description } = await FundRequestRepo.getFundRequest({ criteria: { code, company } });
    // update record

    const adminUser = await UserRepo.getOneUser({
      queryParams: { id: reviewer },
      selectOptions: ["firstName", "lastName"],
    });

    NotificationService.notifyUser(
      {
        email: recipient.email,
      },
      "fund-request-declined",
      {
        firstName: recipient.firstName,
        description,
        note,
        amount: (parseInt(amount, 10) / 100).toLocaleString(),
        login: `${process.env.DASHBOARD_URL}/login`,
        currency,
        code,
        approvedBy: `${adminUser.firstName} ${adminUser.lastName}`,
        userFullname: `${recipient.firstName} ${recipient.lastName}`,
        userNameInitials: Utils.getInitials(`${recipient.firstName} ${recipient.lastName}`),
        companyName: foundCompany.name,
        budget: foundBudget ? foundBudget.name : null,
        companyNameInitials: Utils.getInitials(foundCompany.name),
      }
    );

    // UPDATE PREVIOUS ACTION NOTIFICATION TO INFO
    NotificationService.updateNotification({ reference_code: code }, { type: "info", badge: "info" });

    const notificationPayload = {
      company,
      user_id: recipient.id,
      type: `info`,
      badge: `info`,
      title: `Fund Request Update`,
      message: `${Utils.toTitle(adminUser.firstName)} has disapproved your fund request of ${currency}${Utils.formatAmount(
        amount
      ).toLocaleString()} on ${Utils.getCurrentDate()}`,
      reference_code: code,
      table: {
        code,
        entity: "FundRequest",
      },
      event: "fundRequestUpdate",
    };
    NotificationService.saveNotification(notificationPayload);
  },
  /**
   * Finalize a fund request post payment
   * @param payload
   * @returns {Promise<void>}
   */
  async finalizeFundRequest(payload) {
    const { code, company, transaction, status = STATUSES.PAID } = payload;

    const [result] = await FundRequest.update(
      {
        transaction,
        status,
      },
      { where: { code, company } }
    );
    const fundRequest = await FundRequest.findOne({
      where: {
        code,
        company,
      },
      include: [
        {
          model: User,
          attributes: ["email", "firstName"],
          via: "user",
          as: "User",
        },
        Budget,
      ],
    });
    // update record
    if (status === STATUSES.PAID) {
      const recipient = fundRequest && fundRequest.User;

      NotificationService.notifyUser(
        {
          email: recipient.email,
        },
        "payment-received",
        {
          first_name: recipient.firstName,
          amount: fundRequest.amount.toLocaleString(),
          currency: fundRequest.currency,
          code,
        }
      );
    }
  },

  /**
   * Complete a fund request as part of approval flow
   * @param payload
   * @returns {Promise<void>}
   */
  async completeFundRequest(payload) {
    const { status } = payload;

    if (status === STATUSES.APPROVED) {
      return Service.approveFundRequest({
        ...payload,
        isSQSRequest: true,
      });
    }

    if (status === STATUSES.DECLINED) {
      return Service.declineFundRequest({
        ...payload,
      });
    }
  },

  async validateFundRequest({ code, company, decision, note, actionLater, schedule }) {
    Service.validatePayload({ actionLater, schedule, reason: note, decision, code }, "validateFundRequest");

    const fundRequest = await FundRequest.findOne({
      where: {
        code,
        company,
        status: {
          [Op.ne]: STATUSES.DELETED,
        },
      },
      include: {
        model: User,
        attributes: ["id", "code", "email", "firstName", "lastName", "status"],
        as: "User",
        include: [
          {
            model: BankAccount,
            attributes: ["id", "code", "bankCode", "number", "bankName", "currency"],
            where: { type: "real", ownerType: "user", status: STATUSES.ACTIVE },
            required: false,
            limit: 1,
          },
        ],
      },
    });
    if (!fundRequest) throw new NotFoundError("FundRequest");
    if (fundRequest.status === STATUSES.APPROVED) throw new ValidationError("FundRequest already approved");
    if (!fundRequest.User) throw new NotFoundError("Recipient");
    const cannotProcessFrqForDeactivatedUser = fundRequest.User.status === STATUSES.DELETED && decision === "approve";
    if (cannotProcessFrqForDeactivatedUser) throw new ValidationError("Requester's account deactivated. Fund request cannot be processed");
    else if ([STATUSES.PENDING].includes(fundRequest.status))
      return {
        code,
        message: "Fund Request is valid",
        fundRequest,
      };
    else throw new ValidationError("Fund Request is in invalid state");
  },
  async notifyAdmins({ company, fundRequest }) {
    const user = await UserService.fetchUser(fundRequest.user);
    const { Balance: foundBalance } = await FundRequestRepo.getFundRequest({
      queryParams: { id: fundRequest.id },
    });
    let admins = [];
    if (user.manager) {
      const manager = await UserRepo.getOneUser({
        queryParams: { id: user.manager },
        selectOptions: ["email", "firstName", "lastName"],
      });
      admins.push(manager);
    } else {
      const foundCompanyWithAdmins = await CompanyService.getCompanyWithAdmins({
        id: company,
      });

      admins = foundCompanyWithAdmins?.Users || [];
    }

    if (!admins.length) {
      return;
    }

    const currentBalance = await BalanceRepo.getAvailableBalance({
      company,
      currency: fundRequest.currency,
      ...(foundBalance && { code: foundBalance.code }),
    });
    const payload = {
      firstName: admins[0].firstName,
      currency: fundRequest.currency,
      userFullname: `${user.firstName} ${user.lastName}`,
      url: `${Utils.getDashboardURL()}/requests/funds`,
      login: `${Utils.getDashboardURL()}/login`,
      dashboardUrl: `${Utils.getDashboardURL()}/requests/funds/${fundRequest.code}`,
      currentBalance: Utils.formatAmount(currentBalance).toLocaleString(),
      amountRequest: Utils.formatAmount(fundRequest.amount).toLocaleString(),
      reason: fundRequest.description,
      requestTo: HelperService.deduceRequestTo(fundRequest),
    };

    admins.forEach((admin) => {
      HelperService.notifyRelevantParties({
        payload,
        user,
        company,
        recipient: admin,
        fundRequest,
      });
    });
  },

  async requestForMoreInfo(payload) {
    const { code, company, note, reviewer } = payload;
    const fundRequest = await Service.getFundRequest({ code, company });

    await Promise.all([
      fundRequest.update({ needsMoreInfo: true, moreInfoDescription: note }),
      MoreInfoLog.create({
        entityType: "fundRequest",
        entityId: fundRequest.id,
        company: fundRequest.company,
        reviewer,
        info: note,
      }),
    ]);

    // notify beneficiary
    const { Company: foundCompany, Budget: foundBudget, description, amount, currency } = fundRequest;

    const recipient = fundRequest.User;

    // update record
    const adminUser = await UserRepo.getOneUser({
      queryParams: { id: reviewer },
      selectOptions: ["firstName", "lastName"],
    });

    const sourceConfig = {
      subject: `${adminUser.firstName} has requested for more info on your fund request of ${
        fundRequest.currency
      }${fundRequest.amount.toLocaleString()}`,
      from_name: `${adminUser.firstName} ${adminUser.lastName}`,
    };

    NotificationService.notifyUser(
      {
        email: recipient.email,
      },
      "more-details-request",
      {
        firstName: recipient.firstName,
        amount: (parseInt(amount, 10) / 100).toLocaleString(),
        login: `${process.env.DASHBOARD_URL}/login`,
        currency,
        dashboardUrl: `${Utils.getDashboardURL()}/requests/funds/${fundRequest.code}`,
        requesterName: `${adminUser.firstName} ${adminUser.lastName}`,
        requesterInitials: Utils.getInitials(`${recipient.firstName} ${recipient.lastName}`),
        details: note,
        time: Utils.formatHumanReadableDate(fundRequest.created_at),
      },
      sourceConfig
    );

    // UPDATE PREVIOUS ACTION NOTIFICATION TO INFO
    NotificationService.updateNotification({ reference_code: code }, { type: "info", badge: "info" });

    const notificationPayload = {
      company,
      user_id: recipient.id,
      type: `info`,
      badge: `info`,
      title: `Fund Request Update`,
      message: `${Utils.toTitle(adminUser.firstName)} needs more info to review your fund request of ${currency}${Utils.formatAmount(
        amount
      ).toLocaleString()} on ${Utils.getCurrentDate()}`,
      reference_code: code,
      body: {
        code: fundRequest.code,
        entity: "FundRequest",
      },
      table: {
        code,
        entity: "FundRequest",
      },
      event: "fundRequestUpdate",
    };
    NotificationService.saveNotification(notificationPayload);
  },

  transformFundRequests(fundRequests) {
    return fundRequests.map((model) => {
      const fundRequest = model.toJSON();
      return {
        ...fundRequest,
        relatedApprovalRequests: ApprovalService.transformApprovalRequests(model.FundRequestApprovalRequests || []),
      };
    });
  },

  totalPendingCount(filters) {
    const { company, currency, budget, balance, loggedInUser } = filters;

    const criteria = HelperService.computeStatisticsCriteria({
      company,
      currency,
      budget,
      balance,
    });

    const approvalRequests = {
      model: ApprovalRequest,
      attributes: ["id"],
      required: false,
      as: "FundRequestApprovalRequests",
      where: { entity: "fundRequest" },
      include: [
        {
          model: ApprovalRule,
          attributes: ["id"],
        },
        {
          model: ApprovalStage,
          attributes: ["id"],
          order: [["created_at", "ASC"]],
          include: [
            {
              model: ApproverLevel,
              attributes: ["id"],
              include: [
                {
                  model: Approver,
                  attributes: ["id"],
                  include: [
                    {
                      model: User,
                      attributes: ["id"],
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          model: Approval,
          attributes: ["id"],
          include: [
            {
              model: Approver,
              attributes: ["id"],
              include: [
                {
                  model: User,
                  attributes: ["id"],
                },
              ],
            },
          ],
        },
      ],
    };

    return FundRequest.count({
      where: {
        ...criteria,
        status: [STATUSES.PENDING],
        ...HelperService.returnRelevantRequests("FundRequestApprovalRequests", loggedInUser),
      },
      group: ["currency"],
      include: [approvalRequests],
    });
  },

  totalPendingAmount(filters) {
    const { company, currency, budget, balance, loggedInUser } = filters;

    const criteria = HelperService.computeStatisticsCriteria({
      company,
      currency,
      budget,
      balance,
    });

    const approvalRequests = {
      model: ApprovalRequest,
      attributes: ["id"],
      required: false,
      as: "FundRequestApprovalRequests",
      where: { entity: "fundRequest" },
      include: [
        {
          model: ApprovalRule,
          attributes: ["id"],
        },
        {
          model: ApprovalStage,
          attributes: ["id"],
          order: [["created_at", "ASC"]],
          include: [
            {
              model: ApproverLevel,
              attributes: ["id"],
              include: [
                {
                  model: Approver,
                  attributes: ["id"],
                  include: [
                    {
                      model: User,
                      attributes: ["id"],
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          model: Approval,
          attributes: ["id"],
          include: [
            {
              model: Approver,
              attributes: ["id"],
              include: [
                {
                  model: User,
                  attributes: ["id"],
                },
              ],
            },
          ],
        },
      ],
    };
    return FundRequest.findAll({
      where: {
        ...criteria,
        status: [STATUSES.PENDING],
        ...HelperService.returnRelevantRequests("FundRequestApprovalRequests", loggedInUser),
      },
      attributes: [[fn("SUM", col("amount")), "amount"], "currency"],
      group: ["currency"],
      include: [approvalRequests],
    });
  },

  async declineMultipleRequest(data) {
    const { fundRequests, company, reviewer, reason } = data;
    const response = await Promise.allSettled(
      fundRequests.map((fundRequest) => {
        return Service.declineFundRequest({
          code: fundRequest.code,
          company,
          reviewer,
          recipient: fundRequest.User,
          amount: fundRequest.amount,
          currency: fundRequest.currency,
          note: reason,
        });
      })
    );
    const failedRequest = response.find((requestResponse) => requestResponse.status === "rejected");
    if (failedRequest) throw new ValidationError(failedRequest.value.message);
    return ResponseService.sendObjectResponse("Fund requests declined successfully");
  },

  async approveMultipleRequest(data) {
    const { fundRequests, company, reviewer, configuration, actionLater } = data;
    const response = await Promise.allSettled(
      fundRequests.map((fundRequest) => {
        return Service.approveFundRequest({
          code: fundRequest.code,
          company,
          reviewer,
          recipient: fundRequest.User,
          amount: fundRequest.amount,
          currency: fundRequest.currency,
          budget: fundRequest.sourceBudget,
          plan: configuration,
          balance: fundRequest.balance,
          actionLater,
        });
      })
    );
    const failedRequest = response.find((requestResponse) => requestResponse.status === "rejected");
    if (failedRequest) throw new ValidationError(failedRequest.reason?.message);
    return ResponseService.sendObjectResponse("Fund requests approved successfully");
  },

  async multipleRequestAction(data) {
    const { requests, company, decision, reviewer, configuration, actionLater, reason = null } = data;
    const promiseResponse = await Promise.allSettled(
      requests.map((code) => {
        return Service.validateFundRequest({
          code,
          company,
          decision,
          actionLater,
          note: reason,
        });
      })
    );
    const failedRequest = promiseResponse.find((response) => response.status === "rejected");
    if (failedRequest) throw new ValidationError(failedRequest.reason.message);

    const fundRequests = promiseResponse.map((response) => response.value.fundRequest);

    if (decision === "approve")
      return Service.approveMultipleRequest({
        fundRequests,
        company,
        reviewer,
        configuration,
        actionLater,
      });
    return Service.declineMultipleRequest({
      fundRequests,
      company,
      reviewer,
      reason,
    });
  },

  async massDelete(payload) {
    const { user, company, requests, status } = payload;

    const foundFundRequests = await FundRequestRepo.listFundRequests({
      queryParams: {
        company,
        ...(user && { user }),
        code: requests,
        status: [STATUSES.PENDING, STATUSES.DRAFT],
      },
    });

    if (foundFundRequests.length !== requests.length) {
      throw new ValidationError("Invalid fund request code sent");
    }

    await FundRequestRepo.updateFundRequest({
      queryParams: {
        code: requests,
      },
      payload: {
        status,
      },
    });

    return ApprovalRequest.update(
      { status },
      { where: { entity: "fundRequest", entity_id: foundFundRequests.map((fundRequest) => fundRequest.id) } }
    );
  },
};

module.exports = Service;
async function createScheduledTransaction({ schedule, currency, amount, fundRequest, company, bankAccount, transaction, bujetiFee, cbnFee }) {
  const { recurring, schedule: diffSchedule, startDate, expiryDate } = schedule;
  const scheduledTransaction = await ScheduleService.createScheduleTransaction({
    currency,
    amount,
    budget: fundRequest?.Budget?.code,
    balance: fundRequest?.Balance?.id,
    recurring,
    schedule: diffSchedule,
    startDate,
    expiryDate,
    company,
    user: fundRequest.user,
    cronBody: {},
    payload: {
      bank_account: bankAccount.id,
      recipient: transaction.recipient,
      recipientType: transaction.recipient_type,
      bujeti_fee: bujetiFee,
      processor_fee: cbnFee,
      narration: transaction.narration,
      category: transaction.category,
      description: transaction.description,
      directDebitId: transaction.directDebitId,
    },
  });
  await Transaction.update(
    {
      status: STATUSES.SCHEDULED,
      schedule_id: scheduledTransaction?.data?.id,
    },
    { where: { id: transaction.id } }
  );
}
