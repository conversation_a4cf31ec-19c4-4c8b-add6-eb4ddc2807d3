const axios = require("axios");
const { literal } = require("sequelize");

const ThirdPartyLogService = require("./thirdPartyLog");
const { CARD_ISSUER } = require("../models/cardissuer");
const { GRAPH_WEBHOOK_EVENTS, PROVIDER_PRODUCTS, GRAPH_PAYIN_STATUSES, BLNK_INFLIGHT_STATUS } = require("../mocks/constants.mock");
const RedisService = require("./redis");
const Utils = require("../utils/index");
const { Log, log } = require("../utils/logger.utils");
const { STATUSES } = require("../models/status");
const {
  BankAccountRepo,
  CompanyRepo,
  BalanceRepo,
  TransferRepo,
  TransactionRepo,
  TransactionAttemptRepo,
  BalanceLedgerRepo,
  UserRepo,
  ReimbursementRepo,
  LedgerIdentityRepo,
} = require("../repositories/index.repo");
const { NotFoundError, ValidationError } = require("../utils/error.utils");
const HelperService = require("./helper.service");
const { getDashboardURL } = require("../utils/index");

const httpClient = {};
axios.defaults.timeout = 300000;

const requestHandler = async (request) => {
  try {
    const { data, status = 200, config } = await request;

    return {
      error: false,
      message: data && data.message,
      body: data,
      data,
      status,
      config: {
        url: `${config?.baseURL}${config.url}`,
        method: config.method,
        data: config.data,
      },
    };
  } catch (error) {
    const { response, message, status, config } = error;
    const { status: statusCode = 400, data: errorData = {} } = response || {};

    return {
      message: errorData?.message || message,
      status: status || statusCode,
      data: errorData,
      body: errorData,
      error: true,
      config: {
        url: `${config.baseURL}${config.url}`,
        method: config.method,
        data: config.data,
      },
    };
  }
};

const Service = {
  httpClient() {
    const headers = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${process.env.GRAPH_API_KEY}`,
    };

    if (!httpClient.client) {
      httpClient.client = axios.create({
        baseURL: process.env.GRAPH_BASE_URL,
        headers,
      });
    }

    return httpClient.client;
  },

  people: {
    async createPerson(payload) {
      const {
        firstName,
        lastName,
        middleName,
        phone,
        email,
        idNumber,
        idType,
        idCountry = "NG",
        bvn,
        dob,
        address: { line1, line2, city, state, country, postalCode } = {},
        company,
        documentType,
        documentUrl,
        documentIssueDate,
        documentExpiryDate,
      } = payload;

      const createPersonRequest = {
        name_first: firstName,
        name_last: lastName,
        name_other: middleName,
        phone,
        email,
        id_level: "primary",
        id_type: idType,
        id_number: idNumber,
        id_country: idCountry,
        kyc_level: "basic",
        dob,
        bank_id_number: bvn,
        address: {
          line1,
          line2,
          city,
          state,
          country,
          postal_code: postalCode,
        },
        documents: [
          {
            type: documentType,
            url: documentUrl,
            issue_date: documentIssueDate,
            expiry_date: documentExpiryDate,
          },
        ],
      };

      const request = Service.httpClient().post("/person", createPersonRequest);

      const { data, error, message, config, status } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Failed to create" : "Created"} person`,
        company,
        event: "graph.person.created",
        payload: JSON.stringify(createPersonRequest),
        provider: CARD_ISSUER.graph,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status,
        endpoint: config.url,
        method: config.method,
      });

      return { data, error, message, status };
    },

    async fetchPerson(payload) {
      const { personCode, company } = payload;

      const request = Service.httpClient().get(`/person/${personCode}`);

      const { data, error, message, config, status } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || "Fetched person",
        company,
        event: "graph.person.fetched",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.graph,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status,
        endpoint: config.url,
        method: config.method,
      });

      return { data, error, message, status };
    },
  },

  businesses: {
    async createBusiness(payload) {
      const {
        ownerId,
        name,
        businessType,
        industry,
        idType,
        idNumber,
        idCountry,
        idLevel,
        dateOfFormation,
        contactPhone,
        contactEmail,
        address: { line1, line2, city, state, country, postalCode } = {},
        company,
        idUpload,
      } = payload;

      const createBusinessRequest = {
        owner_id: ownerId,
        name,
        business_type: businessType,
        industry,
        id_type: idType,
        id_number: idNumber,
        id_country: idCountry,
        id_level: idLevel,
        dof: dateOfFormation,
        contact_phone: contactPhone,
        contact_email: contactEmail,
        id_upload: idUpload,
        address: {
          line1,
          line2,
          city,
          state,
          country,
          postal_code: postalCode,
        },
      };

      const request = Service.httpClient().post("/business", createBusinessRequest);

      const { data, error, message, config, status } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Failed to create" : "Created"} business`,
        company,
        event: "graph.business.created",
        payload: JSON.stringify(createBusinessRequest),
        provider: CARD_ISSUER.graph,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status,
        endpoint: config.url,
        method: config.method,
      });

      return { data, error, message, status };
    },

    async fetchBusiness(payload) {
      const { businessId, company } = payload;

      const request = Service.httpClient().get(`/business/${businessId}`);

      const { data, error, message, config, status } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || "Fetched business",
        company,
        event: "graph.business.fetched",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.graph,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status,
        endpoint: config.url,
        method: config.method,
      });

      return { data, error, message, status };
    },
  },

  accounts: {
    async createAccount(payload) {
      const { label, currency, businessId, company } = payload;

      const createAccountRequest = {
        business_id: businessId,
        label,
        currency,
      };

      const request = Service.httpClient().post("/bank_account", createAccountRequest);

      const { data, error, message, config, status } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Failed to create" : "Created"} account`,
        company,
        event: "graph.account.created",
        payload: JSON.stringify(createAccountRequest),
        provider: CARD_ISSUER.graph,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status,
        endpoint: config.url,
        method: config.method,
      });

      return { data, error, message, status };
    },

    async fetchAccount(payload) {
      const { accountId, company } = payload;

      const request = Service.httpClient().get(`/bank_account/${accountId}`);

      const { data, error, message, config, status } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || "Fetched account",
        company,
        event: "graph.account.fetched",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.graph,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status,
        endpoint: config.url,
        method: config.method,
      });

      return { data, error, message, status };
    },

    async fetchAccountBalance(payload) {
      const { accountId, company } = payload;

      const request = Service.httpClient().get(`/wallet_account/${accountId}`);

      const { data, error, message, config, status } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || "Fetched account balance",
        company,
        event: "graph.account.balance.fetched",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.graph,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status,
        endpoint: config.url,
        method: config.method,
      });

      return { data, error, message, status };
    },
  },

  payoutDestinations: {
    async createPayoutDestination(payload) {
      const {
        accountId,
        currency,
        company,
        sourceType = "bank_account",
        label,
        type = "wire",
        wireType,
        destinationType = "bank_account",
        accountType = "business",
        bankCode,
        accountNumber,
        routingNumber,
        routingType,
        beneficiaryName,
        beneficiaryAddress,
        bankName,
        bankAddress,
      } = payload;

      const createPayoutDestinationRequest = {
        account_id: accountId,
        source_type: sourceType,
        label,
        currency,
        type,
        wire_type: wireType,
        destination_type: destinationType,
        account_type: accountType,
        bank_code: bankCode,
        account_number: accountNumber,
        routing_number: routingNumber,
        routing_type: routingType,
        beneficiary_name: beneficiaryName,
        beneficiary_address: beneficiaryAddress,
        bank_name: bankName,
        bank_address: bankAddress,
      };

      const request = Service.httpClient().post("/payout-destination", createPayoutDestinationRequest);

      const { data, error, message, config, status } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Failed to create" : "Created"} payout destination`,
        company,
        event: "graph.payout.destination.created",
        payload: JSON.stringify(createPayoutDestinationRequest),
        provider: CARD_ISSUER.graph,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status,
        endpoint: config.url,
        method: config.method,
      });

      return { data, error, message, status };
    },

    async getPayoutDestination(payload) {
      const { payoutDestinationId, company } = payload;

      const request = Service.httpClient().get(`/payout-destination/${payoutDestinationId}`);

      const { data, error, message, config, status } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || "Fetched payout destination",
        company,
        event: "graph.payout.destination.fetched",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.graph,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status,
        endpoint: config.url,
        method: config.method,
      });

      return { data, error, message, status };
    },
  },

  payouts: {
    async createPayout(payload) {
      const { payoutDestinationId, amount, description, company } = payload;

      const createPayoutRequest = {
        destination_id: payoutDestinationId,
        amount,
        description,
      };

      const request = Service.httpClient().post("/payout", createPayoutRequest);

      const { data, error, message, config, status } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Failed to create" : "Created"} payout`,
        company,
        event: "graph.payout.created",
        payload: JSON.stringify(createPayoutRequest),
        provider: CARD_ISSUER.graph,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status,
        endpoint: config.url,
        method: config.method,
      });

      return { data, error, message, status };
    },

    async fetchPayout(payload) {
      const { payoutId, company } = payload;

      const request = Service.httpClient().get(`/payout/${payoutId}`);

      const { data, error, message, config, status } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || "Fetched payout",
        company,
        event: "graph.payout.fetched",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.graph,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status,
        endpoint: config.url,
        method: config.method,
      });

      return { data, error, message, status };
    },
  },

  webhooks: {
    successEvents: {
      async accountCreated(payload, Providers) {
        const {
          id: accountId,
          bank_code: bankCode,
          account_number: accountNumber,
          routing_number: routingNumber,
          bank_name: bankName,
          account_name: accountName,
          bank_address: bankAddress,
        } = payload;

        const redLock = await RedisService.getRedLock();
        const ttl = 5000; // 5 seconds
        let lock;

        try {
          lock = await redLock.acquire([`account_created_${payload.id}`], ttl);
          log(Log.fg.blue, `Lock acquired for account_created_${payload.id}`);

          const foundBankAccount = await BankAccountRepo.getOneBankAccount({
            queryParams: {
              issuer: CARD_ISSUER.Graph,
              status: STATUSES.PENDING,
              type: "virtual",
              subType: "deposit",
              ownerType: "company",
              externalBankAccountId: accountId,
            },
            addBalance: true,
            selectOptions: ["currency", "owner"],
          });

          if (!foundBankAccount) throw new NotFoundError("Bank Account");

          await ThirdPartyLogService.createLog({
            message: "Graph account created",
            event: "graph.account.created",
            payload: JSON.stringify(payload),
            provider: CARD_ISSUER.graph,
            providerType: "issuer",
            response: JSON.stringify(payload),
            statusCode: 200,
            endpoint: `${Utils.getApiURL()}/webhook/graph`,
            method: "POST",
            company: foundBankAccount.owner,
          });

          const foundCompany = await CompanyRepo.getCompany({
            queryParams: {
              id: foundBankAccount.owner,
              status: STATUSES.VERIFIED,
            },
          });

          if (!foundCompany) throw new NotFoundError("Company");
          const provider = HelperService.getProvider(foundBankAccount.owner, PROVIDER_PRODUCTS.LEDGER);

          const providerHandler = await Providers[provider];

          const { identityId, ledgerId } = await HelperService.createOrReturnLedgerIdentity(foundCompany, providerHandler);

          const { data: createdBalance, error: createBalanceError } = await providerHandler.balances.createBalance(
            {
              ledger_id: ledgerId,
              currency: foundBankAccount.currency,
              identity_id: identityId,
              meta_data: {
                balance: foundBankAccount.Balance?.code,
              },
            },
            foundCompany.id
          );

          if (createBalanceError) {
            throw new ValidationError("Failed to create balance");
          }

          await foundBankAccount.update({
            accountName,
            number: accountNumber,
            routingNumber,
            bankName,
            bankCode,
            status: STATUSES.ACTIVE,
            externalIdentifier: createdBalance.balance_id,
            meta: bankAddress,
          });

          await BalanceRepo.update({ bankAccount: foundBankAccount?.id }, { status: STATUSES.ACTIVE });
        } catch (error) {
          log(Log.fg.red, error.stack);
        } finally {
          log(Log.fg.blue, `Releasing lock for account_created_${payload.id}`);
          if (lock) {
            await redLock.unlock(lock);
          }
        }
      },
      async accountCredited(payload, Providers) {
        const {
          account_id: accountId,
          deposit: {
            id: depositId,
            amount,
            fee,
            payer: { account_number: senderBankAccountNumber, account_name: accountName, name },
          },
          status,
          currency,
          description: narration,
        } = payload;
        const senderName = accountName || name;

        if (status !== GRAPH_PAYIN_STATUSES.SUCCESS) {
          log(Log.fg.cyan, `PayIn is of status: ${status}`);
          return;
        }

        const redLock = await RedisService.getRedLock();
        const ttl = 10000; // 10 seconds
        let lock;

        try {
          const foundBankAccount = await BankAccountRepo.getOneBankAccount({
            queryParams: {
              externalBankAccountId: accountId,
              issuer: CARD_ISSUER.Graph,
              status: STATUSES.ACTIVE,
              ownerType: "company",
              type: "virtual",
              subtype: "deposit",
            },
            addBalance: true,
            selectOptions: ["externalIdentifier", "owner", "number", "bankName", "currency"],
          });

          if (!foundBankAccount) throw new NotFoundError("Bank Account");

          ThirdPartyLogService.createLog({
            message: "Graph account credited",
            event: "graph.account.credited",
            payload: JSON.stringify(payload),
            provider: CARD_ISSUER.graph,
            providerType: "issuer",
            response: JSON.stringify(payload),
            statusCode: 200,
            endpoint: `${Utils.getApiURL()}/webhook/graph`,
            method: "POST",
            company: foundBankAccount.owner,
          });

          const foundCompany = await CompanyRepo.getOneCompany({
            queryParams: {
              id: foundBankAccount.owner,
            },
            addPaymentPlan: true,
            selectOptions: ["name"],
          });

          lock = await redLock.acquire([`lock:account_credited:${accountId}`], ttl);
          log(Log.fg.blue, `Lock acquired for inflow to account: ${accountId}`);

          const hasAlreadyRecordedInflow = await TransferRepo.getTransfer({
            filter: { reference: depositId, status: STATUSES.SUCCESS },
          });

          if (hasAlreadyRecordedInflow) {
            log(Log.fg.blue, `Inflow already recorded for reference: ${depositId}`);
            return;
          }

          const provider = HelperService.getProvider(foundBankAccount.owner, PROVIDER_PRODUCTS.LEDGER);

          const providerHandler = await Providers[provider];

          await HelperService.validateLedgerIdentity(foundCompany);

          const depositFee = HelperService.determineDepositFee(true, amount, currency, foundCompany.PaymentPlan);

          const { data: providerBalance, error: viewBalanceDetailsError } = await providerHandler.balances.viewBalanceDetails(
            foundBankAccount.externalIdentifier,
            foundCompany.id
          );

          if (viewBalanceDetailsError) {
            throw new ValidationError("Failed to view balance details");
          }

          const inflowPayload = {
            amount: Utils.formatAmount(amount),
            description: narration || `Transfer received from ${senderName}(${senderBankAccountNumber})`,
            currency,
            precision: 100,
            reference: depositId,
            destination: foundBankAccount.externalIdentifier,
            source: `@GraphWorld${currency.toUpperCase()}`,
            allow_overdraft: true, // allow credit to proceed even if source is not enough
            skip_queue: true, // credit immediately
            meta_data: {
              transfer: depositId,
            },
          };

          const { data: createdInflow, error: createInflowError } = await providerHandler.transactions.recordTransaction(
            inflowPayload,
            foundCompany.id
          );

          if (createInflowError) {
            throw new ValidationError("Failed to create inflow");
          }

          const processorFee = Utils.getInteger(fee);
          const totalFees = Utils.money(processorFee).plus(Utils.getInteger(depositFee)).toNumber();

          const createTransferPayload = Utils.stripUndefinedAndNullDeep({
            amount: Number(amount) - Number(totalFees),
            actualAmount: Number(amount),
            company: foundCompany.id,
            currency,
            reference: depositId,
            description: `Transfer received from ${senderName}(${senderBankAccountNumber})`,
            processor_fee: processorFee,
            bujeti_fee: depositFee,
            status: STATUSES.SUCCESS,
            narration,
            balance: foundBankAccount?.Balance?.id,
            externalIdentifier: createdInflow.transaction_id,
          });

          const { actualAmount, bujeti_fee: bujetiFee } = createTransferPayload;

          const createdTransfer = await TransferRepo.createTransfer({ data: createTransferPayload });

          const availableBalance = providerBalance.balance;

          await BalanceRepo.createLedger({
            amount: actualAmount,
            currency,
            company: foundCompany.id,
            balanceAfter: availableBalance + parseInt(actualAmount, 10),
            balanceBefore: availableBalance,
            transfer: createdTransfer.id,
            balance: foundBankAccount?.Balance?.id,
            description: createdTransfer.description,
            status: STATUSES.PROCESSED,
          });

          const recordFeesPromises = [];
          if (totalFees > 0) {
            // record fees charged. separately, so we can see where money is going
            if (bujetiFee > 0) {
              const bujetiFeePayload = {
                amount: Utils.formatAmount(bujetiFee),
                description: narration,
                currency,
                precision: 100,
                reference: `fee_${createdTransfer.code}`,
                source: foundBankAccount.externalIdentifier,
                destination: `@GraphBujetiPayInFees${currency.toUpperCase()}`,
                allow_overdraft: false,
                inflight: false,
                skip_queue: true, // debit immediately
                meta_data: {
                  transfer: createdTransfer.code,
                },
              };

              recordFeesPromises.push(providerHandler.transactions.recordTransaction(bujetiFeePayload, foundCompany.id));
            }

            if (processorFee > 0) {
              const processorFeePayload = {
                amount: Utils.formatAmount(processorFee),
                description: narration,
                currency,
                precision: 100,
                reference: `provider_fee_${createdTransfer.code}`,
                source: foundBankAccount.externalIdentifier,
                destination: `@GraphProviderPayInFees${currency.toUpperCase()}`,
                allow_overdraft: false,
                inflight: false,
                skip_queue: true, // debit immediately
                meta_data: {
                  transfer: createdTransfer.code,
                },
              };

              recordFeesPromises.push(providerHandler.transactions.recordTransaction(processorFeePayload, foundCompany.id));
            }

            const [{ error: bujetiFeeError } = {}, { error: processorFeeError } = {}] = await Promise.all(recordFeesPromises);

            await BalanceRepo.createLedger({
              amount: -1 * totalFees,
              currency,
              company: foundCompany.id,
              balanceAfter: availableBalance + parseInt(actualAmount, 10) - parseInt(totalFees, 10),
              balanceBefore: availableBalance + parseInt(actualAmount, 10),
              transfer: createdTransfer.id,
              balance: foundBankAccount?.Balance?.id,
              description: `Deposit fee for ${createdTransfer.code}`,
              status: STATUSES.PROCESSED,
            });

            if (processorFeeError || bujetiFeeError) {
              // TODO: try to charge fees async, if we get here. but let us monitor for now
              throw new ValidationError(`Failed to record fees for transfer ${createdTransfer.code}`);
            }

            HelperService.notifyOnInflow({
              company: foundCompany.id,
              transfer: createdTransfer,
              amount,
              depositFee: totalFees,
              payload: createTransferPayload,
              currency,
              bankAccount: foundBankAccount,
              counterParty: { accountName: senderName },
              virtualNuban: { accountNumber: foundBankAccount.number },
              settlementAccount: { bankName: foundBankAccount.bankName, accountNumber: foundBankAccount.number },
            });
          }
        } catch (error) {
          log(Log.fg.red, error.stack);
        } finally {
          log(Log.fg.blue, `Releasing lock for account_credited_${accountId}`);
          if (lock) {
            await redLock.unlock(lock);
          }
        }
      },

      async payoutInitiated(payload, Providers) {
        const { payout_id: reference } = payload;

        const foundTransaction = await TransactionRepo.getTransaction({
          queryParams: { reference },
          bank: true,
          vendor: true,
          company: true,
          budget: true,
        });

        if (!foundTransaction) {
          throw new NotFoundError("Transaction");
        }

        ThirdPartyLogService.createLog({
          message: "Graph transfer initiated",
          company: foundTransaction.company,
          event: "graph.payout.initiated",
          payload: JSON.stringify(payload),
          provider: CARD_ISSUER.Graph,
          providerType: "issuer",
          response: JSON.stringify({ message: "OK" }),
          statusCode: 200,
          endpoint: `${Utils.getApiURL()}/webhook/graph`,
          method: "POST",
        });

        const lockTTL = 5000; // 5 seconds
        const redLock = await RedisService.getRedLock();
        let lock;
        try {
          lock = await redLock.acquire([`lock:transaction:${foundTransaction.id}`], lockTTL);
          const ledgerCriteria = {
            transaction: foundTransaction.id,
            status: [STATUSES.PENDING],
          };

          const existingLedger = await BalanceLedgerRepo.getBalanceLedger({ filter: ledgerCriteria });

          if (existingLedger) return existingLedger;

          const {
            amount,
            balance,
            currency,
            narration,
            processor_fee: processorFee,
            bujeti_fee: bujetiFee,
            company,
            description,
            id,
          } = foundTransaction;

          const totalAmount = Utils.money(amount)
            .plus(processorFee || 0)
            .plus(bujetiFee || 0)
            .toNumber();

          await TransactionAttemptRepo.createTransactionAttempt({
            payload: {
              reference,
              transaction: foundTransaction.id,
              status: STATUSES.PENDING,
              externalIdentifier: reference,
            },
          });

          const foundBalance = await BalanceRepo.getBalance({
            filter: { currency, company, status: STATUSES.ACTIVE, id: balance },
            includeAccount: true,
          });

          if (!foundBalance) throw new NotFoundError("Balance");

          const availableBalance = await BalanceRepo.getAvailableBalance({
            company,
            currency,
            id: balance,
          });

          await BalanceLedgerRepo.createLedger({
            company,
            currency,
            description,
            amount: -1 * totalAmount,
            transaction: id,
            status: STATUSES.PENDING,
            balance: foundBalance?.id,
            balanceBefore: parseInt(availableBalance, 10),
            balanceAfter: parseInt(availableBalance, 10) - totalAmount,
          });

          const provider = HelperService.getProvider(company, PROVIDER_PRODUCTS.LEDGER);
          const providerHandler = await Providers[provider];

          const outflowPayload = {
            amount: Utils.formatAmount(amount),
            description: narration,
            currency,
            precision: 100,
            reference,
            source: foundBalance.BankAccount.externalIdentifier,
            destination: `@World${currency.toUpperCase()}`,
            allow_overdraft: false, // if not enough money, throw an error
            inflight: true, // held till graph notifies us of success or failed
            skip_queue: true, // debit immediately
            meta_data: {
              transaction: foundTransaction.code,
            },
          };

          const {
            data: createdOutflow,
            error: recordTransactionError,
            message: recordTransactionMessage,
          } = await providerHandler.transactions.recordTransaction(outflowPayload, company);

          if (recordTransactionError) {
            throw new ValidationError(`${recordTransactionMessage} for transaction ${reference}`);
          }

          return TransactionRepo.updateTransaction({
            queryParams: { reference },
            updateFields: { externalIdentifier: createdOutflow.transaction_id },
          });
        } catch (error) {
          log(Log.fg.red, error.stack);
        } finally {
          log(Log.fg.blue, `Lock released for transaction: ${foundTransaction.id}`);
          if (lock) await redLock.unlock(lock);
        }

        return null;
      },

      async payoutSuccessful(payload, Providers) {
        const { payout_id: reference } = payload;

        const foundTransaction = await TransactionRepo.getTransaction({
          queryParams: { reference },
          bank: true,
          vendor: true,
          company: true,
          budget: true,
        });

        if (!foundTransaction) {
          throw new NotFoundError("Transaction");
        }

        ThirdPartyLogService.createLog({
          message: "Graph transfer successful",
          company: foundTransaction.company,
          event: "graph.payout.success",
          payload: JSON.stringify(payload),
          provider: CARD_ISSUER.Graph,
          providerType: "issuer",
          response: JSON.stringify({ message: "OK" }),
          statusCode: 200,
          endpoint: `${Utils.getApiURL()}/webhook/graph`,
          method: "POST",
        });

        const lockTTL = 5000; // 5 seconds
        const redLock = await RedisService.getRedLock();
        let lock;
        try {
          lock = await redLock.acquire([`lock:transaction:${foundTransaction.id}`], lockTTL);
          const existingAttempt = await TransactionAttemptRepo.getTransactionAttempt({
            filter: { externalIdentifier: reference, status: STATUSES.PENDING },
          });

          if (!existingAttempt) {
            return existingAttempt;
          }

          const foundCompany = await CompanyRepo.getCompany({
            queryParams: {
              id: foundTransaction.company,
            },
          });

          const { company, code: transactionCode, currency, description, amount, narration } = foundTransaction;

          const provider = HelperService.getProvider(foundCompany.id, PROVIDER_PRODUCTS.LEDGER);

          const providerHandler = await Providers[provider];

          const foundLedgerIdentity = await LedgerIdentityRepo.getLedgerIdentity({
            company,
            issuer: CARD_ISSUER.Blnk,
          });

          if (!foundLedgerIdentity) throw new NotFoundError("Ledger Identity");

          // commit the transaction on ledger
          const { error: updateInflightTransactionError } = await providerHandler.transactions.updateInflightTransaction({
            transactionId: foundTransaction.externalIdentifier,
            payload: {
              status: BLNK_INFLIGHT_STATUS.COMMIT,
            },
            company,
          });

          if (updateInflightTransactionError) {
            throw new ValidationError("Failed to commit transaction on ledger");
          }

          await Promise.all([
            TransactionAttemptRepo.updateTransactionAttempt({
              filter: { externalIdentifier: reference },
              payload: { status: STATUSES.SUCCESS },
            }),
            TransactionRepo.updateTransaction({
              queryParams: { reference },
              updateFields: {
                status: STATUSES.SUCCESS,
                failure_reason: null,
                paidOn: literal("CURRENT_TIMESTAMP"),
              },
            }),
          ]);

          // charge fees
          const foundBalance = await BalanceRepo.getBalance({
            filter: { currency, company, status: STATUSES.ACTIVE, id: foundTransaction.balance },
            includeAccount: true,
          });

          const totalFees = Utils.money(foundTransaction.processor_fee).plus(foundTransaction.bujeti_fee).toNumber();

          let recordTransactionFeeError;
          if (totalFees > 0) {
            const outflowPayload = {
              amount: Utils.formatAmount(totalFees),
              description: description || narration,
              currency,
              precision: 100,
              reference: `fee_${transactionCode}`,
              source: foundBalance.BankAccount.externalIdentifier,
              destination: `@PayoutFees${currency.toUpperCase()}`,
              allow_overdraft: false,
              inflight: false,
              skip_queue: true, // debit immediately
              meta_data: {
                transaction: foundTransaction.code,
              },
            };

            const recordTransactionFeeResponse = await providerHandler.transactions.recordTransaction(outflowPayload, foundCompany.id);
            recordTransactionFeeError = recordTransactionFeeResponse.error;
          }

          HelperService.makeBatchTransactionsSuccessful(foundTransaction.batch_id);

          await BalanceLedgerRepo.recordSuccessfulTransfer(foundTransaction);

          HelperService.handleCategorySpent(foundTransaction);

          const reason = HelperService.deduceReasonForTransaction(foundTransaction);

          await HelperService.finalizeCardRequestCharge({ reason, transaction: foundTransaction, company });

          HelperService.processBillingViaQueue({ reason, transaction: foundTransaction, company });

          HelperService.finalizeSubscriptionAddons({ reason, transaction: foundTransaction, company });

          const payer = await UserRepo.fetchUser(foundTransaction.payer);

          let email;
          const companyWithAdmin = await CompanyRepo.getCompanyWithAdmins({ id: company }, true);

          if (payer) {
            const { firstName, ...remainingPayerData } = payer.toJSON();
            email = remainingPayerData.email;

            const emailPayload = {
              dashboardUrl: `${getDashboardURL()}/transactions/${transactionCode}`,
              accountNumber: foundTransaction.BankAccount.number,
              amount: (parseInt(amount, 10) / 100).toLocaleString(),
              currency,
              description,
              paidOn: Utils.formatHumanReadableDate(foundTransaction.created_at),
              paidBy: `${foundTransaction.User.firstName} ${foundTransaction.User.lastName}`,
              paidTo: `${foundTransaction.BankAccount.accountName}`,
              companyName: foundCompany.name,
              referenceId: transactionCode,
              firstName,
            };

            HelperService.notifyPayerIfNotBatchTransaction({
              transaction: foundTransaction,
              notify: true,
              email,
              emailPayload,
              firstName,
              currency,
              companyWithAdmin,
            });
          }

          HelperService.notifyRecipientIfNeeded({ transaction: foundTransaction, amount, currency, transactionCode, notify: true });

          const admins = companyWithAdmin?.Users || [];

          HelperService.notifyAdminsIfNotBatchTransaction({
            notify: true,
            transaction: foundTransaction,
            admins,
            email,
            companyWithAdmin,
            payer,
            counterPartyObject: { attributes: { accountNumber: foundBalance.BankAccount?.number }, accountName: foundBalance.bankAccount?.bankName },
            reason,
            company,
            currency,
            amount,
          });

          HelperService.finalizeBillPayment(reason, company, foundTransaction);

          const foundReimbursement = await ReimbursementRepo.getReimbursement({
            criteria: {
              transaction: foundTransaction.id,
            },
          });

          if (foundReimbursement) {
            await HelperService.finalizeReimbursement("reimbursement", company, foundReimbursement.code, foundTransaction, transactionCode);
          }

          if (recordTransactionFeeError) {
            // TODO: try to charge fees async, if we get here. but let us monitor for now
            throw new ValidationError(`Failed to record fees for transaction ${transactionCode}`);
          }

          return foundTransaction;
        } finally {
          log(Log.fg.blue, `Lock released for transaction: ${foundTransaction.id}`);
          if (lock) await redLock.unlock(lock);
        }
      },
    },
    failedEvents: {
      async accountIssuanceFailed(payload) {
        const { id: accountId } = payload;

        const redLock = await RedisService.getRedLock();
        const ttl = 5000; // 5 seconds
        let lock;

        try {
          lock = await redLock.acquire([`lock:account_issuance_failed:${accountId}`], ttl);
          log(Log.fg.blue, `Lock acquired for account issuance failed: ${accountId}`);

          const foundBankAccount = await BankAccountRepo.getOneBankAccount({
            queryParams: {
              externalBankAccountId: accountId,
              issuer: CARD_ISSUER.Graph,
              status: STATUSES.PENDING,
              ownerType: "company",
              type: "virtual",
              subtype: "deposit",
            },
            selectOptions: ["owner"],
          });

          if (!foundBankAccount) throw new NotFoundError("Bank Account");

          ThirdPartyLogService.createLog({
            message: "Graph account issuance failed",
            event: "graph.account.issuance.failed",
            payload: JSON.stringify(payload),
            provider: CARD_ISSUER.graph,
            providerType: "issuer",
            response: JSON.stringify(payload),
            statusCode: 200,
            endpoint: `${Utils.getApiURL()}/webhook/graph`,
            method: "POST",
            company: foundBankAccount.owner,
          });

          await foundBankAccount.update({
            status: STATUSES.FAILED,
          });

          await BalanceRepo.update({ bankAccount: foundBankAccount?.id }, { status: STATUSES.FAILED });
        } catch (error) {
          log(Log.fg.red, error.stack);
        } finally {
          log(Log.fg.blue, `Releasing lock for account_issuance_failed_${accountId}`);
          if (lock) await redLock.unlock(lock);
        }
      },
      async payoutFailed(payload, Providers) {
        const { payout_id: reference } = payload;

        const redLock = await RedisService.getRedLock();
        const ttl = 5000; // 5 seconds
        let lock;

        try {
          lock = await redLock.acquire([`lock:payout_failed:${reference}`], ttl);
          log(Log.fg.blue, `Lock acquired for payout failed: ${reference}`);

          const foundTransaction = await TransactionRepo.getTransaction({
            queryParams: { reference },
            bank: true,
            vendor: true,
            company: true,
            budget: true,
          });

          if (!foundTransaction) throw new NotFoundError("Transaction");

          ThirdPartyLogService.createLog({
            message: "Graph payout failed",
            event: "graph.payout.failed",
            payload: JSON.stringify(payload),
            provider: CARD_ISSUER.graph,
            providerType: "issuer",
            response: JSON.stringify(payload),
            statusCode: 200,
            endpoint: `${Utils.getApiURL()}/webhook/graph`,
            method: "POST",
            company: foundTransaction.company,
          });

          const providerHandler = await HelperService.getInternalLedgerProviderHandler(foundTransaction, Providers);

          // void the transaction on ledger
          const { error: updateInflightTransactionError } = await providerHandler.transactions.updateInflightTransaction({
            transactionId: foundTransaction.externalIdentifier,
            payload: {
              status: BLNK_INFLIGHT_STATUS.VOID,
            },
            company: foundTransaction.company,
          });

          if (updateInflightTransactionError) {
            throw new ValidationError("Failed to void transaction on ledger");
          }

          await TransactionAttemptRepo.updateTransactionAttempt({
            filter: { externalIdentifier: reference },
            payload: { status: STATUSES.FAILED },
          });

          const transactionUpdateData = {
            status: STATUSES.FAILED,
          };

          const { company, code } = foundTransaction;

          await TransactionRepo.updateTransaction({
            queryParams: { company, code },
            updateFields: { ...transactionUpdateData },
          });

          const balanceCriteria = {
            company,
            transaction: foundTransaction.id,
            status: STATUSES.PENDING,
          };

          BalanceLedgerRepo.logFailedTransfer({ criteria: balanceCriteria });

          const reason = HelperService.deduceReasonForTransaction(foundTransaction);

          HelperService.processBillingViaQueue({ reason, foundTransaction, company });

          HelperService.finalizeBillPayment(reason, company, foundTransaction, STATUSES.FAILED);
        } catch (error) {
          log(Log.fg.red, error.stack);
        } finally {
          log(Log.fg.blue, `Releasing lock for payout_failed_${reference}`);
          if (lock) await redLock.unlock(lock);
        }
      },
    },
  },

  async handleWebhook(req, Providers = {}) {
    const { event_type: eventType, data } = req.body;

    log(Log.fg.magenta, `Received Graph webhook event:${JSON.stringify(req.body)}`);

    switch (eventType) {
      case GRAPH_WEBHOOK_EVENTS.ACCOUNT_CREATED:
        await Service.webhooks.successEvents.accountCreated(data, Providers);
        break;
      case GRAPH_WEBHOOK_EVENTS.ACCOUNT_CREDITED:
        await Service.webhooks.successEvents.accountCredited(data, Providers);
        break;
      case GRAPH_WEBHOOK_EVENTS.PAYOUT_SUCCESS:
        await Service.webhooks.successEvents.payoutSuccessful(data, Providers);
        break;
      case GRAPH_WEBHOOK_EVENTS.ACCOUNT_CREATION_FAILED:
        await Service.webhooks.failedEvents.accountIssuanceFailed(data, Providers);
        break;
      case GRAPH_WEBHOOK_EVENTS.PAYOUT_FAILED:
        await Service.webhooks.failedEvents.payoutFailed(data, Providers);
        break;
      case GRAPH_WEBHOOK_EVENTS.PAYOUT_PENDING:
        await Service.webhooks.successEvents.payoutInitiated(data, Providers);
        break;
      default:
        break;
    }
  },
};

module.exports = Service;
