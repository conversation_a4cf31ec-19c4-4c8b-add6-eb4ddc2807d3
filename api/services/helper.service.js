/* eslint-disable no-param-reassign */
/* eslint-disable camelcase */
const { subDays, getDay, startOfDay, endOfDay, getMonth, getDate } = require("date-fns");
const { Op, literal } = require("sequelize");
const dateFns = require("date-fns");
const { parsePhoneNumber, isValidPhoneNumber } = require("libphonenumber-js");
const {
  UserRepo,
  CompanyRepo,
  CountryRepo,
  industryRepo,
  BudgetRepo,
  DocumentRepo,
  PhoneNumberRepo,
  AddressRepo,
  BudgetPolicyRepo,
  MonoAccountHolderRepo,
  VendorRepo,
  CategoryRepo,
  ApproverLevelRepo,
  TransactionRepo,
  BatchTransactionRepo,
  BalanceRepo,
  MccRepo,
  AccountMemberRepo,
  IndividualRepo,
  BankAccountRepo,
  BillingAddonRepo,
  CustomerRepo,
  TransactionAttemptRepo,
  BudgetAccountRepo,
  TransferRepo,
  CardRequestRepo,
  LedgerIdentityRepo,
} = require("../repositories/index.repo");
const responseUtils = require("../utils/response.utils");
const { ValidationError, HttpException, HttpStatus, ForbiddenError, NotFoundError } = require("../utils/error.utils");
const { NigerianStateMock } = require("../mocks/index");
const {
  Mandate,
  sequelize,
  PolicyRestriction,
  UserBudget,
  Budget,
  BudgetLedger,
  Policy,
  PolicyType,
  Sequelize,
  DirectDebit,
  PaymentPlan,
  BillingHistory,
  Individual,
} = require("../models");
const { STATUSES } = require("../models/status");
const Sanitizer = require("../utils/sanitizer");
const { HelperValidator } = require("../validators");
const SettingsService = require("./settings");
const Utils = require("../utils");
const { Banks } = require("../mocks/banks.mock");
const NotificationService = require("./notification");
const { FUND_REQUEST_TYPES } = require("../models/fundrequest");
const {
  METHODS,
  PAYMENT_PLANS,
  BILLING_PERIODS,
  BILLING_ADDONS,
  TRANSACTION_RECIPIENT_TYPES,
  PAYMENT_PROVIDERS,
  PROVIDER_PRODUCTS,
  VIRTUAL_ACCOUNT_PROVIDERS,
  INDIVIDUAL_TYPES,
  COUNTRY_ISO2,
} = require("../mocks/constants.mock");
const { TRANSACTION_TYPES } = require("../models/transaction");
const QueueService = require("./queue.service");
const API = require("./api");
const { CARD_ISSUER } = require("../models/cardissuer");
const RedisService = require("./redis");
const { parseJSON } = require("../utils");

const Service = {
  async UserChecker({ id, password = false, company = null }) {
    const code = String(id).includes("usr_") && id;

    const selectOptions = ["code", "firstName", "lastName", "phoneNumber", "address", "email"];
    if (password) selectOptions.push("password");
    const user = await UserRepo.getOneUser({
      queryParams: {
        ...(!code && { id }),
        ...(code && { code }),
        ...(company && { company }),
      },
      selectOptions,
    });
    if (!user) return responseUtils.BadRequestException("Something went wrong, User not found");

    return responseUtils.sendObjectResponse("User Exists", user);
  },

  async CompanyChecker({ company_code, industry = false }) {
    const companyQuery = { code: company_code };
    const existingCompany = await CompanyRepo.getOneCompany({
      queryParams: companyQuery,
      ...(industry && { addIndustry: true }),
      selectOptions: ["code", "name", "name", "industry", "document_reference", "director", "address", "description", "phoneNumber"],
    });
    if (!existingCompany) throw new HttpException("Company not found", HttpStatus.NOT_FOUND);

    return responseUtils.sendObjectResponse("Company Exists", existingCompany);
  },

  async CountryChecker({ code }) {
    const companyQuery = { code, activate: true };
    const existingCountry = await CountryRepo.getOneCountry({
      queryParams: companyQuery,
      selectOptions: ["code", "name", "iso_code", "caller_code"],
    });
    if (!existingCountry) throw new ForbiddenError("Country not currently available");
    if (existingCountry.name.toUpperCase() === "NIGERIA") existingCountry.dataValues.states = NigerianStateMock;

    return responseUtils.sendObjectResponse("Country Exists", existingCountry);
  },

  async IndustryChecker({ industry_code }) {
    const industryQuery = { code: industry_code };
    const existingCompany = await industryRepo.getOneIndustry({
      queryParams: industryQuery,
      selectOptions: ["name"],
    });
    if (!existingCompany) throw new HttpException("Company not found", HttpStatus.NOT_FOUND);

    return responseUtils.sendObjectResponse("Company Exists", existingCompany);
  },

  async BudgetChecker({ budget_code, code }) {
    const existingBudget = await BudgetRepo.getOneBudget({
      queryParams: { code: budget_code || code },
    });
    if (!existingBudget) throw new HttpException("Budget not found", HttpStatus.NOT_FOUND);

    return responseUtils.sendObjectResponse("Budget Exists", existingBudget);
  },

  async AccountChecker({ account, code }) {
    const existingAccount = await BalanceRepo.getBalance({
      filter: { code: account || code },
    });
    if (!existingAccount) throw new HttpException("Account not found", HttpStatus.NOT_FOUND);

    return responseUtils.sendObjectResponse("Account Exists", existingAccount);
  },

  /**
   * * Here we are checking if a vendor exists
   * @param { string } code vendors code
   */
  async vendorChecker({ code }) {
    const existingVendor = await VendorRepo.getVendor({
      queryParams: { code },
    });
    if (!existingVendor) throw new NotFoundError("Vendor");

    return responseUtils.sendObjectResponse("Vendor Exists", existingVendor);
  },

  /**
   * * Here we are checking if a category exists
   * @param { string } code category code
   */
  async categoryChecker({ code }) {
    const existingCategory = await CategoryRepo.getCategory({
      queryParams: { code },
    });
    if (!existingCategory) throw new NotFoundError("Category");

    return responseUtils.sendObjectResponse("Category Exists", existingCategory);
  },

  async DocumentChecker({ document_code, reference, type, table_type, table_id, documents }, all) {
    const {
      bvn,
      rcNumber,
      incorporationCertificate,
      shareholdersDocument,
      idType,
      utilityBill,
      certificateOfTrustees,
      scumlCertificate,
      cacITForm1,
      bnNumber,
      memorandumOfAssociation,
      taxIdentificationNumber,
      cacForm3,
      cacForm2,
      cacForm7,
      cacForm1,
      cacStatusReport,
    } = documents;

    const documentQuery = {
      ...(document_code && { code: document_code }),
      ...(reference && { reference }),
      ...(type && { type }),
      ...(table_type && { table_type }),
      ...(table_id && { table_id }),
    };

    let existingDocument;
    if (!all)
      existingDocument = await DocumentRepo.getOneDocument({
        queryParams: documentQuery,
      });

    if (all) {
      existingDocument = Sanitizer.sanitizeArray(
        await DocumentRepo.getAllDocuments({
          queryParams: documentQuery,
          notRejected: false,
          selectAttribute: false,
        })
      );
    }

    if (!existingDocument) throw new HttpException("Document not found", HttpStatus.NOT_FOUND);

    let checks = [];
    if (all && reference) {
      const typeChecks = {
        ...(bvn && { bvn: "bvn" }),
        ...(rcNumber && { rcNumber: "rcNumber" }),
        ...(incorporationCertificate && { incorp_C: "incorp_C" }),
        ...(shareholdersDocument && { s_Doc: "s_Doc" }),
        ...(idType && { idType }),
        ...(utilityBill && { utility: "utility-Bill" }),
        ...(scumlCertificate && { slc: "Scum_L_C" }),
        ...(certificateOfTrustees && { cot: "C_of_T" }),
        ...(cacITForm1 && { cit1: "cacITForm1" }),
        ...(bnNumber && { bnNumber: "bnNumber" }),
        ...(memorandumOfAssociation && { moa: "moa" }),
        ...(taxIdentificationNumber && { tin: "tin" }),
        ...(cacForm3 && { cacForm3: "cacForm3" }),
        ...(cacForm2 && { cacForm2: "cacForm2" }),
        ...(cacForm7 && { cacForm7: "cacForm7" }),
        ...(cacForm1 && { cacForm1: "cacForm1" }),
        ...(cacStatusReport && { cacStatusReport: "cacStatusReport" }),
      };
      checks = await Promise.all([
        bvn && existingDocument.some((oneDoc) => oneDoc.type === typeChecks.bvn),
        rcNumber && existingDocument.some((oneDoc) => oneDoc.type === typeChecks.rcNumber),
        idType && existingDocument.some((oneDoc) => oneDoc.type === typeChecks.idType),
        shareholdersDocument && existingDocument.some((oneDoc) => oneDoc.type === typeChecks.s_Doc),
        incorporationCertificate && existingDocument.some((oneDoc) => oneDoc.type === typeChecks.incorp_C),
        utilityBill && existingDocument.some((oneDoc) => oneDoc.type === typeChecks.utility),
        scumlCertificate && existingDocument.some((oneDoc) => oneDoc.type === typeChecks.slc),
        certificateOfTrustees && existingDocument.some((oneDoc) => oneDoc.type === typeChecks.cot),
        cacITForm1 && existingDocument.some((oneDoc) => oneDoc.type === typeChecks.cit1),
        bnNumber && existingDocument.some((oneDoc) => oneDoc.type === typeChecks.bnNumber),
        memorandumOfAssociation && existingDocument.some((oneDoc) => oneDoc.type === typeChecks.moa),
        taxIdentificationNumber && existingDocument.some((oneDoc) => oneDoc.type === typeChecks.tin),
        cacForm3 && existingDocument.some((oneDoc) => oneDoc.type === typeChecks.cacForm3),
        cacForm2 && existingDocument.some((oneDoc) => oneDoc.type === typeChecks.cacForm2),
        cacForm7 && existingDocument.some((oneDoc) => oneDoc.type === typeChecks.cacForm7),
        cacForm1 && existingDocument.some((oneDoc) => oneDoc.type === typeChecks.cacForm1),
        cacStatusReport && existingDocument.some((oneDoc) => oneDoc.type === typeChecks.cacStatusReport),
      ]);
    }

    const [
      bvnExist,
      rcNumberExist,
      idTypeExist,
      s_DocExist,
      incorp_CExist,
      utilityExist,
      slcExist,
      cotExist,
      cit1Exist,
      bnNumberExist,
      moaExist,
      tinExist,
      cacForm3Exist,
      cacForm2Exist,
      cacForm7Exist,
      cacForm1Exist,
      cacStatusReportExist,
    ] = checks;
    return responseUtils.sendObjectResponse("Document Exists", {
      bvnExist,
      rcNumberExist,
      idTypeExist,
      s_DocExist,
      incorp_CExist,
      utilityExist,
      slcExist,
      cotExist,
      cit1Exist,
      bnNumberExist,
      moaExist,
      tinExist,
      cacForm3Exist,
      cacForm2Exist,
      cacForm7Exist,
      cacForm1Exist,
      cacStatusReportExist,
      checks: checks.includes(false),
      response: existingDocument,
    });
  },

  async DocumentReviewer({ reference, oldDocument, newDocument }) {
    let dbTransaction;
    try {
      dbTransaction = await sequelize.transaction();
      let documentQuery;
      documentQuery = { reference, response: "Rejected", code: oldDocument };

      const gottenDocument = await DocumentRepo.getOneDocument({
        queryParams: documentQuery,
        selectOptions: ["type", "number", "url", "processor", "table_type", "table_id"],
        transaction: dbTransaction,
      });

      if (!gottenDocument) throw new HttpException("Rejected Document not found", HttpStatus.NOT_FOUND);

      await DocumentRepo.updateADocument({
        queryParams: documentQuery,
        updateFields: {
          status: STATUSES.REJECTED,
          response: "Rejected:Concluded",
          expiryDate: new Date(),
        },
        transaction: dbTransaction,
      });

      const { type: oldType, processor, table_type, table_id } = gottenDocument;
      const { type: newType, number, url } = newDocument;

      let type = newType;
      if (!type) type = oldType;

      let existingBvn;
      if (number && type === "bvn")
        existingBvn = await DocumentRepo.getOneDocument({
          queryParams: { type: "bvn", number },
        });
      if (existingBvn) throw new HttpException("BVN exists already", HttpStatus.NOT_ACCEPTABLE);

      await DocumentRepo.createADocument({
        queryParams: {
          ...(type && { type }),
          ...(!type && { type: oldType }),
          ...(number && { number }),
          ...(url && { url }),
          ...(processor && { processor }),
          ...(table_type && { table_type }),
          ...(table_id && { table_id }),
          status: STATUSES.UNVERIFIED,
          reference,
        },
        transaction: dbTransaction,
      });

      await dbTransaction.commit();
      return responseUtils.sendObjectResponse("Documents Submitted");
    } catch (error) {
      await dbTransaction.rollback();
      throw error;
    }
  },

  /**
   * Used to create a phone number
   * Requirements
   * @type {
   * payload: {
   *      countryCode: string;
   *      localFormat: string;
   *  }
   * }
   * @returns {
   *       id: number;
   *       code: string;
   *       countryCode: string;
   *       localFormat: string;
   *       internationalFormat: string;
   *  }
   * <AUTHOR> - created
   */
  async findOrCreatePhoneNumber(payload) {
    if (!payload) return null;
    const { error } = HelperValidator.phone.validate(payload);
    if (error) throw new ValidationError(error.message);

    const { countryCode, localFormat } = payload;
    const phoneNumber = await PhoneNumberRepo.getOnePhoneNumber({
      queryParams: payload,
    });
    if (phoneNumber) return phoneNumber;

    const { number: internationalFormat } = parsePhoneNumber(localFormat, {
      defaultCallingCode: countryCode,
    });
    if (!isValidPhoneNumber(internationalFormat)) throw new ValidationError(`Invalid phone number`);
    const createdPhoneNumber = await PhoneNumberRepo.createAPhoneNumber({
      queryParams: {
        countryCode,
        localFormat,
        internationalFormat: internationalFormat.replace("+", ""),
      },
    });
    return createdPhoneNumber;
  },

  async findOrCreateAddress(addressQuery, update) {
    if (!addressQuery) return null;
    const { error } = HelperValidator.address.validate(addressQuery);
    if (error) throw new ValidationError(error.message);

    const { street, state, city, country, user, postalCode } = addressQuery;
    const selectOptions = ["street", "city", "state", "country", "countryIso", "postalCode"];

    const { data: foundCountry } = await Service.CountryChecker({ code: country });
    const existingAddress = await AddressRepo.getOneAddress({
      queryParams: { street, state, city },
      selectOptions,
    });
    if (existingAddress) {
      if (!update) return existingAddress;
      await AddressRepo.updateAnAddress({
        queryParams: { code: existingAddress.code },
        updateFields: {
          state,
          city,
          street,
          country: foundCountry.name,
          countryIso: foundCountry.iso_code,
          ...(postalCode && { postalCode }),
        },
      });
      return AddressRepo.getOneAddress({
        queryParams: { street, state, city },
        selectOptions,
      });
    }
    return AddressRepo.createAnAddress({
      queryParams: {
        country: foundCountry.name,
        state,
        city,
        street,
        address: user,
        countryIso: foundCountry.iso_code,
        ...{ postalCode },
      },
    });
  },

  async findOrCreateBudgetPolicy({ policy, budget }, checker) {
    const { error } = HelperValidator.budgetPolicy.validate({ policy, budget });
    if (error) throw new ValidationError(error.message);

    const budgetPolicy = await BudgetPolicyRepo.getBudgetPolicy({
      queryParams: {
        policy,
        status: { [Op.notIn]: [STATUSES.DELETED] },
        budget,
      },
    });
    if (budgetPolicy) {
      if (checker) throw new ValidationError("Budget Policy already exists");
      return budgetPolicy;
    }

    const createBudgetPolicy = await BudgetPolicyRepo.createBudgetPolicy({
      queryParams: {
        policy,
        status: STATUSES.ACTIVE,
        budget: budget,
      },
    });
    return createBudgetPolicy;
  },

  async findOrCreatePolicyRestriction({ policy, policy_type, company }, checker) {
    const { error } = HelperValidator.policyRestriction.validate({ policy, policy_type });
    if (error) throw new ValidationError(error.message);

    const policyRestriction = await PolicyRestriction.findOne({
      where: {
        policy,
        policy_type,
        status: {
          [Op.ne]: STATUSES.DELETED,
        },
        company,
      },
    });

    if (policyRestriction) {
      if (checker) throw new ValidationError("Policy type already linked to policy");
      return policyRestriction;
    }

    const createPolicyRestriction = await PolicyRestriction.create({
      policy,
      status: STATUSES.ACTIVE,
      policy_type,
      company,
    });
    return createPolicyRestriction;
  },

  encrypt(text) {
    const iv = crypto.randomBytes(16);
    const key = crypto.createHash("sha256").update(String(ENCRYPTION_KEY)).digest("base64").substr(0, 32);
    const cipher = crypto.createCipheriv(algorithm, key, iv);
    let encrypted = cipher.update(text);
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    return { iv: iv.toString("hex"), encryptedData: encrypted.toString("hex") };
  },

  decrypt(text) {
    const iv = Buffer.from(text.iv, "hex");
    const key = crypto.createHash("sha256").update(String(ENCRYPTION_KEY)).digest("base64").substr(0, 32);
    const encryptedText = Buffer.from(text.encryptedData, "hex");
    const decipher = crypto.createDecipheriv(algorithm, key, iv);
    let decrypted = decipher.update(encryptedText);
    decrypted = Buffer.concat([decrypted, decipher.final()]);
    return decrypted.toString();
  },

  async formatTeamMembers({ users, role, company }) {
    const systemRole = role && SettingsService.get("SYSTEM_ROLES");
    const teamMembers = await UserRepo.getAllUsers({
      queryParams: { company, code: users },
    });
    if (teamMembers.length !== users.length) throw new ValidationError(`Invalid team manager sent`);
    return teamMembers.map((user) => {
      return {
        company,
        ...(role && { role: systemRole[role] }),
        user: user.id,
      };
    });
  },

  formatMembersAndManagers(teamMembers) {
    const managers = [];
    const members = [];
    Array.from(teamMembers).forEach((teamMember) => {
      if (teamMember.role === "Manager") managers.push(teamMember);
      if (teamMember.role === "Employee") members.push(teamMember);
    });
    return { members, managers };
  },

  async percentageSpent(array, totalSpent) {
    // eslint-disable-next-line no-param-reassign
    totalSpent = Sanitizer.jsonify(totalSpent[0]);
    if (Array.isArray(array))
      return array.map((item) => {
        const { transactionVolume, transactionCount, ...rest } = Sanitizer.jsonify(item);
        return {
          ...rest,
          spentByBeneficiary: Utils.percentCalculator(transactionVolume, totalSpent.transactionVolume),
          transactedByBeneficiary: Utils.percentCalculator(transactionCount, totalSpent.transactionCount),
        };
      });

    const { transactionVolume, transactionCount, ...rest } = Sanitizer.jsonify(array);
    return {
      ...rest,
      spentByBeneficiary: Utils.percentCalculator(transactionVolume, totalSpent.transactionVolume),
      transactedByBeneficiary: Utils.percentCalculator(transactionCount, totalSpent.transactionCount),
    };
  },

  async findOrCreateMonoAccountHolder(payload) {
    const { error } = HelperValidator.accountHolder.validate(payload);
    if (error) throw new ValidationError(error.message);

    let accountHolder = await MonoAccountHolderRepo.getAccountHolder({
      queryParams: { holderId: payload.holderId },
    });

    if (accountHolder) return accountHolder;

    accountHolder = await MonoAccountHolderRepo.createAccountHolder({
      payload,
    });

    return accountHolder;
  },

  async formatTeamMembersToRemove({ company, members }) {
    const teamMembers = await UserRepo.getAllUsers({
      queryParams: { company, code: members },
    });
    if (teamMembers.length !== members.length) throw new ValidationError(`Invalid team member sent`);
    return teamMembers.map(({ id }) => id);
  },

  async findOrCreateApproverLevel(data) {
    const { error } = HelperValidator.approverLevel.validate(data);
    if (error) throw new ValidationError(error.message);

    const { threshold, rank, rule, status } = data;
    const foundApproverLevel = await ApproverLevelRepo.getApproverLevel({
      queryParams: { approvers_threshold: threshold, rank, rule, status },
      addApprover: false,
    });
    if (foundApproverLevel) return foundApproverLevel;

    const createdApproverLevel = await await ApproverLevelRepo.createApproverLevel({
      queryParams: {
        approvers_threshold: Number(threshold),
        rule,
        rank,
        status,
      },
    });
    return createdApproverLevel;
  },

  async findTransactionRecipient(payload) {
    const { company } = payload;
    if (String(payload.recipient).startsWith("usr_") || String(payload.recipient).startsWith("vdr_")) {
      // RECIPIENT IS USER CODE
      if (String(payload.recipient).startsWith("usr_")) {
        const { id: recipientId, firstName } = await UserRepo.fetchUser(payload.recipient);
        payload.recipient = recipientId;
        payload.recipient_type = "user";
        payload.recipientName = firstName;
      }

      // RECIPIENT IS VENDOR CODE
      if (String(payload.recipient).startsWith("vdr_")) {
        const { id: recipientId, name } = await VendorRepo.getVendorOrThrowError({ code: payload.recipient });
        payload.recipient = recipientId;
        payload.recipient_type = "vendor";
        payload.recipientName = name;
      }
    } else {
      // CREATE NEW VENDOR
      const { id: recipientId, name } = await VendorRepo.findOrCreateVendor({
        name: payload.recipient,
        company: company.id,
        industry: 1,
      });

      payload.recipient = recipientId;
      payload.recipient_type = "vendor";
      payload.recipientName = name;
    }
    return payload;
  },
  async findTransactionPayer(payload) {
    if (!String(payload.payer).startsWith("usr_")) throw new ValidationError(`Invalid user code`);
    const { id: payerId } = await UserRepo.fetchUser(payload.payer);
    payload.payer = payerId;
    return payload;
  },

  async makeBatchTransactionsSuccessful(batchId) {
    const pendingTransaction = await TransactionRepo.getTransactions({
      queryParams: {
        batch_id: batchId,
        status: { [Op.ne]: STATUSES.DECLINED },
      },
      selectOptions: ["status"],
    });

    const pendingTransactionStatus = Utils.mapAnArray(pendingTransaction, "status");
    const batchTransactionCompleted = pendingTransactionStatus.every((status) => status === STATUSES.SUCCESS);
    if (batchTransactionCompleted)
      await BatchTransactionRepo.updateBatchTransaction({
        queryParams: { id: batchId },
        updateFields: { status: STATUSES.SUCCESS },
      });
  },

  getSingleBank(searchString) {
    const filterString = String(searchString).toLowerCase();
    const foundBank = Banks.find((bank) => String(bank.label).toLowerCase() === filterString);
    if (!foundBank) throw new NotFoundError("Bank");
    return foundBank;
  },

  /**
   *
   * @param {number} lhs - Left Hand Side
   * @param {number} rhs - Right Hand Side
   * @param {string} condition - below, above
   * @returns boolean
   */
  compareValues(lhs, rhs, condition) {
    const comparatorMap = {
      below: "<",
      above: ">",
      lt: "<",
      gt: ">",
      eq: "==",
    };
    return new Function("lhs", "rhs", `return lhs ${comparatorMap[condition]} rhs`)(lhs, rhs);
  },

  deduceRequestTo(fundRequest) {
    if (fundRequest.type === FUND_REQUEST_TYPES.BUDGET) {
      return `Fund new budget`;
    }

    if (fundRequest.type === FUND_REQUEST_TYPES.PAYMENT) {
      return `Pay vendor`;
    }

    if (fundRequest.type === FUND_REQUEST_TYPES.TOP_UP) {
      if (fundRequest.budget) {
        return `Top up budget`;
      }

      if (fundRequest.bankAccount) {
        return `Top up bank account`;
      }

      if (fundRequest.card) {
        return `Top up card`;
      }
    }
  },

  notifyRelevantParties({ payload, user, company, recipient, fundRequest, sourceConfig }) {
    if (!sourceConfig) {
      sourceConfig = {
        subject: `${payload.userFullname} sent a funds request of ${payload.currency}${String(payload.amountRequest).toLocaleString()}`,
        from_name: "Bujeti",
      };
    }

    const notificationPayload = {
      company,
      user_id: recipient.id,
      type: `request`,
      badge: `request`,
      title: `${Utils.toTitle(user.firstName)} requests ${fundRequest.currency}${Utils.formatAmount(fundRequest.amount).toLocaleString()}`,
      message: `${Utils.toTitle(user.firstName)} has made a fund request of ${fundRequest.currency}${Utils.formatAmount(
        fundRequest.amount
      ).toLocaleString()}`,
      body: {
        code: fundRequest.code,
        entity: "FundRequest",
      },
      table: {
        code: fundRequest.code,
        entity: "FundRequest",
      },
      reference_code: fundRequest.code,
      event: "fundRequest",
    };
    NotificationService.saveNotification(notificationPayload);
    payload.firstName = recipient.firstName;
    NotificationService.notifyUser(recipient, "fund-request", payload, sourceConfig);
  },

  buildBudgetOwners(incomingBeneficiaries) {
    const isBeneficiaryObject = typeof incomingBeneficiaries[0] === "object";

    let budgetOwnersMap = {};
    if (isBeneficiaryObject) {
      budgetOwnersMap = incomingBeneficiaries.reduce((budgetOwners, { beneficiary, isBudgetOwner, limit, duration, amount } = {}) => {
        if (beneficiary) budgetOwners[beneficiary] = { isBudgetOwner, limit, duration, amount };
        return budgetOwners;
      }, budgetOwnersMap);
    }
    return { isBeneficiaryObject, budgetOwnersMap };
  },

  async checkBudgetOwnerShip({ isManager, user, throwError = true }, budget) {
    if (!isManager) {
      // budget ownership check
      const isBudgetOwner = await UserBudget.findOne({
        where: {
          user,
          budget: budget.id,
          status: STATUSES.ACTIVE,
          isBudgetOwner: true,
        },
      });

      if (!isBudgetOwner && throwError) throw new ValidationError("You are unauthorized to perform this action on this budget");
      return true;
    }
  },

  async checkIfCanManageAccount({ isManager, user, throwError = true }, bankAccount) {
    if (!isManager) {
      const canManageAccount = await AccountMemberRepo.getMember({
        filter: {
          user,
          bankAccount: bankAccount.id,
          designation: {
            [Op.in]: ["Manager", "Owner"],
          },
          status: STATUSES.ACTIVE,
        },
      });

      if (!canManageAccount && throwError) throw new ValidationError("You are unauthorized to perform this action on this account");
      return !!canManageAccount;
    }
  },

  buildAccountMembers(incomingBeneficiaries) {
    return incomingBeneficiaries.reduce((accountManagers, { beneficiary, designation } = {}) => {
      if (beneficiary) accountManagers[beneficiary] = { designation };
      return accountManagers;
    }, {});
  },

  generateFrequencyQuery(frequency) {
    const { format, addDays, addMonths, addYears, lastDayOfMonth, startOfYear, endOfYear } = dateFns;
    const today = new Date();
    const formattedDate = format(today, "yyyy-MM-dd");

    switch (frequency) {
      case "daily":
        return {
          created_at: {
            [Op.gte]: formattedDate,
            [Op.lt]: format(addDays(today, 1), "yyyy-MM-dd"),
          },
        };
      case "weekly":
        return {
          created_at: {
            [Op.gte]: format(addDays(today, -today.getDay() + 1), "yyyy-MM-dd"),
            [Op.lt]: format(addDays(today, 8 - today.getDay()), "yyyy-MM-dd"),
          },
        };
      case "monthly":
        return {
          created_at: {
            [Op.gte]: format(lastDayOfMonth(addMonths(today, -1)), "yyyy-MM-dd"),
            [Op.lt]: format(addDays(lastDayOfMonth(today), 1), "yyyy-MM-dd"),
          },
        };
      case "yearly":
        return {
          created_at: {
            [Op.gte]: format(startOfYear(addYears(today, -1)), "yyyy-MM-dd"),
            [Op.lt]: format(endOfYear(today), "yyyy-MM-dd"),
          },
        };
      default:
        return {};
    }
  },

  async checkBudgetOwnerLimit({ budget, user, amount }) {
    const userBudget = await UserBudget.findOne({
      where: {
        user,
        budget,
        status: STATUSES.ACTIVE,
        isBudgetOwner: true,
        duration: {
          [Op.ne]: null,
        },
        limit: {
          [Op.ne]: null,
        },
      },
      include: [Budget],
    });
    if (!userBudget) return;

    const query = Service.generateFrequencyQuery(userBudget.duration);

    const result = await BudgetLedger.findAll({
      where: {
        budget,
        amount: {
          [Op.lt]: 0,
        },
        user,
        status: {
          [Op.in]: [STATUSES.PENDING, STATUSES.PROCESSED],
        },
        ...query,
      },
      attributes: [[sequelize.fn("sum", sequelize.col("amount")), "spent"]],
    });

    const total = parseInt(result.spent || 0, 10) + parseInt(amount || 0, 10);
    if (total > parseInt(userBudget.limit || 0, 10)) throw new ValidationError("Spend limit exceeded");
  },

  async isManager(userId) {
    if (!userId) return false;
    const user = await UserRepo.getOneUser({
      queryParams: {
        id: userId,
      },
      selectOptions: ["role"],
    });
    return ["manager", "admin"].includes(user?.role?.toLowerCase());
  },

  computeStatisticsCriteria({ company, currency, budget, balance }) {
    const criteria = { company };

    if (currency) {
      criteria.currency = currency;
    }
    if (budget) {
      criteria.budget = budget;
    }
    if (balance) {
      criteria.balance = balance;
    }
    return criteria;
  },

  groupByCurrency(data) {
    return data.filter(Boolean).reduce((accumulatedValue, currentValue) => {
      if (accumulatedValue[currentValue.currency]) {
        accumulatedValue[currentValue.currency] = {
          amount: Utils.stringToInteger(accumulatedValue[currentValue.currency].amount) + Utils.stringToInteger(currentValue.amount),
          count: Utils.stringToInteger(accumulatedValue[currentValue.currency].count) + Utils.stringToInteger(currentValue.count),
        };
      } else {
        accumulatedValue[currentValue.currency] = {
          amount: Utils.stringToInteger(currentValue.amount),
          count: Utils.stringToInteger(currentValue.count),
          currency: currentValue.currency,
        };
      }
      accumulatedValue.totalCount = Utils.stringToInteger(accumulatedValue.totalCount) + Utils.stringToInteger(currentValue.count);
      return accumulatedValue;
    }, {});
  },

  generateWhereClause(targetDate) {
    const day = getDay(targetDate);
    const month = getMonth(targetDate) + 1;
    const dayOfMonth = getDate(targetDate);
    return literal(`(
        (Schedule.cron_expression LIKE '% * * ${day}') OR
		(Schedule.cron_expression LIKE '% ${dayOfMonth} * ${day}') OR 
		(Schedule.cron_expression LIKE '% ${dayOfMonth} * *') OR 
		(Schedule.cron_expression LIKE '% ${dayOfMonth} */3 *') OR 
		(Schedule.cron_expression LIKE '% ${dayOfMonth} ${month} *') OR 
		(Schedule.cron_expression LIKE '% ${dayOfMonth} ${month} ${day}')
      )`);
  },

  getDateRange(currentDate, days) {
    const targetDate = subDays(currentDate, days);
    return { start: startOfDay(targetDate), end: endOfDay(targetDate) };
  },

  isSuccessfulStatusCode(statusCode) {
    // Check if the status code is in the range 200 to 299, inclusive
    return statusCode >= 200 && statusCode < 300;
  },

  generateDelayInMinutes(attempt, baseDelayMinutes = 1) {
    return baseDelayMinutes * attempt ** 2;
  },

  async validateMccs(codes) {
    const mccs = await MccRepo.getMccs({
      queryParams: {
        code: codes,
        status: STATUSES.ACTIVE,
      },
      selectOptions: ["id"],
    });

    if (mccs.length !== codes.length) {
      throw new ValidationError("Invalid mcc sent");
    }

    return mccs;
  },

  async validateSpendingPolicy(code) {
    // const { "Spending limits": Spending_Limits } = SettingsService.get("POLICY_TYPES");
    const spendingPolicy = await Policy.findOne({
      where: {
        code,
        // [Op.or]: [{ type: Spending_Limits }, sequelize.literal(`\`PolicyTypes\`.\`id\` = ${Spending_Limits}`)],
      },
      attributes: ["id", "frequency", "maxAmount", "minAmount"],
      include: [
        PolicyType,
        {
          model: PolicyType,
          as: "PolicyTypes",
          required: false,
          where: { "$PolicyTypes.PolicyRestriction.status$": { [Op.ne]: STATUSES.DELETED } },
        },
      ],
      subQuery: false,
    });

    if (!spendingPolicy) throw new NotFoundError("Spending limit policy");

    return spendingPolicy;
  },

  async findOrCreateDocument({ document }) {
    const { table_id, table_type, type, number, asset } = document;

    const existingDocs = await DocumentRepo.getOneDocument({
      queryParams: { table_id, table_type, type },
      selectOptions: ["type", "asset", "number", "code", "status"],
    });

    if (!existingDocs) return DocumentRepo.createADocument({ queryParams: document });
    if ((asset && existingDocs.asset === asset) || (number && existingDocs.number === number)) {
      await DocumentRepo.updateADocument({
        queryParams: { id: existingDocs.id },
        updateFields: {
          ...(number && { number }),
          ...(asset && { asset }),
        },
      });
      return existingDocs;
    }

    await DocumentRepo.updateADocument({
      queryParams: { id: existingDocs.id },
      updateFields: { status: STATUSES.INACTIVE },
    });

    return DocumentRepo.createADocument({
      queryParams: document,
    });
  },

  /**
   *
   * @param {*} company The Id of the company
   * @param { Boolean } isApproval Boolean Value if this call is aimed for approval. Complete document check is not required
   */
  async buildCompanyOnboardingSteps({ company, isApproval = false }) {
    const [foundCompany, directors] = await Promise.all([
      CompanyRepo.getCompany({
        queryParams: { id: company },
      }),
      IndividualRepo.getOneIndividual({
        queryParams: { company },
      }),
    ]);

    if (!foundCompany) throw new NotFoundError("Company");

    const steps = {
      businessDetails: {
        completed: false,
        substeps: {
          incorperationDetails: true,
          contactInformation: false,
          businessDocs: false,
        },
      },
      businessOwners: {
        completed: false,
      },
      additionalDetails: {
        completed: false,
      },
    };

    const documents = await DocumentRepo.getCompanyDocuments({ companyCode: foundCompany.code });

    const hasSubmittedAllDocs = documents.every(
      ({ submitted, status }) => submitted && ["pending", "approved", "verifying", "verified"].includes(status)
    );

    const hasRcOrBn = documents.length && documents.find((document) => ["rcNumber", "bnNumber"].includes(document.value));
    const { description, size, website, industry, name, businessType, dateOfRegistration, contactEmail, phoneNumber, registeredAddress } =
      foundCompany;

    steps.businessDetails.substeps.incorperationDetails = !!name && !!businessType && !!dateOfRegistration && !!hasRcOrBn;
    steps.businessDetails.substeps.contactInformation = !!contactEmail && !!phoneNumber && !!registeredAddress;
    steps.businessDetails.substeps.businessDocs = isApproval ?? hasSubmittedAllDocs;

    steps.businessDetails.completed = Object.keys(steps.businessDetails.substeps).every((key) => steps.businessDetails.substeps[key]);

    steps.businessOwners.completed = !!directors;

    steps.additionalDetails.completed = !!description && !!size && !!website && !!industry;

    return steps;
  },
  /**
   *
   * @param {*} balance The Balance ID
   */
  async isDirectDebitAccount({ balance }) {
    if (!balance) return null;

    const criteria = {};

    if (String(balance).startsWith("blc_")) criteria.code = balance;
    else criteria.id = balance;

    const foundBalance = await BalanceRepo.getBalance({
      filter: criteria,
      includeAccount: true,
    });
    if (!foundBalance) throw new NotFoundError("Balance");
    const { BankAccount: foundBankAccount } = foundBalance;

    const existingMandate = await Mandate.findOne({
      where: {
        bankAccount: foundBankAccount.id,
        status: "granted",
        isReadyForDebit: true,
      },
    });

    return responseUtils.sendObjectResponse("Balance type fetched", {
      bankAccount: foundBankAccount,
      isDirectDebit: !!existingMandate,
      existingMandate,
    });
  },

  async deduceAccount({ method, budget, company, balance, directDebit }) {
    let account;
    switch (METHODS[method.toUpperCase()]) {
      case METHODS.BUDGET:
        {
          if (!budget) throw new ValidationError("Please specify budget");
          const foundBudget = await BudgetRepo.getOneBudget({ queryParams: { code: budget, company } });
          if (!foundBudget) throw new NotFoundError("Budget");
          account = foundBudget.id;
        }
        break;

      case METHODS.BALANCE:
        {
          if (!balance) throw new ValidationError("Please specify balance");
          const foundBalance = await BalanceRepo.getBalance({
            filter: {
              code: balance,
              company,
            },
          });
          if (!foundBalance) throw new NotFoundError("Balance");
          account = foundBalance.id;
        }
        break;

      case METHODS.DIRECTDEBIT:
        {
          if (!directDebit.bankAccount) throw new ValidationError("Please specify direct debit source");
          const foundBankAccount = await BankAccountRepo.getOneBankAccount({
            queryParams: {
              code: directDebit.bankAccount,
            },
          });

          if (!foundBankAccount) throw new NotFoundError("Bank account");

          // verify active mandate
          const existingMandate = await Mandate.findOne({
            where: {
              bankAccount: foundBankAccount.id,
              status: "granted",
            },
          });
          if (!existingMandate) throw new ValidationError("No active mandate found");
          account = foundBankAccount.id;
        }
        break;

      default: {
        account = null;
      }
    }
    return account;
  },

  getActualTransactionAmount(transaction) {
    if (!transaction) return null;

    const totalAmount = parseInt(transaction.amount, 10);

    return totalAmount;
  },

  addDateToBaseQuery({ bindings, date, table, timestamp, direction, column = "created_at" }) {
    bindings.push(`${date} ${timestamp}`);
    return ` AND ${table}.${column} ${direction === "start" ? ">=" : "<="} $${bindings.length}`;
  },

  formatBreakDown(data) {
    return data?.map((element) => {
      const { month, year, weekday } = element;

      return {
        amount: +element.totalAmount,
        created_at: `${weekday || month} ${year || ""}`,
      };
    });
  },

  groupAmountsByCurrency(data) {
    return data?.reduce((collection, element) => {
      const { currency, amount = 0, balance = 0 } = element;
      if (!collection[currency]) collection[currency] = 0;
      collection[currency] += parseInt(amount || balance, 10);
      return collection;
    }, {});
  },

  mergedTotalsByCurrency(data) {
    return data.reduce((acc, curr) => {
      // Loop through each [currency, total] pair using Object.entries()
      Object.entries(curr).forEach(([currency, total]) => {
        // If the currency already exists in acc, sum the totals
        if (acc[currency]) {
          acc[currency] += total;
        } else {
          // If the currency doesn't exist yet, add it to acc
          acc[currency] = total;
        }
      });
      return acc;
    }, {});
  },

  async deduceCategory(rest) {
    if (rest.category) {
      const foundCategory = await CategoryRepo.getCategory({ queryParams: { code: rest.category } });
      if (!foundCategory) throw new NotFoundError("Category");
      rest.category = foundCategory.id;
    }
  },

  async deduceVendor(rest) {
    if (rest.vendor) {
      const foundVendor = await VendorRepo.getVendor({
        queryParams: {
          code: rest.vendor,
        },
      });

      if (!foundVendor) throw new NotFoundError("Vendor");
      rest.vendor = foundVendor.id;
    }
  },

  /**
   *
   * @param {*} transaction // The Transaction/Transfer Object
   */
  checkTransactionStatusForRetrial(transaction) {
    const isTransfer = transaction?.code.startsWith("trf");
    // Checks for transfer
    if (isTransfer) {
      return [STATUSES.FAILED, STATUSES.PENDING].includes(transaction.status);
    }

    // Check transaction
    return [STATUSES.CANCELLED, STATUSES.FAILED].includes(transaction.status);
  },

  returnRelevantRequests(tableName, loggedInUser, isExclusiveToUser = false) {
    const Or = [
      Sequelize.literal(`
      \`${tableName}->ApprovalStages->ApproverLevel\`.\`rank\` IN (
        SELECT \`rank\`
        FROM \`ApproverLevels\` levels
        INNER JOIN Approvers ON levels.id = Approvers.approver_level AND Approvers.status = '${STATUSES.ACTIVE}'
        WHERE \`levels\`.\`rule\` = \`${tableName}->ApprovalRule\`.\`id\` AND \`levels\`.\`status\` = '${STATUSES.ACTIVE}'
          AND \`${tableName}->ApprovalStages\`.\`status\` = '${STATUSES.PENDING}'
          AND Approvers.id NOT IN (
            SELECT
              approver FROM Approvals aprvl
            WHERE
              aprvl.request = \`${tableName}->ApprovalStages\`.\`request\`
          )
      ) 
      AND \`${tableName}->ApprovalStages->ApproverLevel->Approvers\`.\`user\` = '${loggedInUser}'
      AND \`${tableName}->ApprovalStages->ApproverLevel->Approvers\`.\`status\` = '${STATUSES.ACTIVE}'
    `),
    ];

    if (!isExclusiveToUser) {
      Or.push({
        [`$${tableName}.id$`]: { [Sequelize.Op.is]: null },
      });
    }

    return {
      [Sequelize.Op.or]: Or,
    };
  },
  async prepareToCharge({
    method,
    methodId,
    amount,
    totalAmount,
    payload,
    company,
    BankAccount,
    narration,
    BudgetService,
    BalanceService,
    BankService,
  }) {
    switch (Number(method)) {
      case METHODS.BUDGET:
        {
          if (!methodId) throw new ValidationError("Please specify budget");
          const foundBudget = await BudgetRepo.getBudget({ [Op.or]: [{ id: methodId }, { code: methodId }], company: company.id });
          if (!foundBudget) throw new NotFoundError("Budget");

          await BudgetService.canBudgetHandleTransaction({ budget: foundBudget.id, amount, totalAmount });

          const budgetAccount = await BudgetAccountRepo.getBudgetAccount({
            filter: {
              budget: foundBudget.id,
            },
          });
          payload.budget = foundBudget.id;
          payload.currency = foundBudget.currency;
          payload.useBookTransfer = budgetAccount?.provider === CARD_ISSUER.Anchor;
          payload.senderId = budgetAccount?.externalIdentifier;
          payload.senderType = Utils.getAccountType(payload.senderId);
        }
        break;
      case METHODS.BALANCE:
        {
          if (!methodId) throw new ValidationError("Please specify balance");

          const foundBalance = await BalanceRepo.getBalance({
            filter: {
              [Op.or]: [{ code: methodId }, { id: methodId }],
            },
            includeAccount: true,
          });
          if (!foundBalance) throw new NotFoundError("Balance");

          await BalanceService.canBalanceHandleTransaction({ balance: foundBalance.id, amount, totalAmount, company: company.id });
          payload.balance = foundBalance.id;
          payload.currency = foundBalance.currency;
          payload.useBookTransfer = foundBalance.BankAccount?.issuer === CARD_ISSUER.Anchor;
          payload.senderId = foundBalance.BankAccount?.externalBankAccountId || foundBalance.BankAccount?.externalIdentifier;
          payload.senderType = Utils.getAccountType(payload.senderId);
        }
        break;
      case METHODS.DIRECTDEBIT:
        {
          if (!methodId) throw new ValidationError("Please specify direct debit source");

          const {
            data: { existingMandate },
          } = await BankService.canAccountProcessPayment({ directDebit: { bankAccount: methodId }, amount });

          const { number: accountNumber, bankCode, currency } = BankAccount;

          const createdDirectDebit = await DirectDebit.create({
            mandate: existingMandate.id,
            company: company.id,
            amount,
            beneficiaryAccountNumber: accountNumber,
            beneficiaryBankCode: bankCode,
            narration,
            status: STATUSES.PENDING,
            reference: Utils.generateRandomString(14),
          });

          payload.directDebitId = createdDirectDebit.id;
          payload.currency = currency;
        }
        break;
      default: {
        return null;
      }
    }
    return null;
  },
  async prepareBujetiAccount(payload = {}) {
    const accounts = SettingsService.get("subscriptionCollectionAccounts");

    const specificAccount = accounts[payload.currency];

    if (!specificAccount) throw new ValidationError("Unable to complete billing");

    const foundAccount = await BankAccountRepo.getOneBankAccount({
      queryParams: {
        number: specificAccount.number,
        ownerType: "company",
      },
      selectOptions: ["id", "number", "bankCode", "bankName", "owner", "ownerType", "currency", "externalBankAccountId"],
    });

    if (!foundAccount) throw new ValidationError("Unable to complete billing due to missing account");

    payload.bankAccountId = foundAccount.id;
    payload.externalBankAccountId = foundAccount.externalBankAccountId;

    const isDirectDebit = !!payload.directDebitId;
    return { isDirectDebit, foundAccount };
  },

  async createTransactionByType(payload, type) {
    return TransactionRepo.createTransaction({
      data: {
        amount: payload.amount,
        description: payload.description,
        payer: payload.payer,
        budget: payload.budget,
        company: payload.company,
        currency: payload.currency,
        bank_account: payload.bankAccountId,
        status: STATUSES.PENDING,
        narration: payload.narration,
        recipient: payload.recipient,
        recipient_type: payload.recipientType,
        category: payload.category,
        processor_fee: payload.cbnFee,
        bujeti_fee: payload.bujetiFee,
        team: payload.team,
        balance: payload.balance,
        type,
        directDebitId: payload.directDebitId,
      },
    });
  },

  async createTransfer(payload) {
    return TransferRepo.createTransfer({
      data: {
        amount: -1 * payload.amount,
        currency: payload.currency,
        balance: payload.balance,
        description: payload.description,
        company: payload.company,
        status: STATUSES.PENDING,
        reference: Utils.generateRandomString(14).toLowerCase(),
        processor_fee: 0,
        bujeti_fee: 0,
        narration: payload.narration,
        budget: payload.budget,
      },
    });
  },
  async chargeForAdditionalUsers({
    additionalUsersCost,
    company,
    paymentPlan,
    additionalUsersCount,
    user,
    method,
    budget,
    balance,
    directDebit,
    BillingService,
    BudgetService,
    BalanceService,
    BankService,
    ApprovalService,
  }) {
    if (!additionalUsersCost || !additionalUsersCount) return null;

    const canUseCompanyCredit = company?.credits >= additionalUsersCost;

    if (canUseCompanyCredit) {
      const creditsLeft = Utils.money(company.credits).minus(additionalUsersCost).toNumber();
      await CompanyRepo.updateACompany({
        queryParams: {
          id: company.id,
        },
        updateFields: {
          credits: creditsLeft,
        },
      });
      return { usedCompanyCredit: true };
    }

    if (!method) throw new ValidationError("please specify method");

    const methodId = budget || balance || directDebit?.bankAccount;

    if (!methodId) throw new ValidationError("please specify balance or balance");

    const vat = Utils.money(additionalUsersCost).times(0.075).toNumber();
    const computedAmount = Utils.money(additionalUsersCost).plus(vat).toNumber();

    const narration = `${company.name} - Payment of ${paymentPlan.currency}${(
      parseInt(computedAmount, 10) / 100
    ).toLocaleString()} for extra user (${additionalUsersCount}) on subscription plan (${paymentPlan.name})`;

    const payload = {
      amount: computedAmount,
      description: narration,
      narration,
      payer: user.id,
      company: company.id,
      status: STATUSES.PENDING,
      cbnFee: 0,
      bujetiFee: 0,
      currency: paymentPlan.currency || "NGN",
    };

    const deducedMethod = METHODS[method.toUpperCase()];
    const isDirectDebit = deducedMethod === METHODS.DIRECTDEBIT;

    const { cbnFee, bujetiFee } = await BillingService.computeFees({
      amount: payload.amount,
      companyCode: company.code,
      currency: payload.currency,
      plan: Utils.parseJSON(paymentPlan?.configuration),
      isDirectDebit,
    });

    payload.cbnFee = cbnFee || 0;
    payload.bujetiFee = bujetiFee || 0;

    const totalAmount = Utils.money(computedAmount).plus(payload.cbnFee).plus(payload.bujetiFee).toNumber();
    const { foundAccount } = await Service.prepareBujetiAccount(payload);

    await Service.prepareToCharge({
      method: deducedMethod,
      methodId,
      amount: payload.amount,
      totalAmount,
      payload,
      company,
      BankAccount: foundAccount,
      narration,
      BudgetService,
      BalanceService,
      BankService,
    });
    let createdTransaction;
    let createdTransfer;

    if (payload.useBookTransfer) {
      const { payment } = SettingsService.get("providers");
      const providerToUse = payment[company] || payment.defaultProvider;

      createdTransfer = await Service.createTransfer(payload);

      const bookTransferPayload = {
        recipientId: payload.externalBankAccountId,
        recipientType: "DepositAccount",
        senderId: payload.senderId,
        senderType: payload.senderType,
        currency: payload.currency,
        amount: Math.abs(payload.amount),
        reason: payload.description,
        reference: createdTransfer.reference,
        company,
        paidBy: payload.payer,
        transfer: createdTransfer.code,
        providerToUse,
      };

      const SQSPayload = {
        data: bookTransferPayload,
        id: Utils.generateRandomString(17),
        path: `/transfers/bookTransfer/${createdTransfer.code}/process`,
        key: process.env.INTRA_SERVICE_TOKEN,
      };

      QueueService.addDelayedJob({}, SQSPayload, `BookTransferReference:${bookTransferPayload.reference}`, 5);
    } else {
      createdTransaction = await Service.createTransactionByType(payload, TRANSACTION_TYPES.SUBSCRIPTION_ADDONS);

      createdTransaction.status = STATUSES.PROCESSING;
      await createdTransaction.save();

      ApprovalService.addPaymentToQueue(createdTransaction.code);
    }

    return { transaction: createdTransaction, transfer: createdTransfer };
  },

  async createBillingAddon({ subscription, entity, entityIds = [], transaction, company, user, creditsUsed, status, transfer }) {
    const bulkPayload = entityIds.map(({ entityId, entityStatus } = {}) => {
      return {
        transaction: transaction?.id,
        transfer: transfer?.id,
        company: company.id,
        subscription: subscription.id,
        entity,
        entityId,
        user: user.id,
        creditsUsed,
        status: entityStatus || status,
      };
    });

    return BillingAddonRepo.bulkCreate({ data: bulkPayload });
  },

  groupSummaryByCurrency(data = {}) {
    const summary = {
      NGN: {
        total: 0,
        totalPending: 0,
        totalApproved: 0,
      },
    };
    return data.reduce((prev, current) => {
      const { currency, total, totalPending, totalApproved } = current;
      if (!prev[currency]) prev[currency] = { total: Number(total), totalPending: Number(totalPending), totalApproved: Number(totalApproved) };
      else {
        prev[currency] = { total: Number(total), totalPending: Number(totalPending), totalApproved: Number(totalApproved) };
      }
      return prev;
    }, summary);
  },

  async paymentPlanAliaser(paymentPlan) {
    if (!paymentPlan) return paymentPlan;

    const legacyNewPlanMap = {
      [PAYMENT_PLANS.START]: PAYMENT_PLANS.BASIC,
      [PAYMENT_PLANS.GROWTH]: PAYMENT_PLANS.PRO,
      [PAYMENT_PLANS.SCALE]: PAYMENT_PLANS.ENTERPRISE,
    };

    const isLegacyPlan = legacyNewPlanMap[paymentPlan.name];

    if (!isLegacyPlan) return paymentPlan;

    return PaymentPlan.findOne({
      where: {
        name: legacyNewPlanMap[paymentPlan.name],
      },
    });
  },

  async calculateSubscriptionAmount({ plan, billingPeriod, company, existingSubscription, existingPlan }) {
    const serviceConfig = Utils.parseJSON(plan.configuration || {});
    const numberOfMonths = {
      monthly: 1,
      annually: 12,
    };

    const userCount = await UserRepo.count({
      company,
      status: {
        [Op.ne]: STATUSES.DELETED,
      },
    });

    let isLegacyPlanUser = false;

    if (existingPlan) {
      const isStartPlanUser = existingPlan.name === PAYMENT_PLANS.START;
      isLegacyPlanUser = Utils.isNotOnNewPlan(existingPlan) && !isStartPlanUser;
    }

    const planCurrency = plan.currency || "NGN";
    const freeUsers = serviceConfig.userManagement?.freeUsers || 0;
    const additionalUserCost = isLegacyPlanUser ? 0 : serviceConfig.userManagement?.additionalUsers?.[planCurrency] || 0;
    const extraSeats = userCount - freeUsers;

    const totalExtraUsersCost = userCount > freeUsers ? extraSeats * additionalUserCost : 0;

    let computedPlanAmount = Utils.money(plan.amount).plus(totalExtraUsersCost).times(numberOfMonths[billingPeriod]).toNumber();

    if (billingPeriod === BILLING_PERIODS.ANNUALLY) {
      const settings = SettingsService.get("annualSubscriptionDiscount");

      if (settings) {
        const foundDiscount = settings[plan.company] || settings.default || 0;
        const discount = Utils.money(foundDiscount / 100)
          .times(computedPlanAmount)
          .toNumber();
        computedPlanAmount = Utils.money(computedPlanAmount).minus(discount).toNumber();
      }
    }

    if (existingSubscription) {
      const [billingHistory] = existingSubscription?.BillingHistories || [];
      const dueDate = billingHistory?.dueDate;

      const amountWithoutVAT = await BillingHistory.sum("amount", {
        where: {
          company,
          status: STATUSES.PAID,
          plan: existingSubscription.plan,
          subscription: existingSubscription.id,
        },
      });
      const totalCostsIncurred = Utils.money(amountWithoutVAT).div(1.075).toNumber();

      const basicProratedCost = Utils.calculateProratedCost(totalCostsIncurred, dueDate);

      if (basicProratedCost <= computedPlanAmount) {
        computedPlanAmount = Utils.money(computedPlanAmount).minus(basicProratedCost).toNumber();
      } else {
        // give company difference as credits
        const foundCompany = await CompanyRepo.getOneCompany({ queryParams: { id: company }, selectOptions: ["credits"] });
        const differenceOwed = Utils.money(basicProratedCost).minus(computedPlanAmount).toNumber();
        const creditsLeft = Utils.money(foundCompany.credits).plus(differenceOwed).toNumber();

        await CompanyRepo.updateACompany({
          queryParams: {
            id: foundCompany.id,
          },
          updateFields: {
            credits: creditsLeft,
          },
        });
        computedPlanAmount = 0;
      }
    }

    const vat = Utils.money(7.5 / 100)
      .times(computedPlanAmount)
      .toNumber();
    const subscriptionAmount = Utils.money(computedPlanAmount).plus(vat).toNumber();

    return subscriptionAmount;
  },

  async getSourceDetails(payload) {
    const { code } = payload;
    const sourceDetails = {};
    if (!code) throw new ValidationError("Please specify payment source code");
    if (String(code).startsWith("bdg")) {
      const foundBudget = await BudgetRepo.getBudget({ code }, true);
      if (!foundBudget) throw new NotFoundError("Budget");
      const { BudgetAccount: { externalIdentifier = null, type } = {} } = foundBudget;
      if (!externalIdentifier) throw new NotFoundError("Budget Account");
      sourceDetails.externalIdentifier = externalIdentifier;
      sourceDetails.type = type;
    } else {
      const foundBalance = await BalanceRepo.getBalance({
        filter: { code },
        includeAccount: true,
      });
      if (!foundBalance) throw new NotFoundError("Balance");
      const { BankAccount: { externalBankAccountId = null } = {} } = foundBalance;
      if (!externalBankAccountId) throw new NotFoundError("Bank Account");
      sourceDetails.externalIdentifier = externalBankAccountId;
      sourceDetails.type = Utils.getAccountType(externalBankAccountId);
    }

    return sourceDetails;
  },

  async updateRecipientDetails({ recipient, recipientType, payload }) {
    switch (recipientType) {
      case TRANSACTION_RECIPIENT_TYPES.VENDOR:
        await VendorRepo.updateVendor({
          queryParams: { id: recipient },
          updateFields: { ...payload },
        });
        break;
      case TRANSACTION_RECIPIENT_TYPES.CUSTOMER:
        await CustomerRepo.updateCustomer({
          queryParams: { id: recipient },
          updateFields: { ...payload },
        });
        break;

      default:
        break;
    }
  },
  getProvider(owner, product) {
    const providers = SettingsService.get("providers");
    if (!providers[product]) throw new ValidationError("Invalid product passed");
    const provider = providers[product][owner] || providers[product].defaultProvider;
    return provider;
  },

  handleCategorySpent(transaction) {
    if (transaction.category) {
      CategoryRepo.increaseCategorySpent({
        filter: {
          id: transaction.category,
        },
        amount: transaction.amount,
      });
    }
  },

  async processBillingViaQueue({ reason, transaction, company, transfer, status }) {
    if (reason === "billing") {
      const SQSPayload = {
        id: `finalize_billing:${transaction ? transaction.id : transfer.id}`,
        idempotencyKey: `finalize_billing:${transaction ? transaction.id : transfer.id}`,
        path: `/billing/finalize`,
        key: process.env.INTRA_SERVICE_TOKEN,
        company,
        transaction: transaction?.id,
        transfer: transfer?.id,
        status,
      };
      await QueueService.addDelayedJob({}, SQSPayload, `finalize_billing:${transaction ? transaction.id : transfer.id}`, 10);
    }
  },

  async finalizeCardRequestCharge({ reason, transaction, transfer, company }) {
    if (reason === TRANSACTION_TYPES.CARDREQUEST_CHARGE) {
      // Get Card Request
      const foundCardRequest = await CardRequestRepo.findOne({
        conditions: {
          ...(transfer && { transfer: transfer.id }),
          ...(transaction && { transaction: transaction.id }),
        },
      });

      if (!foundCardRequest) throw new NotFoundError("Card Request");

      const SQSPayload = {
        id: `finalize_cardRequest_charge:${foundCardRequest.id}`,
        idempotencyKey: `finalize_cardRequest_charge:${foundCardRequest.id}`,
        path: `/card-request/finalize`,
        key: process.env.INTRA_SERVICE_TOKEN,
        company,
        ...(transfer && { transfer: transfer.id }),
        ...(transaction && { transaction: transaction.id }),
        data: {
          cardRequest: foundCardRequest.code,
        },
      };
      await QueueService.addDelayedJob({}, SQSPayload, `finalize_cardRequest_charge:${foundCardRequest.id}`, 10);
    }
  },

  async finalizeSubscriptionAddons({ reason, transaction, transfer, company }) {
    if (!reason) return null;
    const [prefix, subscriptionAddonReason] = reason.split("_");

    if (prefix !== BILLING_ADDONS.PREFIX) return null;

    if (subscriptionAddonReason !== BILLING_ADDONS.BENEFICIARIES) return null;

    const SQSPayload = {
      id: `finalize_subscription_addons:${transaction ? transaction.id : transfer.id}`,
      idempotencyKey: `finalize_subscription_addons:${transaction ? transaction.id : transfer.id}`,
      path: `/beneficiaries/finalize`,
      key: process.env.INTRA_SERVICE_TOKEN,
      company,
      transaction: transaction?.code,
      transfer: transfer?.code,
    };
    return QueueService.addDelayedJob({}, SQSPayload, `finalize_subscription_addons:${transaction ? transaction.id : transfer.id}`, 10);
  },

  async notifyPayerIfNotBatchTransaction({ transaction, notify, email, emailPayload, firstName, currency, companyWithAdmin }) {
    if (!transaction.batch_id) {
      if (notify) {
        NotificationService.notifyUser({ email }, "transaction-successful", emailPayload, {
          subject: `Hey ${firstName}, your payment of ${currency}${emailPayload.amount.toLocaleString()} has been made`,
        });
      }

      const transactionAttemptCount = await TransactionAttemptRepo.count({
        filter: { transaction: transaction.id },
      });

      // if it was a retry
      if (transactionAttemptCount > 1) {
        NotificationService.notifyUser({ email: "<EMAIL>" }, "successful-transaction-support", emailPayload, {
          subject: `The payment of ${currency}${emailPayload.amount.toLocaleString()} by ${
            companyWithAdmin.name
          } has been successfully retried and completed`,
        });
      }
    }
  },

  async notifyRecipientIfNeeded({ transaction, amount, currency, transactionCode, notify }) {
    if (transaction.notifyRecipient) {
      let isVendorExist = false;
      const vendorEmail = transaction?.Vendor?.email || transaction?.payment_recipient?.email;
      const vendorName = transaction?.Vendor?.name || transaction?.payment_recipient?.firstName;
      if (vendorEmail) {
        const user = await UserRepo.getOneUser({
          queryParams: {
            email: vendorEmail,
          },
        });
        isVendorExist = !!user;
      }

      const vendorEmailPayLoad = {
        dashboardUrl: isVendorExist ? `${Utils.getDashboardURL()}/transactions/${transaction.code}` : `${Utils.getDashboardURL()}`,
        isVendorExist,
        payerName: transaction.Company.name,
        vendorName,
        amount: (parseInt(amount, 10) / 100).toLocaleString(),
        currency,
        dateReceived: Utils.formatHumanReadableDate(transaction.created_at),
        year: new Date().getFullYear(),
        vendorAccountNumber: `${transaction.BankAccount.number}`,
        bankName: `${transaction.BankAccount.bankName}`,
        referenceId: transactionCode,
        email: vendorEmail,
        description: transaction.description,
      };

      if (vendorName && vendorEmail && notify) {
        NotificationService.notifyUser({ email: vendorEmail }, "vendor-email", vendorEmailPayLoad, {
          subject: `Hi ${vendorName}, a payment has been made to your account by ${transaction.Company.name}`,
        });
      }
    }
  },

  notifyAdminsIfNotBatchTransaction({
    notify,
    transaction,
    admins,
    email,
    companyWithAdmin,
    payer,
    counterPartyObject,
    reason,
    company,
    currency,
    amount,
  }) {
    if (notify && !transaction.batch_id && payer) {
      Promise.all(
        Array.from(admins).map((admin) => {
          if (admin.email === email) return null;
          const notificationPayload = {
            amount: (parseInt(transaction.amount, 10) / 100).toLocaleString(),
            firstName: admin.firstName,
            companyName: companyWithAdmin.name,
            currency: transaction.currency,
            referenceId: transaction.code,
            paidBy: `${payer.firstName} ${payer.lastName}`,
            date: transaction.created_at,
            accountNumber: counterPartyObject.attributes.accountNumber,
            bankName: counterPartyObject.attributes?.bank?.name,
            description: transaction.description,
            dashboardUrl: `${Utils.getDashboardURL()}/transactions/${transaction.code}`,
            paidOn: Utils.formatHumanReadableDate(transaction.created_at),
            paidTo: `${transaction.BankAccount.accountName}`,
          };

          const subject = `Hey ${admin.firstName}, ${payer.firstName} ${payer.lastName} has ${
            reason === "reimbursement" ? "been reimbursed of" : "made"
          } a ${transaction.currency}${notificationPayload.amount} payment`;

          return NotificationService.notifyUser(admin, "transaction-successful-admin", notificationPayload, {
            subject,
          });
        })
      );

      const systemNotificationPayload = {
        company,
        user_id: transaction.payer,
        type: `info`,
        badge: `info`,
        title: `Payment update`,
        message: `Your payment of ${currency}${(amount / 100).toLocaleString()} to ${
          counterPartyObject.accountName || transaction.BankAccount.accountName
        } is successful`,
        table: {
          code: transaction.code,
          entity: "Transaction",
        },
        event: "paymentUpdate",
      };
      NotificationService.saveNotification(systemNotificationPayload);
    }
  },

  async finalizeBillPayment(reason, company, transaction, status = STATUSES.PAID) {
    if (reason === "billPayment") {
      await API.post("/bills/finalize", {
        company,
        transaction: transaction.id,
        status,
      });
    }
  },

  async finalizeReimbursement(reason, company, reimbursementCode, transaction, transactionCode) {
    if (reason === "reimbursement") {
      await API.post("/reimbursements/finalize", {
        company,
        code: reimbursementCode,
        transaction: transaction.id,
        transaction_code: transactionCode,
      });
    }
  },

  deduceReasonForTransaction(transaction) {
    const typeToReasonMap = {
      [TRANSACTION_TYPES.BILL]: "billPayment",
      [TRANSACTION_TYPES.SUBSCRIPTION_ADDONS]: `${BILLING_ADDONS.PREFIX}_${BILLING_ADDONS.BENEFICIARIES}`,
      [TRANSACTION_TYPES.SUBSCRIPTION]: "billing",
      [TRANSACTION_TYPES.PAYMENT]: "payment",
      [TRANSACTION_TYPES.CARDREQUEST_CHARGE]: TRANSACTION_TYPES.CARDREQUEST_CHARGE,
    };

    return typeToReasonMap[transaction.type];
  },

  async determineProviderToUse(transaction) {
    const { code, company, currency = "" } = transaction;
    const { payment } = SettingsService.get("providers");
    const currencyMap = payment[currency.toLowerCase()] || {};
    let providerToUse = currencyMap[company] || currencyMap.defaultProvider || payment[company] || payment.defaultProvider;

    if (!code) return providerToUse;

    let foundBankAccount;

    const foundTransaction = await TransactionRepo.getTransaction({
      queryParams: { code },
      balance: true,
    });

    if (foundTransaction?.Balance?.bankAccount) {
      foundBankAccount = await BankAccountRepo.getOneBankAccount({
        queryParams: {
          issuer: CARD_ISSUER.Paystack,
          status: STATUSES.ACTIVE,
          type: "virtual",
          subType: "deposit",
          id: transaction.Balance?.bankAccount,
        },
        selectOptions: ["issuer"],
      });
    }

    const isPoweredByBlnk = [CARD_ISSUER.Paystack, CARD_ISSUER.Graph].includes(foundBankAccount?.issuer);

    if (isPoweredByBlnk) {
      const remappedProvider = Service.providerRemap(foundBankAccount?.issuer);
      if (remappedProvider) providerToUse = remappedProvider;
    }
    return providerToUse;
  },

  providerRemap(issuer) {
    const providerRemap = {
      [CARD_ISSUER.Paystack]: PAYMENT_PROVIDERS.PAYSTACK,
      [CARD_ISSUER.Graph]: PAYMENT_PROVIDERS.GRAPH,
    };

    return providerRemap[issuer];
  },

  async determineDirectDebitFee({ narration, depositFee, currency }) {
    const isDirectDebitNarration = narration.toLowerCase().includes("directdebittopupto");
    let shouldChargeDirectDebitFees = false;
    let directDebitTopUp;

    let feeToCharge = depositFee;

    if (!isDirectDebitNarration) return { feeToCharge, shouldChargeDirectDebitFees, directDebitTopUp };

    const possibleTokens = narration.split("-");
    directDebitTopUp = await DirectDebit.findOne({
      where: {
        reference: {
          [Op.in]: possibleTokens,
        },
      },
    });

    if (directDebitTopUp) {
      const hasChargedFees = await RedisService.get(`directDebit_fees:${directDebitTopUp.id}`);
      if (!hasChargedFees) {
        shouldChargeDirectDebitFees = true;
      }
    }

    if (shouldChargeDirectDebitFees) {
      const directDebitFeesMap = SettingsService.get("directDebitFees") || {};
      const directDebitFees = directDebitFeesMap[currency.toLowerCase()];
      if (directDebitFees) {
        feeToCharge = directDebitFees;
      }
    }
    return { feeToCharge, shouldChargeDirectDebitFees, directDebitTopUp };
  },

  computeFundingFees({ amount, type, currency, paymentPlan }) {
    let calculatedFee;
    if (type === "inbound") {
      if (currency === "NGN") {
        const serviceConfig = typeof paymentPlan.configuration === "string" ? JSON.parse(paymentPlan.configuration) : paymentPlan.configuration;
        const percentageToCharge = serviceConfig?.treasury?.fundingFees[currency]?.percentage;
        const maxFeeToCharge = serviceConfig?.treasury?.fundingFees[currency]?.max;
        const ngnFee = (percentageToCharge / 100) * amount;
        calculatedFee = Math.min(Math.max(ngnFee, 100), maxFeeToCharge);
      } else if (currency === "USD") {
        // TODO: Implement USD fee calculation based on provider
        const usdFee = (2 / 100) * amount;
        calculatedFee = usdFee;
      }
    }
    return calculatedFee;
  },

  async canAddMorePercentage({ filter, percentage }) {
    const totalPercentageSaved = await IndividualRepo.getCompanyTotalPercentageSubmitted({
      queryParams: { ...filter },
    });
    const totalPercentage = totalPercentageSaved + percentage;
    if (totalPercentage > 100) throw new ValidationError("Total percentage of directors entered cannot exceed 100");
  },

  async getCompanyTotalPercentageSubmitted({ company }) {
    const totalPercentageSaved = await Individual.sum("percentageOwned", {
      where: {
        company,
        status: [STATUSES.PENDING, STATUSES.INVITED],
      },
    });
    return totalPercentageSaved || 0;
  },

  determineDepositFee(shouldChargeDepositFee, amount, currency, paymentPlan) {
    return shouldChargeDepositFee
      ? Service.computeFundingFees({
          amount,
          currency,
          type: "inbound",
          paymentPlan,
        })
      : 0;
  },

  /**
   * This function fetches the account details that will receive the money for any charge bujeti wants to charge
   * @param {string} chargeType
   */
  async getChargeRecipientAccount(chargeType) {
    if (!chargeType) throw new ValidationError("Please specify a charge type");

    const collectionAccounts = SettingsService.get("collectionAccounts") || {};
    const recipientAccountExternalIdentifier = collectionAccounts[chargeType] || collectionAccounts.subscriptionCharge;
    if (!recipientAccountExternalIdentifier) throw new ValidationError("Couldn't find recipient account details");

    const foundBankAccount = await BankAccountRepo.getOneBankAccount({
      queryParams: { externalBankAccountId: recipientAccountExternalIdentifier },
      selectOptions: ["number", "bankCode", "owner", "ownerType"],
    });

    if (!foundBankAccount) throw new NotFoundError("Bank Account");
    return { bankAccount: foundBankAccount };
  },

  async notifyOnInflow({
    company,
    transfer,
    name,
    amount,
    depositFee,
    payload,
    currency,
    bankAccount,
    counterParty,
    virtualNuban,
    settlementAccount,
  }) {
    const companyDetails = await CompanyRepo.getCompanyWithAdmins({ id: company }, true);

    const foundTransfer = await TransferRepo.getTransfer({
      filter: {
        code: transfer.code,
      },
    });

    const companyAdmins = companyDetails?.Users || [];
    const companyName = companyDetails?.name || name;
    const formattedAmount = Utils.getInteger(Number(amount) - Number(depositFee));
    const totalFees = Utils.getInteger(payload?.processor_fee) + Utils.getInteger(payload?.bujeti_fee);
    const total = Utils.formatMoney(formattedAmount + totalFees);
    const notificationPayload = {
      currency,
      amount: Utils.formatMoney(formattedAmount),
      login: `${Utils.getDashboardURL()}/login`,
      companyName,
      dashboardUrl: Utils.getDashboardURL(),
      vat: transfer.vat || 0,
      fees: Utils.formatMoney(totalFees),
      total,
      balanceName: bankAccount?.Balance?.name || "Main Account",
      referenceId: transfer.code,
      paidBy: counterParty.accountName,
      paidOn: Utils.formatHumanReadableDate(foundTransfer.created_at),
      accountNumber: virtualNuban.accountNumber,
      bankName: settlementAccount.bankName,
    };

    Array.from(companyAdmins).forEach((admin) => {
      NotificationService.notifyUser(admin, "topup-notification-v2", notificationPayload);

      const systemNotificationPayload = {
        company,
        user_id: admin.id,
        type: `info`,
        badge: `info`,
        title: `Funding Update`,
        message: `Your wallet(${bankAccount?.Balance?.name || bankAccount.number}) has been credited with ${currency}${(
          amount / 100
        ).toLocaleString()}`,
        table: {
          code: transfer.code,
          entity: "Transaction",
        },
        event: "fundWallet",
      };

      NotificationService.saveNotification(systemNotificationPayload);
    });

    NotificationService.notifyUser({ email: "<EMAIL>" }, "topup-notification-admin", notificationPayload, {
      subject: `[Alert] Funding Alert ${companyName} 💰`,
    });
  },

  async validateLedgerIdentity(foundCompany) {
    const foundLedgerIdentity = await LedgerIdentityRepo.getLedgerIdentity({
      company: foundCompany.id,
      issuer: CARD_ISSUER.Blnk,
    });

    if (!foundLedgerIdentity) throw new NotFoundError("Ledger Identity");
  },

  async createOrReturnLedgerIdentity(foundCompany, providerHandler) {
    let identityId;
    let ledgerId;

    const foundLedgerIdentity = await LedgerIdentityRepo.getLedgerIdentity({
      company: foundCompany.id,
      issuer: CARD_ISSUER.Blnk,
    });

    if (!foundLedgerIdentity) {
      // create identity
      const identityPayload = {
        identity_type: "organization",
        organization_name: foundCompany.name,
        dob: foundCompany.dateOfRegistration,
        email_address: foundCompany.contact_email,
        phone_number: foundCompany.contact_phone,
        meta_data: {
          company: foundCompany.code,
        },
      };

      const { data: createdIdentity } = await providerHandler.identities.createIdentity(identityPayload, foundCompany.id);

      identityId = createdIdentity.identity_id;

      // create ledger
      const ledgerPayload = {
        name: foundCompany.name,
        meta_data: {
          company: foundCompany.code,
        },
      };

      const { data: createdLedger } = await providerHandler.ledgers.createLedger(ledgerPayload, foundCompany.id);
      ledgerId = createdLedger.ledger_id;

      await LedgerIdentityRepo.createLedgerIdentity({
        company: foundCompany.id,
        identity: identityId,
        ledger: ledgerId,
        issuer: CARD_ISSUER.Blnk,
      });
    } else {
      ledgerId = foundLedgerIdentity.ledger;
      identityId = foundLedgerIdentity.identity;
    }
    return { identityId, ledgerId };
  },

  async getInternalLedgerProviderHandler(foundTransaction, Providers) {
    const provider = Service.getProvider(foundTransaction.company, PROVIDER_PRODUCTS.LEDGER);

    const providerHandler = await Providers[provider];

    const foundLedgerIdentity = await LedgerIdentityRepo.getLedgerIdentity({
      company: foundTransaction.company,
      issuer: CARD_ISSUER.Blnk,
    });

    if (!foundLedgerIdentity) throw new NotFoundError("Ledger Identity");
    return providerHandler;
  },

  async createPayoutDestination({ provider, bankAccount, company, balance, Providers }) {
    const providerHandler = await Providers[provider];

    if (provider === PAYMENT_PROVIDERS.GRAPH) {
      const { data: createdPayoutDestination, error: createPayoutDestinationError } =
        await providerHandler.payoutDestinations.createPayoutDestination({
          accountId: balance?.BankAccount?.externalBankAccountId,
          currency: bankAccount.currency,
          company,
          label: bankAccount.accountName,
          wireType: bankAccount?.bankAddress?.country?.toUpperCase() === COUNTRY_ISO2.US ? "domestic" : "international",
          accountType: bankAccount.accountType,
          accountNumber: bankAccount.number,
          bankCode: bankAccount.bankCode,
          routingNumber: bankAccount.routingNumber,
          routingType: bankAccount.routingType,
          beneficiaryName: bankAccount.accountName,
          beneficiaryAddress: {
            ...(bankAccount.beneficiaryAddress || {}),
            postal_code: bankAccount.beneficiaryAddress?.postalCode,
          },
          bankName: bankAccount.bankName,
          bankAddress: {
            ...(bankAccount.bankAddress || {}),
            postal_code: bankAccount.bankAddress?.postalCode,
          },
        });

      if (createPayoutDestinationError) {
        throw new ValidationError("Unable to create payout destination");
      }

      const currentBankAccountMeta = parseJSON(bankAccount.meta || {});

      await BankAccountRepo.updateABankAccount({
        queryParams: {
          id: bankAccount.id,
        },
        updateFields: {
          meta: {
            ...currentBankAccountMeta,
            payoutDestinations: {
              [provider]: createdPayoutDestination?.data.id,
            },
          },
        },
      });

      return createdPayoutDestination?.data?.id;
    }

    return null;
  },

  businessTypeMapper(provider) {
    const businessTypeMap = {
      [VIRTUAL_ACCOUNT_PROVIDERS.GRAPH]: {
        "limited liability": "limitedLiabilityCompany",
        "sole proprietorship": "soleProprietor",
        enterprise: "soleProprietor",
        ngo: "nonProfit",
        partnership: "generalPartnership",
        "private limited liability": "limitedLiabilityCompany",
        "public limited liability": "publiclyTradedCorporation",
        "incorporated trustees": "nonProfit",
        "business name": "soleProprietor",
      },
      [VIRTUAL_ACCOUNT_PROVIDERS.ANCHOR]: {
        "limited liability": "Private_Incorporated",
        "sole proprietorship": "Business_Name",
        enterprise: "Business_Name",
        ngo: "Incorporated_Trustees",
        partnership: "Business_Name",
        "private limited liability": "Private_Incorporated",
        "public limited liability": "Public_Incorporated",
        "incorporated trustees": "Incorporated_Trustees",
        "business name": "Business_Name",
      },
    };

    return businessTypeMap[provider] || {};
  },

  async returnShareHoldersDesc(foundCompany) {
    return IndividualRepo.getAllIndividuals({
      queryParams: {
        company: foundCompany.id,
        type: INDIVIDUAL_TYPES.DIRECTOR,
        status: [STATUSES.PENDING, STATUSES.APPROVED, STATUSES.VERIFIED],
      },
      order: [["percentageOwned", "DESC"]],
      includeDocument: true,
      includeAddress: true,
      includePhoneNumber: true,
    });
  },
};

module.exports = Service;
