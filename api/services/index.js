/* eslint-disable global-require */

module.exports = {
  Analytics: require("./analytics"),
  Authentication: require("./authentication"),
  Bank: require("./bank"),
  Balance: require("./balance"),
  Beneficiary: require("./beneficiary.service"),
  Budget: require("./budget"),
  Company: require("./company"),
  Charge: require("./charge"),
  User: require("./user"),
  Utils: require("../utils/utils"),
  OTP: require("./otp"),
  Notification: require("./notification"),
  Mono: require("./mono"),
  Response: require("./response"),
  Transaction: require("./transaction"),
  Redis: require("./redis"),
  Helper: require("./helper.service"),
  Payment: require("./paymentservice"),
  Recipient: require("./recipient.service"),
  Verification: require("./verification"),
  UserBudget: require("./userBudget.service"),
  Teams: require("./teams.service"),
  TeamMember: require("./teamMember.service"),
  TeamBudget: require("./teamBudget.service"),
  Role: require("./roles.service"),
  Setting: require("./settings"),
  OnboardingInvite: require("./onboardingInvite.service"),
  Approval: require("./approval.service"),
  Invoice: require("./invoice.service"),
  Product: require("./product.service"),
  InvoiceProduct: require("./invoiceProduct.service"),
  Customer: require("./customer.service"),
  ExternalIntegration: require("./externalIntegration.service"),
  Withdrawal: require("./settlement.service"),
  FundRequest: require("./fundRequest.service"),
  CardRequest: require("./cardRequest.service"),
  BillingService: require("./billing"),
  ScannerService: require("./scanner.service"),
  Vendor: require("./vendorService"),
  ScheduledInvoice: require("./scheduledInvoice"),
  BillService: require("./bill.service"),
  ScheduledBillService: require("./scheduledBill.service"),
  BackgroundService: require("./backgroundService"),
  CompanyPreferences: require("./companyPreferences"),
  TaxService: require("./tax.service"),
};
