const { ExternalIntegrationRepo } = require("../repositories/index.repo");
const { STATUSES } = require("../models/status");
const responseUtils = require("../utils/response.utils");
const Helper = require("./helper.service");
const { SmileIdIntegrator } = require("../integrations");
const ExternalIntegrationService = require("./externalIntegration.service");

const SlackMappingService = require("./slackMapping.service");
const { log, Log } = require("../utils/logger.utils");
const { getSlackChannels } = require("../utils/slack.utils");

const Service = {
  idTypes: "nin" || "vi" || "dl",
  idNameType: {
    nin: SmileIdIntegrator.smileIDType.NIN,
    vi: SmileIdIntegrator.smileIDType.VOTER,
    dl: SmileIdIntegrator.smileIDType.DRIVERS,
  },

  async smileId(payload) {
    // expand payload
    const { id, company_code, document, type } = payload;
    const { bvn, idType, idNumber, idCopy, utilityBill } = document;
    let companyQuery = { code: company_code };

    // Checkers
    const { data: user } = await Helper.UserChecker({ id });
    await Helper.CompanyChecker({ company_code });

    // Store Docs on Details on Doc Table
    let directorsBvn = {
      type: "bvn",
      number: bvn,
      status: STATUSES.UNVERIFIED,
      processor: "smileID",
      table_type: "user",
      table_id: user.id,
    };
    const validId = await SmileIdIntegrator.basicKyc({
      id_type: Service.idNameType[idType],
      id_number: idNumber,
      first_name: user.firstName,
      last_name: user.lastName,
      partner_params: {
        table_code: user.code,
        table_id: String(user.id),
        table_type: directorsBvn.table_type,
      },
    });

    return responseUtils.sendObjectResponse("Company Docs successfully updated", validId);
  },
  async getByPlatform(criteria) {
    return ExternalIntegrationRepo.getIntegration({
      filter: criteria,
      selectedOptions: ["metadata", "status", "platform"],
    });
  },

  async setupSlackIntegrationTasks(companyId, integrationCode, accessToken, botUserId, appId, team) {
    try {
      const [channelsResult] = await Promise.all([
        // task 1: Fetch and store channels
        (async () => {
          try {
            const channels = await getSlackChannels(accessToken);
            log(Log.fg.green, {
              message: `Successfully fetched ${channels?.length || 0} Slack channels`,
            });

            await ExternalIntegrationService.updateIntegration(
              { code: integrationCode },
              {
                metadata: {
                  bot_user_id: botUserId,
                  app_id: appId,
                  team,
                  channels,
                },
              }
            );

            log(Log.fg.green, {
              message: "Successfully updated integration with channels data",
            });
            return true;
          } catch (channelError) {
            log(Log.bg.red, {
              message: "Error fetching or storing Slack channels",
              data: {
                company: companyId,
                integrationCode,
                error: String(channelError),
                statusCode: channelError.status || 500,
              },
            });
            return false;
          }
        })(),

        // task 2: Map Slack users to beneficiaries
        (async () => {
          try {
            const mappingResult = await SlackMappingService.mapSlackUsersToBeneficiaries({
              companyId,
              accessToken,
            });
            log(Log.fg.green, {
              message: "Slack user mapping completed successfully",
              data: {
                mappedCount: mappingResult.mappedCount,
                company: companyId,
                integrationCode,
                result: mappingResult,
              },
            });
            return true;
          } catch (mappingError) {
            log(Log.bg.red, {
              message: `Error mapping Slack users: ${mappingError.message}`,
              data: {
                company: companyId,
                integrationCode,
                error: mappingError,
              },
            });
            return false;
          }
        })(),
      ]);

      return channelsResult;
    } catch (error) {
      log(Log.bg.red, {
        message: "Error in Slack Integration Setup",
        data: {
          company: companyId,
          integrationCode,
          error: String(error),
        },
      });
      return false;
    }
  },
};

module.exports = Service;
