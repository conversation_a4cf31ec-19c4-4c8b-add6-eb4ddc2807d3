const { literal } = require("sequelize");
const { InvoiceAccount } = require("../models");
const {
  InvoiceRepo,
  InvoiceProductRepo,
  ProductRepo,
  InvoiceAccountRepo,
  BankAccountRepo,
  AccountHolderRepo,
  InvoiceInstallmentRepo,
  BalanceRepo,
  CustomerRepo,
  TransferRepo,
  InvoicePaymentRepo,
  AuditLogsRepo,
  CompanyRepo,
  BudgetRepo,
  InvoiceTemplateRepo,
  ScheduledInvoiceRepo,
  CounterPartyRepo,
  UserRepo,
} = require("../repositories/index.repo");
const { ValidationError, NotFoundError } = require("../utils/error.utils");
const Utils = require("../utils/utils");
const ResponseUtils = require("../utils/response.utils");
const { STATUSES } = require("../models/status");
const NotificationService = require("./notification");
const Providers = require("./providers");
const SettingsService = require("./settings");
const { CARD_ISSUER } = require("../models/cardissuer");
const TransferService = require("./transfer");
const HelperService = require("./helper.service");
const RedisService = require("./redis");
const ApprovalService = require("./approval.service");
const InvoiceProductService = require("./invoiceProduct.service");
const QueueService = require("./queue.service");
const CronService = require("./cron.service");
const ChargeService = require("./charge");
const Sanitizer = require("../utils/sanitizer");
const { updateAsset } = require("./asset");
const companyPreferences = require("./companyPreferences");
const { syncInvoiceToZoho, syncInvoicePaymentToZoho } = require("../utils/zoho.utils");
const { sendSlackNotification } = require("../utils/slack.utils");
const { money } = require("../utils/utils");
const { NOTIFICATION_TYPE } = require("../constants/notifications");

const Service = {
  async createInvoice(payload) {
    const { data, user } = payload;
    const createdInvoice = await InvoiceRepo.createInvoice({ payload: data });

    const isDraft = createdInvoice.status === STATUSES.DRAFT;
    let hasApproval = false;
    if (!isDraft) {
      const { success } = await ApprovalService.conditionDetector({
        id: createdInvoice.id,
        type: "invoice",
        company: createdInvoice.company,
        user,
      });
      if (!success) {
        // If there's approval rule
        await InvoiceRepo.update({
          filter: { id: createdInvoice.id },
          data: { status: STATUSES.VERIFYING },
        });
      }
      hasApproval = !success;

      // Only send notification if it's not a draft and if there's an approval needed
      if (!isDraft && hasApproval) {
        try {
          // Get user details
          const invoiceCreator = await UserRepo.getOneUser({
            queryParams: { id: user },
          });

          // Get company details
          const companyDetails = await CompanyRepo.getCompany({
            filter: { id: createdInvoice.company },
          });

          const totalAmount = createdInvoice.amount;

          // Prepare notification payload
          const notificationPayload = {
            amount: totalAmount,
            invoiceNumber: createdInvoice.code,
            code: createdInvoice.code,
            status: createdInvoice.status,
            companyName: companyDetails.name,
            company: createdInvoice.company,
            description: data.description,
            currency: data.products?.[0]?.currency || createdInvoice.currency || "NGN",
            dueDate: data.scheduleInvoice?.dueDate
              ? new Date(`${data.scheduleInvoice.dueDate.date}T${data.scheduleInvoice.dueDate.timestamp}`)
              : undefined,
            user: {
              code: invoiceCreator.code,
              firstName: invoiceCreator.firstName,
              lastName: invoiceCreator.lastName,
            },
            customer: data.customer,
            terms: data.terms,
            vat: data.vat,
            date: createdInvoice.createdAt,
          };

          // Send the Slack notification
          sendSlackNotification(NOTIFICATION_TYPE.INVOICE_REQUEST, notificationPayload);
        } catch (error) {
          console.error("Failed to send Slack notification for invoice request:", error);
          // Continue with the flow, don't fail if notification fails
        }
      }
    }

    return ResponseUtils.sendObjectResponse(!hasApproval ? "Invoice created successfully" : "Invoice created successfully and pending approval", {
      invoice: createdInvoice,
      hasApproval,
    });
  },

  async completeCreationOfInvoice(payload) {
    const { invoice, installments, products } = payload;
    const callsToMake = [];
    if (products) callsToMake.push(InvoiceProductService.createInvoiceProducts(invoice, products));
    if (installments && Object.keys(installments).length > 0) {
      const { type, payments } = installments;
      const invoiceInstallmentPayload = payments.map((payment) => {
        const { percentage, amount, ...rest } = payment;
        const installmentAmount = amount || Utils.calculatePercentageAmount(percentage, invoice.amount);
        return { ...rest, type, invoice: invoice.id, percentage, amount: installmentAmount };
      });
      callsToMake.push(InvoiceInstallmentRepo.createBulkInvoiceInstallments({ payload: invoiceInstallmentPayload }));
      // Activate cron for reminders

      const reminderJobRedisKey = "invoiceInstallmentReminder:running";
      const reminderJobIsRunning = await RedisService.get(reminderJobRedisKey);
      if (!reminderJobIsRunning) {
        await RedisService.set(reminderJobRedisKey, 1);
        const jobId = SettingsService.get("invoiceInstallmentReminderJobId");
        CronService.updateCron(jobId, { activate: true });
      }
    }

    return Promise.all([...callsToMake]);
  },

  /**
   * Attach files to an invoice
   * @param {*} id invoice ID
   * @param {*} attachments attachments array
   */
  async addAttachmentToInvoice(id, attachments) {
    attachments.forEach((code) => {
      updateAsset({
        queryParams: {
          code,
        },
        updateFields: {
          entityType: "Invoice",
          entityId: id,
        },
      });
    });
  },

  async list(filter) {
    const { company, ...rest } = filter;
    const query = { company, ...rest };

    const [{ invoices, meta }, summary] = await Promise.all([
      InvoiceRepo.listInvoices({ filter: query }),
      InvoiceRepo.getInvoiceStats({ filter: query }),
    ]);

    return {
      invoices,
      summary,
      meta,
    };
  },

  buildInvoiceTimeline(invoice) {
    const activities = [];
    const {
      User: { firstName, lastName, code },
      amount: invoiceAmount,
      ApprovalRequest,
      created_at: createdAt,
      InvoiceInstallments,
      Customer: { name: customerName },
      currency,
      ScheduledInvoice,
    } = invoice.toJSON();

    activities.push({
      title: "Created by",
      type: "creation",
      performedBy: {
        firstName,
        lastName,
        code,
      },
      performedAt: createdAt,
      status: "success",
    });

    if (ScheduledInvoice) {
      const formattedDueDate = Utils.formatDate(createdAt, "dd MMMM, yyyy");
      activities.push({
        title: "Scheduled",
        type: "schedule",
        description: `This invoice was scheduled to be sent on the ${formattedDueDate}`,
        status: "success",
      });
    }

    if (ApprovalRequest) {
      const { Approvals, status: approvalStatus, ApprovalStages } = ApprovalRequest;
      // get Current stage
      if (approvalStatus === STATUSES.PENDING) {
        const pendingStage = ApprovalStages && ApprovalStages.find((stage) => stage.status === STATUSES.PENDING);
        const threshold = SettingsService.get("APPROVERS_THRESHOLD");
        const condition = Utils.getKeyByValue(threshold, pendingStage.ApproverLevel.approvers_threshold);
        const approvers = pendingStage.ApproverLevel?.Approvers.map((approver) => {
          const { User } = approver;
          return {
            firstName: User?.firstName,
            lastName: User?.lastName,
          };
        });
        activities.push({
          title: `Pending approval`,
          type: "pending_approval",
          status: "pending",
          condition,
          approvers,
        });
      } else {
        // eslint-disable-next-line no-unused-expressions
        Approvals &&
          Approvals.length &&
          Approvals.forEach((approval, index) => {
            const { Approver: { User } = {}, status, created_at: performedAt } = approval;
            let decision;
            if (status === STATUSES.APPROVED) decision = "Approved";
            else if (status === STATUSES.DECLINED) decision = "Declined";
            else decision = "Awaiting approval";

            activities.push({
              title: `${decision} by`,
              type: `approval_action`,
              status: Utils.getKeyByValue(STATUSES, status === STATUSES.APPROVED ? STATUSES.SUCCESS : status).toLowerCase(),
              performedBy: {
                firstName: User?.firstName,
                lastName: User?.lastName,
              },
              performedAt,
            });
          });
      }
    }

    // eslint-disable-next-line no-unused-expressions
    InvoiceInstallments &&
      InvoiceInstallments.length &&
      InvoiceInstallments.forEach((installment, index) => {
        const { percentage, status, amount, paid_on: paidOn, due_date: dueDate } = installment;
        const parsedAmount = Number(amount || Utils.calculatePercentageAmount(percentage, invoiceAmount));
        const installmentAmount = Utils.formatMoney(parsedAmount);
        if (status === STATUSES.PAID) {
          activities.push({
            title: `${Utils.getSymbolFromCurrency(currency)}${installmentAmount} paid by`,
            type: "paid_installment",
            performedBy: {
              name: customerName,
            },
            status: "success",
            performedAt: Utils.formatDate(paidOn, "MMMM dd, yyyy"),
          });
        } else if (Utils.firstDateIsAfterSecondDate(dueDate)) {
          const formattedDueDate = Utils.formatDate(dueDate, "MMMM dd, yyyy");
          activities.push({
            title: `Payment due on ${formattedDueDate}`,
            type: "pending_installment",
            description: `Reminder is scheduled to be sent ${Utils.formatDate(Utils.getFutureDate(-1, dueDate), "MMMM dd, yyyy")}`,
            performedBy: {
              name: customerName,
            },
            status: "pending",
          });
        } else {
          const formattedDueDate = Utils.formatDate(dueDate, "MMMM dd, yyyy");
          activities.push({
            title: `Payment is over due since ${formattedDueDate}`,
            type: "overdue_installment",
            description: `Reminder was sent ${Utils.formatDate(Utils.getFutureDate(-1, dueDate), "MMMM dd, yyyy")}`,
            status: "success",
          });
        }
      });
    return activities;
  },

  async view(filter) {
    const { includeTimeline = false, ...rest } = filter;
    const invoice = await InvoiceRepo.getSingleInvoice({
      filter: { ...rest },
      includeProducts: true,
      addCompany: true,
      includeBalance: true,
      includePayments: true,
      includeApproval: Boolean(includeTimeline),
      includeSchedule: Boolean(includeTimeline),
    });
    if (!invoice) throw new NotFoundError(`Invoice`);
    // Update VAT amount where it is 0. This is for old invoices
    // TODO: Take this out after a while
    if (invoice.vat && invoice.vatAmount === 0) {
      const vatAmount = Utils.getVatAmountFromTotalAmount(invoice.amount, invoice.vat);
      InvoiceRepo.update({ filter: { id: invoice.id }, data: { vatAmount } });
    }
    return invoice;
  },

  async viewPublic(filter) {
    const { includeTimeline = false, ...rest } = filter;
    const invoice = await InvoiceRepo.getSingleInvoice({
      filter: { ...rest },
      includeProducts: true,
      addCompany: true,
      includeBalance: true,
      includePayments: true,
      includeApproval: Boolean(includeTimeline),
      includeSchedule: Boolean(includeTimeline),
    });
    if (!invoice) throw new NotFoundError(`Invoice`);
    // Update VAT amount where it is 0. This is for old invoices
    // TODO: Take this out after a while
    if (invoice.vat && invoice.vatAmount === 0) {
      const vatAmount = Utils.getVatAmountFromTotalAmount(invoice.amount, invoice.vat);
      InvoiceRepo.update({ filter: { id: invoice.id }, data: { vatAmount } });
    }
    const meta = {};
    const preferences = await companyPreferences.listPreferences({ company: invoice.company, feature: "invoices" });
    const enhancedPreferences = await companyPreferences.fetchAssociatedEntities(preferences);
    enhancedPreferences.forEach(({ key, value }) => {
      meta[key] = value;
    });
    return { invoice, meta };
  },

  /**
   *
   * @param {Number} invoice
   */
  async updateInvoiceCalculation(invoice) {
    const invoiceFilter = { id: invoice };
    const foundInvoice = await InvoiceRepo.getSingleInvoice({
      filter: invoiceFilter,
      includeProducts: true,
    });
    if (!foundInvoice) ResponseUtils.BadRequestException("Invoice not found");

    const { InvoiceProducts, vat, discount, discount_type } = foundInvoice;

    let invoiceAmount = Array.from(InvoiceProducts).reduce((prev, invoiceProduct) => {
      const {
        id,
        discount,
        discount_type,
        quantity,
        Product: { price: unitPrice },
      } = invoiceProduct;
      const payload = { discount, discount_type, quantity, unitPrice };
      const invoiceProductPrice = Utils.calculateProductPrice(payload);
      InvoiceProductRepo.update({
        filter: { id },
        data: { amount: invoiceProductPrice },
      });
      return prev + invoiceProductPrice;
    }, 0);

    if (discount) invoiceAmount = invoiceAmount - (discount_type === "amount" ? discount : invoiceAmount * (discount / 100));

    if (vat) invoiceAmount += invoiceAmount * (vat / 100);

    InvoiceRepo.update({
      filter: invoiceFilter,
      data: { amount: invoiceAmount },
    });
  },

  async updateInvoiceProducts(invoice, productsData) {
    const foundInvoice = await InvoiceRepo.getSingleInvoice({ filter: { id: invoice }, includeProducts: true });
    if (!foundInvoice) throw NotFoundError("Invoice");
    const { company, InvoiceProducts = [] } = foundInvoice;
    const previousInvoiceProducts = InvoiceProducts.map((invoiceProduct) => invoiceProduct.toJSON());
    const response = Utils.compareObjectArray({ previousItems: previousInvoiceProducts, newItems: productsData, key: "code" });
    const { itemsToAdd, itemsToDelete, itemsToUpdate } = response;

    if (itemsToDelete.length > 0) {
      const invoiceCodes = Utils.mapAnArray(itemsToDelete, "code");
      await InvoiceProductRepo.update({
        filter: { code: invoiceCodes },
        data: { status: STATUSES.DELETED },
      });
    }

    if (itemsToUpdate.length > 0) {
      await Promise.allSettled(
        // eslint-disable-next-line array-callback-return
        itemsToUpdate.map(async (item) => {
          const { code, name, currency, Product, unitPrice, ...rest } = item;
          // If product data exist to update
          if (name || unitPrice)
            ProductRepo.update({
              filter: { id: Product.id },
              payload: {
                ...(name && { name }),
                ...(unitPrice && { price: unitPrice }),
              },
            });

          // Update Invoice product
          let amount = 0;
          if (unitPrice) amount = Utils.calculateProductPrice({ ...rest, unitPrice });
          if (Object.keys(rest).length > 0)
            await InvoiceProductRepo.update({
              filter: { code },
              data: { ...rest, ...(amount && { amount }) },
            });
        })
      );
    }

    if (itemsToAdd.length > 0) {
      const invoiceProductsItems = [];
      await Promise.allSettled(
        // eslint-disable-next-line array-callback-return
        itemsToAdd.map(async (item) => {
          const { name, currency, Product, unitPrice, ...rest } = item;
          const createdProduct = await ProductRepo.createProduct({
            data: { name, currency, company, price: unitPrice },
          });
          const invoiceProductAmount = Utils.calculateProductPrice({ ...rest, unitPrice });

          invoiceProductsItems.push({ ...rest, company, amount: invoiceProductAmount, invoice, product: createdProduct.id });
        })
      );
      await InvoiceProductRepo.createBulkInvoiceProducts({ payload: invoiceProductsItems });
    }
  },

  async removeInvoice(criteria) {
    const invoice = await InvoiceRepo.getSingleInvoice({ filter: criteria });
    if (!invoice) throw new NotFoundError("Invoice");

    const payload = { status: STATUSES.DELETED };
    return InvoiceRepo.update({ filter: criteria, data: payload });
  },

  async shareInvoice(data) {
    const { code, method } = data;

    const invoice = await InvoiceRepo.getSingleInvoice({
      filter: { code },
      addCompany: true,
    });
    if (!invoice) throw new NotFoundError("Invoice");

    const { Customer, Company, amount, User, currency } = invoice;

    if (method === "email" && !Customer.email) throw new ValidationError("Customer email not found");
    if (method === "sms" && !Customer.PhoneNumber) throw new ValidationError("Customer Phone Number not found");

    if (method === "email") {
      return Service.sendInvoiceCreatedEmail({ invoice: invoice.code });
    }

    return {};
  },

  async generateInvoiceVirtualAccount(company, invoice, provider) {
    const { error, data, message } = await Providers[provider].virtualAccount.generateDisposableVirtualAccount(company);
    if (error) throw new ValidationError(`Provider error: ${data.errors.map(({ detail }) => detail || "").join("\n")}`);

    const { id: externalIdentifier, type, attributes } = data;
    const {
      accountNumber,
      accountName,
      bank: { name: bankName, nipCode },
      currency,
    } = attributes;

    const invoiceBankAccount = await InvoiceAccount.create({
      invoice,
      externalIdentifier,
      accountName,
      accountNumber,
      bankName,
      nipCode,
      currency,
      issuer: CARD_ISSUER[provider],
    });

    await InvoiceRepo.update({
      filter: { id: invoice },
      data: { invoiceAccount: invoiceBankAccount.id },
    });
    const jsonifiedResponse = invoiceBankAccount.toJSON();
    delete jsonifiedResponse.invoice;
    return jsonifiedResponse;
  },

  async notifyAdminsOfInvoicePayment({ payload }) {
    const { invoice, amountPaid } = payload;
    const foundInvoice = await InvoiceRepo.getSingleInvoice({
      filter: { id: invoice },
      includeProducts: true,
      addCompany: true,
    });
    if (!foundInvoice) throw new NotFoundError("Invoice");
    const {
      currency,
      code,
      Customer: { name: customerName },
      InvoiceInstallments,
      amount,
      vat,
      discount,
      discount_type: discountType,
      InvoiceProducts,
    } = foundInvoice;

    const { Users: admins } = await CompanyRepo.getCompanyWithAdmins({ id: foundInvoice.company }, true);

    const { discount: calculatedDiscount } = Utils.getInvoiceAmountBreakdown({
      currency,
      amount,
      vat,
      discount,
      discountType,
    });

    const itemCount = (InvoiceProducts && InvoiceProducts.length) || 0;
    const totalPaid = await Service.getInvoiceTotalPayed(foundInvoice.id);

    const emailPayload = {
      customer: customerName,
      invoiceId: code,
      currency: Utils.getSymbolFromCurrency(currency),
      discount: calculatedDiscount,
      itemCount: `${itemCount} item${itemCount > 1 ? "s" : ""}`,
      totalAmount: Utils.formatMoney(amount),
      amountDue: Utils.formatMoney(amountPaid),
      amountPaid: Utils.formatMoney(totalPaid),
      amountRemaining: (parseInt(amount - totalPaid, 10) / 100).toLocaleString(),
      dashboardUrl: Utils.getDashboardURL(),
    };

    if (InvoiceInstallments) {
      const { installmentAmountResponse, installmentDueDateResponse, installmentStatusResponse } =
        Utils.prepareInvoiceInstallmentPayloadForEmail(InvoiceInstallments);
      Object.assign(emailPayload, installmentAmountResponse);
      Object.assign(emailPayload, installmentDueDateResponse);
      Object.assign(emailPayload, installmentStatusResponse);
    }

    if (admins.length) {
      admins.map((admin) => {
        const { firstName, lastName, email } = admin;
        emailPayload.recipientName = `${firstName} ${lastName}`;
        NotificationService.notifyUser({ email }, "invoice-payment-received", emailPayload);
      });
    }
  },

  async getInvoiceDisposableAccount(invoice) {
    const { id, Company: company } = invoice;
    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[company.code] || payment.defaultProvider;
    const invoiceAccount = await InvoiceAccount.findOne({
      where: {
        invoice: id,
        issuer: CARD_ISSUER[providerToUse],
        status: STATUSES.ACTIVE,
      },
      attributes: ["code", "accountNumber", "accountName", "currency", "bankName", "nipCode", "status", "created_at"],
      order: [["created_at", "DESC"]],
    });

    if (!invoiceAccount) return Service.generateInvoiceVirtualAccount(company.id, id, providerToUse);
    const formattedDate = new Date(invoiceAccount.created_at);
    const expiryDate = formattedDate.setDate(formattedDate.getDate() + 2); //Expires in 48hrs
    const today = new Date();
    //  Generates new one if it has expired
    if (expiryDate < today) return Service.generateInvoiceVirtualAccount(company.id, id, providerToUse);
    return invoiceAccount;
  },

  async logOrReverseInvoicePayment(payload) {
    const { provider, transfer, amount: paymentAmount, company, currency, accountId: externalIdentifier, sender: recipient, counterParty } = payload;
    const customerAccount = await BankAccountRepo.getOneBankAccount({
      queryParams: { externalIdentifier },
      selectOptions: ["owner", "number", "externalBankAccountId"],
    });
    if (!customerAccount) throw new NotFoundError("Customer Account");
    const { owner: customer, number } = customerAccount;

    const pendingCustomerSettlement = Utils.parseJSON(await RedisService.get(`pending_customer_settlement:${externalIdentifier}`)) || null;
    let foundInvoice;

    if (pendingCustomerSettlement) {
      const { entity } = pendingCustomerSettlement;
      foundInvoice = await InvoiceRepo.getSingleInvoice({
        filter: { id: entity },
        addCompany: true,
      });
    } else {
      foundInvoice = await InvoiceRepo.getInvoiceByPaymentAmount({
        criteria: { company, customer, status: [STATUSES.PENDING, STATUSES.PARTIAL, STATUSES.OVERDUE], amount: paymentAmount },
      });
    }

    const foundTransfer = await TransferRepo.getTransfer({ filter: { code: transfer, company, status: STATUSES.PENDING } });

    if (!foundInvoice) {
      foundInvoice = await InvoiceRepo.getInvoiceForPartialPayment({ queryParams: { amount: paymentAmount, company, customer } });
    }

    if (!foundTransfer) throw new NotFoundError("Pending Transfer");
    if (!foundInvoice) throw new ValidationError("Invoice with this amount not found or already paid");
    if (![STATUSES.PARTIAL, STATUSES.PENDING, STATUSES.OVERDUE, STATUSES.PROCESSING].includes(foundInvoice.status))
      throw new ValidationError(`Invoice already ${foundInvoice.Status.value}`);

    const {
      Customer: { email, name },
      User,
      Company,
      amount: invoiceAmount,
      InvoiceInstallments = [],
    } = foundInvoice;
    let invoiceInstallments;
    if (invoiceAmount === paymentAmount) {
      // Mark invoice as paid and all installment as paid
      await Promise.all([
        InvoiceRepo.update({ filter: { id: foundInvoice.id }, data: { status: STATUSES.PAID, paidOn: literal("CURRENT_TIMESTAMP") } }),
        InvoiceInstallmentRepo.updateInvoiceInstallment({
          filter: { invoice: foundInvoice.id },
          payload: { status: STATUSES.PAID, paid_on: new Date() },
        }),
      ]);
      invoiceInstallments = await InvoiceInstallmentRepo.listInvoiceInstallment({ filter: { invoice: foundInvoice.id } });
    } else {
      // Possibly an Installment payment
      let [foundInvoiceInstallment] = InvoiceInstallments;
      if (!foundInvoiceInstallment) {
        // Create an Installment and mark it as paid
        const installmentPayload = {
          invoice: foundInvoice.id,
          status: STATUSES.PAID,
          amount: paymentAmount,
          due_date: foundInvoice.due_date,
          type: "amount",
          paid_on: new Date(),
        };
        // Create the remaining amount as pending installment
        const pendingInstallmentPayload = {
          invoice: foundInvoice.id,
          status: STATUSES.PENDING,
          amount: Utils.money(invoiceAmount).minus(paymentAmount),
          due_date: foundInvoice.due_date,
          type: "amount",
        };
        const createdInstallments = await InvoiceInstallmentRepo.createBulkInvoiceInstallments({
          payload: [installmentPayload, pendingInstallmentPayload],
        });
        [foundInvoiceInstallment] = createdInstallments;
      } else {
        // Loop through install, find the first pending installment where amount is greater than or equal to amount paid
        foundInvoiceInstallment = InvoiceInstallments.find(
          (installment) => installment.status === STATUSES.PENDING && installment.amount >= paymentAmount
        );

        if (foundInvoiceInstallment && foundInvoiceInstallment.amount === paymentAmount) {
          // Mark the installment as paid
          await InvoiceInstallmentRepo.updateInvoiceInstallment({
            filter: { invoice: foundInvoice.id, id: foundInvoiceInstallment.id },
            payload: { status: STATUSES.PAID, paid_on: new Date() },
          });
        } else {
          // Found installment is greater than. Update the installment amount and create another installment with the delta
          const pendingBalance = foundInvoiceInstallment.amount - paymentAmount;
          const pendingInstallmentPayload = {
            invoice: foundInvoice.id,
            status: STATUSES.PENDING,
            amount: pendingBalance,
            due_date: foundInvoice.due_date,
            type: "amount",
          };

          foundInvoiceInstallment.amount = paymentAmount;

          await Promise.all([
            InvoiceInstallmentRepo.updateInvoiceInstallment({
              filter: { invoice: foundInvoice.id, id: foundInvoiceInstallment.id },
              payload: { amount: paymentAmount, status: STATUSES.PAID, paid_on: new Date() },
            }),
            InvoiceInstallmentRepo.createInvoiceInstallment({ payload: pendingInstallmentPayload }),
          ]);
        }
      }
      Service.sendInstallmentPaidEmail(foundInvoice, foundInvoiceInstallment); // Send Payment email
      invoiceInstallments = [foundInvoiceInstallment];
    }

    if (invoiceInstallments.length) {
      // Create Installment payment
      const bulkInvoicePaymentData = invoiceInstallments.map((invoiceInstallment) => ({
        invoice: foundInvoice.id,
        installment: invoiceInstallment.id,
        company,
        status: STATUSES.PAID,
        transfer: foundTransfer.id,
      }));
      const invoicePayments = await InvoicePaymentRepo.createBulkInvoicePayment({ payload: bulkInvoicePaymentData });
      invoicePayments.forEach((payment) => syncInvoicePaymentToZoho(payment.code, payment.company));
    } else {
      const invoicePaymentData = {
        invoice: foundInvoice.id,
        company,
        status: STATUSES.PAID,
        transfer: foundTransfer.id,
      };
      const invoicePayment = await InvoicePaymentRepo.createInvoicePayment({ payload: invoicePaymentData });
      syncInvoicePaymentToZoho(invoicePayment.code, payload.company);
    }
    // update transfer status to success so it can't be used anymore
    await TransferRepo.update({ filter: { id: foundTransfer.id }, payload: { status: STATUSES.SUCCESS } });

    const totalAmountPaid = await InvoiceRepo.getInvoiceTotalPaid({ queryParams: { invoice: foundInvoice.id } });

    if (parseInt(totalAmountPaid, 10) === parseInt(invoiceAmount, 10)) {
      await InvoiceRepo.update({ filter: { id: foundInvoice.id }, data: { status: STATUSES.PAID, paidOn: literal("CURRENT_TIMESTAMP") } });
    } else await InvoiceRepo.update({ filter: { id: foundInvoice.id }, data: { status: STATUSES.PARTIAL } });

    // Leaving this here for when I work on Reversal
    // if (amount !== invoiceAmount)
    //   return Service.reverseInvoicePaidAmount({
    //     transfer,
    //     provider,
    //     recipient,
    //     invoiceAccount: invoiceAccount.id,
    //     invoice: id,
    //   });

    await Service.sendInvoiceAmountToCollectionBalance({
      payload: { provider, company, customerAccount, invoice: foundInvoice, amount: paymentAmount, counterParty },
    });
    // NOTIFY ADMIN
    await RedisService.delete(`pending_customer_settlement:${externalIdentifier}`);
    Service.notifyAdminsOfInvoicePayment({ payload: { invoice: foundInvoice.id, amountPaid: paymentAmount } });
  },

  async reverseInvoicePaidAmount(payload) {
    const { transfer, provider, recipient, invoiceAccount, invoicePayment, invoice } = payload;
    const { amount, company, currency, reference, description, processor_fee, bujeti_fee, narration } = await TransferService.getTransfer(transfer);
    const pendingTransfer = await TransferService.createTransfer({
      amount: -1 * parseInt(amount, 10),
      company,
      currency,
      reference,
      description,
      processor_fee,
      bujeti_fee,
      narration: `[Reversal] Incorrect Invoice Amount paid`,
      invoice,
    });
    // SEND THE MONEY
    const bankAccount = await BankAccountRepo.getOneBankAccount({
      queryParams: {
        owner: company,
        ownerType: "company",
        issuer: CARD_ISSUER[provider],
        status: STATUSES.ACTIVE,
        currency,
      },
      selectOptions: ["externalIdentifier"],
    });

    if (!bankAccount) throw new NotFoundError("Bank Account");

    const { nipCode } = HelperService.getSingleBank(recipient.bankName);
    await InvoiceAccountRepo.updateInvoiceAccount({
      filter: { id: invoiceAccount },
      data: { status: STATUSES.INACTIVE },
    });
    const debitPayload = {
      accountId: bankAccount.externalIdentifier,
      amount,
      currency,
      reference,
      narration: `Reversal of incorrect amount for Invoice`,
      meta: {
        reason: "reversal",
        invoicePayment,
        transfer: pendingTransfer.code,
      },
      account_number: recipient.accountNumber,
      account_name: recipient.accountName,
      bank_code: nipCode,
    };

    return Providers[provider].debitAccount(debitPayload);
  },

  async markInvoiceAsPaid({ filter, payload, extras = {} }) {
    const foundInvoice = await InvoiceRepo.getSingleInvoice({
      filter: { ...filter, status: [STATUSES.PENDING, STATUSES.PARTIAL, STATUSES.OVERDUE] },
    });
    if (!foundInvoice) throw new NotFoundError("Invoice");

    const { installments = [] } = payload;
    if (installments && installments.length) {
      // Mark installments as paid
      const foundInstallments = await InvoiceInstallmentRepo.listInvoiceInstallment({
        filter: { code: installments, invoice: foundInvoice.id },
      });

      if (!(foundInstallments && foundInstallments.length)) throw new ValidationError("One or more invoice installment not found");
      const hasPaidInstallment = Array.from(foundInstallments).find((installment) => installment.status === STATUSES.PAID);
      if (hasPaidInstallment) throw new ValidationError("An already paid installment cannot be marked as paid");
      await InvoiceInstallmentRepo.updateInvoiceInstallment({
        filter: { code: installments },
        payload: { status: STATUSES.PAID, paid_on: new Date() },
      });
    }

    // Check if it has other pending installment
    if (installments && installments.length) {
      const hasunpaidInstallment = await InvoiceInstallmentRepo.listInvoiceInstallment({
        filter: { invoice: foundInvoice.id, status: [STATUSES.PENDING, STATUSES.OVERDUE] },
      });
      if (foundInvoice.status === STATUSES.PENDING) await InvoiceRepo.update({ filter: { id: foundInvoice.id }, data: { status: STATUSES.PARTIAL } });
      if (hasunpaidInstallment.length) return ResponseUtils.sendObjectResponse("Invoice Installment marked as paid");
    }

    // Installment not sent, mark all pending has paid
    await Promise.all([
      InvoiceInstallmentRepo.updateInvoiceInstallment({
        filter: { invoice: foundInvoice.id, status: [STATUSES.PENDING, STATUSES.OVERDUE] },
        payload: { status: STATUSES.PAID, paid_on: new Date() },
      }),

      InvoiceRepo.update({ filter, data: { status: STATUSES.PAID, paidOn: literal("CURRENT_TIMESTAMP") } }),
    ]);

    AuditLogsRepo.createAnAuditLogs({
      queryParams: {
        event: "mark-invoice-as-paid",
        user: extras?.user.id || foundInvoice.user,
        table_type: "Invoices",
        table_id: foundInvoice.id,
        initial_state: Sanitizer.sanitizeInvoice(foundInvoice),
        delta: { ...payload },
      },
    });

    return ResponseUtils.sendObjectResponse("Your invoice has been marked as paid");
  },

  sendReminderToCustomer({ invoice }) {
    return Service.sendInvoiceCreatedEmail({ invoice: invoice.code });
  },

  async sendInvoiceAmountToCollectionBalance({ payload }) {
    const { provider, company, customerAccount, invoice, amount, counterParty } = payload;
    const { currency, code, id, balance, budget } = invoice;
    let collectionAccount;
    if (balance) {
      collectionAccount = await BalanceRepo.getBalance({ filter: { id: balance }, includeAccount: true });
    } else if (budget) {
      collectionAccount = await BudgetRepo.getBudget({ id: budget }, true);
    } else
      collectionAccount = await BankAccountRepo.getCollectionAccount({
        queryParams: { company, currency },
      });
    if (!collectionAccount) throw new NotFoundError("Invoice Settlement Account");

    let transferCounterParty;
    if (counterParty) {
      transferCounterParty = await CounterPartyRepo.getCounterParty({
        queryParams: { code: counterParty },
      });
    }

    // create transfer
    const description = `Settlement of invoice (${code}) into Collection Balance`;
    const transfer = await TransferService.createTransfer({
      amount: -1 * amount,
      company,
      currency,
      reference: Utils.generateRandomString(17).toLowerCase(),
      description,
      processor_fee: 0,
      bujeti_fee: 0,
      status: STATUSES.PENDING,
      narration: description,
      invoice: id,
      ...(transferCounterParty && { counterParty: transferCounterParty?.id }),
    });
    // move money
    let bankAccountObject;
    if (budget) {
      ({ BudgetAccount: bankAccountObject } = collectionAccount);
    } else {
      ({ BankAccount: bankAccountObject } = collectionAccount);
    }

    // TODO: Refactor this: Modify Charge function to handle both book and nip
    if (budget || bankAccountObject.type === "virtual") {
      const bookTransferPayload = {
        recipientId: budget ? bankAccountObject?.externalIdentifier : bankAccountObject?.externalBankAccountId,
        recipientType: budget ? bankAccountObject?.type : "DepositAccount",
        senderId: customerAccount.externalBankAccountId,
        senderType: "SubAccount",
        currency,
        amount: Math.abs(amount),
        reason: description,
        reference: transfer.reference,
        company,
        invoice: code,
        purpose: "INVOICE_SETTLEMENT",
        providerToUse: provider,
      };

      const data = {
        data: bookTransferPayload,
        id: Utils.generateRandomString(17),
        path: `/transfers/bookTransfer/${transfer.code}/process`,
        key: process.env.INTRA_SERVICE_TOKEN,
      };
      return QueueService.addDelayedJob({}, data, `BookTransferReference:${bookTransferPayload.reference}`, 5);
      // eslint-disable-next-line no-else-return
    } else {
      // Settling to external account
      const chargePayload = {
        company,
        amount: Math.abs(parseInt(amount, 10)),
        currency: "NGN",
        accountId: customerAccount.externalBankAccountId,
        accountType: "SubAccount",
        reference: transfer.reference,
        recipient: {
          bankCode: bankAccountObject.bankCode,
          bankName: bankAccountObject.bankName,
          accountName: bankAccountObject.accountName,
          number: bankAccountObject.number,
          reason: description,
          purpose: "INVOICE_SETTLEMENT",
          transfer: transfer.code,
          invoice: code,
        },
        narration: transfer.description,
        provider,
      };
      await ChargeService.chargeCompany(chargePayload);
    }
  },

  async createCollectionAccount(payload) {
    const { company } = payload;
    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[company] || payment.defaultProvider;

    const accountHolder = await AccountHolderRepo.getAccountHolder({
      filter: { company, provider: CARD_ISSUER[providerToUse] },
      selectOptions: ["externalIdentifier"],
    });

    if (!accountHolder) throw new NotFoundError("Account Holder");

    const { error, data } = await Providers[providerToUse].virtualAccount.createBankAccount(accountHolder.externalIdentifier, company);
    if (error) Providers[providerToUse].throwProviderError(data);

    const { id } = data;
    await RedisService.set(id, JSON.stringify({ type: "Revenue", company }));
  },

  /**
   * complete invoice approval
   * @param {*} invoice invoice id
   * @returns
   */
  async completeApproval(payload) {
    const { code: invoiceCode } = payload;
    const foundInvoice = await InvoiceRepo.getSingleInvoice({ filter: { code: invoiceCode } });
    if (!foundInvoice) throw new NotFoundError("Invoice");
    const { shouldSplitTax, vat, company } = foundInvoice;
    if (shouldSplitTax && vat) Service.createTaxAccount({ company });
    syncInvoiceToZoho(invoiceCode, foundInvoice.company); // Sync to zoho
    return Service.sendInvoiceCreatedEmail({ invoice: invoiceCode });
  },

  async disableScheduleInvoiceCron(code) {
    const foundScheduledInvoice = await ScheduledInvoiceRepo.getScheduledInvoice({ queryParams: { code } });
    if (!foundScheduledInvoice) throw new NotFoundError("Scheduled Invoice");
    const jobId = foundScheduledInvoice.Schedule?.cron_id;
    if (!jobId) return true;
    return CronService.updateCron(jobId, { activate: false });
  },

  async completeInvoiceEditing({ invoice, payload }) {
    const shouldTriggerrApprovalCheck = invoice.status === STATUSES.DRAFT && Utils.getStatusValues(payload.status) === STATUSES.PENDING;
    if (shouldTriggerrApprovalCheck) {
      const { success } = await ApprovalService.conditionDetector({
        id: invoice.id,
        type: "invoice",
        company: invoice.company,
        user: invoice.User, // Check THIS
      });
      if (!success) {
        // If there's approval rule
        await InvoiceRepo.update({ filter: { id: invoice.id }, data: { status: STATUSES.VERIFYING } });
      }
      return ResponseUtils.sendObjectResponse("Approval triggered");
    }

    const shouldNotifyCustomer = invoice.status === STATUSES.DRAFT && payload?.status === STATUSES.PENDING && invoice.Customer.email;
    if (shouldNotifyCustomer) {
      Service.sendInvoiceCreatedEmail({ invoice: invoice.code });
    }
    return ResponseUtils.sendObjectResponse("Approval triggered");
  },

  async updateInvoice({ filter, payload }) {
    const foundInvoice = await InvoiceRepo.getSingleInvoice({ filter });
    if (!foundInvoice) throw new NotFoundError("Invoice");

    if ([STATUSES.PENDING, STATUSES.PAID].includes(foundInvoice.status)) throw new ValidationError("Cannot update this invoice");

    const { products = null, settlementAccount, customer, installments, attachments, template, ...rest } = payload;
    if (products) await Service.updateInvoiceProducts(foundInvoice.id, products);

    if (settlementAccount) {
      const foundBalance = await BalanceRepo.getBalance({ filter: { code: settlementAccount, company: foundInvoice.company } });
      if (!foundBalance) throw new NotFoundError("Balance");
      rest.balance = foundBalance.id;
    }

    if (customer) {
      const foundCustomer = await CustomerRepo.getCustomer({
        filter: { code: customer, company: foundInvoice.company },
      });
      if (!foundCustomer) throw new NotFoundError("Customer");
      rest.customer = foundCustomer.id;
    }

    if (installments) {
      const { InvoiceInstallments } = foundInvoice;
      const previousItems = InvoiceInstallments.map((installment) => installment.toJSON());
      const response = Utils.compareObjectArray({ previousItems, newItems: installments.payments, key: "code" });
      const { itemsToAdd, itemsToDelete, itemsToUpdate } = response;

      if (itemsToDelete.length > 0) {
        const invoiceCodes = Utils.mapAnArray(itemsToDelete, "code");
        await InvoiceInstallmentRepo.updateInvoiceInstallment({
          filter: { code: invoiceCodes },
          payload: { status: STATUSES.DELETED },
        });
      }

      if (itemsToUpdate.length > 0) {
        await Promise.allSettled(
          // eslint-disable-next-line array-callback-return
          itemsToUpdate.map((item) => {
            const { id, code, ...remainingData } = item;
            InvoiceInstallmentRepo.updateInvoiceInstallment({
              filter: { code },
              payload: { ...remainingData },
            });
          })
        );
      }

      if (itemsToAdd.length > 0) {
        const { type } = installments;
        const newInstallments = itemsToAdd.map((item) => ({ ...item, invoice: foundInvoice.id, type }));
        await InvoiceInstallmentRepo.createBulkInvoiceInstallments({ payload: newInstallments });
      }
    }

    if (attachments) {
      Service.addAttachmentToInvoice(foundInvoice.id, attachments);
    }

    if (template) {
      const foundTemplate = await InvoiceTemplateRepo.getTemplateByCode(template);
      rest.template = foundTemplate.id;
    }

    if (rest.status) rest.status = Utils.getStatusValues(rest.status);
    if (Object.keys(rest).length > 0) await InvoiceRepo.update({ filter, data: rest });
    Service.updateInvoiceCalculation(foundInvoice.id);
    return ResponseUtils.sendObjectResponse("Invoice updated successfully", foundInvoice);
  },

  async getPaidInstallmentString(invoice, installmentCode) {
    const installmentStrings = ["1st", "2nd", "3rd", "4th"];
    let paidInstallmentString = "";
    const invoiceInstallments = await InvoiceInstallmentRepo.listInvoiceInstallment({ filter: { invoice } });
    Array.from(invoiceInstallments).forEach((installment, index) => {
      const { code } = installment;
      if (code === installmentCode) {
        paidInstallmentString = `${installmentStrings[index]} Installment paid`;
      }
    });
    return paidInstallmentString;
  },

  async getInvoiceTotalPayed(invoice) {
    const invoicePayments = await InvoicePaymentRepo.listInvoicePayments({
      filter: { invoice, status: STATUSES.PAID },
    });
    let totalPaid = 0;
    totalPaid =
      invoicePayments.length &&
      invoicePayments.reduce((prev, nextPayment) => {
        const { Transfer = {} } = nextPayment || {};
        const { amount = 0 } = Transfer;
        return Number(prev) + Number(amount);
      }, totalPaid);
    return totalPaid;
  },

  async sendInstallmentPaidEmail(invoice, installment) {
    const {
      code: invoiceId,
      due_date: dueDate,
      currency,
      Customer: { name: recipientName, email },
      Company: { name: companyName },
      amount: totalAmount,
    } = invoice;
    const { amount, code } = installment;
    const paidInstallment = await Service.getPaidInstallmentString(invoice.id, code);
    const totalPaid = await Service.getInvoiceTotalPayed(invoice.id);
    const emailPayload = {
      companyName,
      invoiceId,
      recipientName,
      currency: Utils.getSymbolFromCurrency(currency),
      instalmentAmount: (parseInt(amount, 10) / 100).toLocaleString(),
      instalmentPaid: paidInstallment,
      amountRemaining: (parseInt(totalAmount - totalPaid, 10) / 100).toLocaleString(),
      dueDate: Utils.formatHumanReadableDate(dueDate),
      invoiceUrl: Service.getInvoiceEmailURL(Utils.generatePublicId(invoiceId)),
    };

    if (email) {
      NotificationService.notifyUser({ email }, "instalment-paid", emailPayload);
    }
  },

  async sendInvoiceCreatedEmail({ invoice }) {
    if (!(invoice && String(invoice).startsWith("inv_"))) throw new ValidationError("Please specify invoice code");

    const foundInvoice = await InvoiceRepo.getSingleInvoice({ filter: { code: invoice }, addCompany: true });
    if (!foundInvoice) throw new NotFoundError("Invoice");

    const {
      InvoiceInstallments,
      User: foundUser,
      currency,
      Customer: foundCustomer,
      amount,
      code,
      vat,
      discount,
      discount_type: discountType,
      due_date: dueDate,
      Company: { name: companyName },
    } = foundInvoice;

    const { firstName, lastName } = foundUser;

    const emailPayload = {};
    if (InvoiceInstallments) {
      const { installmentAmountResponse } = Utils.prepareInvoiceInstallmentPayloadForEmail(InvoiceInstallments);
      Object.assign(emailPayload, installmentAmountResponse);
    }

    if (foundCustomer.Address) {
      emailPayload.billingAddress = Utils.formatBillingAddress(foundCustomer.Address);
    }

    const { subTotal, discount: calculatedDiscount } = Utils.getInvoiceAmountBreakdown({
      currency,
      amount,
      vat,
      discount,
      discountType,
    });

    const invoiceId = Utils.generatePublicId(code);
    NotificationService.notifyUser({ email: foundCustomer.email }, "create-invoice", {
      recipientName: foundCustomer.name,
      creatorName: `${firstName} ${lastName}`,
      companyName,
      currency: Utils.getSymbolFromCurrency(currency),
      invoiceId: invoiceId.substring(invoiceId.length, 14),
      totalAmount: (parseInt(amount, 10) / 100).toLocaleString(),
      invoiceUrl: Service.getInvoiceEmailURL(invoiceId),
      recipientEmail: foundCustomer.email,
      year: new Date().getFullYear(),
      ...(vat && { vat }),
      ...(dueDate && { dueDate: Utils.formatHumanReadableDate(dueDate) }),
      discount: calculatedDiscount,
      subTotal,
      ...emailPayload,
    });
  },

  /**
   * Customers use this function to claim they have made payment
   * @param {Object} param
   * @param {*} param.filter filter criteria for getting invoice
   * @param {*} param.payload { installments }
   * @returns void
   */
  async claimPayment({ filter, payload }) {
    const foundInvoice = await InvoiceRepo.getSingleInvoice({ filter, addCompany: true });
    if (!foundInvoice) throw new NotFoundError("Invoice");

    const {
      Status: { value },
      InvoiceInstallments = [],
      User: { firstName, lastName, email },
      Company: { name: companyName },
      Customer: { name: customerName },
      currency,
      amount,
    } = foundInvoice;
    // Check that status is pending, overdue or partial paid
    if (![STATUSES.PENDING, STATUSES.OVERDUE, STATUSES.PARTIAL, STATUSES.PROCESSING].includes(foundInvoice.status))
      throw new ValidationError(`Only pending, overdue, partial or processing invoices can be claimed. Invoice status is ${value.toLowerCase()}`);

    const invoiceId = Utils.generatePublicId(foundInvoice.code);
    const emailPayload = {
      invoiceId: invoiceId.substring(invoiceId.length, 14),
      recipientName: firstName,
      createdBy: `${firstName} ${lastName}`,
      companyName,
      customer: customerName,
      paidAction: `${Utils.getDashboardURL()}/receivables/invoices/${foundInvoice.code}/details`,
      denyAction: `${Utils.getDashboardURL()}/receivables/invoices/${foundInvoice.code}/details`,
      currency: Utils.getSymbolFromCurrency(currency),
      amountPaid: Utils.formatMoney(amount),
    };

    if (!InvoiceInstallments.length) return NotificationService.notifyUser({ email }, "payment-claim", emailPayload); // Claims to have paid the full invoice

    const { installments = [] } = payload;
    const foundInstallment =
      InvoiceInstallments.length &&
      InvoiceInstallments.filter(
        (installment) => installments.include(installment.code) && [STATUSES.OVERDUE, STATUSES.PENDING].includes(installment.status)
      );
    if (installments.length !== foundInstallment.length) throw new NotFoundError("One or more Installment not found");
    const totalAmount = foundInstallment.reduce((prev, nextInstallment) => {
      return prev + parseInt(nextInstallment.amount, 10);
    }, 0);

    emailPayload.amountPaid = Utils.formatMoney(totalAmount);
    return NotificationService.notifyUser({ email }, "payment-claim", emailPayload);
  },

  async generatePaymentLink({ filter, payload }) {
    const { installments = [] } = payload;

    const foundInvoice = await InvoiceRepo.getSingleInvoice({ filter, addCompany: true, includePayments: true });
    if (!foundInvoice) throw new NotFoundError("Invoice");

    const {
      Status: { value },
      InvoiceInstallments = [],
      InvoicePayments = [],
      Customer: { name: customerName, email: customerEmail, code: customerCode },
      currency,
      amount,
    } = foundInvoice;

    if (!customerEmail) throw new ValidationError(`You cannot use this payment method as you dont have an email`);
    // Check that status is pending, overdue or partial paid
    if (![STATUSES.PENDING, STATUSES.OVERDUE, STATUSES.PARTIAL].includes(foundInvoice.status))
      throw new ValidationError(`Invoice already ${value.toLowerCase()}`);

    // Get amount already paid
    let totalPaid = 0;
    totalPaid = InvoicePayments?.reduce((prev, nextPayment) => {
      const { Transfer = {} } = nextPayment || {};
      const { amount = 0 } = Transfer;
      return Number(prev) + Number(amount);
    }, totalPaid);

    // Get all pain installment IDS
    const paidInstallments = InvoicePayments?.map((invoicePayment) => invoicePayment.installment) || [];

    let totalInstallmentMarkedAsPaid = 0;
    totalInstallmentMarkedAsPaid = InvoiceInstallments?.reduce((prev, nextInstallment) => {
      const isPaidInstallment = nextInstallment.status === STATUSES.PAID && !paidInstallments.includes(nextInstallment.id);
      return prev + (isPaidInstallment ? nextInstallment.amount : 0);
    }, totalInstallmentMarkedAsPaid);

    totalPaid += totalInstallmentMarkedAsPaid;

    // Check for Installment
    let paymentAmount = amount - parseInt(totalPaid, 10);
    if (installments.length) {
      const foundInstallment = InvoiceInstallments.filter(
        (installment) => installments.include(installment.code) && [STATUSES.OVERDUE, STATUSES.PENDING].includes(installment.status)
      );
      if (installments.length !== foundInstallment.length) throw new NotFoundError("One or more Installment not found");
      paymentAmount = foundInstallment.reduce((prev, nextInstallment) => {
        return prev + parseInt(nextInstallment.amount, 10);
      }, 0);
    }

    const paymentLinkPayload = {
      amount: paymentAmount,
      email: customerEmail,
      currency,
      reference: Utils.generateRandomString(17),
      redirectUrl: `${Utils.getDashboardURL()}/collections/invoices/details/${Utils.generatePublicId(foundInvoice.code)}`,
      company: foundInvoice.company,
      invoice: foundInvoice.code,
      customer: customerCode,
    };

    const { message, data } = await Providers.paystack.payments.generatePaymentLink(paymentLinkPayload);
    return ResponseUtils.sendObjectResponse(message, { url: data.authorization_url });
  },

  async verifyPaymentReference({ filter, payload }) {
    const foundInvoice = await InvoiceRepo.getSingleInvoice({ filter, addCompany: true });
    if (!foundInvoice) throw new NotFoundError("Invoice");

    const { data } = await Providers.paystack.payments.verifyPaymentReference({ ...payload, company: foundInvoice.company });
    const { message, status, gateway_response: gatewayResponse = null } = data;
    if (!status) throw new ValidationError(message);

    return ResponseUtils.sendObjectResponse(message || gatewayResponse, data);
  },

  /**
   * This function automatically creates a tax account for a company
   * @param {*} company The company ID
   */
  async createTaxAccount({ company }) {
    const foundTaxAccount = await BankAccountRepo.getCompanyBalanceByType({
      queryParams: { company, type: "Taxes" },
    });

    if (foundTaxAccount) return;

    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[company] || payment.defaultProvider;

    const accountHolder = await AccountHolderRepo.getAccountHolder({
      filter: { company, provider: CARD_ISSUER[providerToUse] },
      selectOptions: ["externalIdentifier"],
    });

    if (!accountHolder) throw new NotFoundError("Account Holder");

    const { error, data } = await Providers[providerToUse].virtualAccount.createBankAccount(accountHolder.externalIdentifier, company);
    if (error) Providers[providerToUse].throwProviderError(data);

    const { id } = data;
    await RedisService.set(id, JSON.stringify({ type: "Taxes", name: "Tax Account", purpose: "taxes", company }));
  },

  getInvoiceEmailURL(invoiceId) {
    const isProd = Utils.isProd();
    if (!isProd) return `${Utils.getWebsiteURL()}/i/${invoiceId}`;
    return `${Utils.getInvoiceBaseURL()}/${invoiceId}`;
  },

  async syncInvoices({ filter }) {
    const foundInvoices = await InvoiceRepo.fetchInvoices({ filter });

    if (!foundInvoices.length) return [];
    return Promise.all(foundInvoices.map(async (invoice) => syncInvoiceToZoho(invoice.code, filter.company)));
  },
};

module.exports = Service;
