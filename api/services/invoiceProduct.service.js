const { InvoiceProductRepo } = require("../repositories/index.repo");
const ProductService = require("./product.service");
const Utils = require("../utils");

const Service = {
  async createInvoiceProducts(invoice, products) {
    const { id: invoiceId, currency, company } = invoice;
    // Create products
    let productsResponse = [];
    const shouldCreateProducts = !!products[0]?.name;
    if (shouldCreateProducts) {
      const productData = products.map((product) => ({
        name: product.name,
        company,
        currency,
        price: product.unitPrice,
        ...(product.description && { description: product.description }),
      }));
      productsResponse = await ProductService.findOrCreate(productData);
    }
    // Create Invoice Products
    const invoiceProductsData = products.map((singleProduct) => ({
      currency,
      company,
      invoice: invoiceId,
      quantity: singleProduct.quantity,
      ...(singleProduct.discount && { discount: singleProduct.discount }),
      ...(singleProduct.discount_type && { discount_type: singleProduct.discount_type }),
      amount: Utils.calculateProductPrice(singleProduct),
      product: !shouldCreateProducts ? singleProduct.product : productsResponse.find((data) => data.name === singleProduct.name).id,
    }));

    return Service.createBulkInvoiceProducts(invoiceProductsData);
  },

  async createBulkInvoiceProducts(payload) {
    return InvoiceProductRepo.createBulkInvoiceProducts({ payload });
  },
};

module.exports = Service;
