const { CompanyRepo, DocumentRepo } = require("../repositories/index.repo");
const { getStatuses } = require("../repositories/status.repo");
const NotificationService = require("./notification");

const DocumentMap = {
  anchor: {
    CERTIFICATE_OF_BUSINESS_NAME: "cac",
    BN_NUMBER: "bnNumber",
    FORM_CAC_BN_1: "cacBn1",
    PROOF_OF_ADDRESS: "utility-Bill",
    RC_NUMBER: "rcNumber",
    TIN: "tin",
    CERTIFICATE_OF_INCORPORATION: "incorp_C",
    MEMORANDUM_OF_ASSOCIATION: "moa",
    CAC_STATUS_REPORT: "cacStatusReport",
    FORM_CAC_3: "cacForm3",
    FORM_CAC_2: "cacForm2",
    FORM_CAC_7: "cacForm7",
    FORM_CAC_1_1: "cacForm1",
    BVN: "bvn",
  },
};

module.exports = {
  async handleRejectedDocument({ company, documentType, message, provider }) {
    const companyAdmins = (await CompanyRepo.getCompanyWithAdmins({ company })) || {};

    // Update the status of the document
    await DocumentRepo.updateADocument({
      queryParams: {
        reference: companyAdmins.document_reference,
        type: DocumentMap[provider][documentType],
      },
      updateFields: {
        status: getStatuses().REJECTED,
      },
    });

    // notify the customers support team
    NotificationService.notifyUser(
      {
        email: "<EMAIL>",
      },
      "rejected-document",
      {
        message,
        companyName: companyAdmins.name,
        reference: companyAdmins.document_reference,
        documentName: documentType.replace(/_/g, ""),
      }
    );

    const admins = companyAdmins.Users;
    // notify the admins
    admins.forEach((admin) => {
      NotificationService.notifyUser(
        {
          email: admin.email,
        },
        "kyb-rejected",
        {
          message,
          firstName: admin.firstName,
          companyName: companyAdmins.name,
          documentName: documentType.replace(/_/g, ""),
        }
      );
    });
  },
};
