const { industryRepo, CountryRepo, UserRepo } = require("../repositories/index.repo");
const responseUtils = require("../utils/response.utils");
const { ValidationError, HttpException, HttpStatus } = require("../utils/error.utils");
const { MockDataValidator } = require("../validators");
const { BusinessSizeMock, UserIdTypeMock, CompanyTypeMock } = require("../mocks/index");
const Helper = require("./helper.service");

const Service = {
  async getIndustries(id) {
    const { error } = MockDataValidator.auth.validate({ id });
    if (error) throw new ValidationError(error.message);

    await Helper.UserChecker({ id });

    const industries = await industryRepo.getAllIndustries({ selectOptions: ["name"] });
    if (!industries) return responseUtils.BadRequestException("Something went wrong");

    return responseUtils.sendObjectResponse("Industries successfully retrieved", industries);
  },

  async getCountries() {
    const countries = await CountryRepo.getAllCountries({ selectOptions: ["code", "name", "iso_code", "alpha", "currency"] });
    if (!countries) throw new HttpException("Something went wrong", HttpStatus.NOT_FOUND);

    return responseUtils.sendObjectResponse("Countries successfully retrieved", countries);
  },

  async getBusinessSize(id) {
    const { error } = MockDataValidator.auth.validate({ id });
    if (error) throw new ValidationError(error.message);

    await Helper.UserChecker({ id });

    return responseUtils.sendObjectResponse("Business Size successfully retrieved", BusinessSizeMock);
  },

  async getACountry({ country_code }) {
    const { error } = MockDataValidator.getACountry.validate({ country_code });
    if (error) throw new ValidationError(error.message);

    const { data: country } = await Helper.CountryChecker({ code: country_code });

    return responseUtils.sendObjectResponse("Country successfully retrieved", country);
  },

  async getCompanyType(id) {
    const { error } = MockDataValidator.auth.validate({ id });
    if (error) throw new ValidationError(error.message);

    await Helper.UserChecker({ id });

    return responseUtils.sendObjectResponse("Company type successfully retrieved", CompanyTypeMock);
  },

  async userIdType(id) {
    const { error } = MockDataValidator.auth.validate({ id });
    if (error) throw new ValidationError(error.message);

    await Helper.UserChecker({ id });

    return responseUtils.sendObjectResponse("Type of user ID successfully retrieved", UserIdTypeMock);
  },
};

module.exports = Service;
