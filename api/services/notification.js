/* eslint-disable camelcase */
const format = require("date-fns/format");
const mandrill = require("@mailchimp/mailchimp_transactional")(process.env.MANDRILL_KEY);
const logoURL = "https://bujeti.com/images/full_logo.png";
const models = require("../models");
const { ValidationError } = require("../utils/error.utils");
const { NotificationValidator } = require("../validators");
const NotificationRepo = require("../repositories/notification.repo");
const { isProd } = require("../utils/utils");
const responseUtils = require("../utils/response.utils");

const Notifier = {
  sendSMS(phoneNumbers, message) {},

  sendEmail(emails, template, variables, senderConfig = {}, attachments = []) {
    const templateContent = Object.keys(variables).map((key) => ({ name: key, content: variables[key] }));
    const to = Array.isArray(emails) ? emails.map((email) => ({ email, type: "to" })) : [{ email: emails, type: "to" }];
    if (senderConfig && senderConfig.subject && !senderConfig.subject.startsWith("[Staging]")) {
      senderConfig.subject = `${!isProd() ? "[Staging] " : ""}${senderConfig.subject}`;
    }
    const body = {
      template_name: template,
      template_content: [],
      message: {
        to,
        global_merge_vars: templateContent,
        ...senderConfig,
        ...(attachments.length && { attachments }),
      },
    };
    mandrill.messages.sendTemplate(body);
  },

  notifyUser({ email, phoneNumber }, template, payload, sourceConfig = {}, attachments = []) {
    const finalPayload = {
      ...payload,
      year: new Date().getFullYear(),
      sendingDate: format(new Date(), "MMMM dd, yyyy"),
      logo: logoURL,
    };
    if (sourceConfig && sourceConfig.subject) {
      sourceConfig.subject = `${!isProd() ? "[Staging] " : ""}${sourceConfig.subject}`;
    }
    if (email) {
      const emails = Array.isArray(email) ? email : [email];
      Notifier.sendEmail(emails, template, finalPayload, sourceConfig, attachments);
    }
    Notifier.sendSMS([phoneNumber], template, payload);
  },

  /**
   * @description send a push notification
   * Requirements
   * @type {
   * [{
   *      user_id: number,
   *      event: string,
   *      body: {
   *          title: string,
   *          body: string,
   *          entity: string,
   *          code: string,
   *          message: string,
   *          badge: string,
   *      }
   *  }]
   * }
   * @return void
   * <AUTHOR> - created
   */
  async sendPushNotification(user_id, event, { title, body, entity, code, message, badge }) {
    const { error } = NotificationValidator.verify.validate({ user_id, event, body: { title, body, entity, code, message, badge } });
    if (error) throw new ValidationError(error.message);

    PusherIntegrator.channel_trigger(user_id, event, {
      title,
      ...(body && { body }),
      ...(entity && { entity }),
      ...(code && { code }),
      ...(badge && { badge }),
      ...(company_id && { company_id }),
      message,
    });
  },

  /**
   * @description save a notifications
   * Requirements
   * @type {
   * [{
   *      user_id: number,
   *      type: string,
   *      message: string,
   *      badge: string,
   *      title: string,
   *      body: string,
   *      table: {
   *          entity: string,
   *          code: string,
   *      }
   *  }]
   * }
   * @return void
   * <AUTHOR> - created
   */
  async saveNotification({ company, user_id, type, title, body, reference_code, message, badge, event, table: { entity = null, code = null } }) {
    const { error } = NotificationValidator.saveNotification.validate({
      user_id,
      type,
      title,
      body,
      reference_code,
      message,
      badge,
      event,
      table: { entity, code },
    });
    if (error) throw new ValidationError(error.message);
    let table_id = null;
    if (code && entity) {
      const foundEntity = await models[entity].findOne({
        where: { code },
        attributes: ["id"],
      });
      table_id = foundEntity && foundEntity.id;
    }

    NotificationRepo.saveNotification({
      queryParams: {
        user: user_id,
        type,
        title,
        badge,
        body: JSON.stringify({
          message,
          body,
        }),
        reference_code,
        company,
        table: entity,
        table_id,
      },
    });
  },

  /**
   * @description this fetches all notifications for a user
   * Requirements
   * @type {user_id: number},
   * @return void
   * <AUTHOR> - created
   */
  async getNotifications(user_id) {
    const { error } = NotificationValidator.userChecker.validate({ user_id });
    if (error) throw new ValidationError(error.message);

    return NotificationRepo.getAllNotifications({
      queryParams: { id: user_id },
    });
  },

  /**
   * @description this updates the state of one notification
   * Requirements
   * @type { user_id: number, code: string },
   * @return void
   * <AUTHOR> - created
   */
  async markNotificationAsRead(user_id, code) {
    const { error } = NotificationValidator.markANotification.validate({ user_id, code });
    if (error) throw new ValidationError(error.message);

    return NotificationRepo.markNotificationAsRead({ user: user_id, code });
  },

  /**
   * @description this updates the state of one or more notification
   * Requirements
   * @type { user_id: number },
   * @return void
   * <AUTHOR> - created
   */
  async markAllNotificationAsRead(user_id) {
    const { error } = NotificationValidator.userChecker.validate({ user_id });
    if (error) throw new ValidationError(error.message);

    return NotificationRepo.markAllNotificationAsRead({ user: user_id });
  },

  async listNotifications(filter) {
    return NotificationRepo.listNotificationWithCount({
      filter,
    });
  },

  async updateNotification(criteria, payload) {
    return NotificationRepo.updateNotification({ criteria, payload });
  },

  async sendNotification(notificationPayload) {
    const { payload, template, recipients, type = "email", ...rest } = notificationPayload;
    if (type !== "email") return responseUtils.BadRequestException("Only email notification is currently supported");
    const emailRecipients = recipients.map((recipient) => {
      if (typeof recipient === "object") return recipient.email || false;
      return recipient;
    });
    const recipientCheck = Array.from(emailRecipients).every(Boolean);
    if (!recipientCheck) throw new ValidationError("One or more invalid recipient object");
    Notifier.sendEmail(emailRecipients, template, payload, { ...rest });
    return responseUtils.sendObjectResponse("Notification sent successfully");
  },
};

module.exports = Notifier;
