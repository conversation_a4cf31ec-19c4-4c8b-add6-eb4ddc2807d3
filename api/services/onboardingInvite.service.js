const { Op } = require("sequelize");
const Utils = require("../utils");
const { STATUSES } = require("../models/status");
const { OnboardingInviteRepo, DocumentRepo } = require("../repositories/index.repo");
const { ExistsError, ValidationError, NotFoundError } = require("../utils/error.utils");
const NotificationService = require('./notification');
const CompanyService = require('./company')
const RedisService = require('./redis');
const responseUtils = require("../utils/response.utils");
const Service = {
    
    /***
     * Creates a new Onboarding invite and sends an invitation link
     * @param {Object} body
     * @param String body.name Name of the Invitee
     * @param String body.email Email of the Invitee
     * @param Array[] body.fields Fields assigned to thiis invitee to feel
     * @param String body.note Customized note for the Invitee
     * @param Number body.company The company the Invitee belongs to
     */
    async create(body) {
        const isExisting = await OnboardingInviteRepo.getOnboardingInvite({ queryParams: { email: body.email, company: body.company, status: { [Op.notIn]: [STATUSES.INACTIVE, STATUSES.DELETED] } } });
        if(isExisting) throw new ValidationError(`${Utils.toTitle(isExisting.name)} has already been invited`);

        const { fields, company } = body;
        if(fields) {
            const queryParams = { company };
            queryParams[Op.or] = [];
            Array.from(fields).map(field => queryParams[Op.or].push( { fields: { [Op.like]: `%${field}%` } } ));
            const foundExistingInvite = await OnboardingInviteRepo.getOnboardingInvite({ queryParams })
            if(foundExistingInvite) throw new ValidationError('One or more of this field has already been assigned to another invitee');
            body.fields = fields && Array.from(fields).join(',');
        }

        body.hash = Utils.generateRandomString(17)

        const invitee = await OnboardingInviteRepo.create({ payload: body });
        // Save to redis
        RedisService.set(body.hash, JSON.stringify(invitee));

        const companyDetails = await CompanyService.getCompanyWithAdmins({ id: body.company });

        const notificationPayload = {
            name: invitee.name,
            note: body.note,
            companyName: Utils.toTitle(companyDetails.name),
            adminName: Utils.toTitle(`${companyDetails.Users[0].firstName} ${companyDetails.Users[0].lastName}`),
            onboardingUrl: `${Utils.getDashboardURL()}/complete-onboarding/${invitee.hash}`
        }
        NotificationService.notifyUser(invitee, 'invite-director', notificationPayload, { subject: `${Utils.toTitle(companyDetails.name)} onboarding process on Bujeti` });
        return invitee
    },

    /**
     * List all invitees
     */
     async list(query){
        const { company, status } = query;
        const filter = { company };
        
        if(status) filter.status = Utils.getStatusValues(status);
        
        return OnboardingInviteRepo.getOnboardingInvites({ queryParams: filter })
    },

    /**
     * View a single invite
     */
     async view(query) {
        const { hash, company } = query;
        const filter  = {  company, status: { [Op.notIn]: [STATUSES.DELETED, STATUSES.INACTIVE] } };
        String(hash).startsWith('oiv') ? filter.code = hash : filter.hash = hash

        // Get invite
        const invite = await OnboardingInviteRepo.getOnboardingInvite({ queryParams: filter });
        if(!invite) throw new NotFoundError('Invite');

        let { fields, Company: { document_reference } } = invite;
        if(fields) {
            fields = String(fields).split(',');
            const documentFilter = { type: fields, ...(document_reference && { reference: document_reference }) };

            const documents = await DocumentRepo.getAllDocuments({ queryParams: documentFilter, selectOptions: ['type', 'number', 'url', 'reference'] })
            fields = Array.from(fields).map(field => {
                const document = documents && Array.from(documents).find(document => document.type === field)
                if(!document) return { field };
                const { number, url, reference } = document;
                return {
                    field,
                    ...(number && { number } ),
                    ...(reference && { reference }),
                    ...(url && { url } )
                }
            })
        }
        
        
        return { invite, fields }
    },

    /**
     * Deletes an invite by code
     */
     async removeInvite(query) {
        const invite = await OnboardingInviteRepo.getOnboardingInvite({ queryParams: query });
        if(!invite) throw new NotFoundError('Invite');
        const deletePayload = { status: STATUSES.DELETED };
        await OnboardingInviteRepo.update({ queryParams: { id: invite.id }, payload: deletePayload });
        
        return responseUtils.sendObjectResponse(`${Utils.toTitle(invite.name)} has been deleted successfully`)
    },

    /**
     * Updates an Invitee
     */
    async updateInvite(query, payload){
        const invite = await OnboardingInviteRepo.getOnboardingInvite({ queryParams: query });
        if(!invite) throw new NotFoundError('Invite');

        const { fields } = payload;

        if(fields){
            const queryParams = { company: query.company, code: { [Op.ne]: query.code } };
            queryParams[Op.or] = [];
            Array.from(fields).map(field => queryParams[Op.or].push( { fields: { [Op.like]: `%${field}%` } } ));
            const foundExistingInvite = await OnboardingInviteRepo.getOnboardingInvite({ queryParams })
            if(foundExistingInvite) throw new ValidationError('One or more of this field has already been assigned to another invitee');
            let { fields: existingFields } = invite;
            existingFields = existingFields && String(existingFields).split(',') || [];
            payload.fields = [...new Set([...existingFields, ...fields])].join(',');
        }
        await OnboardingInviteRepo.update({ queryParams: { id: invite.id }, payload })
        return responseUtils.sendObjectResponse(`Invite updated successfully`)
    },

    async resendDirectorInvite(code) {
        const invite = await OnboardingInviteRepo.getOnboardingInvite({ queryParams: { code } });
        if(!invite) throw new NotFoundError('Invite');

        const companyDetails = await CompanyService.getCompanyWithAdmins({ id: invite.company });

        const notificationPayload = {
            name: invite.name,
            note: invite.note,
            companyName: Utils.toTitle(companyDetails.name),
            adminName: Utils.toTitle(`${companyDetails.Users[0].firstName} ${companyDetails.Users[0].lastName}`),
            onboardingUrl: `${Utils.getDashboardURL()}/complete-onboarding/${invite.hash}`
        }
        NotificationService.notifyUser(invite, 'invite-director', notificationPayload, { subject: `${Utils.toTitle(companyDetails.name)} onboarding process on Bujeti` });
        return invite;
    }
}

module.exports = Service