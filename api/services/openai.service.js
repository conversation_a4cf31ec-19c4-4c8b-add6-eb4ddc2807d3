const { Configuration, OpenAI } = require("openai");
const axios = require("axios");
const fs = require("fs");
const path = require("path");
const pdfParse = require("pdf-parse");
const sharp = require("sharp");
const { createWorker } = require("tesseract.js");
const { getAssetDownloadURL } = require("./asset");
const { isProd, ensureDirectoryExistence } = require("../utils");
require("dotenv").config();

const Tesseract = createWorker();

async function runOCR(filePath) {
  await Tesseract.load();
  await Tesseract.loadLanguage("eng");
  await Tesseract.initialize("eng");

  const grayScaleFile = `${filePath}cleaned.png`;
  await sharp.grayscale().normalize().toFile(grayScaleFile);

  const { data } = await Tesseract.recognize(grayScaleFile);
  await Tesseract.terminate();
  // eslint-disable-next-line no-use-before-define
  deleteFile(grayScaleFile);
  return data?.text;
}

const MOCKED_DATA = [
  {
    invoiceNumber: "AFÁRÁ-000073",
    invoiceDate: "04 Oct 2022",
    dueDate: "04 Oct 2022",
    billTo: "Afárá Group, 19 Betty Pride Way, Off Asa-Fariogun Road, Ajao Estate, Lagos, Nigeria",
    billFrom: "BLENT",
    shipTo: "",
    totalAmount: "NGN240,000.00",
    totalBeforeTax: "NGN220,000.00",
    taxAmount: "20,000.00",
    lineItems: [
      {
        description: "Website Development",
        quantity: 1,
        price: 220000,
        total: 220000,
      },
    ],
    currency: "NGN",
    paymentTerms: "Due on Receipt",
    bankName: "STERLING BANK",
    bankAccountNumber: "**********",
    bankAccountRoutingNumber: "",
    discount: "5000",
    purchaseOrderNumber: "",
    contactInformation: "N/A",
    paymentLink: "",
    taxIdentificationNumber: "",
    businessRegistrationNumber: "",
  },
  {
    invoiceNumber: "AFÁRÁ-000073",
    invoiceDate: "04 Oct 2022",
    dueDate: "04 Oct 2022",
    billTo: {
      name: "BLENT",
      address: "Not provided",
    },
    shipTo: {
      name: "Afárá Group",
      address: "19 Betty Pride Way, Off Asa-Fariogun Road, Ajao Estate, Lagos, Nigeria.",
    },
    totalAmount: "NGN220,000.00",
    taxAmount: "Not specified",
    lineItems: [
      {
        description: "Website Development",
        quantity: 1,
        price: 220000,
        total: 220000,
      },
    ],
  },
  {},
  {
    invoiceNumber: "AFÁRÁ-000073",
    invoiceDate: "04 Oct 2022",
    dueDate: "04 Oct 2022",
    billTo: "Afárá Group, 19 Betty Pride Way, Off Asa-Fariogun Road, Ajao Estate, Lagos, Nigeria",
    billFrom: "BLENT",
    shipTo: "",
    totalAmount: "NGN220,000.00",
    totalBeforeTax: "NGN220,000.00",
    taxAmount: "",
    lineItems: [
      {
        description: "Website Development",
        quantity: 1,
        price: 220000,
        total: 220000,
      },
    ],
    currency: "NGN",
    paymentTerms: "Due on Receipt",
    bankName: "STERLING BANK",
    bankAccountNumber: "**********",
    bankAccountRoutingNumber: "",
    discount: "10%",
    purchaseOrderNumber: "PO908977",
    contactInformation: "N/A",
    paymentLink: "",
    taxIdentificationNumber: "*************",
    businessRegistrationNumber: "RC4345455445",
  },
];

const PROMPTS = {
  invoices: (body, fileContents) => {
    return `
  You are a Financial OCR and AI software specialized in data extraction. You will receive an email containing multiple attached files, which may include invoices or receipts.
    Extract the following details from this invoice and return them as a JSON object, isSubscription is a boolean that is true
    when the invoice is for a subscription, recurring payment, and false when it is a one off invoice, subtotal or subtotal tax excluded are the same
    total tax included and total amount are the same:
      
    - invoiceNumber
    - invoiceDate
    - dueDate
    - billTo (name, address, email)
    - billFrom (name, address, email, website)
    - shipTo (name, address, email)
    - totalAmount
    - balanceDue
    - totalExcludedTax
    - taxAmount
    - lineItems (description, quantity, price, total)
    - currency
    - paymentTerms
    - bankName
    - bankAccountNumber
    - bankAccountRoutingNumber
    - discount
    - purchaseOrderNumber
    - contactInformation
    - paymentLink
    - taxIdentificationNumber
    - businessRegistrationNumber
    - swiftOrBic
    - isSubscription
  
    Body: ${body}
    ${fileContents || ""}

    For amounts, only retrieve the numerical values
    For currency use the ISO codes
    Put dates in US format
    If a field is missing or ambiguous, return 'N/A' for that field
    The invoice might contain information in multiple languages. Extract the following fields, regardless of language
    Maintain the JSON structure even if no valid data is detected
    Please return the details in JSON format. Just the json object no extra characters needed. Make sure you double check
    `;
  },
  subscriptions: (body, fileContents) => {
    return `
    You are a Financial OCR and AI software specialized in data extraction. You will receive an email containing multiple attached files, which are receipts for SaaS payments.
    Extract the following details from this email or file and return them as a JSON object, subtotal or subtotal tax excluded are the same
    total tax included and total amount are the same:
    
    - invoiceID
    - billingDate
    - startDate
    - endDate
    - total
    - amount
    - totalBeforeTax
    - taxAmount
    - seats
    - merchantName
    - merchantAddress
    - merchantEmail
    - merchantWebsite
    - planName
    - planAmount
    - cancellationPolicyURL
    - billingFrequency
    - currency
  
  Body: ${body}
  ${fileContents || ""}
  
    These will be coming from known SaaS providers so be sure you get the usual names for example LinkedIn SN is just LinkedIn
    For amounts, only retrieve the numerical values
    For currency use the ISO codes
    Put dates in US format
    If a field is missing or ambiguous, return 'N/A' for that field
    The invoice might contain information in multiple languages. Extract the following fields, regardless of language
    Maintain the JSON structure even if no valid data is detected
    Please return the details in JSON format. Just the json object no extra characters needed. Make sure you double check`;
  },
};

// Load your OpenAI API key from an environment variable
// Set up OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || "dfdfd",
});

// Step 1: Download the invoice file from a remote URL
const downloadInvoice = async (url, outputPath) => {
  ensureDirectoryExistence(outputPath);
  const writer = fs.createWriteStream(outputPath);

  const response = await axios({
    url,
    method: "GET",
    responseType: "stream",
  });

  response.data.pipe(writer);

  return new Promise((resolve, reject) => {
    writer.on("finish", resolve);
    writer.on("error", reject);
  });
};

async function sendToOpenAI(body, fileContents, feature = "invoices") {
  if (!PROMPTS[feature]) return;

  const prompt = PROMPTS[feature](body, fileContents);

  const { choices = [] } = await openai.chat.completions.create({
    model: "gpt-3.5-turbo",
    messages: [{ role: "user", content: prompt }],
  });
  // eslint-disable-next-line no-unused-vars
  const [response = {}, _] = choices;

  // eslint-disable-next-line consistent-return
  return response?.message?.content || {};
}

async function readPdfContent(filePath) {
  const dataBuffer = fs.readFileSync(filePath);
  const parsedData = await pdfParse(dataBuffer);
  if (parsedData.text) return parsedData.text; // Extracted text from PDF

  // increase contrast

  const grayScaleFile = `${filePath}cleaned.png`;
  await sharp.grayscale().normalize().toFile(grayScaleFile);

  // read again

  const { data } = await Tesseract.recognize(grayScaleFile);
  await Tesseract.terminate();
  // eslint-disable-next-line no-use-before-define
  deleteFile(grayScaleFile);
  return data?.text;
}

async function deleteFile(filePath) {
  fs.unlink(filePath, () => {});
}

module.exports = {
  async parseInvoice(asset) {
    if (!isProd()) {
      return MOCKED_DATA[Math.round(Math.random() * 100) % 4];
    }
    const remoteUrl = await getAssetDownloadURL(asset);
    // get content
    const outputPath = path.join(".tmp/", asset.key);
    await downloadInvoice(remoteUrl, outputPath);
    let parsedContent = null;
    // parse content
    if (asset.type === "pdf") {
      parsedContent = await readPdfContent(outputPath);
    } else if (["png", "jpg", "jpeg"].includes(asset.type)) {
      parsedContent = await runOCR(outputPath);
    }

    if (parsedContent) {
      // scan content openai
      const openaiResponse = await sendToOpenAI(parsedContent);
      // return json
      deleteFile(outputPath);
      return JSON.parse(openaiResponse);
    }
    deleteFile(outputPath);
    return {};
  },
};
