const Utils = require("../utils");
const NotificationService = require("./notification");
const RedisService = require("./redis");
const { Otp: OTP, User, Role, Company, Status } = require("../models");
const { STATUSES } = require("../models/status");
const ResponseUtils = require("../utils/response.utils");
const SettingService = require("./settings");
const TooManyConnectionError = require("../utils/too-many-log-in-error");
const { md5 } = require("../utils");
const { ForbiddenError, NotFoundError } = require("../utils/error.utils");
const AuthenticationService = require("./authentication");

module.exports = {
  async createOTP(user) {
    return OTP.create({
      user: user.id,
      hash: Utils.generateUniqueId(Utils.alphabet.alphanumeric, 24),
      code: Utils.generateUniqueId(Utils.alphabet.digits, 6),
      is_used: 0,
    });
  },

  async sendVerificationCode({ first_name: firstName = "there", email, phoneNumber }, { code, hash }, template = "otp-verification") {
    return NotificationService.notifyUser({ email, phoneNumber }, template, { firstName, code: code.split(""), hash });
  },

  async verify({ hash, code }) {
    if (!(hash && code)) {
      return { code: 400, message: "Invalid payload, must specify hash and code" };
    }
    if (String(code).length !== 6) {
      return {
        code: 400,
        message: "Invalid code",
      };
    }
    const foundOtp = await OTP.findOne({
      where: { hash, code, is_used: 0 },
      include: {
        model: User,
        attributes: ["id", "firstName", "lastName", "code"],
      },
    });
    if (!foundOtp) {
      return {
        code: 400,
        message: "Invalid code, try again or request a new code",
      };
    }
    await Promise.all([
      OTP.update({ is_used: 1 }, { where: { hash, code } }),
      User.update({ status: STATUSES.ACTIVE }, { where: { id: foundOtp.User.id } }),
    ]);
    return {
      code: 200,
      message: "Verification successful",
      user: foundOtp.User,
    };
  },

  async expireOldOTPs(user) {
    return OTP.update({ is_used: 1 }, { where: { user: user.id, is_used: 0 } });
  },

  async expireOldOTPRedis(user) {
    const otp = await RedisService.get(user.id);
    if (otp) await RedisService.delete(user.id);
    return {
      code: 200,
      message: `Previous OTP's expired`,
    };
  },

  async createOTPRedisByID(user) {
    const code = Utils.generateUniqueId(Utils.alphabet.digits, 6);
    const hash = Utils.generateUniqueId(Utils.alphabet.digits, 24);
    await RedisService.hmset(user.id, { hash, code });
    await RedisService.expiresIn(user.id, 60 * 10);
    return {
      userId: user.id,
      hash,
      code,
    };
  },

  async createOTPRedisCode(payload) {
    let code;
    if (payload.email === "<EMAIL>") code = "101010";
    else code = Utils.generateUniqueId(Utils.alphabet.digits, 6);
    const hash = Utils.generateUniqueId(Utils.alphabet.digits, 24);
    await RedisService.setex(hash, JSON.stringify({ hash, code, entityCode: payload.code, trial: 0 }), 300); // Made otp to expire in 5 minutes
    await RedisService.setex(`otp:${payload.email}`, 1, 300); // Save the user email for the same duration as the OTP
    return {
      entityCode: payload.code,
      hash,
      code,
    };
  },

  async verifyRedis({ hash, code }, reason = null) {
    if (!(hash && code)) return { code: 400, message: "Invalid payload, must specify hash and code" };
    if (String(code).length !== 6)
      return {
        code: 400,
        message: "Invalid code",
      };

    const otp = JSON.parse(await RedisService.get(hash));

    if (!otp)
      return {
        code: 400,
        message: "Invalid code, try again or request a new code",
      };

    const { trial, code: originalCode, entityCode } = otp;

    const user = await User.findOne({ where: { code: otp.entityCode }, include: [Company, Role, Status] });
    if (!user) return { code: 400, message: "Invalid code, try again or request a new code" };

    const isValidCode = originalCode === code;

    if (!isValidCode) {
      if (trial + 1 >= SettingService.get("MAX_OTP_TRIAL")) {
        const foundUser = await User.findOne({ where: { code: entityCode }, attributes: ["email", "code"] });
        NotificationService.notifyUser({ email: foundUser.email }, "login-attempt");
        const key = `auth:${foundUser.email}`;
        await RedisService.setex(key, 3, 3600); // Blocks the account for 1hr
        RedisService.delete(hash); // delete the hash so it;s no longer usable
        throw new TooManyConnectionError();
      }
      const remainingTime = await RedisService.getTTl(hash);
      RedisService.setex(hash, JSON.stringify({ ...otp, trial: trial + 1 }), remainingTime);

      return {
        code: 400,
        message: "Invalid code, try again or request a new code",
      };
    }
    await RedisService.delete(hash);
    if (reason) return ResponseUtils.sendObjectResponse("OTP verified", otp);

    // if(user.status !== STATUSES.ACTIVE) await User.update({ status: STATUSES.ACTIVE }, { where: { id: user.id } })
    return {
      code: 200,
      message: "Verification successful",
      user,
    };
  },
};
