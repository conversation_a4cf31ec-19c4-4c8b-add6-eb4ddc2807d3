const axios = require("axios");
const { literal } = require("sequelize");
const { format } = require("date-fns");
const ThirdPartyLogService = require("./thirdPartyLog");
const { ValidationError, NotFoundError } = require("../utils/error.utils");
const CustomerRepo = require("../repositories/customer.repo");
const InvoiceRepo = require("../repositories/invoice.repo");
const InvoiceInstallmentRepo = require("../repositories/invoiceInstallment.repo");
const PendingSettlementRepo = require("../repositories/pendingSettlement.repo");
const TransferRepo = require("../repositories/transfer.repo");
const { STATUSES } = require("../models/status");
const Utils = require("../utils/index");
const ScheduleService = require("./schedule.service");
const { PAYSTACK_PAYMENT_CHANNELS, PAYSTACK_WEBHOOK_EVENTS, PROVIDER_PRODUCTS, BLNK_INFLIGHT_STATUS } = require("../mocks/constants.mock");
const { CARD_ISSUER } = require("../models/cardissuer");
const {
  BankAccountRepo,
  CompanyRepo,
  BalanceRepo,
  LedgerIdentityRepo,
  TransactionAttemptRepo,
  TransactionRepo,
  BalanceLedgerRepo,
  UserRepo,
  ReimbursementRepo,
} = require("../repositories/index.repo");
const RedisService = require("./redis");
const { Log, log } = require("../utils/logger.utils");
const HelperService = require("./helper.service");
const { ONBOARDING_LEVEL } = require("../models/company");
const Helper = require("./helper.service");
const { getDashboardURL } = require("../utils/index");
const ApprovalService = require("./approval.service");
const { Banks } = require("../mocks/banks.mock");

const httpClient = {};
axios.defaults.timeout = 300000;

const requestHandler = async (request) => {
  try {
    const { data: response, status = 200, config } = await request;
    const { message, data } = response;

    return {
      error: false,
      message: (data && data.message) || message,
      body: data || response,
      data,
      status,
      config: {
        url: `${config?.baseURL}${config.url}`,
        method: config.method,
        data: config.data,
      },
    };
  } catch (error) {
    const { response, message, status, config } = error;
    const { status: statusCode = 400, data: errorData = {} } = response || {};

    return {
      message: errorData?.message || message,
      status: status || statusCode,
      data: errorData,
      body: errorData,
      error: true,
      config: {
        url: `${config.baseURL}${config.url}`,
        method: config.method,
        data: config.data,
      },
    };
  }
};

const handleTransferFailed = async (data, Providers) => {
  const { reference, reason: failureReason, session: { id: sessionId } = {}, transfer_code: paystackReference } = data;
  // Log payload
  ThirdPartyLogService.createLog({
    message: "Paystack Transfer failed",
    company: -1,
    event: "paystack.transfer.failed",
    payload: JSON.stringify(data),
    provider: CARD_ISSUER.paystack,
    providerType: "issuer",
    response: JSON.stringify({ message: "OK" }),
    statusCode: 200,
    endpoint: `${Utils.getApiURL()}/webhook/paystack`,
    method: "POST",
  });

  // Check if it is payment
  const foundTransaction = await TransactionRepo.getTransaction({
    queryParams: { code: reference },
    bank: true,
    vendor: true,
    company: true,
    budget: true,
  });

  if (!foundTransaction) {
    // Mark Transfer as failed
    await TransferRepo.update({ filter: { reference }, payload: { status: STATUSES.FAILED } });
    // Mark settlement as failed
    await PendingSettlementRepo.updatePendingSettlement({ filter: { reference }, payload: { status: STATUSES.FAILED } });
    // Notify engineers and support of failed
    const foundSettlement = await PendingSettlementRepo.getPendingSettlement({ filter: { reference } });
    if (!foundSettlement) throw new NotFoundError("Pending Settlement");

    throw new ValidationError(`Setlement with id ${foundSettlement.code} failed`);
  }

  const lockTTL = 5000; // 5 seconds
  const redLock = await RedisService.getRedLock();
  let lock;

  try {
    lock = await redLock.acquire([`lock:transaction:${foundTransaction.id}`], lockTTL);

    const existingAttempt = await TransactionAttemptRepo.getTransactionAttempt({
      filter: { externalIdentifier: paystackReference, status: STATUSES.PENDING },
    });

    if (!existingAttempt) {
      return existingAttempt;
    }

    const providerHandler = await HelperService.getInternalLedgerProviderHandler(foundTransaction, Providers);

    // void the transaction on ledger
    const { error: updateInflightTransactionError } = await providerHandler.transactions.updateInflightTransaction({
      transactionId: foundTransaction.externalIdentifier,
      payload: {
        status: BLNK_INFLIGHT_STATUS.VOID,
      },
      company: foundTransaction.company,
    });

    if (updateInflightTransactionError) {
      throw new ValidationError("Failed to void transaction on ledger");
    }

    await TransactionAttemptRepo.updateTransactionAttempt({
      filter: { externalIdentifier: paystackReference },
      payload: { status: STATUSES.FAILED },
    });

    const transactionUpdateData = {
      status: STATUSES.FAILED,
      failure_reason: failureReason,
    };

    const { company, code } = foundTransaction;

    await TransactionRepo.updateTransaction({
      queryParams: { company, code },
      updateFields: { ...transactionUpdateData, ...(sessionId && { sessionId }) },
    });

    const balanceCriteria = {
      company,
      transaction: foundTransaction.id,
      status: STATUSES.PENDING,
    };
    BalanceLedgerRepo.logFailedTransfer({ criteria: balanceCriteria, reason: failureReason });

    const reason = Helper.deduceReasonForTransaction(foundTransaction);

    Helper.processBillingViaQueue({ reason, foundTransaction, company });

    Helper.finalizeBillPayment(reason, company, foundTransaction, STATUSES.FAILED);
  } finally {
    log(Log.fg.blue, `Lock released for transaction: ${foundTransaction.id}`);
    if (lock) await redLock.unlock(lock);
  }

  return foundTransaction;
};

const handleTransferSuccessful = async (data, Providers) => {
  const { reference, session: { id: sessionId } = {}, recipient: details = {}, transfer_code: paystackReference } = data;
  // Log payload
  ThirdPartyLogService.createLog({
    message: "Paystack Transfer successful",
    company: -1,
    event: "paystack.transfer.successful",
    payload: JSON.stringify(data),
    provider: CARD_ISSUER.paystack,
    providerType: "issuer",
    response: JSON.stringify({ message: "OK" }),
    statusCode: 200,
    endpoint: `${Utils.getApiURL()}/webhook/paystack`,
    method: "POST",
  });
  // Check if it is payment
  const foundTransaction = await TransactionRepo.getTransaction({
    queryParams: { code: reference },
    bank: true,
    vendor: true,
    company: true,
    budget: true,
  });

  if (!foundTransaction) {
    // Mark Transfer as Successfull
    const foundTransfer = await TransferRepo.getTransfer({ filter: { reference, status: STATUSES.PENDING } });
    if (!foundTransfer) throw new NotFoundError("Transfer");

    await TransferRepo.update({ filter: { reference }, payload: { status: STATUSES.SUCCESS } });
    // Mark settlement as successfull
    return PendingSettlementRepo.updatePendingSettlement({ filter: { reference }, payload: { status: STATUSES.PROCESSED } });
  }

  const lockTTL = 5000; // 5 seconds
  const redLock = await RedisService.getRedLock();
  let lock;

  try {
    lock = await redLock.acquire([`lock:transaction:${foundTransaction.id}`], lockTTL);
    const existingAttempt = await TransactionAttemptRepo.getTransactionAttempt({
      filter: { externalIdentifier: paystackReference, status: STATUSES.PENDING },
    });

    if (!existingAttempt) {
      return existingAttempt;
    }

    const foundCompany = await CompanyRepo.getCompany({
      queryParams: {
        id: foundTransaction.company,
      },
    });

    const { company, code: transactionCode, currency, description, amount, narration } = foundTransaction;

    const { onboardingLevel } = foundCompany;

    const provider = HelperService.getProvider(foundCompany.id, PROVIDER_PRODUCTS.LEDGER);

    const providerHandler = await Providers[provider];

    const foundLedgerIdentity = await LedgerIdentityRepo.getLedgerIdentity({
      company,
      issuer: CARD_ISSUER.Blnk,
    });

    if (!foundLedgerIdentity) throw new NotFoundError("Ledger Identity");

    // commit the transaction on ledger
    const { error: updateInflightTransactionError } = await providerHandler.transactions.updateInflightTransaction({
      transactionId: foundTransaction.externalIdentifier,
      payload: {
        status: BLNK_INFLIGHT_STATUS.COMMIT,
      },
      company,
    });

    if (updateInflightTransactionError) {
      throw new ValidationError("Failed to commit transaction on ledger");
    }

    await TransactionAttemptRepo.updateTransactionAttempt({
      filter: { externalIdentifier: paystackReference },
      payload: { status: STATUSES.SUCCESS },
    });
    await TransactionRepo.updateTransaction({
      queryParams: { code: reference },
      updateFields: {
        status: STATUSES.SUCCESS,
        failure_reason: null,
        paidOn: literal("CURRENT_TIMESTAMP"),
        ...(sessionId && { sessionId }),
      },
    });

    // charge fees
    const foundBalance = await BalanceRepo.getBalance({
      filter: { currency, company, status: STATUSES.ACTIVE, id: foundTransaction.balance },
      includeAccount: true,
    });

    const outflowPayload = {
      amount: Utils.formatAmount(Utils.money(foundTransaction.processor_fee).plus(foundTransaction.bujeti_fee).toNumber()),
      description: description || narration,
      currency,
      precision: 100,
      reference: `fee_${transactionCode}`,
      source: foundBalance.BankAccount.externalIdentifier,
      destination: `@PayoutFees${currency.toUpperCase()}`,
      allow_overdraft: false,
      inflight: false,
      skip_queue: true, // debit immediately
      meta_data: {
        transaction: foundTransaction.code,
      },
    };

    const { error: recordTransactionError } = await providerHandler.transactions.recordTransaction(outflowPayload, foundCompany.id);

    if (onboardingLevel !== ONBOARDING_LEVEL.LEVEL_3) {
      const newAmountSpent = Utils.money(foundTransaction.amount)
        .plus(foundTransaction.processor_fee || 0)
        .plus(foundTransaction.bujeti_fee || 0)
        .toNumber();
      await CompanyRepo.updateCompanySpent({
        action: "increment",
        filter: { id: foundTransaction.company },
        amount: newAmountSpent,
      });
    }

    Helper.makeBatchTransactionsSuccessful(foundTransaction.batch_id);

    await BalanceLedgerRepo.recordSuccessfulTransfer(foundTransaction);

    Helper.handleCategorySpent(foundTransaction);

    const reason = Helper.deduceReasonForTransaction(foundTransaction);

    await Helper.finalizeCardRequestCharge({ reason, transaction: foundTransaction, company });

    Helper.processBillingViaQueue({ reason, transaction: foundTransaction, company });

    Helper.finalizeSubscriptionAddons({ reason, transaction: foundTransaction, company });

    const payer = await UserRepo.fetchUser(foundTransaction.payer);

    let email;
    const companyWithAdmin = await CompanyRepo.getCompanyWithAdmins({ id: company }, true);

    if (payer) {
      const { firstName, ...remainingPayerData } = payer.toJSON();
      email = remainingPayerData.email;

      const emailPayload = {
        dashboardUrl: `${getDashboardURL()}/transactions/${transactionCode}`,
        accountNumber: details?.account_number,
        bankName: details?.bank_name,
        amount: (parseInt(amount, 10) / 100).toLocaleString(),
        currency,
        description,
        paidOn: Utils.formatHumanReadableDate(foundTransaction.created_at),
        paidBy: `${foundTransaction.User.firstName} ${foundTransaction.User.lastName}`,
        paidTo: `${foundTransaction.BankAccount.accountName}`,
        companyName: foundCompany.name,
        referenceId: transactionCode,
        firstName,
      };

      Helper.notifyPayerIfNotBatchTransaction({
        transaction: foundTransaction,
        notify: true,
        email,
        emailPayload,
        firstName,
        currency,
        companyWithAdmin,
      });
    }

    Helper.notifyRecipientIfNeeded({ transaction: foundTransaction, amount, currency, transactionCode, notify: true });

    const admins = companyWithAdmin?.Users || [];

    Helper.notifyAdminsIfNotBatchTransaction({
      notify: true,
      transaction: foundTransaction,
      admins,
      email,
      companyWithAdmin,
      payer,
      counterPartyObject: {
        attributes: { accountNumber: details.account_number, bank: { name: details?.bank_name } },
        accountName: details.account_name,
      },
      reason,
      company,
      currency,
      amount,
    });

    Helper.finalizeBillPayment(reason, company, foundTransaction);

    const foundReimbursement = await ReimbursementRepo.getReimbursement({
      criteria: {
        transaction: foundTransaction.id,
      },
    });

    if (foundReimbursement) {
      await Helper.finalizeReimbursement("reimbursement", company, foundReimbursement.code, foundTransaction, transactionCode);
    }

    if (recordTransactionError) {
      // TODO: try to charge fees async, if we get here. but let us monitor for now
      throw new ValidationError(`Failed to record fees for transaction ${transactionCode}`);
    }
  } finally {
    log(Log.fg.blue, `Lock released for transaction: ${foundTransaction.id}`);
    if (lock) await redLock.unlock(lock);
  }

  return foundTransaction;
};

const handleInflowToDedicatedAccount = async (data, Providers) => {
  const {
    reference,
    amount,
    authorization: {
      receiver_bank_account_number: receiverBankAccountNumber,
      narration,
      sender_bank_account_number: senderBankAccountNumber,
      sender_name: senderName,
    } = {},
    currency,
    fees = 0,
    skipInstantPayout = false,
  } = data;

  const foundBankAccount = await BankAccountRepo.getOneBankAccount({
    queryParams: {
      number: receiverBankAccountNumber,
      issuer: CARD_ISSUER.Paystack,
      status: STATUSES.ACTIVE,
      ownerType: "company",
      type: "virtual",
      subtype: "deposit",
    },
    addBalance: true,
    selectOptions: ["externalIdentifier", "owner", "bankName"],
  });

  if (!foundBankAccount) throw new NotFoundError("Bank Account");

  const redLock = await RedisService.getRedLock();
  const ttl = 10000; // 10 seconds
  let lock;

  try {
    const foundCompany = await CompanyRepo.getOneCompany({
      queryParams: {
        id: foundBankAccount.owner,
      },
      addPaymentPlan: true,
      selectOptions: ["name"],
    });

    if (!foundCompany) throw new NotFoundError("Company");

    lock = await redLock.acquire([`lock:dedicated_account_inflow:${foundCompany.id}`], ttl);
    log(Log.fg.blue, `Lock acquired for inflow to dedicated account for reference: ${reference}`);

    const hasAlreadyRecordedInflow = await TransferRepo.getTransfer({
      filter: { reference, status: STATUSES.SUCCESS },
    });

    if (hasAlreadyRecordedInflow) {
      log(Log.fg.blue, `Inflow already recorded for reference: ${reference}`);
      return;
    }

    if (!skipInstantPayout) {
      await validateInstantSettlementStatus({ currency, amount, fees, senderName, senderBankAccountNumber });
    }

    const provider = HelperService.getProvider(foundCompany.id, PROVIDER_PRODUCTS.LEDGER);

    const providerHandler = await Providers[provider];

    const foundLedgerIdentity = await LedgerIdentityRepo.getLedgerIdentity({
      company: foundCompany.id,
      issuer: CARD_ISSUER.Blnk,
    });

    if (!foundLedgerIdentity) throw new NotFoundError("Ledger Identity");

    const depositFee = Helper.determineDepositFee(true, amount, currency, foundCompany.PaymentPlan);

    const { feeToCharge } = await Helper.determineDirectDebitFee({ narration, depositFee, currency });

    const { data: providerBalance, error: viewBalanceDetailsError } = await providerHandler.balances.viewBalanceDetails(
      foundBankAccount.externalIdentifier,
      foundCompany.id
    );

    if (viewBalanceDetailsError) {
      throw new ValidationError("Failed to view balance details");
    }

    const availableBalance = providerBalance.balance;

    const inflowPayload = {
      amount: Utils.formatAmount(amount),
      description: narration || `Transfer received from ${senderName}(${senderBankAccountNumber})`,
      currency,
      precision: 100,
      reference,
      destination: foundBankAccount.externalIdentifier,
      source: `@World${currency.toUpperCase()}`,
      allow_overdraft: true, // allow credit to proceed even if source is not enough
      skip_queue: true, // credit immediately
      meta_data: {
        transfer: reference,
      },
    };

    const { data: createdInflow, error: createInflowError } = await providerHandler.transactions.recordTransaction(inflowPayload, foundCompany.id);

    if (createInflowError) {
      throw new ValidationError("Failed to create inflow");
    }

    const payload = Utils.stripUndefinedAndNullDeep({
      amount: Number(amount) - Number(depositFee),
      actualAmount: Number(amount),
      company: foundCompany.id,
      currency,
      reference,
      description: `Transfer received from ${senderName}(${senderBankAccountNumber})`,
      processor_fee: 0,
      bujeti_fee: feeToCharge || depositFee,
      status: STATUSES.SUCCESS,
      narration,
      balance: foundBankAccount?.Balance?.id,
      externalIdentifier: createdInflow.transaction_id,
    });

    const { actualAmount, bujeti_fee: bujetiFee, processor_fee: processorFee } = payload;

    const totalFees = Utils.getInteger(processorFee) + Utils.getInteger(bujetiFee);

    const createdTransfer = await TransferRepo.createTransfer({ data: payload });

    await BalanceRepo.createLedger({
      amount: actualAmount,
      currency,
      company: foundCompany.id,
      balanceAfter: availableBalance + parseInt(actualAmount, 10),
      balanceBefore: availableBalance,
      transfer: createdTransfer.id,
      balance: foundBankAccount?.Balance?.id,
      description: createdTransfer.description,
      status: STATUSES.PROCESSED,
    });

    if (totalFees > 0) {
      // record fees charged
      const outflowPayload = {
        amount: Utils.formatAmount(totalFees),
        description: narration,
        currency,
        precision: 100,
        reference: `fee_${createdTransfer.code}`,
        source: foundBankAccount.externalIdentifier,
        destination: `@PayInFees${currency.toUpperCase()}`,
        allow_overdraft: false,
        inflight: false,
        skip_queue: true, // debit immediately
        meta_data: {
          transfer: createdTransfer.code,
        },
      };

      const { error: recordFeesError } = await providerHandler.transactions.recordTransaction(outflowPayload, foundCompany.id);

      await BalanceRepo.createLedger({
        amount: -1 * totalFees,
        currency,
        company: foundCompany.id,
        balanceAfter: availableBalance + parseInt(actualAmount, 10) - parseInt(totalFees, 10),
        balanceBefore: availableBalance + parseInt(actualAmount, 10),
        transfer: createdTransfer.id,
        balance: foundBankAccount?.Balance?.id,
        description: `Deposit fee for ${createdTransfer.code}`,
        status: STATUSES.PROCESSED,
      });

      if (recordFeesError) {
        // TODO: try to charge fees async, if we get here. but let us monitor for now
        throw new ValidationError(`Failed to record fees for transfer ${createdTransfer.code}`);
      }

      Helper.notifyOnInflow({
        company: foundCompany.id,
        transfer: createdTransfer,
        amount,
        depositFee,
        payload,
        currency,
        bankAccount: foundBankAccount,
        counterParty: { accountName: senderName },
        virtualNuban: { accountNumber: receiverBankAccountNumber },
        settlementAccount: { bankName: foundBankAccount.bankName },
      });
    }
  } finally {
    log(Log.fg.blue, `Lock released for inflow to dedicated account for reference: ${reference}`);
    if (lock) await redLock.unlock(lock);
  }
};

const handleSuccessfulTransaction = async (data, Providers) => {
  const {
    metadata,
    amount,
    currency,
    reference,
    message,
    gateway_response: gatewayResponse,
    authorization: { account_name: accountName, last4, channel },
  } = data;
  const { invoice, customer } = metadata || {};

  ThirdPartyLogService.createLog({
    message: message || gatewayResponse,
    company: -1,
    event: "paystack.charge.successful",
    payload: JSON.stringify(data),
    provider: CARD_ISSUER.paystack,
    providerType: "issuer",
    response: JSON.stringify({ message: "OK" }),
    statusCode: 200,
    endpoint: `${Utils.getApiURL()}/webhook/paystack`,
    method: "POST",
  });

  if (channel === PAYSTACK_PAYMENT_CHANNELS.DEDICATED_NUBAN) {
    return handleInflowToDedicatedAccount(data, Providers);
  }

  if (!customer) return;

  const foundCustomer = await CustomerRepo.getCustomer({
    filter: { code: customer },
  });

  if (!foundCustomer) throw new NotFoundError("Customer");

  const { company, id } = foundCustomer;

  const foundInvoice = await InvoiceRepo.getInvoiceByPaymentAmount({
    criteria: { company, customer: id, status: [STATUSES.PENDING, STATUSES.PARTIAL, STATUSES.OVERDUE], amount },
  });

  if (!foundInvoice) throw new ValidationError("Invoice with this amount not found or already paid");

  if (![STATUSES.PARTIAL, STATUSES.PENDING, STATUSES.OVERDUE, STATUSES.PROCESSING].includes(foundInvoice.status))
    throw new ValidationError(
      `Only pending, overdue, partial or processing invoices can be processed. Invoice status is ${foundInvoice.Status.value}`
    );

  const createdTransfer = await TransferRepo.createTransfer({
    data: {
      amount,
      company,
      currency,
      reference,
      description: `Card payment received from ${accountName} with card ending with (${last4})`,
      processor_fee: 0,
      bujeti_fee: 0,
      status: STATUSES.PROCESSED,
      narration: `Invoice payment from ${foundCustomer.name}`,
    },
  });

  const {
    Customer: { email, name },
    amount: invoiceAmount,
    InvoiceInstallments,
  } = foundInvoice;

  if (invoiceAmount === amount) {
    // Mark invoice as paid and all installment as paid
    await Promise.all([
      InvoiceRepo.update({ filter: { id: foundInvoice.id }, data: { status: STATUSES.PROCESSING, paidOn: literal("CURRENT_TIMESTAMP") } }),
      InvoiceInstallmentRepo.updateInvoiceInstallment({
        filter: { invoice: foundInvoice.id },
        payload: { status: STATUSES.PROCESSING, paid_on: literal("CURRENT_TIMESTAMP") },
      }),
    ]);
  } else {
    // Possibly an Installment payment
    const [foundInvoiceInstallment] = InvoiceInstallments;
    await InvoiceInstallmentRepo.updateInvoiceInstallment({
      filter: { invoice: foundInvoice.id, id: foundInvoiceInstallment.id },
      payload: { status: STATUSES.PROCESSING, paid_on: literal("CURRENT_TIMESTAMP") },
    });
    const pendingInvoiceInstallment = await InvoiceInstallmentRepo.getInvoiceInstallment({
      filter: { invoice: foundInvoice.id, status: STATUSES.PENDING },
    });
    if (!pendingInvoiceInstallment) {
      await InvoiceRepo.update({ filter: { id: foundInvoice.id }, data: { status: STATUSES.PROCESSING, paidOn: literal("CURRENT_TIMESTAMP") } });
    } else await InvoiceRepo.update({ filter: { id: foundInvoice.id }, data: { status: STATUSES.PARTIAL } });
  }

  const pendingSettlementData = {
    amount,
    reference,
    company,
    entity: foundInvoice.id,
    entityType: "invoice",
  };
  const createdSettlement = await PendingSettlementRepo.createPendingSettlement({ data: pendingSettlementData });

  // Create a cron for next day settlement
  const timeToTrigger = Utils.isProd() ? 60 * 25 : 3; // 3 minutes for staging and next day for prod

  const { minutes, hours, dayOfMonth, month, dayOfWeek } = ScheduleService.convertDateToSchedule(
    ScheduleService.splitDateAndTimeStamp(Utils.getUTCPlusOneTime(timeToTrigger))
  );

  const schedule = { hours, minutes, dayOfMonth, month, dayOfWeek };
  const startDate = ScheduleService.splitDateAndTimeStamp(Utils.getUTCPlusOneTime(timeToTrigger - 1));
  const {
    data: { cronExpression, createdSchedule, isStartDateToday },
  } = await ScheduleService.validateAndInitiateScheduleRecord({
    recurring: false,
    schedule,
    startDate,
  });

  return ScheduleService.generateAndRecordScheduleWithCronJob({
    createdSchedule,
    isStartDateToday,
    startDate,
    cronExpression,
    title: `settlements/${createdSettlement.code}`,
    customQuarterly: true,
  });
};

const handleDedicatedAccountCreated = async (data, Providers = {}) => {
  const { customer: { email } = {}, dedicated_account: dedicatedAccount = {} } = data;

  const { account_number: accountNumber, id: bankId, account_name: accountName, bank: { name: bankName, slug: bankCode } = {} } = dedicatedAccount;

  const foundCompany = await CompanyRepo.getCompany({
    queryParams: {
      contact_email: email,
      status: STATUSES.VERIFIED,
    },
  });

  if (!foundCompany) throw new NotFoundError("Company");

  const redLock = await RedisService.getRedLock();
  const ttl = 5000; // 5 seconds
  let lock;

  await ThirdPartyLogService.createLog({
    message: "Dedicated Account created",
    company: foundCompany.id,
    event: "paystack.dedicated_account.created",
    payload: JSON.stringify(data),
    provider: CARD_ISSUER.paystack,
    providerType: "issuer",
    response: JSON.stringify({ message: "OK" }),
    statusCode: 200,
    endpoint: `${Utils.getApiURL()}/webhook/paystack`,
    method: "POST",
  });

  try {
    lock = await redLock.acquire([`lock:dedicated_account:${foundCompany.id}`], ttl);
    log(Log.fg.blue, `Lock acquired for dedicated account for customer: ${email}`);

    const foundBankAccount = await BankAccountRepo.getOneBankAccount({
      queryParams: {
        issuer: CARD_ISSUER.Paystack,
        status: STATUSES.PENDING,
        type: "virtual",
        subType: "deposit",
        ownerType: "company",
        owner: foundCompany.id,
      },
      addBalance: true,
      selectOptions: ["currency"],
    });

    if (!foundBankAccount) throw new NotFoundError("Bank Account");

    const titanPaystackBank = Banks.find((bank) => bank.label === "Titan Paystack");

    const provider = HelperService.getProvider(foundCompany.id, PROVIDER_PRODUCTS.LEDGER);

    const providerHandler = await Providers[provider];

    const foundLedgerIdentity = await LedgerIdentityRepo.getLedgerIdentity({
      company: foundCompany.id,
      issuer: CARD_ISSUER.Blnk,
    });

    let ledgerId;
    let identityId;

    if (!foundLedgerIdentity) {
      // create identiy
      const identityPayload = {
        identity_type: "organization",
        organization_name: foundCompany.name,
        dob: foundCompany.dateOfRegistration,
        email_address: foundCompany.contact_email,
        phone_number: foundCompany.contact_phone,
        meta_data: {
          company: foundCompany.code,
        },
      };

      const { data: createdIdentity, error: createIdentityError } = await providerHandler.identities.createIdentity(identityPayload, foundCompany.id);

      if (createIdentityError) {
        throw new ValidationError("Failed to create identity");
      }

      identityId = createdIdentity.identity_id;

      // create ledger
      const ledgerPayload = {
        name: foundCompany.name,
        meta_data: {
          company: foundCompany.code,
        },
      };

      const { data: createdLedger, error: createLedgerError } = await providerHandler.ledgers.createLedger(ledgerPayload, foundCompany.id);

      if (createLedgerError) {
        throw new ValidationError("Failed to create ledger");
      }

      ledgerId = createdLedger.ledger_id;

      await LedgerIdentityRepo.createLedgerIdentity({
        company: foundCompany.id,
        identity: identityId,
        ledger: ledgerId,
        issuer: CARD_ISSUER.Blnk,
      });
    } else {
      ledgerId = foundLedgerIdentity.ledger;
      identityId = foundLedgerIdentity.identity;
    }

    const { data: createdBalance, error: createBalanceError } = await providerHandler.balances.createBalance(
      {
        ledger_id: ledgerId,
        currency: foundBankAccount.currency,
        identity_id: identityId,
        meta_data: {
          balance: foundBankAccount.Balance?.code,
        },
      },
      foundCompany.id
    );

    if (createBalanceError) {
      throw new ValidationError("Failed to create balance");
    }

    await foundBankAccount.update({
      accountName,
      number: accountNumber,
      bankName,
      bankCode: titanPaystackBank?.bankCode,
      externalBankAccountId: bankId,
      status: STATUSES.ACTIVE,
      externalIdentifier: createdBalance.balance_id,
    });

    await BalanceRepo.update({ bankAccount: foundBankAccount?.id }, { status: STATUSES.ACTIVE });
  } catch (error) {
    log(Log.fg.red, error.stack);
  } finally {
    log(Log.fg.blue, `Lock released for dedicated account for customer: ${email}`);
    if (lock) await redLock.unlock(lock);
  }
};

const handleDedicatedAccountFailed = async (data) => {
  const { customer: { email } = {} } = data;

  const foundCompany = await CompanyRepo.getOneCompany({
    queryParams: {
      contact_email: email,
      status: STATUSES.VERIFIED,
    },
  });

  if (!foundCompany) throw new NotFoundError("Company");

  await ThirdPartyLogService.createLog({
    message: "Dedicated Account creation failed",
    company: foundCompany.id,
    event: "paystack.dedicated_account.failed",
    payload: JSON.stringify(data),
    provider: CARD_ISSUER.paystack,
    providerType: "issuer",
    response: JSON.stringify({ message: "OK" }),
    statusCode: 200,
    endpoint: `${Utils.getApiURL()}/webhook/paystack`,
    method: "POST",
  });

  const redLock = await RedisService.getRedLock();
  const ttl = 5000; // 5 seconds
  let lock;

  try {
    lock = await redLock.acquire([`lock:dedicated_account:${foundCompany.id}`], ttl);
    log(Log.fg.blue, `Lock acquired for dedicated account for customer: ${email}`);

    const foundBankAccount = await BankAccountRepo.getOneBankAccount({
      queryParams: {
        issuer: CARD_ISSUER.Paystack,
        status: STATUSES.PENDING,
        type: "virtual",
        subtype: "deposit",
        ownerType: "company",
        owner: foundCompany.id,
      },
    });

    if (!foundBankAccount) throw new NotFoundError("Bank Account");

    await foundBankAccount.update({
      status: STATUSES.FAILED,
    });

    await BalanceRepo.update({ bankAccount: foundBankAccount?.id }, { status: STATUSES.FAILED });
  } catch (error) {
    log(Log.fg.red, error.stack);
  } finally {
    log(Log.fg.blue, `Lock released for dedicated account for customer: ${email}`);
    if (lock) await redLock.unlock(lock);
  }
};

const Service = {
  httpClient() {
    const headers = {
      Authorization: `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
      "Content-Type": "application/json",
    };
    if (!httpClient.client)
      httpClient.client = axios.create({
        headers,
        baseURL: process.env.PAYSTACK_BASE_URL,
      });
    return httpClient.client;
  },

  balances: {
    async getBalance(currency) {
      const request = Service.httpClient().get("/balance");
      const { data, error, status, config, message } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Error getting balance" : "Balance fetched succesfully"}`,
        company: -1,
        event: "paystack.balance.fetched",
        payload: null,
        provider: CARD_ISSUER.paystack,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      if (error || status !== 200) throw new ValidationError(message);

      const currencyBalance = data.find((balance) => balance.currency === currency.toUpperCase());
      if (!currencyBalance) throw new NotFoundError(`${currency} balance`);

      return currencyBalance.balance;
    },
  },

  customers: {
    async createCustomer(payload) {
      const request = Service.httpClient().post("/customer", payload);
      const { data, error, status, config, message } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Error creating customer" : "Customer created succesfully"}`,
        company: -1,
        event: "paystack.customer.created",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.paystack,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      if (error) throw new ValidationError(message);

      return { message, data, status, error };
    },

    async validateCustomer(cusomerCode, payload) {
      const request = Service.httpClient().post(`/customer/${cusomerCode}/identification`, payload);

      const { data, error, status, config, message } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Error validating customer" : "Customer validated succesfully"}`,
        company: -1,
        event: "paystack.customer.validate",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.paystack,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      return { message, data, status, error };
    },

    async fetchCustomer(email) {
      const request = Service.httpClient().get(`/customer/${email}`);
      const { data, error, status, config, message } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Error fetching customer" : "Customer fetched succesfully"}`,
        company: -1,
        event: "paystack.customer.fetch",
        payload: JSON.stringify({ email }),
        provider: CARD_ISSUER.paystack,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      if (error) throw new ValidationError(message);

      return { message, data, status, error };
    },
  },

  accounts: {
    async createDedicatedAccount(payload) {
      const { customer, preferredBank = "titan-paystack" } = payload;

      const dedicatedAccountPayload = {
        customer,
        preferred_bank: Utils.isProd() ? preferredBank : "test-bank",
      };
      const request = Service.httpClient().post("/dedicated_account", dedicatedAccountPayload);

      const { data, error, status, config, message } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Error creating dedicated account" : "Dedicated account created succesfully"}`,
        company: -1,
        event: "paystack.dedicated.account.created",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.paystack,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      if (error) throw new ValidationError(message);

      return { message, data, status, error };
    },

    async assignDedicatedAccount(payload) {
      const { email, firstName, middleName, lastName, phone, preferredBank = "titan-paystack", country, accountNumber, bvn, bankCode } = payload;
      const assignDedicatedAccountPayload = {
        email,
        first_name: firstName,
        middle_name: middleName,
        last_name: lastName || firstName,
        phone,
        preferred_bank: Utils.isProd() ? preferredBank : "test-bank",
        country,
        account_number: accountNumber,
        bvn,
        bank_code: bankCode,
      };

      const request = Service.httpClient().post("/dedicated_account/assign", assignDedicatedAccountPayload);

      const { data, error, status, config, message } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Error assigning dedicated account" : "Dedicated account assigned succesfully"}`,
        company: -1,
        event: "paystack.dedicated.account.assigned",
        payload: JSON.stringify(assignDedicatedAccountPayload),
        provider: CARD_ISSUER.paystack,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      if (error) throw new ValidationError(message);

      return { message, data, status, error };
    },

    async requeryDedicatedAccount(payload) {
      const { accountNumber, bankCode, date } = payload;

      const requeryDedicatedAccountPayload = {
        account_number: accountNumber,
        provider_slug: bankCode,
        ...(date && { date: format(new Date(date), "yyyy-MM-dd") }),
      };

      const request = Service.httpClient().get("/dedicated_account/requery", {
        params: requeryDedicatedAccountPayload,
      });

      const { data, error, status, config, message } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Error requerying dedicated account" : "Dedicated account requeried succesfully"}`,
        company: -1,
        event: "paystack.dedicated.account.requery",
        payload: JSON.stringify(requeryDedicatedAccountPayload),
        provider: CARD_ISSUER.paystack,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      if (error) throw new ValidationError(message);

      return { message, data, status, error };
    },

    async fetchBanks(payload) {
      const { next, previous, perPage = 100, country = "nigeria" } = payload;

      const request = Service.httpClient().get("/bank", {
        params: { perPage, next, previous, country },
      });

      const { data, error, status, config, message } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Error fetching banks" : "Banks fetched succesfully"}`,
        company: -1,
        event: "paystack.banks.fetch",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.paystack,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      if (error) throw new ValidationError(message);

      return { message, data, status, error };
    },
  },

  payments: {
    async createRecipient(payload) {
      const { accountName, number, bankCode } = payload;

      const isProd = Utils.isProd();

      const recipientPayload = {
        type: "nuban",
        name: isProd ? accountName : "John Doe",
        account_number: isProd ? number : "**********",
        bank_code: isProd ? bankCode : "044",
        currency: "NGN",
      };

      const request = Service.httpClient().post("/transferrecipient", recipientPayload);
      const { data, error, status, config, message } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Error creating transfer recipient" : "Transfer recipient created succesfully"}`,
        company: -1,
        event: "paystack.recipient.created",
        payload: JSON.stringify(recipientPayload),
        provider: CARD_ISSUER.paystack,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      if (error) throw new ValidationError(message);

      return { message, data, status, error };
    },

    async initiateTransfer(payload, Providers = {}) {
      const { accountName, number, bankCode, amount, reason, reference, currency, balance, isInitiatingTransfer } = payload;

      let foundLedgerIdentity;
      let foundTransaction;

      const { data: recipientData } = await Service.payments.createRecipient({
        accountName,
        number,
        bankCode: Utils.getNIPCodeFromBankCode(bankCode),
      });
      const { recipient_code: recipientCode = null } = recipientData || {};

      const transferPayload = {
        source: "balance",
        amount,
        reference,
        recipient: recipientCode,
        reason,
      };

      if (isInitiatingTransfer) {
        foundTransaction = await TransactionRepo.findByIdOrCode(reference);
        if (!foundTransaction) throw new NotFoundError("Transaction");

        foundLedgerIdentity = await LedgerIdentityRepo.getLedgerIdentity({
          company: foundTransaction.company,
          issuer: CARD_ISSUER.Blnk,
        });

        if (!foundLedgerIdentity) throw new NotFoundError("Ledger Identity");
      }

      const request = Service.httpClient().post("/transfer", transferPayload);
      const { data, error, status, config, message } = await requestHandler(request);

      await ThirdPartyLogService.createLog({
        message: message || `${error ? "Error initiating transfer" : "Transfer initiated succesfully"}`,
        company: -1,
        event: "paystack.transfer.created",
        payload: JSON.stringify(transferPayload),
        provider: CARD_ISSUER.paystack,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      if (error) {
        if (isInitiatingTransfer) {
          await TransactionRepo.updateTransaction({
            queryParams: { code: reference },
            updateFields: { status: STATUSES.FAILED, failure_reason: message },
          });
        }
        throw new ValidationError(message);
      }

      if (!isInitiatingTransfer) return { message, data, status };

      const lockTTL = 5000; // 5 seconds
      const redLock = await RedisService.getRedLock();
      let lock;

      try {
        lock = await redLock.acquire([`lock:balance:${balance}`], lockTTL);
        log(Log.fg.blue, `Lock acquired for balance: ${reference}`);

        const ledgerCriteria = {
          transaction: foundTransaction.id,
          status: [STATUSES.PENDING],
        };

        const existingLedger = await BalanceLedgerRepo.getBalanceLedger({ filter: ledgerCriteria });

        if (existingLedger) return existingLedger;

        const { narration, processor_fee: processorFee, bujeti_fee: bujetiFee, company, description, id } = foundTransaction;

        const totalAmount = Utils.money(amount)
          .plus(processorFee || 0)
          .plus(bujetiFee || 0)
          .toNumber();

        await TransactionAttemptRepo.createTransactionAttempt({
          payload: {
            reference,
            transaction: foundTransaction.id,
            status: STATUSES.PENDING,
            externalIdentifier: data?.transfer_code,
          },
        });

        const foundBalance = await BalanceRepo.getBalance({
          filter: { currency, company, status: STATUSES.ACTIVE, id: balance },
          includeAccount: true,
        });

        if (!foundBalance) throw new NotFoundError("Balance");

        const availableBalance = await BalanceRepo.getAvailableBalance({
          company,
          currency,
          id: balance,
        });

        await BalanceLedgerRepo.createLedger({
          company,
          currency,
          description,
          amount: -1 * totalAmount,
          transaction: id,
          status: STATUSES.PENDING,
          balance: foundBalance?.id,
          balanceBefore: parseInt(availableBalance, 10),
          balanceAfter: parseInt(availableBalance, 10) - totalAmount,
        });

        const provider = HelperService.getProvider(company, PROVIDER_PRODUCTS.LEDGER);
        const providerHandler = await Providers[provider];

        const outflowPayload = {
          amount: Utils.formatAmount(amount),
          description: narration,
          currency,
          precision: 100,
          reference,
          source: foundBalance.BankAccount.externalIdentifier,
          destination: `@World${currency.toUpperCase()}`,
          allow_overdraft: false, // if not enough money, throw an error
          inflight: true, // held till paystack notifies us of success or failed
          skip_queue: true, // debit immediately
          meta_data: {
            transaction: foundTransaction.code,
          },
        };

        const {
          data: createdOutflow,
          error: recordTransactionError,
          message: recordTransactionMessage,
        } = await providerHandler.transactions.recordTransaction(outflowPayload, company);

        if (recordTransactionError) {
          throw new ValidationError(`${recordTransactionMessage} for transaction ${reference}`);
        }

        await TransactionRepo.updateTransaction({
          queryParams: { code: reference },
          updateFields: { externalIdentifier: createdOutflow.transaction_id },
        });
      } finally {
        log(Log.fg.blue, `Lock released for balance: ${reference}`);
        if (lock) await redLock.unlock(lock);
      }

      return { message, data, status };
    },

    async generatePaymentLink(payload) {
      const { amount, email, currency, reference, redirectUrl, company, ...rest } = payload;
      const paymentLinkData = {
        email,
        amount,
        currency,
        reference,
        callback_url: redirectUrl,
        metadata: { ...rest },
      };
      const request = Service.httpClient().post("/transaction/initialize", paymentLinkData);
      const { data, error, status, config, message } = await requestHandler(request);

      ThirdPartyLogService.createLog({
        message: message || `${error ? "Error generating payment link" : "Payment link generated"}`,
        company: company || -1,
        event: "paystack.payment.link",
        payload: JSON.stringify(paymentLinkData),
        provider: CARD_ISSUER.paystack,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      if (error || status !== 200) throw new ValidationError(message);

      return { message, data };
    },

    async verifyPaymentReference(payload) {
      const { reference, company } = payload;
      if (!reference) throw new ValidationError(`Please specify reference`);

      const request = Service.httpClient().get(`/transaction/verify/${reference}`);
      const { data, error, status, config, message } = await requestHandler(request);

      ThirdPartyLogService.createLog({
        message: message || `${error ? "Error verifying payment" : "payment reference verified"}`,
        company: company || -1,
        event: "paystack.reference.validate",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.paystack,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      return { message, data };
    },
  },

  settlements: {
    async checkInstantSettlementProfile() {
      const request = Service.httpClient().get("/settlement/instant/profile");
      const { data, error, status, config, message } = await requestHandler(request);

      ThirdPartyLogService.createLog({
        message: message || `${error ? "Error checking instant profile" : "Instant profile checked"}`,
        company: -1,
        event: "paystack.instant.profile",
        payload: null,
        provider: CARD_ISSUER.paystack,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      return { message, data, error };
    },

    async requestPayoutCheck(payload) {
      const { amount, currency } = payload;
      const request = Service.httpClient().post("/settlement/request_payout/check", {
        amount,
        currency,
      });
      const { data, error, status, config, message } = await requestHandler(request);

      ThirdPartyLogService.createLog({
        message: message || `${error ? "Error requesting payout check" : "Payout request check"}`,
        company: -1,
        event: "paystack.instant.payout.check",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.paystack,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      return { message, data, error };
    },

    async requestPayout(payload) {
      const { amount, currency, description } = payload;
      const request = Service.httpClient().post("/settlement/request_payout", {
        amount,
        currency,
        merchant_note: description,
      });
      const { data, error, status, config, message } = await requestHandler(request);

      ThirdPartyLogService.createLog({
        message: message || `${error ? "Error initiating payout request" : "Payout request initiated"}`,
        company: -1,
        event: "paystack.instant.payout.initiate",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.paystack,
        providerType: "issuer",
        response: (data && JSON.stringify(data)) || "",
        statusCode: status || (data && data.status_code),
        endpoint: config.url,
        method: config.method,
      });

      return { message, data, error };
    },
  },

  autoQuerySingleTransaction() {},

  retryTransaction(transactionCode) {
    return ApprovalService.addPaymentToQueue(transactionCode);
  },

  async handleWebhook(req, Providers) {
    const { event, data } = req.body;
    switch (event) {
      case PAYSTACK_WEBHOOK_EVENTS.CHARGE_SUCCESS:
        await handleSuccessfulTransaction(data, Providers);
        break;
      case PAYSTACK_WEBHOOK_EVENTS.TRANSFER_SUCCESS:
        await handleTransferSuccessful(data, Providers);
        break;
      case PAYSTACK_WEBHOOK_EVENTS.TRANSFER_FAILED:
        await handleTransferFailed(data, Providers);
        break;
      case PAYSTACK_WEBHOOK_EVENTS.CUSTOMER_IDENTIFICATION_SUCCESS:
        // Not used yet
        break;
      case PAYSTACK_WEBHOOK_EVENTS.CUSTOMER_IDENTIFICATION_FAILED:
        // Not used yet
        break;
      case PAYSTACK_WEBHOOK_EVENTS.DEDICATED_ACCOUNT_ASSIGN_SUCCESS:
        await handleDedicatedAccountCreated(data, Providers);
        break;
      case PAYSTACK_WEBHOOK_EVENTS.DEDICATED_ACCOUNT_ASSIGN_FAILED:
        await handleDedicatedAccountFailed(data);
        break;
      default:
        break;
    }
  },
};
module.exports = Service;

async function validateInstantSettlementStatus({ currency, amount, fees, senderName, senderBankAccountNumber }) {
  const isProd = Utils.isProd();
  const actualAmount = Utils.money(amount).minus(Utils.money(fees)).toNumber();

  const checkInstantSettlementProfileResult = await Service.settlements.checkInstantSettlementProfile();
  const isEnabledForInstantPayout = (checkInstantSettlementProfileResult?.data || [])?.some((account) => {
    return account?.currency === currency && account?.is_supported && account?.can_request_payout;
  });

  if (!isEnabledForInstantPayout) {
    log(Log.fg.red, `Account does not support instant payout for currency ${currency}`);
    if (isProd) throw new ValidationError(`Account does not support instant payout for currency ${currency}`);
  }

  const requestPayoutCheckResult = await Service.settlements.requestPayoutCheck({
    amount: actualAmount,
    currency,
  });
  const canRequestAmountForInstantPayout = requestPayoutCheckResult?.data?.is_eligible;

  if (!canRequestAmountForInstantPayout) {
    log(Log.fg.red, `Account does not support instant payout for amount ${Utils.formatMoney(amount)}`);
    if (isProd) throw new ValidationError(`Account does not support instant payout for amount ${Utils.formatMoney(amount)}`);
  }

  const requestPayoutResult = await Service.settlements.requestPayout({
    amount: actualAmount,
    currency,
    merchant_note: `Transfer received from ${senderName}(${senderBankAccountNumber})`,
  });
  const isSuccessfulPayoutRequest = !requestPayoutResult?.error;

  if (!isSuccessfulPayoutRequest) {
    log(Log.fg.red, `Failed to request payout for amount ${Utils.formatMoney(amount)}: ${requestPayoutResult?.data?.meta?.nextStep}`);
    if (isProd) throw new ValidationError(`Failed to request payout for amount ${Utils.formatMoney(amount)}`);
  }
}
