const axios = require("axios");

class ApiAction {
  /**
   *  @param {string} url - The URL for the request.
   *  @param {string} token - The authentication token.
   *  @param {Object} payload - The request payload.
   *  @param {string} method - The HTTP method (e.g., 'GET', 'POST', 'PUT').
   */
  // eslint-disable-next-line class-methods-use-this
  _initiateMethodAction = async (url, token, payload, method) => {
    const response = await axios[method](`${url}`, payload, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });
    return response;
  };

  /**
   * @param {string} url
   * @param {string} token
   */
  // eslint-disable-next-line class-methods-use-this
  _initiateGetAction = async (url, token) => {
    return axios.get(`${url}`, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });
  };

  /**
   * Initiates an HTTP request.
   *
   * @param {Object} options - The request options.
   * @param {string} options.url - The URL for the request.
   * @param {string} options.method - The HTTP method (e.g., 'GET', 'POST', 'PUT').
   * @param {Object} [options.payload={}] - The request payload (default is an empty object).
   * @param {boolean} [options.vaultUrl=false] - A boolean indicating whether it's a vault URL (default is false).
   * @param {string | null} [options.cardToken=null] - An optional card token (default is null).
   *
   * @returns {Promise} A promise that resolves when the request is completed.
   *
   * @example
   * // Example of making a GET request
   * await initiateRequest({
   *   url: 'https://api.example.com/data',
   *   method: 'GET'
   * });
   *
   * @example
   * // Example of making a POST request with a payload
   * await initiateRequest({
   *   url: 'https://api.example.com/resource',
   *   method: 'POST',
   *   payload: { key: 'value' }
   * });
   */
  initiateRequest = async ({
    url,
    method,
    payload = {},
    vaultUrl = false,
    cardToken = null,
  }) => {
    try {
      let call;
      const endpoint = `${
        !vaultUrl ? process.env.SUDO_BASE_URL : process.env.SUDO_BASE_URL_VAULT
      }${url}`;
      const token = cardToken || process.env.SUDO_AUTH_TOKEN;

      if (method === "get") {
        // eslint-disable-next-line no-underscore-dangle
        call = await this._initiateGetAction(endpoint, token);
      } else {
        // eslint-disable-next-line no-underscore-dangle
        call = await this._initiateMethodAction(
          endpoint,
          token,
          payload,
          method
        );
      }
      const {
        data: { message, statusCode, ...rest },
      } = call;
      return {
        statusCode,
        message,
        ...(!(statusCode >= 200 && statusCode <= 299)
          ? { error: true }
          : { error: false }),
        ...rest,
      };
    } catch (error) {
      const { response = {} } = error;
      return {
        error: true,
        message: `${response?.data?.message} | ${error?.message}`,
        ...(response && { data: response?.data }),
      };
    }
  };
}

module.exports = ApiAction;
