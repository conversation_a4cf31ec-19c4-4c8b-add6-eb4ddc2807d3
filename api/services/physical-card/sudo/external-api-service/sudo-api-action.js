const ApiAction = require("./api-action");
const { CARD_ISSUER } = require("../../../../models/cardissuer");
const ThirdPartyLogService = require("../../../thirdPartyLog");

class SudoApiAction extends ApiAction {
  /**
   * Create a Customer with the provided payload, company, and user information.
   *
   * @param {Object} payload - The payload for creating the customer.
   * @param {number} [company] - The company id (default is null).
   * @param {Object|null} [user] - The user information (default is null).
   *
   * @returns {Object} An object with the following properties:
   * @property {string} message - A message indicating the result of the operation.
   * @property {string|null} error - An error message (null if no error occurred).
   * @property {any} data - Additional data related to the Customerr creation.
   * @property {number} status - The HTTP status code or status indicator.
   *
   * @example
   * const payload = { name: '<PERSON>' };
   * const company = { name: 'Example Inc' };
   * const user = { id: 123 };
   * const result = createCardHolder(payload, company, user);
   */
  createCustomer = async (payload, company, user) => {
    const response = await this.initiateRequest({
      url: "/customers",
      method: "post",
      payload,
    });
    const { error, message, statusCode, data } = response;

    // TODO Use a standard event listener here
    await ThirdPartyLogService.createLog({
      company,
      event: "sudo.create.cardholder",
      payload: JSON.stringify(payload),
      message,
      provider: CARD_ISSUER.sudo,
      providerType: "issuer",
      response: JSON.stringify(response),
      statusCode,
      endpoint: `${process.env.SUDO_BASE_URL}/customers`,
      method: "POST",
    });
    return {
      providerStatus:
        statusCode >= 200 && statusCode <= 299 ? "success" : "failed",
      message,
      error,
      data,
      statusCode,
    };
  };

  /**
   * Create a Customer with the provided payload, company, and user information.
   *
   * @param {Object} payload - The payload for creating the customer.
   * @param {Object|null} [company=null] - The company information (default is null).
   * @param {Object|null} [user=null] - The user information (default is null).
   *
   * @returns {Object} An object with the following properties:
   * @property {string} message - A message indicating the result of the operation.
   * @property {string|null} error - An error message (null if no error occurred).
   * @property {any} data - Additional data related to the Customerr creation.
   * @property {number} status - The HTTP status code or status indicator.
   *
   * @example
   * const payload = { name: 'John Doe' };
   * const company = { name: 'Example Inc' };
   * const user = { id: 123 };
   * const result = createCardHolder(payload, company, user);
   */
  createCard = async (payload, company, user = null) => {
    const response = await this.initiateRequest({
      url: "/cards",
      method: "post",
      payload,
    });
    const { statusCode, message, error, data } = response;

    await ThirdPartyLogService.createLog({
      company,
      event: "sudo.create.card",
      payload: JSON.stringify(payload),
      message,
      provider: CARD_ISSUER.sudo,
      providerType: "issuer",
      response: JSON.stringify(response),
      statusCode,
      endpoint: `${process.env.SUDO_BASE_URL}/cards`,
      method: "POST",
    });

    return {
      providerStatus:
        statusCode >= 200 && statusCode <= 299 ? "success" : "failed",
      message,
      error,
      data,
      statusCode,
    };
  };

  generateCardToken = async (payload, company, user = null) => {
    const response = await this.initiateRequest({
      url: `/cards/${payload.cardId}/token`,
      method: "get",
    });
    const { statusCode, message, error, data } = response;

    const maskedResponse = {
      ...response,
      data: {
        ...response.data,
        token: "****",
      },
    };

    await ThirdPartyLogService.createLog({
      company,
      event: "sudo.get.cardToken",
      payload: JSON.stringify(payload),
      message,
      provider: CARD_ISSUER.sudo,
      providerType: "issuer",
      response: JSON.stringify(maskedResponse),
      statusCode,
      endpoint: `${process.env.SUDO_BASE_URL}/cards/${payload.cardId}/token`,
      method: "POST",
    });

    return {
      providerStatus:
        statusCode >= 200 && statusCode <= 299 ? "success" : "failed",
      message,
      error,
      data,
      statusCode,
    };
  };

  getCardDefaultPin = async (payload, company, user = null) => {
    const { cardId, cardToken } = payload;
    const response = await this.initiateRequest({
      url: `/cards/${cardId}/secure-data/defaultPin`,
      method: "get",
      vaultUrl: true,
      cardToken,
    });
    const { statusCode, message, error, data } = response;

    const maskedResponse = {
      ...response,
      data: {
        ...response.data,
        defaultPin: "****",
      },
    };

    await ThirdPartyLogService.createLog({
      company,
      event: "sudo.get.cardDefaultPin",
      payload: JSON.stringify(payload),
      message,
      provider: CARD_ISSUER.sudo,
      providerType: "issuer",
      response: JSON.stringify(maskedResponse),
      statusCode,
      endpoint: `${process.env.SUDO_BASE_URL}/cards/${cardId}/secure-data/defaultPin`,
      method: "POST",
    });

    return {
      providerStatus:
        statusCode >= 200 && statusCode <= 299 ? "success" : "failed",
      message,
      error,
      data,
      statusCode,
    };
  };
}
module.exports = SudoApiAction;
