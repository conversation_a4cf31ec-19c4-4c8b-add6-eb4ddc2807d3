const SudoCardProcessRequest = require("./sudo-card-process-request");
const SudoProcessState = require("./sudo-process-state");
const mapOfState = require("./state-flow");
const { NotFoundError } = require("../../../utils/error.utils");
const cardResponseFormatter = require("./response-transformer/card-response-formatter");

class SudoProcessor {
  /**
   * @param {string} [cardPan]
   * @param {string} [bvn]
   * @param {object} [company = null]
   * @param {object} [user = null]
   */
  // eslint-disable-next-line class-methods-use-this
  process = async ({ cardPan, bvn, company = null, user = null, cardRequest }) => {
    const request = new SudoCardProcessRequest(
      SudoProcessState.START_STATE,
      user,
      company,
      cardPan,
      bvn,
      cardRequest,
    );

    while (request.getState() !== SudoProcessState.STOP_STATE) {
      if (!mapOfState.has(request.getState())) {
        throw new NotFoundError(request.getState);
      }
      const state = mapOfState.get(request.getState());
      // eslint-disable-next-line no-await-in-loop
      await state.handleRequest(request);
    }

    return cardResponseFormatter(request);
  };
}

module.exports = SudoProcessor;
