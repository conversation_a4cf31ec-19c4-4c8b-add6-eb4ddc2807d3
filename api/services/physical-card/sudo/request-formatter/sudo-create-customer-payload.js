const {
  ValidationError,
  NotFoundError,
} = require("../../../../utils/error.utils");

const {
  UserRepo,
  CompanyRepo,
} = require("../../../../repositories/index.repo");
const { formatPhoneNumber } = require("../../../../utils");

/**
 *
 * @param userId
 * @param companyId
 * @param bvn
 * @returns {Promise<{address: {country: *, address: *, city: *, house_no: *, state: *, postal_code: *}, email_address, phone: *, identity: {dob: *, id_type: string, bvn}, last_name, first_name}>}
 */
const createCustomerRequestFormatter = async ({ userId, companyId, bvn }) => {
  if (!(userId && companyId))
    throw new ValidationError("Please specify user and company value");
  if (typeof userId !== "number" || typeof companyId !== "number")
    throw new ValidationError("Both user and company values should be number");
  const [foundUser, foundCompany] = await Promise.all([
    UserRepo.getOneUser({
      queryParams: { id: userId },
      includePhonenumber: true,
      includeAddress: true,
    }),
    CompanyRepo.getOneCompany({
      queryParams: { id: companyId },
      addPhoneNumber: true,
      addAddress: true,
    }),
  ]);

  if (!foundUser) throw new NotFoundError("User");
  if (!foundCompany) throw new NotFoundError("Company");
  const {
    firstName,
    email,
    lastName,
    Address: userAddress,
    PhoneNumber: userPhoneNumber,
    dob = null,
  } = foundUser;
  const { Address: companyAddress } = foundCompany;

  if (!userPhoneNumber) throw new NotFoundError("User's Phone Number");
  if (!dob) throw new ValidationError("Please update your Date of Birth");

  const internationalPhoneNumber =
    userPhoneNumber.internationalFormat ||
    formatPhoneNumber(userPhoneNumber.localFormat, userPhoneNumber.countryCode);

  return {
    type: "individual",
    name: `${firstName} ${lastName}`,
    status: "active",
    phoneNumber: internationalPhoneNumber,
    emailAddress: email,
    individual: {
      firstName,
      lastName,
      dob,
      identity: {
        type: "BVN",
        number: bvn,
      },
    },
    billingAddress: {
      line1: userAddress?.street || companyAddress?.street,
      line2: userAddress?.street || companyAddress?.street,
      city: userAddress?.city || companyAddress?.city,
      state: userAddress?.state || companyAddress?.state,
      country: userAddress?.country || companyAddress?.country,
      postalCode: userAddress?.postalCode || "123009",
    },
  };
};

module.exports = createCustomerRequestFormatter;
