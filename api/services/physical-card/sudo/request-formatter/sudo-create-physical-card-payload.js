const { PolicyRepo } = require("../../../../repositories/index.repo");
const SettingsService = require("../../../settings");

/**
 *
 * @param cardHolder
 * @param cardPan
 */
const createCardRequestFormatter = async ({ cardHolder, cardPan, cardRequest, mccCardRequests = [] }) => {
  const { accountId } = SettingsService.get("cardIssuingAccounts").sudo;
  const fundingSource = SettingsService.get("sudoFundingSources").ngn.default;
  const mccs = mccCardRequests.map((mccCardRequest) => mccCardRequest.Mcc?.mcc).filter(Boolean);
  const spendingLimit =
    cardRequest?.policy &&
    (await PolicyRepo.getPolicy({
      queryParams: {
        id: cardRequest.policy,
      },
    }));
  return {
    type: "physical",
    currency: "NGN",
    issuerCountry: "NGA",
    status: "active",
    spendingControls: {
      allowedCategories: mccs,
      blockedCategories: [],
      channels: {
        atm: cardRequest?.atmWithdrawals || true,
        pos: cardRequest?.posTransaction || true,
        web: cardRequest?.onlineTransaction || true,
        mobile: true,
      },
      spendingLimits: spendingLimit
        ? [
            {
              amount: spendingLimit.maxAmount / 100,
              interval: spendingLimit.frequency,
            },
          ]
        : [],
    },
    sendPINSMS: false,
    customerId: cardHolder.externalIdentifier,
    brand: "Verve",
    fundingSourceId: fundingSource,
    debitAccountId: accountId,
    number: cardPan,
  };
};

module.exports = createCardRequestFormatter;
