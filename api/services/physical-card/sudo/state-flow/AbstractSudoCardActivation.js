const NotFoundError = require("../../../../utils/not-found-error");

class AbstractSudoCardActivation {
  /**
   * @returns {string}
   * @throws
   */
  // eslint-disable-next-line class-methods-use-this
  getState = () => {
    throw new NotFoundError("Subclass state not defined");
  };

  /**
   *
   * @param {SudoCardProcessRequest} sudoCardProcessRequest
   * @throws
   */
  // eslint-disable-next-line class-methods-use-this
  handleRequest = async (sudoCardProcessRequest) => {
    throw new NotFoundError(
      `Subclass AbstractSudoCardActivation Implementation for request ${sudoCardProcessRequest}`
    );
  };
}

module.exports = AbstractSudoCardActivation;
