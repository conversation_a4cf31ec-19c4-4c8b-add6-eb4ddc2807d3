const AbstractSudoCardActivation = require("./AbstractSudoCardActivation");
const SudoProcessState = require("../sudo-process-state");
const { CARD_ISSUER } = require("../../../../models/cardissuer");
const { STATUSES } = require("../../../../models/utils");
const { VirtualCardRepo, CardRequestRepo, MccRepo, CardPolicyRepo } = require("../../../../repositories/index.repo");
const { cardType, getLastDayOfMonth, getCurrencyNameFromIso3 } = require("../../../../utils");
const NotificationService = require("../../../notification");

class CardCreationSuccessfulState extends AbstractSudoCardActivation {
  // eslint-disable-next-line class-methods-use-this
  getState = () => {
    return SudoProcessState.CARD_CREATION_SUCCESSFUL_STATE;
  };

  /**
   *
   * @param {SudoCardProcessRequest} sudoCardProcessRequest
   * @throws
   */
  // eslint-disable-next-line class-methods-use-this
  handleRequest = async (sudoCardProcessRequest) => {
    // TODO SAVE CARD DETAILS TO THE DATABASE & IMPLEMENT EVENT LISTENER
    const { brand, currency, maskedPan, expiryMonth, expiryYear, _id } = sudoCardProcessRequest.getCreateCardResponse().data;
    const cardRequestObject = sudoCardProcessRequest.getDBCardRequest();

    const mccCardRequests = sudoCardProcessRequest.getMccCardRequests();

    const mccs = mccCardRequests?.map((mccCardRequest) => mccCardRequest?.Mcc).filter(Boolean);
    const cardModelPayload = {
      name: `${sudoCardProcessRequest.getCardOwner().firstName} ${sudoCardProcessRequest.getCardOwner().lastName}(Physical Card)`,
      currency,
      amount: 0,
      company: sudoCardProcessRequest.getCompany().id,
      budget: cardRequestObject?.budget || null,
      balance: cardRequestObject?.balance || null,
      type: cardType().physical,
      issuer: CARD_ISSUER.sudo,
      status: STATUSES.ACTIVE,
      user: sudoCardProcessRequest.getDBCardRequest().owner,
      requiresPinChange: false,
      onlineTransaction: cardRequestObject?.onlineTransaction || true,
      atmWithdrawals: cardRequestObject?.atmWithdrawals || true,
      posTransaction: cardRequestObject?.posTransaction || true,
      contactlessTransaction: cardRequestObject?.contactlessTransaction || true,
      brand,
      pan: maskedPan,
      exp_month: expiryMonth,
      exp_year: expiryYear,
      externalIdentifier: _id,
    };
    const createdCard = await VirtualCardRepo.create(cardModelPayload);

    if (mccs?.length) {
      await MccRepo.createMccCards(createdCard.id, mccs);
    }

    if (cardRequestObject?.policy) {
      await CardPolicyRepo.create({
        payload: {
          policy: cardRequestObject.policy,
          card: createdCard.id,
        },
      });
    }

    notifyCardOwner({ sudoCardProcessRequest, currency, cardRequestObject, cardModelPayload, createdCard });

    sudoCardProcessRequest.setSavedCardDbResponse(createdCard);
    sudoCardProcessRequest.setState(SudoProcessState.GENERATE_CARD_TOKEN_STATE);

    // Mark card request as processed
    await CardRequestRepo.update({ queryParams: { code: cardRequestObject.code }, payload: { status: STATUSES.PROCESSED } });
  };
}

module.exports = CardCreationSuccessfulState;

function notifyCardOwner({ sudoCardProcessRequest, currency, cardRequestObject, cardModelPayload, createdCard }) {
  const payload = {
    companyName: sudoCardProcessRequest.getCompany().name,
    cardType: "physical",
    currencyName: getCurrencyNameFromIso3(currency),
    firstName: sudoCardProcessRequest.getCardOwner().firstName,
    cardUsage: "debit card",
    cardHolder: `${sudoCardProcessRequest.getCardOwner().firstName} ${sudoCardProcessRequest.getCardOwner().lastName}`,
    budget: cardRequestObject?.Budget?.name,
    balance: cardRequestObject?.Balance?.name,
    validity: getLastDayOfMonth(cardModelPayload.exp_month, cardModelPayload.exp_year),
    isPhysicalCard: true,
    bvnRequired: false,
    verificationUrl: null,
  };

  const sourceConfig = {
    subject: `Physical Card creation`,
    from_name: `Bujeti`,
  };

  const notificationPayload = {
    company: sudoCardProcessRequest.getCompany().id,
    user_id: sudoCardProcessRequest.getDBCardRequest().owner,
    type: `info`,
    badge: `info`,
    title: `Your physical ${payload.currencyName} card has been created`,
    message: `Your physical ${payload.currencyName} card has been created`,
    body: {
      code: createdCard.code,
      entity: "CreditCard",
    },
    table: {
      code: createdCard.code,
      entity: "CreditCard",
    },
    reference_code: createdCard.code,
    event: "cardCreated",
  };
  NotificationService.saveNotification(notificationPayload);
  NotificationService.notifyUser(sudoCardProcessRequest.getCardOwner().email, "card-created-successfully", payload, sourceConfig);
}
