const AbstractSudoCardActivation = require("./AbstractSudoCardActivation");
const SudoProcessState = require("../sudo-process-state");
const { ValidationError } = require("../../../../utils/error.utils");

class CardCreationUnSuccessfulState extends AbstractSudoCardActivation {
  // eslint-disable-next-line class-methods-use-this
  getState = () => {
    return SudoProcessState.CARD_CREATION_UNSUCCESSFUL_STATE;
  };

  /**
   *
   * @param {SudoCardProcessRequest} sudoCardProcessRequest
   * @throws
   */
  // eslint-disable-next-line class-methods-use-this
  handleRequest = async (sudoCardProcessRequest) => {
    // TODO use a switch case to filter all possible errors
    if (sudoCardProcessRequest.getCreateCardResponse().error) {
      throw new ValidationError(
        sudoCardProcessRequest.getCreateCardResponse().message ||
          "Could not create card. Please try again later."
      );
    }
  };
}

module.exports = CardCreationUnSuccessfulState;
