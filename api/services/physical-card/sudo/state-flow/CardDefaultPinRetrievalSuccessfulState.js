const AbstractSudoCardActivation = require("./AbstractSudoCardActivation");
const SudoProcessState = require("../sudo-process-state");

class CardDefaultPinRetrievalSuccessfulState extends AbstractSudoCardActivation {
  // eslint-disable-next-line class-methods-use-this
  getState = () => {
    return SudoProcessState.CARD_DEFAULT_PIN_RETRIEVAL_SUCCESSFUL_STATE;
  };

  /**
   *
   * @param {SudoCardProcessRequest} sudoCardProcessRequest
   * @throws
   */
  // eslint-disable-next-line class-methods-use-this
  handleRequest = async (sudoCardProcessRequest) => {
    sudoCardProcessRequest.setState(SudoProcessState.COMPLETE_STATE);
  };
}

module.exports = CardDefaultPinRetrievalSuccessfulState;
