const AbstractSudoCardActivation = require("./AbstractSudoCardActivation");
const SudoProcessState = require("../sudo-process-state");
const { ValidationError } = require("../../../../utils/error.utils");

class CardDefaultPinRetrievalUnSuccessfulState extends AbstractSudoCardActivation {
  // eslint-disable-next-line class-methods-use-this
  getState = () => {
    return SudoProcessState.CARD_DEFAULT_PIN_RETRIEVAL_UNSUCCESSFUL_STATE;
  };

  /**
   *
   * @param {SudoCardProcessRequest} sudoCardProcessRequest
   * @throws
   */
  // eslint-disable-next-line class-methods-use-this
  handleRequest = async (sudoCardProcessRequest) => {
    // TODO use a switch case to filter all possible errors
    const { error, message } = sudoCardProcessRequest.getCardDefaultPin();
    if (error) {
      throw new ValidationError(
        message ||
          "Could not retrieve card default pin. Please try again later."
      );
    }
  };
}

module.exports = CardDefaultPinRetrievalUnSuccessfulState;
