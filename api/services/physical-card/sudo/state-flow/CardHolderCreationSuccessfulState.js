const AbstractSudoCardActivation = require("./AbstractSudoCardActivation");
const SudoProcessState = require("../sudo-process-state");
const { CARD_ISSUER } = require("../../../../models/cardissuer");
const { STATUSES } = require("../../../../models/utils");
const { CardHolderRepo } = require("../../../../repositories/index.repo");

class CardHolderCreationSuccessfulState extends AbstractSudoCardActivation {
  // eslint-disable-next-line class-methods-use-this
  getState = () => {
    return SudoProcessState.CARD_HOLDER_CREATION_SUCCESSFUL_STATE;
  };

  /**
   *
   * @param {SudoCardProcessRequest} sudoCardProcessRequest
   * @throws
   */
  // eslint-disable-next-line class-methods-use-this
  handleRequest = async (sudoCardProcessRequest) => {
    // TODO IMPLEMENT EVENT LISTENER FOR THINGS LIKE THIS
    const { _id } = sudoCardProcessRequest.getSudoCustomerResponse().data;
    const saveCustomerPayload = {
      company: sudoCardProcessRequest.getCompany().id,
      user: sudoCardProcessRequest.getDBCardRequest().owner,
      externalIdentifier: _id,
      provider: CARD_ISSUER.sudo,
      status: STATUSES.ACTIVE,
    };
    const savedCustomer = await CardHolderRepo.create(saveCustomerPayload);

    sudoCardProcessRequest.setBujetiCustomerResponse(savedCustomer);
    sudoCardProcessRequest.setState(SudoProcessState.CREATE_CARD_WITH_PAN_STATE);
  };
}

module.exports = CardHolderCreationSuccessfulState;
