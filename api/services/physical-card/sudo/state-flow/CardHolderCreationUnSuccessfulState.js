const AbstractSudoCardActivation = require("./AbstractSudoCardActivation");
const SudoProcessState = require("../sudo-process-state");
const { ValidationError } = require("../../../../utils/error.utils");

class CardHolderCreationUnSuccessfulState extends AbstractSudoCardActivation {
  // eslint-disable-next-line class-methods-use-this
  getState = () => {
    return SudoProcessState.CARD_HOLDER_CREATION_UNSUCCESSFUL_STATE;
  };

  /**
   * Handles a cardholder creation request in an unsuccessful state.
   * @param {SudoCardProcessRequest} sudoCardProcessRequest - The request object.
   * @throws {ValidationError} Throws a validation error if there is an error in the response.
   * @returns {Promise<void>} A Promise that resolves when the request is handled.
   */
  // eslint-disable-next-line class-methods-use-this
  handleRequest = async (sudoCardProcessRequest) => {
    // TODO use a switch case to filter all possible errors
    if (sudoCardProcessRequest.getSudoCustomerResponse().error) {
      throw new ValidationError(
        sudoCardProcessRequest.getSudoCustomerResponse().message ||
          "Could not profile this user for a Card. Please try again later."
      );
    }
  };
}

module.exports = CardHolderCreationUnSuccessfulState;
