const AbstractSudoCardActivation = require("./AbstractSudoCardActivation");
const SudoProcessState = require("../sudo-process-state");

class CardPanDoesNotExistInDbState extends AbstractSudoCardActivation {
  // eslint-disable-next-line class-methods-use-this
  getState = () => {
    return SudoProcessState.CARD_PAN_DOES_NOT_EXIST_IN_DB_STATE;
  };

  /**
   *
   * @param {SudoCardProcessRequest} sudoCardProcessRequest
   * @throws
   */
  // eslint-disable-next-line class-methods-use-this
  handleRequest = async (sudoCardProcessRequest) => {
    sudoCardProcessRequest.setState(
      SudoProcessState.VERIFY_CARD_HOLDER_EXIST_STATE
    );
  };
}

module.exports = CardPanDoesNotExistInDbState;
