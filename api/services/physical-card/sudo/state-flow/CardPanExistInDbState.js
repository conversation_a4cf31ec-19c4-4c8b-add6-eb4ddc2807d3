const AbstractSudoCardActivation = require("./AbstractSudoCardActivation");
const SudoProcessState = require("../sudo-process-state");

class CardPanExistInDbState extends AbstractSudoCardActivation {
  // eslint-disable-next-line class-methods-use-this
  getState = () => {
    return SudoProcessState.CARD_PAN_EXIST_IN_DB_STATE;
  };

  /**
   *
   * @param {SudoCardProcessRequest} sudoCardProcessRequest
   * @throws
   */
  // eslint-disable-next-line class-methods-use-this
  handleRequest = async (sudoCardProcessRequest) => {
    sudoCardProcessRequest.setState(SudoProcessState.GENERATE_CARD_TOKEN_STATE);
  };
}

module.exports = CardPanExistInDbState;
