
const AbstractSudoCardActivation = require("./AbstractSudoCardActivation");
const SudoProcessState = require("../sudo-process-state");
const { ValidationError } = require("../../../../utils/error.utils");

class CardTokenGenerationUnSuccessfulState extends AbstractSudoCardActivation {
  // eslint-disable-next-line class-methods-use-this
  getState = () => {
    return SudoProcessState.CARD_TOKEN_GENERATION_UNSUCCESSFUL_STATE;
  };

  /**
   *
   * @param {SudoCardProcessRequest} sudoCardProcessRequest
   * @throws
   */
  // eslint-disable-next-line class-methods-use-this
  handleRequest = async (sudoCardProcessRequest) => {
    // TODO use a switch case to filter all possible errors
    const { error, message } = sudoCardProcessRequest.getCardToken();
    if (error) {
      throw new ValidationError(
        message || "Could not generate card token. Please try again later."
      );
    }
  };
}

module.exports = CardTokenGenerationUnSuccessfulState;
