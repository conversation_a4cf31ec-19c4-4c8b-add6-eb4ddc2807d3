const AbstractSudoCardActivation = require("./AbstractSudoCardActivation");
const SudoProcessState = require("../sudo-process-state");
const createCustomerRequestFormatter = require("../request-formatter/sudo-create-customer-payload");
const sudoApiAction = require("../external-api-service");

class CreateCardHolderState extends AbstractSudoCardActivation {
  // eslint-disable-next-line class-methods-use-this
  getState = () => {
    return SudoProcessState.CREATE_CARD_HOLDER_STATE;
  };

  /**
   *
   * @param {SudoCardProcessRequest} sudoCardProcessRequest
   * @throws
   */
  // eslint-disable-next-line class-methods-use-this
  handleRequest = async (sudoCardProcessRequest) => {
    const sudoRequestPayload = await createCustomerRequestFormatter({
      userId: sudoCardProcessRequest.getDBCardRequest()?.owner || sudoCardProcessRequest.getUser().id,
      companyId: sudoCardProcessRequest.getCompany().id,
      bvn: sudoCardProcessRequest.getBvn(),
    });
    const sudoResponse = await sudoApiAction.createCustomer(sudoRequestPayload, sudoCardProcessRequest.getCompany().id);

    if (sudoResponse.providerStatus === "success") {
      sudoCardProcessRequest.setSudoCustomerResponse({
        data: sudoResponse.data,
      });
      sudoCardProcessRequest.setState(SudoProcessState.CARD_HOLDER_CREATION_SUCCESSFUL_STATE);
    } else {
      sudoCardProcessRequest.setSudoCustomerResponse({
        message: sudoResponse.message,
        error: sudoResponse.error,
      });
      sudoCardProcessRequest.setState(SudoProcessState.CARD_HOLDER_CREATION_UNSUCCESSFUL_STATE);
    }
  };
}

module.exports = CreateCardHolderState;
