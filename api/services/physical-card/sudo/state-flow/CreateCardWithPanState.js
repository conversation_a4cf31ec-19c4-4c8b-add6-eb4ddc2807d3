const AbstractSudoCardActivation = require("./AbstractSudoCardActivation");
const SudoProcessState = require("../sudo-process-state");
const createCardRequestFormatter = require("../request-formatter/sudo-create-physical-card-payload");
const sudoApiAction = require("../external-api-service");

class CreateCardWithPanState extends AbstractSudoCardActivation {
  // eslint-disable-next-line class-methods-use-this
  getState = () => {
    return SudoProcessState.CREATE_CARD_WITH_PAN_STATE;
  };

  /**
   *
   * @param {SudoCardProcessRequest} sudoCardProcessRequest
   * @throws
   */
  // eslint-disable-next-line class-methods-use-this
  handleRequest = async (sudoCardProcessRequest) => {
    const sudoCardRequestPayload = await createCardRequestFormatter({
      cardHolder: sudoCardProcessRequest.getCardHolder() || sudoCardProcessRequest.getBujetiCustomerResponse(),
      cardPan: sudoCardProcessRequest.getCardPan(),
      cardRequest: sudoCardProcessRequest.getDBCardRequest(),
      mccCardRequests: sudoCardProcessRequest.getMccCardRequests(),
    });
    const sudoResponse = await sudoApiAction.createCard(sudoCardRequestPayload, sudoCardProcessRequest.getCompany().id);

    if (sudoResponse.providerStatus === "success") {
      sudoCardProcessRequest.setCreateCardRequest(sudoCardRequestPayload);
      sudoCardProcessRequest.setCreateCardResponse({ data: sudoResponse.data });
      sudoCardProcessRequest.setState(SudoProcessState.CARD_CREATION_SUCCESSFUL_STATE);
    } else {
      sudoCardProcessRequest.setCreateCardResponse({
        message: sudoResponse.message,
        error: sudoResponse.error,
      });
      sudoCardProcessRequest.setState(SudoProcessState.CARD_CREATION_UNSUCCESSFUL_STATE);
    }
  };
}

module.exports = CreateCardWithPanState;
