const AbstractSudoCardActivation = require("./AbstractSudoCardActivation");
const SudoProcessState = require("../sudo-process-state");
const sudoApiAction = require("../external-api-service");

class GenerateCardTokenState extends AbstractSudoCardActivation {
  // eslint-disable-next-line class-methods-use-this
  getState = () => {
    return SudoProcessState.GENERATE_CARD_TOKEN_STATE;
  };

  /**
   *
   * @param {SudoCardProcessRequest} sudoCardProcessRequest
   * @throws
   */
  // eslint-disable-next-line class-methods-use-this
  handleRequest = async (sudoCardProcessRequest) => {
    const cardId =
      // eslint-disable-next-line no-underscore-dangle
      sudoCardProcessRequest.getCreateCardResponse()?.data?._id ||
      sudoCardProcessRequest.getSavedCardDbResponse()?.externalIdentifier;
    const sudoResponse = await sudoApiAction.generateCardToken(
      { cardId },
      sudoCardProcessRequest.getCompany().id
    );

    if (sudoResponse.providerStatus === "success") {
      sudoCardProcessRequest.setCardToken({ data: sudoResponse.data });
      sudoCardProcessRequest.setState(
        SudoProcessState.CARD_TOKEN_GENERATION_SUCCESSFUL_STATE
      );
    } else {
      sudoCardProcessRequest.setCardToken({
        message: sudoResponse.message,
        error: sudoResponse.error,
      });
      sudoCardProcessRequest.setState(
        SudoProcessState.CARD_TOKEN_GENERATION_UNSUCCESSFUL_STATE
      );
    }
  };
}

module.exports = GenerateCardTokenState;
