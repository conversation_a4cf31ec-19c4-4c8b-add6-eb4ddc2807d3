const AbstractSudoCardActivation = require("./AbstractSudoCardActivation");
const SudoProcessState = require("../sudo-process-state");
const sudoApiAction = require("../external-api-service");

class RetrieveCardDefaultPinState extends AbstractSudoCardActivation {
  // eslint-disable-next-line class-methods-use-this
  getState = () => {
    return SudoProcessState.RETRIEVE_CARD_DEFAULT_PIN_STATE;
  };

  /**
   *
   * @param {SudoCardProcessRequest} sudoCardProcessRequest
   * @throws
   */
  // eslint-disable-next-line class-methods-use-this
  handleRequest = async (sudoCardProcessRequest) => {
    const cardId =
      // eslint-disable-next-line no-underscore-dangle
      sudoCardProcessRequest.getCreateCardResponse()?.data?._id ||
      sudoCardProcessRequest.getSavedCardDbResponse()?.externalIdentifier;
    const cardToken = sudoCardProcessRequest.getCardToken().data.token;
    const sudoResponse = await sudoApiAction.getCardDefaultPin(
      {
        cardId,
        cardToken,
      },
      sudoCardProcessRequest.getCompany().id
    );

    if (sudoResponse.providerStatus === "success") {
      sudoCardProcessRequest.setCardDefaultPin({ data: sudoResponse.data });
      sudoCardProcessRequest.setState(
        SudoProcessState.CARD_DEFAULT_PIN_RETRIEVAL_SUCCESSFUL_STATE
      );
    } else {
      sudoCardProcessRequest.setCardDefaultPin({
        message: sudoResponse.message,
        error: sudoResponse.error,
      });
      sudoCardProcessRequest.setState(
        SudoProcessState.CARD_DEFAULT_PIN_RETRIEVAL_UNSUCCESSFUL_STATE
      );
    }
  };
}

module.exports = RetrieveCardDefaultPinState;
