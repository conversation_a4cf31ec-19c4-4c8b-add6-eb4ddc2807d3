const AbstractSudoCardActivation = require("./AbstractSudoCardActivation");
const SudoProcessState = require("../sudo-process-state");
const { VirtualCardRepo, CardRequestRepo, MccRepo } = require("../../../../repositories/index.repo");
const { STATUSES } = require("../../../../models/status");

class StartState extends AbstractSudoCardActivation {
  // eslint-disable-next-line class-methods-use-this
  _generateMaskPan = (cardPan) => {
    return `${cardPan.slice(0, 6)}******${cardPan.slice(-4)}`;
  };

  // eslint-disable-next-line class-methods-use-this
  getState = () => {
    return SudoProcessState.START_STATE;
  };

  /**
   *
   * @param {SudoCardProcessRequest} sudoCardProcessRequest
   * @throws
   */
  // eslint-disable-next-line class-methods-use-this
  handleRequest = async (sudoCardProcessRequest) => {
    // Check if the cardPan already exists in the db
    // If card exist it means it has been mapped to a physical card in sudo ;
    // eslint-disable-next-line no-underscore-dangle
    const maskedPan = this._generateMaskPan(sudoCardProcessRequest.getCardPan());
    const card = await VirtualCardRepo.find({
      pan: maskedPan,
    });

    const cardRequest = await CardRequestRepo.findOne({
      conditions: { code: sudoCardProcessRequest.getCardRequestCode() },
    });

    if (cardRequest) {
      sudoCardProcessRequest.setDBCardRequest(cardRequest);
      const { cardOwner } = cardRequest;
      sudoCardProcessRequest.setCardOwner(cardOwner);
      const mccCardRequests = await MccRepo.getMccCardRequests({
        queryParams: {
          cardRequest: cardRequest.id,
          status: STATUSES.ACTIVE,
        },
      });
      sudoCardProcessRequest.setMccCardRequests(mccCardRequests);
    }

    if (card) {
      sudoCardProcessRequest.setSavedCardDbResponse(card);
      sudoCardProcessRequest.setState(SudoProcessState.CARD_PAN_EXIST_IN_DB_STATE);
    } else {
      sudoCardProcessRequest.setState(SudoProcessState.CARD_PAN_DOES_NOT_EXIST_IN_DB_STATE);
    }
  };
}

module.exports = StartState;
