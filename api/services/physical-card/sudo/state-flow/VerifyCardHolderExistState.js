const AbstractSudoCardActivation = require("./AbstractSudoCardActivation");
const SudoProcessState = require("../sudo-process-state");
const { CardHolderRepo } = require("../../../../repositories/index.repo");
const { STATUSES } = require("../../../../models/status");
const { CARD_ISSUER } = require("../../../../models/cardissuer");

class VerifyCardHolderExistState extends AbstractSudoCardActivation {
  // eslint-disable-next-line class-methods-use-this
  getState = () => {
    return SudoProcessState.VERIFY_CARD_HOLDER_EXIST_STATE;
  };

  /**
   *
   * @param {SudoCardProcessRequest} sudoCardProcessRequest
   * @throws
   */
  // eslint-disable-next-line class-methods-use-this
  handleRequest = async (sudoCardProcessRequest) => {
    const cardholder = await CardHolderRepo.find({
      condition: {
        user: sudoCardProcessRequest.getDBCardRequest()?.owner || sudoCardProcessRequest.getUser().id,
        status: STATUSES.ACTIVE,
        provider: CARD_ISSUER.sudo,
      },
    });
    if (cardholder) {
      sudoCardProcessRequest.setCardHolder(cardholder);
      sudoCardProcessRequest.setState(SudoProcessState.CARD_HOLDER_EXIST_STATE);
    } else {
      sudoCardProcessRequest.setState(
        SudoProcessState.CARD_HOLDER_DOES_NOT_EXIST_STATE
      );
    }
  };
}

module.exports = VerifyCardHolderExistState;
