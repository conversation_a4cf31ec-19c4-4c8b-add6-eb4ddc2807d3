const StartState = require("./StartState");
const RetrieveCardDefaultPinState = require("./RetrieveCardDefaultPinState");
const GenerateCardTokenState = require("./GenerateCardTokenState");
const CreateCardWithPanState = require("./CreateCardWithPanState");
const CreateCardHolderState = require("./CreateCardHolderState");
const CompleteState = require("./CompleteState");
const CardTokenGenerationUnSuccessfulState = require("./CardTokenGenerationUnSuccessfulState");
const CardTokenGenerationSuccessfulState = require("./CardTokenGenerationSuccessfulState");
const CardHolderExistState = require("./CardHolderExistState");
const CardHolderDoesNotExistState = require("./CardHolderDoesNotExistState");
const CardHolderCreationUnSuccessfulState = require("./CardHolderCreationUnSuccessfulState");
const CardHolderCreationSuccessfulState = require("./CardHolderCreationSuccessfulState");
const CardDefaultPinRetrievalUnSuccessfulState = require("./CardDefaultPinRetrievalUnSuccessfulState");
const CardDefaultPinRetrievalSuccessfulState = require("./CardDefaultPinRetrievalSuccessfulState");
const CardCreationUnSuccessfulState = require("./CardCreationUnSuccessfulState");
const CardCreationSuccessfulState = require("./CardCreationSuccessfulState");
const VerifyCardHolderExistState = require("./VerifyCardHolderExistState");
const CardPanDoesNotExistInDbState = require("./CardPanDoesNotExistInDbState");
const CardPanExistInDbState = require("./CardPanExistInDbState");

const states = [
  new StartState(),
  new RetrieveCardDefaultPinState(),
  new GenerateCardTokenState(),
  new CreateCardWithPanState(),
  new CreateCardHolderState(),
  new CompleteState(),
  new CardTokenGenerationUnSuccessfulState(),
  new CardTokenGenerationSuccessfulState(),
  new CardHolderExistState(),
  new CardHolderDoesNotExistState(),
  new CardHolderCreationUnSuccessfulState(),
  new CardHolderCreationSuccessfulState(),
  new CardDefaultPinRetrievalUnSuccessfulState(),
  new CardDefaultPinRetrievalSuccessfulState(),
  new CardCreationUnSuccessfulState(),
  new CardCreationSuccessfulState(),
  new VerifyCardHolderExistState(),
  new CardPanDoesNotExistInDbState(),
  new CardPanExistInDbState(),
];

const mapOfStates = new Map();
states.forEach((state) => mapOfStates.set(state.getState(), state));

module.exports = mapOfStates;
