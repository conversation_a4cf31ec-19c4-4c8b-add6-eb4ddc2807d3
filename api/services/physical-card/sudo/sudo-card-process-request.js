class SudoCardProcessRequest {
  constructor(state, user, company, cardPan, bvn, cardRequest) {
    this.state = state;
    this.user = user;
    this.company = company;
    this.cardPan = cardPan;
    this.bvn = bvn;
    this.cardRequestCode = cardRequest;
  }

  getBvn() {
    return this.bvn;
  }

  /**
   * Set the state of the card process request.
   * @param {string} state - The new state to set.
   */
  setState(state) {
    this.state = state;
  }

  /**
   * Set the state of the card process request.
   * @returns {SudoProcessState} state - The new state of the request.
   */
  getState() {
    return this.state;
  }

  setCardHolder(cardHolder) {
    this.cardHolder = cardHolder;
  }

  getCardHolder() {
    return this.cardHolder;
  }

  getUser() {
    return this.user;
  }

  getCompany() {
    return this.company;
  }

  getCardPan() {
    return this.cardPan;
  }

  getCardRequestCode() {
    return this.cardRequestCode;
  }

  /**
   * Set the Sudo Customer Response, which can have either message and error properties or a data property.
   * @param {Object} sudoCustomerResponse - The Sudo Customer Response object.
   * @property {string} [message] - The message property (present if there's an error).
   * @property {string} [error] - The error property (present if there's an error).
   * @property {any} [data] - The data property (present if there's no error).
   */
  setSudoCustomerResponse(sudoCustomerResponse) {
    this.sudoCustomerResponse = sudoCustomerResponse;
  }

  /**
   * Perform some action and return either a response with message and error or a response with data.
   * @returns {Object} An object representing the response.
   * @property {string} [message] - The message property (present if there's an error).
   * @property {string} [error] - The error property (present if there's an error).
   * @property {any} [data] - The data property (present if there's no error).
   */
  getSudoCustomerResponse() {
    return this.sudoCustomerResponse;
  }

  setBujetiCustomerResponse(savedCustomer) {
    this.savedCustomer = savedCustomer;
  }

  getBujetiCustomerResponse() {
    return this.savedCustomer;
  }

  setCreateCardRequest(createCardRequest) {
    this.createCardRequest = createCardRequest;
  }

  getCreateCardRequest() {
    return this.createCardRequest;
  }

  setCreateCardResponse(createCardResponseData) {
    this.createCardResponseData = createCardResponseData;
  }

  getCreateCardResponse() {
    return this.createCardResponseData;
  }

  setSavedCardDbResponse(createCardModel) {
    this.createCardModel = createCardModel;
  }

  getSavedCardDbResponse() {
    return this.createCardModel;
  }

  setCardToken(cardToken) {
    this.cardToken = cardToken;
  }

  getCardToken() {
    return this.cardToken;
  }

  setCardDefaultPin(cardDefaultPin) {
    this.cardDefaultPin = cardDefaultPin;
  }

  getCardDefaultPin() {
    return this.cardDefaultPin;
  }

  setDBCardRequest(dbCardRequestPayload) {
    this.dbCardRequest = dbCardRequestPayload;
  }

  getDBCardRequest() {
    return this.dbCardRequest;
  }

  setCardOwner(payload) {
    this.cardOwner = payload;
  }

  getCardOwner() {
    return this.cardOwner;
  }

  getMccCardRequests() {
    return this.mccCardRequests;
  }

  setMccCardRequests(payload) {
    this.mccCardRequests = payload;
  }
}

module.exports = SudoCardProcessRequest;
