const SudoProcessState = Object.freeze({
  START_STATE: "START_STATE",

  CARD_PAN_EXIST_IN_DB_STATE: "CARD_PAN_EXIST_IN_DB_STATE",
  CARD_PAN_DOES_NOT_EXIST_IN_DB_STATE: "CARD_PAN_DOES_NOT_EXIST_IN_DB_STATE",

  VERIFY_CARD_HOLDER_EXIST_STATE: "VERIFY_CARD_HOLDER_EXIST_STATE",
  CARD_HOLDER_EXIST_STATE: "CARD_HOLDER_EXIST_STATE",
  CARD_HOLDER_DOES_NOT_EXIST_STATE: "CARD_HOLDER_DOES_NOT_EXIST_STATE",

  CREATE_CARD_HOLDER_STATE: "CREATE_CARD_HOLDER_STATE",
  CARD_HOLDER_CREATION_SUCCESSFUL_STATE:
    "CARD_HOLDER_CREATION_SUCCESSFUL_STATE",
  CARD_HOLDER_CREATION_UNSUCCESSFUL_STATE:
    "CARD_HOLDER_CREATION_UNSUCCESSFUL_STATE",

  CREATE_CARD_WITH_PAN_STATE: "CREATE_CARD_WITH_PAN_STATE",
  CARD_CREATION_SUCCESSFUL_STATE: "CARD_CREATION_SUCCESSFUL_STATE",
  CARD_CREATION_UNSUCCESSFUL_STATE: "CARD_CREATION_UNSUCCESSFUL_STATE",

  GENERATE_CARD_TOKEN_STATE: "GENERATE_CARD_TOKEN_STATE",
  CARD_TOKEN_GENERATION_SUCCESSFUL_STATE:
    "CARD_TOKEN_GENERATION_SUCCESSFUL_STATE",
  CARD_TOKEN_GENERATION_UNSUCCESSFUL_STATE:
    "CARD_TOKEN_GENERATION_UNSUCCESSFUL_STATE",

  RETRIEVE_CARD_DEFAULT_PIN_STATE: "FETCH_CARD_DEFAULT_PIN_STATE",
  CARD_DEFAULT_PIN_RETRIEVAL_SUCCESSFUL_STATE:
    "CARD_DEFAULT_PIN_RETRIEVAL_SUCCESSFUL_STATE",
  CARD_DEFAULT_PIN_RETRIEVAL_UNSUCCESSFUL_STATE:
    "CARD_DEFAULT_PIN_RETRIEVAL_UNSUCCESSFUL_STATE",

  COMPLETE_STATE: "COMPLETE_STATE",
  STOP_STATE: "END_STATE",
});

module.exports = SudoProcessState;
