/* eslint-disable consistent-return */
/* eslint-disable no-lonely-if */
/* eslint-disable no-extra-boolean-cast */
/* eslint-disable camelcase */
const { Op } = require("sequelize");
const {
  PolicyRepo,
  PolicyTypeRepo,
  BudgetPolicyRepo,
  BudgetRepo,
  TransactionRepo,
  PolicyViolationRepo,
  PolicyConditionRepo,
  PolicyConditionOperandRepo,
} = require("../repositories/index.repo");
const responseUtils = require("../utils/response.utils");
const { NotFoundError, ValidationError, ExistsError, HttpException, HttpStatus } = require("../utils/error.utils");
const Helper = require("./helper.service");
const { sequelize, PolicyRestriction } = require("../models");
const { STATUSES } = require("../models/status");
const SettingsService = require("./settings");
const NotificationService = require("./notification");
const Utils = require("../utils/utils");
const Sanitizer = require("../utils/sanitizer");
const { PolicyValidator } = require("../validators");
const { getAssets, getAsset } = require("./asset");
const { getUsers } = require("./user");
const { PolicyDocument, PolicyUserException } = require("../models");
const { linkPolicyDocuments, addExceptionList } = require("../repositories/policy.repo");
const BackgroundService = require("./backgroundService");
const { BACKGROUND } = require("../mocks/constants.mock");

const Service = {
  selectOptions: [
    "name",
    "type",
    "description",
    "company",
    "status",
    "currency",
    "minAmount",
    "maxAmount",
    "frequency",
    "period",
    "receiptPolicyCondition",
    "receiptPolicyValue",
    "requiresBudget",
    "requiresCategory",
    "requiresDescription",
    "created_at",
    "strict",
  ],
  async getPolicyTypes() {
    const policyTypes = await PolicyTypeRepo.getPolicyTypes({ selectOptions: ["name"] });
    if (!policyTypes.length) throw new NotFoundError("Policy types");

    return responseUtils.sendObjectResponse("Policy types successfully retrieved", policyTypes);
  },

  async createPolicy(data) {
    const {
      company,
      description,
      name,
      strict = true,
      currency,
      type: types,
      amountRange,
      frequency,
      period,
      receiptAmount: { condition: receiptPolicyCondition, value: receiptPolicyValue } = {},
      requiresBudget,
      requiresDescription,
      requiresCategory,
      documents = [],
      exceptions = [],
      conditions = [],
      lastModifiedBy,
    } = data;

    let hasNonCalendarPolicyType = false;
    const {
      "Calendar limits": Calendar_Limits,
      "Spending limits": Spending_Limits,
      "Receipt Policy": Reciept_Policy,
      "Custom Policy": CustomPolicy,
    } = SettingsService.get("POLICY_TYPES");
    const policyTypes = [{ id: CustomPolicy }];

    // Checking if policy with same name already exists
    const existingPolicy = await PolicyRepo.getPolicy({
      queryParams: { name, company, status: { [Op.notIn]: [STATUSES.DELETED] } },
      selectOptions: ["name"],
    });
    if (existingPolicy) throw new ExistsError("Policy already exists");

    const policy = await PolicyRepo.createPolicy({
      queryParams: {
        company,
        name,
        strict,
        status: STATUSES.ACTIVE,
        description,
        ...(hasNonCalendarPolicyType && { currency: currency || "NGN" }),
        ...(amountRange && {
          ...(amountRange.maxAmount && { maxAmount: amountRange.maxAmount }),
          ...(amountRange.minAmount && { minAmount: amountRange.minAmount }),
        }),
        ...(frequency && { frequency }),
        ...(period && { period: JSON.stringify(period) }),
        ...(receiptPolicyCondition && { receiptPolicyCondition }),
        ...(receiptPolicyValue && { receiptPolicyValue }),
        requiresBudget,
        requiresDescription,
        requiresCategory,
        lastModifiedBy,
      },
    });

    const policyTypesToLink = policyTypes.map((policyType) => ({ policy_type: policyType.id, policy: policy.id, company, status: STATUSES.ACTIVE }));

    await PolicyRestriction.bulkCreate(policyTypesToLink);

    const foundDocuments = (await getAssets({ code: documents })) || [];
    const documentIds = foundDocuments.map((document) => document.id);
    if (documentIds.length) await linkPolicyDocuments(policy.id, documentIds, company);

    const foundUsers = (await getUsers({ code: exceptions })) || [];
    const userIds = foundUsers.map((user) => user.id);
    if (userIds.length) await addExceptionList(policy.id, userIds, company);

    const response = Sanitizer.jsonify(policy);
    response.PolicyType = policyTypes;
    response.documents = foundDocuments;
    response.exceptions = foundUsers;

    if (conditions.length > 0) {
      const conditionsCreationResponse = await Promise.all(
        conditions.map((condition) => Service.createPolicyCondition({ ...condition, policy: policy.id }))
      );
      response.conditions = conditionsCreationResponse.some(({ success }) => !success) ? conditionsCreationResponse.map(({ data }) => data) : [];
    } else response.conditions = [];

    BackgroundService.addToQueue(BACKGROUND.QUEUES.POLICIES_PROMPT, BACKGROUND.JOBS.POLICIES_PROMPT, response, {
      delay: 30 * 1000, // 30 secs delay
    });

    return responseUtils.sendObjectResponse("Policy successfully created", response);
  },

  async createBudgetPolicy(data) {
    const { policy, budget: budgetCodes, throwErrorIfBudgetPolicyExists = true } = data;

    const budgets = await BudgetRepo.getAllBudgets({ queryParams: { code: budgetCodes } });
    if (!budgets.length) throw new NotFoundError("Budget");

    const BudgetPolicy = await Promise.all(
      budgets.map((budget) => Helper.findOrCreateBudgetPolicy({ policy, budget: budget.id }, throwErrorIfBudgetPolicyExists))
    );

    return responseUtils.sendObjectResponse("Budget's policy successfully created", BudgetPolicy);
  },

  async assignRolesToPolicy(data) {
    const { company, policy, roles: assignedRoles } = data;

    const roles = await RoleRepo.getAllRoles({ queryParams: { code: assignedRoles } });
    if (!roles) throw new NotFoundError("Roles");

    /**
     * Creating records of roles getting assigned to a policy
     */

    return responseUtils.sendObjectResponse("Roles assigned to policy successfully");
  },

  async getPolicies(data) {
    let { page = 1, perPage = 50 } = data;
    const { company, type: policyTypeCodeOrName, search, budget, category, status, to, from } = data;

    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    const skip = Math.max(page - 1, 0) * perPage;

    let policyType;

    if (policyTypeCodeOrName) {
      policyType = await PolicyTypeRepo.getPolicyType({
        queryParams: { [Op.or]: [{ code: policyTypeCodeOrName }, { name: policyTypeCodeOrName }] },
        selectOptions: ["name", "description"],
      });
      if (!policyType) throw new NotFoundError("Policy types");
    }

    const criteria = {
      company,
      ...(category && { category }),
      ...(policyTypeCodeOrName && {
        [Op.or]: [{ type: policyType.id }, sequelize.literal(`\`PolicyTypes\`.\`id\` = ${policyType.id}`)],
      }),
      status: {
        [Op.ne]: STATUSES.DELETED,
      },
    };

    if (from || to) {
      criteria.created_at = {};
      if (from && Utils.isValidDate(from)) criteria.created_at[Op.gte] = from;
      if (to && Utils.isValidDate(to)) criteria.created_at[Op.lte] = to;
    }

    if (status) {
      criteria.status = Utils.getStatusValues(status);
    }

    const [existingPolicies, total] = await Promise.all([
      PolicyRepo.getPolicies({
        ...(status ? { status } : { status: STATUSES.ACTIVE }),
        ...(search && { search }),
        ...(budget && { budget }),
        queryParams: criteria,
        perPage,
        skip,
        selectOptions: [...Service.selectOptions],
      }),
      PolicyRepo.countPolicies({
        ...(status ? { status } : { status: STATUSES.ACTIVE }),
        ...(search && { search }),
        ...(budget && { budget }),
        queryParams: criteria,
      }),
    ]);

    return responseUtils.sendObjectResponse("Policy successfully retrieved", {
      meta: {
        page,
        total,
        perPage,
        hasMore: total > page * perPage,
        nextPage: page + 1,
      },
      policies: existingPolicies,
    });
  },

  async getPolicy(data) {
    const { company, code } = data;
    const existingPolicy = await PolicyRepo.getPolicy({
      queryParams: {
        company,
        code,
        status: { [Op.notIn]: [STATUSES.DELETED] },
      },
      selectOptions: [...Service.selectOptions],
      budget: true,
    });
    if (!existingPolicy) throw new NotFoundError("Policy");

    const BudgetPolicies = await BudgetPolicyRepo.getBudgetPolicies({
      queryParams: { policy: existingPolicy.id },
    });

    const { ...response } = Sanitizer.jsonify(existingPolicy);
    response.BudgetPolicies = BudgetPolicies;

    return responseUtils.sendObjectResponse("Policy successfully retrieved", response);
  },

  async updatePolicy(data) {
    const {
      company,
      code,
      type: types = [],
      name,
      strict,
      description,
      currency,
      amountRange,
      amount,
      frequency,
      period,
      status,
      budget,
      receiptAmount: { condition: receiptPolicyCondition, value: receiptPolicyValue } = {},
      requiresBudget = undefined,
      requiresDescription = undefined,
      requiresCategory = undefined,
      lastModifiedBy,
      exceptions,
      documents,
      conditionsToUpdate = [],
      conditionsToAdd = [],
      conditionsToRemove = [],
    } = data;
    const {
      "Spending limits": Spending_Limits,
      "Calendar limits": Calendar_Limits,
      "Receipt Policy": Reciept_Policy,
    } = SettingsService.get("POLICY_TYPES");

    // Checking if policy with same name already exists
    const existingPolicy = await PolicyRepo.getPolicy({
      queryParams: { status: { [Op.notIn]: [STATUSES.DELETED] }, code, company },
      selectOptions: ["name"],
    });
    if (!existingPolicy) throw new NotFoundError("Policy");

    if (name) {
      const fetchPolicy = await PolicyRepo.getPolicy({ queryParams: { name, company }, selectOptions: ["name"] });
      if (fetchPolicy && existingPolicy.name !== fetchPolicy.name) throw new ValidationError("Policy with this name already exists");
    }

    const typesToInclude = [];
    const policyTypes = [];
    let updatePayload = {};

    await Promise.all(
      types.map(async (type) => {
        // Checking if type comes with these parameters
        if ((amount || currency || amountRange || frequency || period) && !type) throw new ValidationError("Must add policy type");
        // Checking if maxAmount is exceeded by minimum amount
        if (amountRange && amountRange.maxAmount < amountRange.minAmount)
          throw new ValidationError("Maximum amount must be greater than or equal to the minimum amount");

        let policyType;
        if (type) {
          policyType = await PolicyTypeRepo.getPolicyType({ queryParams: { code: type }, selectOptions: ["name", "description"] });
          if (!policyType) throw new NotFoundError("Policy types");

          // Checking if policy type meets criteria
          const invalidConfiguration =
            ((!amountRange || !frequency) && policyType.id === Spending_Limits) ||
            (!period && policyType.id === Calendar_Limits) ||
            (policyType.id !== Reciept_Policy && !frequency && !amountRange && !period);
          if (invalidConfiguration) throw new ValidationError("Wrong policy type");

          // Checking policy type to create update payload
          if (policyType.id === Spending_Limits) {
            const { error } = PolicyValidator.updateSpendingPolicy.validate({ frequency, currency, amountRange });
            if (error) throw new ValidationError(error.message);

            updatePayload = {
              ...updatePayload,
              ...(currency && { currency }),
              ...(amountRange && {
                ...(amountRange.maxAmount && amountRange.minAmount && { minAmount: amountRange.minAmount, maxAmount: amountRange.maxAmount }),
                ...(!amountRange.maxAmount && amountRange.minAmount && { minAmount: amountRange.minAmount, maxAmount: null }),
                ...(amountRange.maxAmount && !amountRange.minAmount && { minAmount: null, maxAmount: amountRange.maxAmount }),
              }),
              ...(frequency && { frequency }),
            };
          }
          if (policyType.id === Calendar_Limits) {
            const { error } = PolicyValidator.updateCalendarPolicy.validate({ period });
            if (error) throw new ValidationError(error.message);

            updatePayload = {
              ...updatePayload,
              ...(period && { period: JSON.stringify(period) }),
            };
          }
          const isExistingPolicyRestriction = await PolicyRestriction.findOne({
            where: {
              policy_type: policyType.id,
              policy: existingPolicy.id,
            },
            attributes: ["id"],
            raw: true,
          });

          if (isExistingPolicyRestriction) {
            typesToInclude.push(isExistingPolicyRestriction.id);
          }
          policyTypes.push(policyType);
        }
      })
    );

    if (types && types.length) {
      // drop former types no longer needed
      await Service.updatePolicyRestrictions(existingPolicy, typesToInclude, policyTypes, company);
    }

    if (exceptions) {
      await Service.updatePolicyExceptions(exceptions, existingPolicy, company);
    }

    if (documents) {
      await Service.updatePolicyDocuments(documents, existingPolicy, company);
    }

    if (conditionsToAdd.length) {
      await Promise.all(conditionsToAdd.map((condition) => Service.createPolicyCondition({ ...condition, policy: existingPolicy.id })));
    }

    if (conditionsToRemove.length) {
      await Service.deletePolicyConditions(existingPolicy, conditionsToRemove);
    }

    if (conditionsToUpdate.length) {
      await Service.updatePolicyConditions(conditionsToUpdate, existingPolicy);
    }

    // updating the policy
    await PolicyRepo.updatePolicy({
      updateFields: {
        ...("strict" in data && { strict }),
        ...("requiresBudget" in data && { requiresBudget }),
        ...("requiresDescription" in data && { requiresDescription }),
        ...("requiresCategory" in data && { requiresCategory }),
        ...("lastModifiedBy" in data && { lastModifiedBy }),
        ...(name && { name }),
        ...(description && { description }),
        ...(status && { status: STATUSES[status.toUpperCase()] }),
        ...(receiptPolicyCondition && { receiptPolicyCondition }),
        ...(receiptPolicyValue && { receiptPolicyValue }),
        ...updatePayload,
      },
      queryParams: { company, status: { [Op.notIn]: [STATUSES.DELETED] }, code },
    });

    if (budget) {
      const { success } = await Service.createBudgetPolicy({ policy: existingPolicy.id, budget, throwErrorIfBudgetPolicyExists: false });
      if (!success) throw new HttpException("Error assigning policy to budget", HttpStatus.PARTIAL_CONTENT);
    }

    return responseUtils.sendObjectResponse("Policy successfully updated");
  },

  async pauseBudgetPolicy(data) {
    const { code: policyCode, company, budgetCode, status: policyStatus = "pause" } = data;

    const policy = await PolicyRepo.getPolicy({
      queryParams: { company, code: policyCode, status: { [Op.notIn]: [STATUSES.DELETED] } },
      selectOptions: ["name", "description"],
      budget: true,
      budgetCode,
    });
    if (!policy) throw new NotFoundError("Policy for this Budget");

    const {
      id,
      BudgetPolicies: [budgetPolicy],
    } = Sanitizer.jsonify(policy);
    if (budgetPolicy.status === STATUSES.DELETED) throw new NotFoundError("Policy");

    const budget = budgetPolicy.Budget;

    const existingPolicy = await BudgetPolicyRepo.updateBudgetPolicy({
      updateFields: {
        status: policyStatus === "pause" ? STATUSES.PAUSE : STATUSES.ACTIVE,
      },
      queryParams: {
        policy: id,
        status: { [Op.notIn]: [STATUSES.DELETED] },
        budget: budget.id,
      },
    });

    let responseMessage = "Budget policy successfully paused";
    if (existingPolicy[0] === 0) responseMessage = `Budget policy already paused`;

    return responseUtils.sendObjectResponse(responseMessage);
  },

  async removeBudgetPolicy(data) {
    const { code: policyCode, company, budgetCode } = data;

    const policy = await PolicyRepo.getPolicy({
      queryParams: {
        company,
        code: policyCode,
        status: { [Op.notIn]: [STATUSES.DELETED] },
      },
      selectOptions: ["name", "description"],
      budget: true,
      budgetCode,
    });
    if (!policy) throw new NotFoundError("Policy for this Budget");

    const {
      id,
      BudgetPolicies: [budgetPolicy],
    } = Sanitizer.jsonify(policy);
    if (budgetPolicy.status === STATUSES.DELETED) throw new NotFoundError("Policy for this Budget");

    const [budget] = budgetPolicy.Budgets;

    await BudgetPolicyRepo.updateBudgetPolicy({
      updateFields: { status: STATUSES.DELETED },
      queryParams: {
        policy: id,
        status: { [Op.notIn]: [STATUSES.DELETED] },
        budget: budget.id,
      },
    });

    return responseUtils.sendObjectResponse("Budget policy successfully deleted");
  },

  /**
   * Add Document to Policy
   * @param {*} param0
   * @returns
   */
  async addDocumentToPolicy({ code, company, asset }) {
    const payload = {
      company,
    };
    if (code) {
      const policy = await PolicyRepo.getPolicy({
        queryParams: { company, code, status: { [Op.notIn]: [STATUSES.DELETED] } },
        selectOptions: ["name", "description", "status"],
      });
      if (!policy) throw new NotFoundError("Policy");
      payload.policy = policy.id;
    }
    const foundAsset = await getAsset(asset);
    if (!foundAsset) throw new NotFoundError("Document");
    payload.asset = foundAsset.id;

    const [newPolicyDocument] = await PolicyDocument.findOrCreate({
      where: payload,
    });
    if (newPolicyDocument?.id && newPolicyDocument?.status !== STATUSES.ACTIVE) {
      await PolicyDocument.update(
        {
          status: STATUSES.ACTIVE,
        },
        {
          where: {
            id: newPolicyDocument.id,
          },
        }
      );
    }
    return newPolicyDocument;
  },

  /**
   * Remove document from a policy
   * @param {Object} param0
   * @returns
   */
  async removeDocumentFromPolicy({ code, company, asset }) {
    const criteria = {
      company,
    };
    if (code) {
      const policy = await PolicyRepo.getPolicy({
        queryParams: { company, code, status: { [Op.notIn]: [STATUSES.DELETED] } },
        selectOptions: ["name", "description", "status"],
      });
      if (!policy) throw new NotFoundError("Policy");
      criteria.policy = policy.id;
    }
    const foundAsset = await getAsset(asset);
    if (!foundAsset) throw new NotFoundError("Document");
    criteria.asset = foundAsset.id;
    const [updatedCount] = await PolicyDocument.update(
      {
        status: STATUSES.DELETED,
      },
      {
        where: criteria,
      }
    );
    return updatedCount;
  },

  async removePolicy(data) {
    const { code: policyCode, company } = data;

    const policy = await PolicyRepo.getPolicy({
      queryParams: { company, code: policyCode, status: { [Op.notIn]: [STATUSES.DELETED] } },
      selectOptions: ["name", "description", "status"],
    });
    if (!policy) throw new NotFoundError("Policy");

    const [existingPolicy] = await PolicyRepo.updatePolicy({
      updateFields: { status: STATUSES.DELETED },
      queryParams: {
        id: policy.id,
        status: { [Op.notIn]: [STATUSES.DELETED] },
        company,
      },
    });

    let responseMessage = "Policy successfully deleted";
    if (!existingPolicy) responseMessage = `Policy already deleted`;

    return responseUtils.sendObjectResponse(responseMessage);
  },

  /**
   * Calendar Limit Policy Enforcer
   * @param { number } company company id
   * @param { string } code policy code
   * @param { boolean } strictDefault determines if a policy enforcer should be strict or not
   * @returns
   */
  async calenderLimitEnforcer({ code: policy, company, strict, strictDefault }) {
    const { "Calendar limits": Calendar_Limits } = SettingsService.get("POLICY_TYPES");
    const fetchPolicy = await PolicyRepo.getPolicy({
      queryParams: {
        company,
        code: policy,
        status: { [Op.notIn]: [STATUSES.DELETED, STATUSES.PAUSE] },
      },
      selectOptions: [...Service.selectOptions],
    });
    if (!fetchPolicy) return responseUtils.sendObjectResponse("Policy passed successfully");

    const violations = [];
    let policyType;
    const { PolicyType, PolicyTypes } = fetchPolicy;

    // backwards compatibility
    if (fetchPolicy.PolicyType) {
      policyType = PolicyType.id;
    } else if (PolicyTypes) {
      const foundPolicy = PolicyTypes.find((policyType) => policyType && policyType.id === Calendar_Limits);

      if (!foundPolicy) return responseUtils.sendObjectResponse("Policy passed successfully");

      policyType = foundPolicy.id;
    }
    if (policyType !== Calendar_Limits) throw new ValidationError("Wrong policy application");

    const today = new Date();
    let day = today.getDay();
    const policyDays = JSON.parse(fetchPolicy.period);
    const dates = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

    if (!policyDays.includes(dates[String(day)])) {
      if (!strict || strictDefault) {
        const calenderViolation = await Service.recordPolicyViolation({ policy: fetchPolicy.id });
        if (calenderViolation.data) violations.push(calenderViolation.data);
        return calenderViolation;
      }
      throw new ValidationError(`Payments via selected budget can only be made on ${policyDays.join(", ")}`);
    }
    return responseUtils.sendObjectResponse("Policy passed Succefully", violations);
  },

  /**
   * Spend Limit Policy Enforcer
   * @param { number } budget budget id
   * @param { number } company company id
   * @param { string } code policy code
   * @param { number } amount amount for transaction
   * @param { string } currency currency NGN | USD
   * @param { boolean } strictDefault determines if a policy enforcer should be strict or not
   * @returns
   */
  async spendLimitEnforcer({ code: policy, company, amount, currency, budget, strict, strictDefault }) {
    const { "Spending limits": Spending_Limits } = SettingsService.get("POLICY_TYPES");
    const fetchPolicy = await PolicyRepo.getPolicy({
      queryParams: {
        company,
        code: policy,
        status: { [Op.notIn]: [STATUSES.DELETED, STATUSES.PAUSE] },
      },
      selectOptions: [...Service.selectOptions],
    });
    if (!fetchPolicy) return responseUtils.sendObjectResponse("Policy passed successfully");

    const { frequency } = fetchPolicy;

    let policyType;
    const { PolicyType, PolicyTypes } = fetchPolicy;
    // backwards compatibility
    if (fetchPolicy.PolicyType) {
      policyType = PolicyType.id;
    } else if (PolicyTypes) {
      const foundPolicy = PolicyTypes.find((policyType) => policyType && policyType.id === Spending_Limits);

      if (!foundPolicy) return responseUtils.sendObjectResponse("Policy passed successfully");

      policyType = foundPolicy.id;
    }

    if (policyType !== Spending_Limits) throw new ValidationError("Wrong policy application");

    const violations = [];

    const { data: policyViolation } = await Service.amountEnforcer({ amount, currency, strict, strictDefault, fetchPolicy });
    if (policyViolation) violations.push(policyViolation);

    const { data: frequencyCheck } = await Service.frequencyEnforcer({
      budget,
      frequency,
      currency,
    });
    const { message, data: frequencyViolation } = await Service.amountEnforcer({
      amount,
      strict,
      strictDefault,
      fetchPolicy,
      frequencyCheck,
    });
    if (frequencyViolation) violations.push(frequencyViolation);

    return responseUtils.sendObjectResponse(message || "Policy passed Successfully", violations);
  },

  async recieptPolicyEnforcer({ reciept, amount, currency, code: policy, company, strict, strictDefault }) {
    const { "Receipt Policy": Reciept_Policy } = SettingsService.get("POLICY_TYPES");
    const responseMessage = "Policy passed successfully";

    const fetchPolicy = await PolicyRepo.getPolicy({
      queryParams: {
        company,
        code: policy,
        status: { [Op.notIn]: [STATUSES.DELETED, STATUSES.PAUSE] },
      },
      selectOptions: [...Service.selectOptions],
    });
    if (!fetchPolicy) return responseUtils.sendObjectResponse(responseMessage);

    let policyType;
    const { PolicyType, PolicyTypes, receiptPolicyCondition, receiptPolicyValue } = fetchPolicy;

    // backwards compatibility
    if (fetchPolicy.PolicyType) {
      policyType = PolicyType.id;
    } else if (PolicyTypes) {
      const foundPolicy = PolicyTypes.find((policyType) => policyType && policyType.id === Reciept_Policy);

      if (!foundPolicy) return responseUtils.sendObjectResponse("Policy passed successfully");

      policyType = foundPolicy.id;
    }

    if (policyType !== Reciept_Policy) throw new ValidationError("Wrong policy application");

    let policyApplies = true;

    if (receiptPolicyCondition && receiptPolicyValue && ["above", "below"].includes(receiptPolicyCondition)) {
      // ideally, this check should always be true due to how policies and budgets are related (same currency) but you never know
      if (currency === fetchPolicy.currency) policyApplies = Helper.compareValues(amount, receiptPolicyValue, receiptPolicyCondition);
    }

    const hasViolatedPolicy = policyApplies && (!reciept || !reciept.length);
    const violations = [];
    if (hasViolatedPolicy) {
      if (!strict) {
        const recieptViolation = await Service.recordPolicyViolation({ policy: fetchPolicy.id });
        if (recieptViolation.data) violations.push(recieptViolation.data);
        return recieptViolation;
      }
      throw new ValidationError(`A receipt is required for transactions in this budget`);
    }

    return responseUtils.sendObjectResponse(responseMessage, violations);
  },

  async amountEnforcer({ amount, currency, strict, strictDefault, fetchPolicy, frequencyCheck, budgetBalance }) {
    const { minAmount, maxAmount, name } = fetchPolicy;
    const strictCondition = !strict || strictDefault;
    if (frequencyCheck) {
      if (Number(frequencyCheck.amount) + Number(amount) > maxAmount) {
        if (strictCondition) return Service.recordPolicyViolation({ policy: fetchPolicy.id });
        throw new ValidationError(`You have exceed Maximum amount of ${name} Policy`);
      }
      return responseUtils.sendObjectResponse("Maximum amount not exceeded");
    }

    if (budgetBalance) {
      if (budgetBalance < minAmount) {
        if (strictCondition) return Service.recordPolicyViolation({ policy: fetchPolicy.id });
        throw new ValidationError(`You have spent below the Minimum amount of ${name} Policy`);
      }
      return responseUtils.sendObjectResponse("Minimum amount not exceeded");
    }

    const { currency: policyCurrency } = fetchPolicy;

    if (minAmount && minAmount > amount) {
      if (strictCondition) return Service.recordPolicyViolation({ policy: fetchPolicy.id });
      throw new ValidationError(
        `This transaction is below the minimum spend limit (${policyCurrency}${(minAmount / 100).toLocaleString()}) allowed on this budget`
      );
    }
    if (maxAmount && maxAmount < amount) {
      if (strictCondition) return Service.recordPolicyViolation({ policy: fetchPolicy.id });
      throw new ValidationError(
        `This transaction exceeds the maximum spend limit (${policyCurrency}${(maxAmount / 100).toLocaleString()}) allowed on this budget`
      );
    }
    if (currency && policyCurrency !== currency) {
      if (strictCondition) return Service.recordPolicyViolation({ policy: fetchPolicy.id });
      throw new ValidationError(`${name} Policy currency violation`);
    }
    return responseUtils.sendObjectResponse("No violation");
  },

  /**
   * Gets the total amount of transaction performed from a frequency period
   * @param { number } budget budget id
   * @param { string } frequency frequency type
   * @param { string } currency currency NGN | USD
   * @returns
   */
  async frequencyEnforcer({ budget, frequency, currency = "NGN" }) {
    const query = Helper.generateFrequencyQuery(frequency);

    const transactions = await TransactionRepo.getTransactions({
      queryParams: {
        budget,
        currency,
        status: [STATUSES.PROCESSED, STATUSES.PROCESSING, STATUSES.FAILED],
        ...query,
      },
      selectOptions: ["currency", [sequelize.fn("sum", sequelize.col("amount")), "amount"]],
      groupOptions: ["currency"],
      noOrder: true,
    });
    return responseUtils.sendObjectResponse("Policy transactions retrieved Successfully", transactions[0]);
  },

  /**
   * Record policy violations
   * @param { number } transaction transaction id
   * @param { number } policy policy id
   * @returns
   */
  async recordPolicyViolation({ policy, transaction }) {
    const policyViolation = await PolicyViolationRepo.findOrCreatePolicyViolation({
      queryParams: {
        policy,
        ...(transaction && { transaction }),
      },
      selectOptions: [...Service.selectOptions],
    });
    return responseUtils.sendObjectResponse("Policy passed Succefully with violation", policyViolation);
  },

  async generateNotificationData({ transaction }) {
    const dashboardUrl = `${Utils.getDashboardURL()}/transactions/requests`;

    const {
      User: requester,
      currency,
      amount,
      narration,
      code,
    } = await TransactionRepo.getTransaction({
      queryParams: { id: transaction },
      selectOptions: ["currency", "amount", "narration", "code"],
    });
    const bodyMessage = `for ${narration}`;
    return {
      dashboardUrl,
      requester,
      currency,
      amount,
      code,
      bodyMessage,
    };
  },

  async runService(service, item, supportData) {
    return Service[service]({
      ...(typeof item === "object" ? { ...Sanitizer.jsonify(item) } : { item }),
      ...supportData,
    });
  },

  async callService(service, payload, supportData) {
    if (Array.isArray(payload)) {
      return Promise.all(payload.map((item) => Service.runService(service, item, supportData)));
    }
    return Service.runService(service, payload, supportData);
  },

  async notifyPolicyViolation(data) {
    const { notificationData, ...policyViolation } = data;
    const { dashboardUrl, requester, currency, amount, code, bodyMessage } = notificationData;
    const { company } = requester;
    // const policy = await PolicyRepo.getPolicyLight({ queryParams: { id: policyViolation.policy } });
    NotificationService.saveNotification({
      company,
      user_id: requester.id,
      type: `info`,
      badge: `info`,
      title: `${Utils.toTitle(requester.firstName)} your transaction has been flagged for violating a policy`,
      message: `${Utils.toTitle(requester.firstName)} your transaction ${bodyMessage} has violated a policy`,
      table: {
        code,
        entity: "Transaction",
      },
      event: "policyViolation",
      body: {
        code: policyViolation.code,
        entity: "PolicyViolation",
      },
      reference_code: policyViolation.code,
    });
  },

  async updatePolicyViolations(data) {
    const { transaction, id, policy, created_at, ...rest } = data;
    PolicyViolationRepo.updatePolicyViolation({
      updateFields: { transaction },
      queryParams: rest,
    });
  },

  /**
   * Counts number of violations
   * @param { number } transaction transaction id
   * @param { object[] } policyViolations array of policy violation responses;
   * @returns
   */
  async trackPolicyViolations({ transaction, policyViolations }) {
    let responses;
    let policiesViolated = [];
    const notificationData = await Service.generateNotificationData({ transaction });
    if (Array.isArray(policyViolations)) {
      responses = await Promise.all(
        policyViolations.map(({ data } = {}) => {
          if (data) {
            Service.callService("updatePolicyViolations", data, { resolved: false, transaction });
            Service.callService("notifyPolicyViolation", data, { notificationData });
            return data;
          }
        })
      );
      policiesViolated = responses.filter(Boolean);
    } else {
      if (policyViolations.data) {
        await Service.updatePolicyViolations({ ...policyViolations.data, resolved: false, transaction });
        await Service.notifyPolicyViolation({ ...policyViolations.data, notificationData });
        policiesViolated.push(policyViolations.data.id);
      }
    }
    if (policiesViolated.length > 0)
      await TransactionRepo.updateTransaction({
        queryParams: { id: transaction },
        updateFields: { policiesViolated: policiesViolated.length },
      });

    return responseUtils.sendObjectResponse(`A Total of ${policiesViolated.length} policies were violated`);
  },

  /**
   * Enforces all policies Applied to a budget
   * @param { number } budget budget id
   * @param { number } amount amount for transaction
   * @param { String } currency currency of transaction
   * @param { number } company company id
   * @param { boolean } strictDefault determines if a policy enforcer should be strict or not
   * @param { boolean } isReimbursement determines if this is a reimbursement in which case only some policies will be enforced
   * @returns
   */
  async policyBudgetEnforcer({ reciept, budget, amount, currency, company, team, category, account, type = null }, strictDefault) {
    const { data: { policies = [] } = {} } = await Service.getPolicies({ company, budget, team, category, account, type });

    if (policies.length === 0) return responseUtils.sendObjectResponse("Policy passed successfully");
    const responses = await Promise.all(
      policies.map(async (Policy) => {
        // handle backwards compatibility
        return Service.handlePolicies({ reciept, budget, amount, currency, company, strict: Policy.strict, strictDefault: Policy.strict }, Policy);
      })
    );
    // A check for a bad response is made here
    const check = responses.find(({ success } = {}) => success === false);
    if (!!check) throw new ValidationError(`Policy violated`);

    return responses;
  },

  async policyEnforcer({ code, company, account, budget, amount, vendor, user, team, subsidiary, category, type, entity, overrideStrictness }) {
    const {
      budget: budgetToCheck,
      receipt,
      description,
      currency,
      category: hasCategory,
      amount: amountToCompare,
      user: userToCheck,
      team: teamToCheck,
      subsidiary: subsidiaryToCheck,
      category: categoryToCheck,
      vendor: vendorToCheck,
      account: accountToCheck,
      type: typeToCheck,
      requestType = null,
    } = entity;

    const policies = await PolicyRepo.getPolicies({
      queryParams: {
        budget,
        account,
        amount,
        company,
        vendor,
        user,
        team,
        subsidiary,
        category,
        type,
        status: STATUSES.ACTIVE,
        ...(code && { code }),
      },
      includeExceptions: true,
    });

    if (!policies.length) return;

    const transformedPolicies = policies.map((policy) => {
      const transformedPolicy = policy;
      if (policy?.conditions.length) {
        const exceptions = Utils.mapAnArray(policy.exceptions, "user");
        const isExceptionalUser = exceptions.includes(userToCheck);
        transformedPolicy.conditions = policy?.conditions.map((condition) => {
          return {
            policyId: policy.id,
            ...Sanitizer.jsonify(policy),
            ...Sanitizer.jsonify(condition),
            isExceptionalUser,
          };
        });
      }
      return transformedPolicy;
    });

    const mergedConditions = transformedPolicies.flatMap((policy) => policy.conditions);

    const violations = [];
    const promises = mergedConditions.map(async (condition) => {
      const { receiptPolicyValue, receiptPolicyCondition, policyId, strict, operator, PolicyConditionOperands, isExceptionalUser, trigger } =
        condition;
      const operands = Utils.mapAnArray(PolicyConditionOperands, "operand");
      if (isExceptionalUser) return;

      const requiresAPolicyCheck = Service.requiresAPolicyCheck({
        operator: operator || receiptPolicyCondition,
        operands: operands || receiptPolicyValue,
        toCompare: {
          account: accountToCheck,
          budget: budgetToCheck,
          amount: amountToCompare,
          vendor: vendorToCheck,
          user: userToCheck,
          team: teamToCheck,
          subsidiary: subsidiaryToCheck,
          category: categoryToCheck,
          type: typeToCheck,
        },
        trigger,
      });

      if (!requiresAPolicyCheck) return;

      if (receiptPolicyCondition) {
        let policyApplies = true;

        if (receiptPolicyValue && ["above", "below"].includes(receiptPolicyCondition)) {
          if (currency === condition.currency) policyApplies = Helper.compareValues(amountToCompare, receiptPolicyValue, receiptPolicyCondition);
        }

        if (policyApplies && !receipt?.length) {
          const violation = await Service.actOnViolation({
            hasViolatedPolicy: !receipt?.length,
            strict: strict && !overrideStrictness,
            policyId,
            message: "A receipt is required for this transaction",
          });
          violations.push(violation);
        }
      }
      if (condition.requiresDescription) {
        if (!description) {
          const violation = await Service.actOnViolation({
            hasViolatedPolicy: !description,
            strict: strict && !overrideStrictness,
            policyId,
            message: "A description is required for this transaction",
          });
          violations.push(violation);
        }
      }

      if (condition.requiresCategory) {
        const categoryExclusions = ["top_up", "budget"]; // Excludes Top up, and Budget creations as they don't have category
        if (!hasCategory && !categoryExclusions.includes(requestType)) {
          const violation = await Service.actOnViolation({
            hasViolatedPolicy: !hasCategory,
            strict: strict && !overrideStrictness,
            policyId,
            message: "A category is required for this transaction",
          });
          violations.push(violation);
        }
      }

      if (condition.requiresBudget) {
        const budgetExclusions = ["top_up"];
        if (!budgetToCheck && !budgetExclusions.includes(requestType)) {
          const violation = Service.actOnViolation({
            hasViolatedPolicy: !budgetToCheck,
            strict: strict && !overrideStrictness,
            policyId,
            message: "A budget is required for this transaction",
          });
          violations.push(violation);
        }
      }

      // Because it requires a policy check and none of the above is required, it should fail here
      await Service.actOnViolation({
        hasViolatedPolicy: true,
        strict: strict && !overrideStrictness,
        policyId,
        message: `Transaction is failing to pass all conditions for ${condition.name} policy`,
      });

      return true;
    });

    await Promise.all(promises);
    return violations;
  },

  async actOnViolation({ hasViolatedPolicy, strict, policyId, message = `A receipt is required for this transaction` }) {
    if (hasViolatedPolicy) {
      if (!strict) {
        const recordViolation = await Service.recordPolicyViolation({ policy: policyId });
        return recordViolation;
      }
      throw new ValidationError(message);
    }
  },

  requiresAPolicyCheck({ operator, operands, toCompare, trigger }) {
    const isNegation = operator.startsWith("not:");
    const evaluations = {
      amount: () => Helper.compareValues(toCompare[trigger], operands?.[0], operator),
    };

    let operation;

    if (trigger !== "amount") {
      if (trigger === "type") {
        const types = SettingsService.get("TRANSACTION_TYPES");
        operation = operands.includes(types.all) || operands.includes(types[toCompare[trigger]]);
      } else {
        operation = operands.includes(toCompare[trigger]);
      }
    } else {
      operation = evaluations[trigger]();
    }

    const requiresAPolicyCheck = isNegation ? !operation : operation;

    return !!requiresAPolicyCheck;
  },

  /**
   * Switches between different policy functions
   * @param { number } budget budget id
   * @param { number } amount amount for transaction
   * @param { String } currency currency of transaction NGN | USD
   * @param { number } company company id
   * @param { boolean } strictDefault determines if a policy enforcer should be strict or not
   * @returns
   */
  async handlePolicies({ reciept, budget, amount, currency, company, strict, strictDefault }, policy) {
    const {
      "Spending limits": Spending_Limits,
      "Calendar limits": Calendar_Limits,
      "Receipt Policy": Reciept_Policy,
    } = SettingsService.get("POLICY_TYPES");
    const { type, code } = policy;

    if (type === Spending_Limits) return Service.spendLimitEnforcer({ budget, amount, currency, code, company, strict, strictDefault });
    if (type === Calendar_Limits) return Service.calenderLimitEnforcer({ code, company, strict, strictDefault });
    if (type === Reciept_Policy) return Service.recieptPolicyEnforcer({ reciept, amount, currency, code, company, strict, strictDefault });
    return responseUtils.sendObjectResponse("Policy passed Succefully");
  },

  /**
   * * Here the policy conditions are created
   * @param { object } data payload
   * @param { string } trigger what would trigger this approval condition
   * @param { string } operator what condition the trigger would be operating with
   * @param { string[] } operands an array of what we would be operating on
   * @param { object } transaction DB transaction
   * @returns
   */
  async createPolicyCondition(data) {
    const { trigger, operator, policy, operands, transaction = null } = data;

    const existingCondition = await PolicyConditionRepo.getCondition({
      queryParams: { trigger, operator, policy, status: STATUSES.ACTIVE },
      transaction,
    });

    const condition =
      existingCondition ||
      (await PolicyConditionRepo.createCondition({
        queryParams: {
          trigger,
          operator,
          policy,
        },
        transaction,
      }));

    await Service.callService("createPolicyConditionOperand", operands, {
      trigger,
      condition: condition.id,
      transaction,
    });

    return responseUtils.sendObjectResponse("Policy condition successfully created", condition);
  },

  /**
   * * Here the Operands for a policy condition are set
   * @param { object } data payload
   * @param { string } trigger what would trigger this approval condition
   * @param { number } condition approval condition id
   * @param { string } operandValue the valu we would be operating on
   * @param { object } transaction DB transaction
   * @returns
   */
  async createPolicyConditionOperand(data) {
    const { trigger, condition, item: operandValue, transaction = null, shouldDeduceOperand = true } = data;

    const operand = shouldDeduceOperand ? await Service.deduceOperand(operandValue, trigger) : operandValue;

    const existingOperand = await PolicyConditionOperandRepo.getOperand({
      queryParams: {
        operand,
        condition,
        status: { [Op.notIn]: [STATUSES.DELETED] },
      },
      transaction,
    });
    if (existingOperand) return responseUtils.sendObjectResponse("Policy conditions already created", existingOperand);

    const createdOperand = await PolicyConditionOperandRepo.createOperand({
      queryParams: {
        condition,
        operand,
      },
      transaction,
    });

    return responseUtils.sendObjectResponse("Policy conditions successfully created", createdOperand);
  },

  async removeException(user, policy) {
    const [response] = await PolicyUserException.update(
      {
        status: STATUSES.DELETED,
      },
      {
        where: {
          policy,
          user,
        },
      }
    );
    return response;
  },

  async deletePolicyConditions(existingPolicy, conditionsToRemove) {
    const conditions = await PolicyConditionRepo.validatePolicyConditions({
      policy: existingPolicy.id,
      conditions: conditionsToRemove,
    });

    const conditionsIds = Utils.mapAnArray(conditions, "id");

    await Promise.all([
      PolicyConditionRepo.updateCondition({
        queryParams: {
          id: {
            [Op.in]: conditionsIds,
          },
        },
        updateFields: {
          status: STATUSES.DELETED,
        },
      }),

      PolicyConditionOperandRepo.updateOperand({
        queryParams: {
          condition: {
            [Op.in]: conditionsIds,
          },
        },
        updateFields: {
          status: STATUSES.DELETED,
        },
      }),
    ]);
  },

  async updatePolicyDocuments(documents, existingPolicy, company) {
    const foundDocuments = (await getAssets({ code: documents })) || [];
    const documentIds = Utils.mapAnArray(foundDocuments, "id");

    await PolicyDocument.update(
      {
        status: STATUSES.DELETED,
      },
      {
        where: {
          policy: existingPolicy.id,
          asset: {
            [Op.notIn]: documentIds,
          },
        },
      }
    );

    await PolicyRepo.findOrLinkPolicyDocuments({
      policy: existingPolicy.id,
      company,
      documents: documentIds,
    });
  },

  async updatePolicyExceptions(exceptions, existingPolicy, company) {
    const foundUsers = (await getUsers({ code: exceptions })) || [];
    const userIds = Utils.mapAnArray(foundUsers, "id");

    await PolicyUserException.update(
      {
        status: STATUSES.DELETED,
      },
      {
        where: {
          policy: existingPolicy.id,
          user: {
            [Op.notIn]: userIds,
          },
        },
      }
    );

    await PolicyRepo.findOrCreateExceptionList({
      policy: existingPolicy.id,
      company,
      users: userIds,
    });
  },

  async updatePolicyRestrictions(existingPolicy, typesToInclude, policyTypes, company) {
    await PolicyRestriction.update(
      {
        status: STATUSES.DELETED,
      },
      {
        where: {
          policy: existingPolicy.id,
          id: {
            [Op.notIn]: typesToInclude,
          },
        },
      }
    );

    await Promise.all(
      policyTypes.map((policyType) =>
        Helper.findOrCreatePolicyRestriction({
          policy: existingPolicy.id,
          policy_type: policyType.id,
          company,
        })
      )
    );
  },

  async deduceOperand(operandValue, trigger) {
    let operand = operandValue;
    if (trigger !== "amount") {
      if (trigger === "type") operand = SettingsService.get("TRANSACTION_TYPES")[operandValue];
      else {
        let checker;
        if (trigger === "vendor") checker = "vendorChecker";
        if (trigger === "category") checker = "categoryChecker";
        if (trigger === "budget") checker = "BudgetChecker";
        if (trigger === "account") checker = "AccountChecker";

        if (["vendor", "category", "budget", "account"].includes(trigger)) {
          const { data: checkResponse } = await Helper[checker]({
            code: operandValue,
          });
          operand = checkResponse.id;
        }
      }
    }
    return operand;
  },

  async updatePolicyConditions(conditionsToUpdate, existingPolicy) {
    const conditionCodes = Utils.mapAnArray(conditionsToUpdate, "condition");

    const conditions = await PolicyConditionRepo.validatePolicyConditions({
      policy: existingPolicy.id,
      conditions: conditionCodes,
    });

    const conditionsMap = conditions.reduce((accumulated, condition) => {
      // eslint-disable-next-line no-param-reassign
      accumulated[condition.code] = condition;
      return accumulated;
    }, {});

    await Promise.all(
      conditionsToUpdate.map(async (condition) => {
        const { condition: conditionCode, trigger, operator, operands: operandValue } = condition;
        await PolicyConditionRepo.updateCondition({
          queryParams: {
            code: conditionCode,
          },
          updateFields: {
            trigger,
            operator,
          },
        });

        const operands = Array.isArray(operandValue) ? operandValue : [operandValue];

        const deducedOperands = await Promise.all(
          operands.map((operand) => {
            return Service.deduceOperand(operand, trigger);
          })
        );

        await PolicyConditionOperandRepo.updateOperand({
          queryParams: {
            operand: {
              [Op.notIn]: deducedOperands,
            },
            condition: conditionsMap[conditionCode]?.id,
          },
          updateFields: {
            status: STATUSES.DELETED,
          },
        });

        Promise.all(
          deducedOperands.map(async (operand) => {
            return Service.createPolicyConditionOperand({
              trigger,
              condition: conditionsMap[conditionCode]?.id,
              item: operand,
              shouldDeduceOperand: false,
            });
          })
        );

        return condition;
      })
    );
  },

  async getConditionsViolated(transaction) {
    const foundTransaction = await TransactionRepo.getTransaction({
      queryParams: {
        id: transaction,
      },
      receipt: true,
    });

    if (!foundTransaction) throw new NotFoundError("Transaction");

    const hasReceipt = !!foundTransaction.TransactionAssets?.length || !!foundTransaction?.Reimbursement?.ReimbursementAssets?.length;
    const hasCategory = !!foundTransaction.category;
    const hasDescription = !!foundTransaction.description;
    const hasBudget = !!foundTransaction.budget;

    const violations = await PolicyViolationRepo.getPolicyViolations({
      queryParams: { transaction, resolved: false },
    });

    if (!violations.length) {
      return violations;
    }

    const outstandingViolations = new Set();
    const promises = violations.map((violation) => {
      const { Policy } = Sanitizer.jsonify(violation);
      const { receiptPolicyCondition, receiptPolicyValue, currency, requiresBudget, requiresDescription, requiresCategory } =
        Sanitizer.jsonify(Policy);
      let requiresReceipt = false;

      if (receiptPolicyCondition) {
        requiresReceipt = true;
        if (currency === foundTransaction.currency) {
          requiresReceipt = Helper.compareValues(foundTransaction.amount, receiptPolicyValue, receiptPolicyCondition);
        }
      }

      if (requiresReceipt) {
        if (!hasReceipt) {
          outstandingViolations.add("receipt");
        }
      }

      if (requiresCategory) {
        if (!hasCategory) {
          outstandingViolations.add("category");
        }
      }

      if (requiresBudget) {
        if (!hasBudget) {
          outstandingViolations.add("budget");
        }
      }

      if (requiresDescription) {
        if (!hasDescription) {
          outstandingViolations.add("description");
        }
      }

      return violation;
    });

    await Promise.all(promises);

    return [...outstandingViolations];
  },

  async singlePolicyEnforcer({ policyCode, company, entity, overrideStrictness }) {
    const {
      budget: budgetToCheck,
      receipt,
      description,
      currency,
      category: hasCategory,
      amount: amountToCompare,
      user: userToCheck,
      team: teamToCheck,
      subsidiary: subsidiaryToCheck,
      category: categoryToCheck,
      vendor: vendorToCheck,
      account: accountToCheck,
      type: typeToCheck,
      requestType = null,
    } = entity;

    const foundPolicy = await PolicyRepo.getPolicy({
      queryParams: {
        code: policyCode,
        company,
        status: STATUSES.ACTIVE,
      },
      includeExceptions: true,
    });

    if (!foundPolicy) throw new NotFoundError("Policy");

    if (foundPolicy?.conditions.length) {
      const exceptions = Utils.mapAnArray(foundPolicy.exceptions, "user");
      const isExceptionalUser = exceptions.includes(userToCheck);
      foundPolicy.conditions = foundPolicy?.conditions.map((condition) => {
        return {
          policyId: foundPolicy.id,
          ...Sanitizer.jsonify(foundPolicy),
          ...Sanitizer.jsonify(condition),
          isExceptionalUser,
        };
      });
    }

    const violations = [];
    const promises = foundPolicy.conditions.map(async (condition) => {
      const { receiptPolicyValue, receiptPolicyCondition, policyId, strict, operator, PolicyConditionOperands, isExceptionalUser, trigger } =
        condition;
      const operands = Utils.mapAnArray(PolicyConditionOperands, "operand");
      if (isExceptionalUser) return;

      const requiresAPolicyCheck = Service.requiresAPolicyCheck({
        operator: operator || receiptPolicyCondition,
        operands: operands || receiptPolicyValue,
        toCompare: {
          account: accountToCheck,
          budget: budgetToCheck,
          amount: amountToCompare,
          vendor: vendorToCheck,
          user: userToCheck,
          team: teamToCheck,
          subsidiary: subsidiaryToCheck,
          category: categoryToCheck,
          type: typeToCheck,
        },
        trigger,
      });

      if (!requiresAPolicyCheck) return;

      if (receiptPolicyCondition) {
        let policyApplies = true;

        if (receiptPolicyValue && ["above", "below"].includes(receiptPolicyCondition)) {
          if (currency === condition.currency) policyApplies = Helper.compareValues(amountToCompare, receiptPolicyValue, receiptPolicyCondition);
        }

        if (policyApplies && !receipt?.length) {
          const violation = await Service.actOnViolation({
            hasViolatedPolicy: !receipt?.length,
            strict: strict && !overrideStrictness,
            policyId,
            message: "A receipt is required for this transaction",
          });
          violations.push(violation);
        }
      }
      if (condition.requiresDescription) {
        if (!description) {
          const violation = await Service.actOnViolation({
            hasViolatedPolicy: !description,
            strict: strict && !overrideStrictness,
            policyId,
            message: "A description is required for this transaction",
          });
          violations.push(violation);
        }
      }

      if (condition.requiresCategory) {
        const categoryExclusions = ["top_up", "budget"]; // Excludes Top up, and Budget creations as they don't have category
        if (!hasCategory && !categoryExclusions.includes(requestType)) {
          const violation = await Service.actOnViolation({
            hasViolatedPolicy: !hasCategory,
            strict: strict && !overrideStrictness,
            policyId,
            message: "A category is required for this transaction",
          });
          violations.push(violation);
        }
      }

      if (condition.requiresBudget) {
        const budgetExclusions = ["top_up"];
        if (!budgetToCheck && !budgetExclusions.includes(requestType)) {
          const violation = Service.actOnViolation({
            hasViolatedPolicy: !budgetToCheck,
            strict: strict && !overrideStrictness,
            policyId,
            message: "A budget is required for this transaction",
          });
          violations.push(violation);
        }
      }

      // Because it requires a policy check and none of the above is required, it should fail here
      await Service.actOnViolation({
        hasViolatedPolicy: true,
        strict: strict && !overrideStrictness,
        policyId,
        message: `Transaction is failing to pass all conditions for ${condition.name} policy`,
      });

      return true;
    });

    await Promise.all(promises);
    return violations;
  },
};

module.exports = Service;
