const { ProductRepo } = require("../repositories/index.repo");

module.exports = {
  async createProducts(data) {
    return ProductRepo.createProducts({ data });
  },

  async findOrCreate(data) {
    if (!data) return null;
    if (!Array.isArray(data)) {
      return ProductRepo.findOrCreateProduct({ data });
    }

    return Promise.all(
      data.map((product) => {
        return ProductRepo.findOrCreateProduct({ data: product });
      })
    );
  },

  async createProduct({ data, transaction = null }) {
    return ProductRepo.createProduct({ data, transaction });
  },
};
