const Utils = require("../utils/utils");
const AWS = require("aws-sdk");
AWS.config.update({ region: process.AWS_REGION });

const Service = {
  getDefaultConfig(req = { tag: null }, delayDuration) {
    return {
      QueueUrl: delayDuration ? process.env.SQS_DELAYED_QUEUE_URL : process.env.SQS_QUEUE_URL,
      ...(delayDuration && { DelaySeconds: delayDuration }),
      MessageAttributes: {
        tag: {
          StringValue: req.tag || Utils.generateRandomString(20),
          DataType: "String",
        },
        sqsJob: {
          StringValue: "true",
          DataType: "String",
        },
      },
    };
  },
  /**
   * Tells if a req is already queued
   * @param {Object} req
   * @returns
   */
  isQueued(req) {
    const { headers = {} } = req;
    return headers["x-aws-sqsd-attr-sqsjob"] === "true";
  },
  /**
   *
   * @param {Object} req the request
   * @param {Object} payload the payload to queue
   * @param {String} description the request's description
   * @returns
   */
  async addJob(req, payload, description = "Job from API", delayDuration) {
    const SQS = new AWS.SQS();
    const options = Service.getDefaultConfig(req, delayDuration);
    const { name = "new job", path, key, id, ...rest } = payload;
    const url = `${Utils.getApiURL()}${path || req.path}`;
    Object.assign(options.MessageAttributes, {
      task_name: {
        StringValue: name,
        DataType: "String",
      },
      "beanstalk.sqsd.path": {
        StringValue: url,
        DataType: "String",
      },
      url: {
        StringValue: url,
        DataType: "String",
      },
      ...(key && { key: { StringValue: key, DataType: "String" } }),
    });
    Object.assign(options, { MessageBody: Utils.toString(rest) });
    const sqsPayload = {
      ...options,
      ...(!delayDuration && { MessageDeduplicationId: String(id), MessageGroupId: description }),
    };

    SQS.sendMessage(sqsPayload, function (err) {
      if (err) {
        console.log("Error", err);
      }
    });
  },

  /**
   *
   * @param {Object} req the request
   * @param {Object} payload the payload to queue
   * @param {String} description the request's description
   * @returns
   */

  addDelayedJob(req, payload, description = "Job from API", delayDuration = process.env.SQS_JOB_DELAY_SECONDS || 300) {
    return Service.addJob(req, payload, description, Math.min(delayDuration, 900));
  },
  /**
   * Delete a message from the queue
   * @param {String} jobId the job's ID
   * @returns
   */
  deleteJob(jobId) {
    const SQS = new AWS.SQS();
    const options = Object.assign(Service.getDefaultConfig({}), {
      ReceiptHandle: jobId,
    });

    return SQS.deleteMessage(options);
  },
};

module.exports = Service;
