const { Op, QueryTypes } = require("sequelize");
const Sanitizer = require("../utils/sanitizer");
const ValidationError = require("../utils/validation-error");
const Utils = require("../utils");
const { User, Status, Vendor, Address, sequelize, BankAccount } = require("../models");
const { UserRepo, VendorRepo } = require("../repositories/index.repo");
const { STATUSES } = require("../models/status");

module.exports = {
  async search(company, parameters) {
    const { query } = parameters;

    const rows = await Promise.all([
      User.findAll({
        where: {
          [Op.or]: {
            firstName: { [Op.like]: `%${query}%` },
            lastName: { [Op.like]: `%${query}%` },
            middleName: { [Op.like]: `%${query}%` },
          },
          company,
        },
        attributes: ["id", "email", "code", [sequelize.fn("CONCAT", sequelize.col("firstName"), " ", sequelize.col("lastName")), "name"]],
        include: [
          {
            model: BankAccount,
            where: { ownerType: "user", type: "real", status: STATUSES.ACTIVE },
            limit: 1,
            required: false,
          },
          Status,
          Address,
        ],
        limit: 5,
      }),
      Vendor.findAll({
        where: { company, name: { [Op.like]: `%${query}%` } },
        attributes: ["id", "email", "code", "name"],
        include: [
          {
            model: BankAccount,
            limit: 1,
            required: false,
            where: { ownerType: "vendor", type: "real", status: STATUSES.ACTIVE },
          },
          Status,
          Address,
        ],
        limit: 5,
      }),
    ]).then((modelReturn) => {
      return modelReturn.flat();
    });

    return {
      recipients: Sanitizer.sanitizeRecipients(rows),
    };
  },
  async list(company, filter, isMobile = false) {
    const rows = await Promise.all([
      UserRepo.getAllUsers({
        addBankAccount: true,
        queryParams: { ...filter, company },
        selectOptions: ["id", "email", "code", [sequelize.fn("CONCAT", sequelize.col("firstName"), " ", sequelize.col("lastName")), "name"]],
        isMobile,
      }),
      VendorRepo.getVendors({
        addBankAccount: true,
        addCategory: true,
        queryParams: { ...filter, company },
        selectOptions: ["name", "email"],
        isMobile,
      }),
    ]).then((modelReturn) => {
      return modelReturn.flat();
    });

    // Loop through the response. Some users have multiple bank accounts. Show them as multiple user/vendor
    const recipients = [];
    rows.forEach((recipient) => {
      const recipientData = recipient.toJSON();
      const { BankAccounts } = recipientData;
      if (!BankAccounts.length) recipients.push(recipientData);
      else {
        BankAccounts.forEach((bankAccount) => {
          const singleRecipient = { ...recipientData };
          singleRecipient.BankAccounts = [bankAccount];
          recipients.push(singleRecipient);
        });
      }
    });

    const sanitizedRecipients = Sanitizer.sanitizeRecipients(recipients);

    if (isMobile) {
      sanitizedRecipients.sort((a, b) => b.transactionCount - a.transactionCount);
    }

    return {
      recipients: sanitizedRecipients,
    };
  },
};
