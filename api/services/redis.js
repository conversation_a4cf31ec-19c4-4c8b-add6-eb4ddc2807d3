const Redlock = require("redlock");
const Redis = require("redis");
const { log, Log } = require("../utils/logger.utils");

const client = Redis.createClient({ url: `${process.env.REDIS_URL}` });
let redlock;
client.on("error", (err) => log(Log.bg.red, { message: "Redis Client Error", err: String(err) }));
client.on("connect", () => log(Log.bg.green, "Redis connected"));

module.exports = {
  expiresIn(key, duration) {
    return client.expire(key, duration);
  },
  set(key, value) {
    return new Promise((resolve, reject) => {
      client.set(key, value, (err, result) => {
        if (err) reject(err);
        resolve(result);
      });
    });
  },
  get(key) {
    return new Promise((resolve, reject) => {
      client.get(key, (err, reply) => {
        if (err) reject(err);
        resolve(reply);
      });
    });
  },
  delete(key) {
    return new Promise((resolve, reject) => {
      client.del(key, (err, reply) => {
        if (err) reject(err);
        resolve(reply);
      });
    });
  },

  async hmset(key, value) {
    return new Promise((resolve, reject) => {
      client.hmset(key, value, (err, result) => {
        if (err) reject(err);
        resolve(result);
      });
    });
  },

  async hgetall(key) {
    return new Promise((resolve, reject) => {
      client.hgetall(key, (err, reply) => {
        if (err) reject(err);
        resolve(reply);
      });
    });
  },

  async setex(key, value, duration) {
    return new Promise((resolve, reject) => {
      client.setex(key, duration, value, (err, reply) => {
        if (err) reject(err);
        resolve(reply);
      });
    });
  },

  async getRedLock({ retryCount = 10, retryDelay = 500, retryJitter = 100 } = {}) {
    if (redlock) return redlock; // Return existing instance if available
    if (!client) return null; // Ensure Redis client exists

    redlock = new Redlock([client], {
      retryCount,
      retryDelay,
      retryJitter,
    });

    redlock.on("error", (error) => {
      if (error instanceof Redlock.LockError) {
        log(Log.bg.yellow, { message: "Redlock Lock Error", error: error.message });
      } else {
        log(Log.bg.red, { message: "Redlock Error", error: error.stack || String(error) });
      }
    });

    return redlock;
  },

  /**
   * Incr key by value
   * @param {string} key
   * @param {integer} value
   * @returns
   */
  async incr(key, value) {
    return new Promise((resolve, reject) => {
      client.incrby(key, Number(value), (err, reply) => {
        if (err) reject(err);
        resolve(reply);
      });
    });
  },

  /**
   * Add element to a set
   * @param { string } key
   * @param { string | [] } value
   */
  async sAdd(key, value) {
    return client.sadd(key, value);
  },

  /**
   * Get all element in a set
   * @param { string } key
   */
  async getSetMembers(key) {
    return new Promise((resolve, reject) => {
      client.smembers(key, (error, reply) => {
        if (error) reject(error);
        resolve(reply);
      });
    });
  },

  /**
   * Remove element in a set
   * @param { string } key
   * @param { string | [] } value
   */
  async removeSetMember(key, value) {
    client.srem(key, value);
  },

  async getSetMember(key, value) {
    return new Promise((resolve, reject) => {
      client.smembers(key, (error, elements) => {
        if (error) reject(error);
        const foundElement = Array.from(elements).filter((element) => JSON.parse(element).reference === value);
        if (!foundElement || foundElement.length === 0) resolve(null);
        resolve(foundElement);
      });
    });
  },

  async getTTl(key) {
    return new Promise((resolve, reject) => {
      client.ttl(key, (error, ttl) => {
        if (error) reject(error);
        resolve(ttl);
      });
    });
  },
};
