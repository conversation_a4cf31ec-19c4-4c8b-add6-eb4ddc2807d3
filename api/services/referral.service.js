const { ReferralCode, Referral } = require("../models");
const { STATUSES } = require("../models/utils");
const { ReferralRepo, CompanyRepo, TransactionRepo } = require("../repositories/index.repo");
const { notifyUser } = require("./notification");

const Service = {
  async linkCompanyToReferrer(referralCode, company) {
    const foundReferralCode = await ReferralRepo.getReferralCode(referralCode);
    if (!foundReferralCode) return null;

    return CompanyRepo.updateACompany({ updateFields: { referrer: foundReferralCode.owner }, queryParams: { id: company } });
  },

  async claimReferral({ email, referralCode, firstName, companyName, company }) {
    const [updateExistingReferral] = await Referral.update(
      {
        status: STATUSES.ACTIVE,
        claimedOn: new Date(),
      },
      {
        where: {
          email,
        },
        include: [
          {
            model: ReferralCode,
            where: { code: `rfc_${referralCode}` },
          },
        ],
      }
    );
    if (!updateExistingReferral) {
      const foundReferralCode = await ReferralCode.findOne({ where: { code: `rfc_${referralCode}` } });
      if (!foundReferralCode) return;
      Referral.create({
        email,
        status: STATUSES.ACTIVE,
        referralCode: foundReferralCode?.id,
        claimedOn: new Date(),
        referrer: foundReferralCode.owner,
        validUntil: new Date(),
      });
    }
    return Service.notifyUserNewClaimedReferral({
      email,
      firstName,
      companyName,
      referralCode,
    });
  },

  async notifyUserNewClaimedReferral({ email, ...payload }) {
    /**
     * Get company and referrer details
     * send email to referrer that company has claimed
     */
    notifyUser({ email: "<EMAIL>" }, "referral-claimed", payload, {
      subject: `You have a new friend on Bujeti! ${payload.companyName} just signed up`,
    });
  },

  async processRewards({ idempotencyKey: id }) {
    const transaction = await TransactionRepo.getTransaction({
      queryParams: { id },
      selectOptions: ["company", "amount", "status"],
    });
    if (transaction.status !== STATUSES.SUCCESS) return null;
    let company = null;
    try {
      company = await transaction.getCompany();
    } catch (error) {
      // Cause I don't have energy to rebuild my confidence in built-in methods.
      // IF IT BREAKS, IT BREAKS.
      company = await CompanyRepo.getCompany({
        queryParams: { id: transaction.company },
        selectOptions: ["referrer"],
      });
    }

    const rewards = Math.min(transaction.amount * 0.00001, 1000);
    await Promise.all([
      ReferralRepo.addCompanyRewardPoints(transaction.company, rewards),
      ReferralRepo.addUserRewardPoints(company.referrer, rewards),
    ]);
    return {
      rewards,
      company: company.id,
      transaction: id,
    };
  },
};

module.exports = Service;
