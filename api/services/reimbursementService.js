/* eslint-disable no-unused-vars */
/* eslint-disable no-param-reassign */
const { Op, fn, col, Sequelize } = require("sequelize");
const ReimbursementValidator = require("../validators/reimbursement");
const ValidationError = require("../utils/validation-error");
const { STATUSES } = require("../models/status");
const NotFoundError = require("../utils/not-found-error");
const {
  User,
  Company,
  Reimbursement,
  Status,
  Asset,
  Vendor,
  Budget,
  BankAccount,
  Transaction,
  Category,
  sequelize,
  Team,
  ApprovalRequest,
  Approval,
  Approver,
  ApprovalStage,
  ApproverLevel,
  ApprovalRule,
  Balance,
} = require("../models");
const NotificationService = require("./notification");
const ChargeService = require("./charge");
const BudgetService = require("./budget");
const CompanyService = require("./company");
const TransactionService = require("./transaction");
const SettingsService = require("./settings");
const ApprovalService = require("./approval.service");
const PaymentService = require("./paymentservice");
const BillingService = require("./billing");
const Utils = require("../utils");
const UserService = require("./user");
const UserRepo = require("../repositories/user.repo");
const paymentPlan = require("../models/paymentPlan");
const { BalanceRepo, ReimbursementRepo, BudgetRepo, BankAccountRepo } = require("../repositories/index.repo");
const { Mandate, DirectDebit } = require("../models");
const BankService = require("./bank");
const { TRANSACTION_TYPES } = require("../models/transaction");
const responseUtils = require("../utils/response.utils");
const HelperService = require("./helper.service");
const ScheduleService = require("./schedule.service");

const Service = {
  validateCreationPayload(payload) {
    const { error } = ReimbursementValidator.create.validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: true, message: "Valid payload" };
  },

  validateUpdatePayload(payload) {
    const { error } = ReimbursementValidator.update.validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: true, message: "Valid payload" };
  },

  validateMoreInfoPayload(payload) {
    const { error } = ReimbursementValidator.moreInfo.validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: true, message: "Valid payload" };
  },

  validatePayload(payload, name) {
    const { error } = ReimbursementValidator[name].validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: true, message: "Valid payload" };
  },

  async createReimbursement({
    company,
    currency,
    user,
    budget,
    amount,
    vendor,
    team = null,
    description,
    category,
    // eslint-disable-next-line camelcase
    expense_date = new Date(),
    receipt = null,
    balance,
    directDebit,
    foundCompany,
  }) {
    const payload = {
      company,
      user,
      budget,
      amount,
      team,
      vendor,
      currency,
      // eslint-disable-next-line camelcase
      expense_date,
      description,
      category,
      balance,
    };

    if (directDebit) {
      const bankAccount = await BankAccountRepo.getOneBankAccount({
        queryParams: {
          code: directDebit.bankAccount,
        },
        selectOptions: ["number", "bankCode"],
      });

      if (!bankAccount) throw new NotFoundError("Bank account");

      // verify active mandate
      const existingMandate = await Mandate.findOne({
        where: {
          bankAccount: bankAccount.id,
        },
      });
      if (!existingMandate) throw new ValidationError("No active mandate found");

      const createdDirectDebit = await DirectDebit.create({
        mandate: existingMandate.id,
        company: foundCompany.id,
        amount: payload.amount,
        beneficiaryAccountNumber: bankAccount.number,
        beneficiaryBankCode: bankAccount.bankCode,
        narration: description || `Reimbursement request`,
        status: STATUSES.PENDING,
        reference: Utils.generateRandomString(14),
      });

      payload.directDebitId = createdDirectDebit.id;
    }

    const reimbursement = await Reimbursement.create(payload);
    if (Array.isArray(receipt) && receipt.length) {
      await PaymentService.callService("updateAssetWithEntity", receipt, { entityId: reimbursement.id, entityType: "Reimbursement" });
    }
    const { success } = await ApprovalService.conditionDetector({
      id: reimbursement.id,
      type: "reimbursement",
      company,
      user,
    });
    // If no approval, notify admins
    if (success) {
      Service.notifyAdmins({
        company,
        reimbursement,
      });
    }

    return reimbursement;
  },

  async listReimbursements(query = {}) {
    const {
      company,
      user,
      currency,
      from,
      team,
      owner,
      to,
      view_as: viewAs,
      status = "pending",
      perPage = 50,
      page = 1,
      loggedInUser,
      deadLine,
      min_amount: minAmount,
      max_amount: maxAmount,
    } = query;
    const isReviewerView = viewAs === "reviewer";
    const skip = (parseInt(page, 10) - 1) * parseInt(perPage, 10);

    const userCriteria = {
      model: User,
      via: "user",
      as: "User",
    };

    if (owner) {
      userCriteria.where = {
        "$User.code$": owner,
      };
    }

    const teamCriteria = {
      model: Team,
      required: !!team,
      where: {
        ...(team && { "$Team.code$": team }),
      },
    };

    const criteria = {
      company,
      ...(status
        ? {
            status: String(status)
              .split(",")
              .map((value) => STATUSES[value.toUpperCase()]),
          }
        : {
            status: {
              [Op.ne]: STATUSES.DELETED,
            },
          }),
      ...(currency && { currency: String(currency).split(",") }),
    };

    const isPendingStatus = Array.isArray(criteria.status)
      ? criteria.status.length === 1 && criteria.status.includes(STATUSES.PENDING) && (!user || isReviewerView)
      : criteria.status === STATUSES.PENDING && (!user || isReviewerView);

    const shouldFilterByUser = !isPendingStatus && user;

    if (shouldFilterByUser) {
      criteria.user = user;
    }

    if (from || to) {
      criteria.created_at = {};
      if (from && Utils.isValidDate(from)) {
        criteria.created_at[Op.gte] = from;
      }
      if (to && Utils.isValidDate(to)) {
        criteria.where = sequelize.where(sequelize.fn("date", sequelize.col("Reimbursement.created_at")), "<=", to);
      }
    }

    if (deadLine) {
      criteria.deadLine = {
        [Op.gte]: deadLine,
        [Op.lte]: deadLine,
      };
    }

    if (minAmount || maxAmount) {
      criteria.amount = {};
      if (minAmount) criteria.amount[Op.gte] = Number(minAmount);
      if (maxAmount) criteria.amount[Op.lte] = maxAmount;
    }

    const include = [];

    if (isPendingStatus) {
      const approvalRequests = {
        model: ApprovalRequest,
        attributes: ["id", "status"],
        required: false,
        as: "ReimbursementApprovalRequests",
        where: { entity: "reimbursement" },
        include: [
          {
            model: ApprovalRule,
            attributes: ["id"],
          },
          {
            model: ApprovalStage,
            attributes: ["id", "status"],
            order: [["created_at", "ASC"]],
            include: [
              {
                model: ApproverLevel,
                attributes: ["id"],
                include: [
                  {
                    model: Approver,
                    attributes: ["id", "status"],
                    include: [
                      {
                        model: User,
                        attributes: ["id", "code"],
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            model: Approval,
            attributes: ["id"],
            include: [
              {
                model: Approver,
                attributes: ["id", "status"],
                include: [
                  {
                    model: User,
                    attributes: ["id", "code"],
                  },
                ],
              },
            ],
          },
        ],
      };

      include.push(approvalRequests);
    }

    const [total = 0, reimbursements] = await Promise.all([
      Reimbursement.count({
        where: {
          ...criteria,
          ...(isPendingStatus && {
            ...HelperService.returnRelevantRequests("ReimbursementApprovalRequests", loggedInUser, !!user),
          }),
        },
        include: [...include, userCriteria, teamCriteria],
        distinct: true,
        subQuery: false,
        col: "id",
      }),
      Reimbursement.findAll({
        where: {
          ...criteria,
          ...(isPendingStatus && {
            ...HelperService.returnRelevantRequests("ReimbursementApprovalRequests", loggedInUser, !!user),
          }),
        },
        include: [
          ...include,
          userCriteria,
          {
            model: User,
            via: "reviewer",
            as: "Reviewer",
          },
          {
            model: Budget,
            required: false,
          },
          teamCriteria,
          {
            model: Asset,
            as: "ReimbursementAssets",
            required: false,
            where: {
              $entityType$: "Reimbursement",
            },
          },
          Vendor,
          Company,
          Status,
          Category,
          Balance,
        ],
        limit: Number(perPage),
        offset: skip,
        order: [["created_at", "DESC"]],
        distinct: true,
        subQuery: false,
        col: "id",
      }),
    ]);
    return {
      meta: {
        total,
        perPage: Number(perPage),
        page: Number(page),
        nextPage: Number(page) + 1,
        hasMore: total >= page * perPage,
      },
      reimbursements: Service.transformReimbursements(reimbursements),
    };
  },

  async getReimbursement(criteria) {
    const approvalRequest = {
      model: ApprovalRequest,
      required: false,
      where: {
        entity: "reimbursement",
      },
      include: [
        ApprovalRule,
        {
          model: ApprovalStage,
          include: [
            {
              model: ApproverLevel,
              include: [
                {
                  model: Approver,
                  include: [
                    {
                      model: User,
                      where: { status: STATUSES.ACTIVE },
                    },
                  ],
                },
              ],
            },
          ],
          order: [["created_at", "ASC"]],
        },
        { model: Approval, include: [{ model: Approver, include: [User] }] },
      ],
    };

    const approvalRequests = {
      model: ApprovalRequest,
      required: false,
      as: "ReimbursementApprovalRequests",
      where: {
        entity: "reimbursement",
      },
      include: [
        ApprovalRule,
        {
          model: ApprovalStage,
          order: [["created_at", "ASC"]],
          include: [
            {
              model: ApproverLevel,
              include: [
                {
                  model: Approver,
                  include: [
                    {
                      model: User,
                      where: { status: STATUSES.ACTIVE },
                    },
                  ],
                },
              ],
            },
          ],
        },
        { model: Approval, include: [{ model: Approver, include: [User] }] },
      ],
    };

    const vendorCriteria = {
      model: Vendor,
      required: false,
      include: [
        {
          model: BankAccount,
          attributes: ["id", "code", "bankCode", "number", "bankName", "currency"],
          where: { type: "real", ownerType: "vendor", status: STATUSES.ACTIVE },
          required: false,
          limit: 1,
        },
      ],
    };
    const reimbursement = await Reimbursement.findOne({
      where: {
        ...criteria,
        status: {
          [Op.ne]: STATUSES.DELETED,
        },
      },
      include: [
        {
          model: User,
          via: "user",
          as: "User",
          required: false,
          include: [
            {
              model: BankAccount,
              attributes: ["id", "code", "bankCode", "number", "bankName", "currency"],
              where: { type: "real", ownerType: "user", status: STATUSES.ACTIVE },
              required: false,
              limit: 1,
            },
          ],
        },
        {
          model: User,
          via: "reviewer",
          as: "Reviewer",
          required: false,
        },
        {
          model: Asset,
          as: "ReimbursementAssets",
          required: false,
          where: {
            $entityType$: "Reimbursement",
          },
        },
        approvalRequests,
        approvalRequest,
        Budget,
        Status,
        Company,
        Category,
        vendorCriteria,
        {
          model: Transaction,
          required: false,
          include: [
            {
              model: BankAccount,
              required: false,
              where: {
                ownerType: "vendor",
              },
            },
          ],
        },
        Balance,
      ],
    });
    if (!reimbursement) throw new NotFoundError("Reimbursement");
    if (reimbursement.Budget) {
      reimbursement.Budget.available = await BudgetService.getBudgetAvailableBalance(reimbursement.Budget);
    }
    return reimbursement;
  },

  async updateReimbursement(code, payload) {
    const { status, company, receipt, user, source, ...rest } = payload;
    const { budget } = payload;

    const reimbursement = await Service.getReimbursement({ code, company });

    if (!reimbursement) throw new NotFoundError("Reimbursement");

    if (user && status === STATUSES.DELETED && reimbursement.user !== user) {
      throw new ValidationError(`Only the requester can delete a reimbursement request`);
    }

    if (user && reimbursement.user !== user) {
      throw new ValidationError("You cannot edit this reimbursement");
    }

    if (reimbursement.status !== STATUSES.PENDING)
      throw new ValidationError(`Action cannot be performed, Reimbursement already ${reimbursement.Status.value.toLowerCase()}`);
    if (status === "approved") {
      if (!reimbursement.budget) throw new ValidationError(`Reimbursement must be paid out of a budget`);
      const plan = await paymentPlan.findOne({
        where: {
          id: reimbursement.Company.paymentPlan,
          status: STATUSES.ACTIVE,
        },
      });
      return Service.approveReimbursement({
        code,
        companyCode: reimbursement.Company.code,
        ...payload,
        budget: reimbursement.budget,
        plan: plan && plan.configuration,
      });
    }
    if (status === "declined") {
      return Service.declineReimbursement({ code, ...payload });
    }

    Reimbursement.update({ status, ...rest, budget }, { where: { code } });
    if (Array.isArray(receipt) && receipt.length) {
      await PaymentService.callService("updateAssetWithEntity", receipt, { entityId: reimbursement.id, entityType: "Reimbursement" });
    }

    if (status === STATUSES.DELETED) {
      await ApprovalRequest.update({ status }, { where: { entity: "reimbursement", entity_id: reimbursement.id } });
    }

    return Reimbursement.findOne({ where: { code } });
  },
  // eslint-disable-next-line consistent-return
  async approveReimbursement(payload) {
    const {
      code,
      company,
      reviewer,
      recipient,
      amount,
      currency,
      budget,
      category,
      status = STATUSES.APPROVED,
      companyCode,
      plan = SettingsService.get("pricing_config"),
      actionLater,
      schedule,
      canDoDirectDebit,
      existingMandate,
    } = payload;
    // update record

    const shouldProcessPaymentImmediately = !actionLater;
    const reimbursement = await Reimbursement.findOne({
      where: { code, company },
      attributes: ["vendor", "budget", "code", "team", "id", "reviewer", "user", "balance", "directDebitId", "description"],
      include: [
        {
          model: Vendor,
          include: [
            {
              model: BankAccount,
              attributes: ["id", "code", "bankCode", "number", "bankName", "currency"],
              where: { currency, type: "real", ownerType: "vendor" },
              required: false,
              limit: 1,
            },
          ],
        },
        {
          model: Budget,
          required: false,
        },
        {
          model: Balance,
          required: false,
        },
        {
          model: User,
          required: true,
          as: "User",
        },
      ],
    });

    if (!reimbursement) {
      throw new NotFoundError("Reimbursement");
    }

    if (!(reimbursement.budget || reimbursement.balance)) {
      if (shouldProcessPaymentImmediately) throw new ValidationError("Please specify a source account to debit for this request");
    }

    // if condition detected stop here
    const { success, data } = await ApprovalService.conditionDetector(
      {
        id: reimbursement.id,
        type: "reimbursement",
        company,
      },
      true
    );

    if (!success) {
      return ApprovalService.reviewApprovalRequest({
        code: data.code,
        user: reviewer,
        company: reimbursement.company,
        status: "approved",
        actionLater,
        schedule,
      });
    }

    const actualRecipient = {
      type: "vendor",
      id: reimbursement.vendor,
    };
    if (!reimbursement.vendor) {
      actualRecipient.id = recipient.id;
      actualRecipient.type = "user";
    }

    const bankAccount = Array.isArray(recipient.BankAccounts) && recipient.BankAccounts.length > 0 ? recipient.BankAccounts[0] : null;

    if (!bankAccount) throw new NotFoundError("Recipient bank account");

    const isDirectDebit = !!canDoDirectDebit;

    const { cbnFee, bujetiFee } = BillingService.computeFees({ amount, companyCode, currency, plan, isDirectDebit });

    const narration = Utils.truncateSentence(reimbursement.description);

    const transaction = await TransactionService.createTransaction({
      amount,
      description: reimbursement.description,
      payer: recipient.id,
      budget,
      company,
      currency,
      bank_account: bankAccount.id,
      status: STATUSES.PENDING,
      narration,
      recipient: actualRecipient.id,
      recipient_type: actualRecipient.type,
      category,
      // exclude fees for direct debit for now
      ...(!isDirectDebit && {
        processor_fee: cbnFee,
        bujeti_fee: bujetiFee,
      }),
      team: reimbursement.team,
      balance: reimbursement.balance,
      type: isDirectDebit ? TRANSACTION_TYPES.DIRECT_DEBIT : TRANSACTION_TYPES.PAYMENT,
      directDebitId: reimbursement.directDebitId,
    });

    const receiptDetails = {
      bankCode: bankAccount.bankCode,
      bankName: bankAccount.bankName,
      number: bankAccount.number,
      reason: "reimbursement",
      reimbursement_code: code,
      transaction: transaction.code,
      budget: reimbursement.Budget?.code,
      balance: reimbursement.Balance?.code,
    };

    await Reimbursement.update(
      {
        transaction: transaction.id,
        status,
        reviewer,
        reviewed_on: new Date(),
      },
      { where: { code, company } }
    );

    const {
      Team: team,
      Company: foundCompany,
      Budget: foundBudget,
      description,
    } = await ReimbursementRepo.getReimbursement({ criteria: { code: reimbursement.code } });
    if (schedule) {
      await createScheduledTransaction({
        schedule,
        currency,
        amount,
        reimbursement,
        company,
        bankAccount,
        actualRecipient,
        bujetiFee,
        cbnFee,
        narration,
        category,
        description,
        transaction,
      });
    } else {
      // eslint-disable-next-line no-use-before-define
      await processPayment({
        isDirectDebit,
        reimbursement,
        actionLater,
        narration,
        foundCompany,
        transaction,
        company,
        amount,
        currency,
        budget,
        receiptDetails,
        existingMandate,
      });
    }

    // notify beneficiary of pending payment
    const adminUser = await UserRepo.getOneUser({
      queryParams: { id: reviewer },
      selectOptions: ["firstName", "lastName"],
    });

    NotificationService.notifyUser({ email: recipient.email }, "reimbursement-approved", {
      firstName: recipient.firstName,
      approvedBy: `${adminUser.firstName} ${adminUser.lastName}`,
      userFullname: `${recipient.firstName} ${recipient.lastName}`,
      userNameInitials: Utils.getInitials(`${recipient.firstName} ${recipient.lastName}`),
      companyName: foundCompany.name,
      budget: foundBudget?.name || null,
      companyNameInitials: Utils.getInitials(foundCompany.name),
      teamName: team?.name || null,
      amount: (parseInt(amount, 10) / 100).toLocaleString(),
      currency,
      code: reimbursement.code,
      description,
    });

    // UPDATE PREVIOUS ACTION NOTIFICATION TO INFO
    NotificationService.updateNotification({ reference_code: reimbursement.code }, { type: "info", badge: "info" });

    const notificationPayload = {
      company,
      user_id: recipient.id,
      type: `info`,
      badge: `info`,
      title: `Reimbursement approved`,
      message: `${Utils.toTitle(adminUser.firstName)} has approved your reimbursement request of ${currency}${Utils.formatAmount(
        amount
      ).toLocaleString()} on ${Utils.getCurrentDate()}`,
      table: {
        code: reimbursement.code,
        entity: "Reimbursement",
      },
      event: "reimbursementUpdate",
    };
    NotificationService.saveNotification(notificationPayload);
    // return reimbursement;

    // ApprovalService.completeReimbursement({
    // 	reimbursement,
    // 	company,
    // 	amount,
    // 	currency,
    // 	budget,
    // 	transaction,
    // 	recipient,
    // 	narration: transaction.narration,
    // 	receiptDetails,
    // });
    // Turn above to service
  },

  async declineReimbursement(payload) {
    const { code, company, reviewer, recipient, amount, currency, note, status = STATUSES.DECLINED } = payload;
    await Reimbursement.update(
      {
        status,
        reviewer,
        note,
        reviewed_on: new Date(),
      },
      { where: { code, company } }
    );
    const {
      description,
      Team: team,
      Company: foundCompany,
      Budget: foundBudget,
    } = await ReimbursementRepo.getReimbursement({ criteria: { code, company } });
    // update record
    const adminUser = await UserRepo.getOneUser({
      queryParams: { id: reviewer },
      selectOptions: ["firstName", "lastName"],
    });

    const isDeactivatedUser = recipient.status === STATUSES.DELETED;

    if (isDeactivatedUser) return;

    NotificationService.notifyUser(
      {
        email: recipient.email,
      },
      "reimbursement-declined",
      {
        firstName: recipient.firstName,
        description,
        approvedBy: `${adminUser.firstName} ${adminUser.lastName}`,
        userFullname: `${recipient.firstName} ${recipient.lastName}`,
        userNameInitials: Utils.getInitials(`${recipient.firstName} ${recipient.lastName}`),
        companyName: foundCompany.name,
        budget: foundBudget ? foundBudget.name : null,
        companyNameInitials: Utils.getInitials(foundCompany.name),
        teamName: team ? team.name : null,
        note,
        amount: (parseInt(amount, 10) / 100).toLocaleString(),
        login: `${process.env.DASHBOARD_URL}/login`,
        currency,
        code,
      }
    );

    // UPDATE PREVIOUS ACTION NOTIFICATION TO INFO
    NotificationService.updateNotification({ reference_code: code }, { type: "info", badge: "info" });

    const notificationPayload = {
      company,
      user_id: recipient.id,
      type: `info`,
      badge: `info`,
      title: `Reimbursement declined`,
      message: `${Utils.toTitle(adminUser.firstName)} has declined your reimbursement request of ${currency}${Utils.formatAmount(
        amount
      ).toLocaleString()} on ${Utils.getCurrentDate()}`,
      reference_code: code,
      table: {
        code,
        entity: "Reimbursement",
      },
      event: "reimbursementUpdate",
    };
    NotificationService.saveNotification(notificationPayload);
  },
  /**
   * Finalize a reimbursement post payment
   * @param payload
   * @returns {Promise<void>}
   */
  async finalizeReimbursement(payload) {
    const { code, company, transaction, status = STATUSES.PAID } = payload;

    // we are making this smarter. if no rbs code, use transaction id instead
    const criteria = { company };

    if (code) {
      criteria.code = code;
    } else {
      criteria.transaction = transaction;
    }

    // eslint-disable-next-line no-unused-vars
    const [result] = await Reimbursement.update(
      {
        transaction,
        status,
      },
      { where: criteria }
    );
    const reimbursement = await Reimbursement.findOne({
      where: criteria,
      include: [
        {
          model: User,
          attributes: ["email", "firstName"],
          via: "user",
          as: "User",
        },
        Budget,
      ],
    });
    // update record
    if (status === STATUSES.PAID) {
      const recipient = reimbursement && reimbursement.User;

      NotificationService.notifyUser(
        {
          email: recipient.email,
        },
        "payment-received",
        {
          first_name: recipient.firstName,
          amount: reimbursement.amount.toLocaleString(),
          currency: reimbursement.currency,
          code,
        }
      );
    }
  },
  async validateReimbursement({ code, company, decision, schedule, note, actionLater }) {
    Service.validatePayload({ actionLater, schedule, note, decision, code }, "validateReimbursement");

    const reimbursementRequest = await Reimbursement.findOne({
      where: {
        code,
        company,
      },
      include: {
        model: User,
        attributes: ["id", "code", "email", "firstName", "lastName", "status"],
        as: "User",
        include: [
          {
            model: BankAccount,
            attributes: ["id", "code", "bankCode", "number", "bankName", "currency"],
            where: { type: "real", ownerType: "user", status: STATUSES.ACTIVE },
            required: false,
            limit: 1,
          },
        ],
      },
    });
    if (!reimbursementRequest) throw new NotFoundError("Reimbursement");
    if (reimbursementRequest.status === STATUSES.APPROVED) throw new ValidationError("Reimbursement already approved");
    if (!reimbursementRequest.User) throw new NotFoundError("Recipient");

    const cannnotProcessRbsForDeactivatedUser = reimbursementRequest.User.status === STATUSES.DELETED && decision === "approve";
    if (cannnotProcessRbsForDeactivatedUser) throw new ValidationError("Requester's account deactivated. Reimbursement cannot be processed");
    if (
      decision === "approve" &&
      (!reimbursementRequest.User.BankAccounts ||
        typeof reimbursementRequest.User.BankAccounts !== "object" ||
        reimbursementRequest.User.BankAccounts.length === 0)
    )
      throw new NotFoundError(`Bank Account`);
    else if (reimbursementRequest.status === STATUSES.PENDING)
      return {
        code,
        message: "Reimbursement valid",
        reimbursement: reimbursementRequest,
      };
    else throw new ValidationError("Reimbursement in invalid state");
  },
  async notifyAdmins({ company, reimbursement }) {
    // TODO notify admins
    const user = await UserService.fetchUser(reimbursement.user);
    const {
      Team: team,
      Company: foundCompany,
      Budget: foundBudget,
    } = await ReimbursementRepo.getReimbursement({ criteria: { id: reimbursement.id } });
    let admins = [];
    if (user.manager) {
      const manager = await UserRepo.getOneUser({ queryParams: { id: user.manager }, selectOptions: ["email", "firstName", "lastName"] });
      admins.push(manager);
    } else
      ({ Users: admins } = await CompanyService.getCompanyWithAdmins({
        id: company,
      }));
    const recipient = (reimbursement.vendor && reimbursement.vendor.name) || `${user.firstName} ${user.lastName}`;
    const payload = {
      firstName: admins[0].firstName,
      currency: reimbursement.currency,
      amount: Utils.formatAmount(reimbursement.amount).toLocaleString(),
      userFullname: `${user.firstName} ${user.lastName}`,
      login: `${Utils.getDashboardURL()}/login`,
      dashboardUrl: `${Utils.getDashboardURL()}/requests/reimbursements/${reimbursement.code}`,
      teamName: team ? team.name : null,
      companyName: foundCompany.name,
      budget: foundBudget ? foundBudget.name : null,
      description: reimbursement.description,
      companyNameInitials: Utils.getInitials(foundCompany.name),
      recipientName: recipient,
      recipientNameInitials: Utils.getInitials(recipient),
      userNameInitials: Utils.getInitials(`${user.firstName} ${user.lastName}`),
    };

    Array.from(admins).forEach((admin) => {
      const sourceConfig = {
        subject: `${payload.userFullname} has requested a reimbursement of ${payload.currency}${payload.amount.toLocaleString()}`,
        from_name: "Bujeti",
      };
      const notificationPayload = {
        company,
        user_id: admin.id,
        type: `request`,
        badge: `request`,
        title: `${Utils.toTitle(user.firstName)} requests ${reimbursement.currency}${Utils.formatAmount(reimbursement.amount).toLocaleString()}`,
        message: `${Utils.toTitle(user.firstName)} has made a reimbursement request of ${reimbursement.currency}${Utils.formatAmount(
          reimbursement.amount
        ).toLocaleString()}`,
        body: {
          code: reimbursement.code,
          entity: "Reimbursement",
        },
        table: {
          code: reimbursement.code,
          entity: "Reimbursement",
        },
        reference_code: reimbursement.code,
        event: "reimbursementRequest",
      };
      NotificationService.saveNotification(notificationPayload);
      payload.firstName = admin.firstName;
      NotificationService.notifyUser(admin, "reimbursement-request", payload, sourceConfig);
    });
  },

  async getReimbursementInternally(filter) {
    const foundReimbursement = await ReimbursementRepo.getReimbursement({ criteria: filter, includeApproval: true });
    if (!foundReimbursement) throw new NotFoundError("Reimbursement");
    return foundReimbursement;
  },

  transformReimbursements(reimbursements) {
    return reimbursements.map((model) => {
      const reimbursement = model.toJSON();
      let { ApprovalRequest: approvalRequest = {} } = reimbursement;

      if (approvalRequest) {
        const { ApprovalStages = [], Approvals = [] } = approvalRequest;

        const isAlreadyApproved = Approvals.reduce((lookUpMap, approval) => {
          if (approval.Approver) {
            lookUpMap[approval.Approver.code] = true;
          }
          return lookUpMap;
        }, {});

        let hasSeenApprovedOrDeclined = false;

        const pendingApprovals = ApprovalStages.map((approvalStage) => {
          let Approvers = [];

          if (
            approvalStage.ApproverLevel &&
            approvalStage.ApproverLevel.Approvers &&
            ![STATUSES.APPROVED, STATUSES.DECLINED].includes(approvalStage.status)
          ) {
            Approvers = approvalStage.ApproverLevel.Approvers.filter((approver) => !isAlreadyApproved[approver.code]);
          } else if ([STATUSES.APPROVED, STATUSES.DECLINED].includes(approvalStage.status)) {
            hasSeenApprovedOrDeclined = true;
          }
          return {
            ...approvalStage,
            ApproverLevel: {
              ...approvalStage.ApproverLevel,
              Approvers: hasSeenApprovedOrDeclined ? [] : Approvers,
            },
          };
        }).filter(Boolean);

        approvalRequest = {
          ...approvalRequest,
          ApprovalStages: pendingApprovals,
        };
      }

      return {
        ...reimbursement,
        ApprovalRequest: approvalRequest,
        relatedApprovalRequests: ApprovalService.transformApprovalRequests(model.ReimbursementApprovalRequests || []),
      };
    });
  },

  async initiateReimbursementTransaction(code) {
    const foundReimbursement = await ReimbursementRepo.getReimbursement({
      criteria: { code },
      includeTransaction: true,
      includeApproval: true,
    });

    if (!foundReimbursement) throw new NotFoundError("Reimbursement");
    if (foundReimbursement.status !== STATUSES.APPROVED) throw new ValidationError("Reimbursement hasn't been approved");
    const { ApprovalRequest: reimbursementApprovalRequest, Transaction: reimbursementTransaction = null } = foundReimbursement;
    if (reimbursementTransaction && reimbursementTransaction.status !== STATUSES.PENDING)
      throw new ValidationError("Reimbursement already has a transaction");

    const requestId = (reimbursementApprovalRequest && reimbursementApprovalRequest.id) || null;
    return ApprovalService.completeReimbursement({
      entityId: foundReimbursement.id,
      recipient: "vendor",
      status: STATUSES.APPROVED,
      requestId,
    });
  },

  async requestForMoreInfo(payload) {
    const { code, company, note, reviewer } = payload;
    const reimbursement = await Service.getReimbursement({ code, company });

    if (reimbursement.status !== STATUSES.PENDING) throw new ValidationError("Reimbursement can only be edited when pending");

    await reimbursement.update({ needsMoreInfo: true, moreInfoDescription: note });

    // notify beneficiary
    const { Company: foundCompany, Budget: foundBudget, description, amount, currency } = reimbursement;

    const recipient = reimbursement.User;

    // update record
    const adminUser = await UserRepo.getOneUser({
      queryParams: { id: reviewer },
      selectOptions: ["firstName", "lastName"],
    });

    const sourceConfig = {
      subject: `${adminUser.firstName} has requested for more info on your reimbursement request of ${
        reimbursement.currency
      }${reimbursement.amount.toLocaleString()}`,
      from_name: `${adminUser.firstName} ${adminUser.lastName}`,
    };

    NotificationService.notifyUser(
      {
        email: recipient.email,
      },
      "reimbursement-info-needed",
      {
        firstName: recipient.firstName,
        description,
        note,
        amount: (parseInt(amount, 10) / 100).toLocaleString(),
        login: `${process.env.DASHBOARD_URL}/login`,
        currency,
        code,
        requestedBy: `${adminUser.firstName} ${adminUser.lastName}`,
        userFullname: `${recipient.firstName} ${recipient.lastName}`,
        userNameInitials: Utils.getInitials(`${recipient.firstName} ${recipient.lastName}`),
        companyName: foundCompany.name,
        budget: foundBudget ? foundBudget.name : null,
        companyNameInitials: Utils.getInitials(foundCompany.name),
      },
      sourceConfig
    );

    // UPDATE PREVIOUS ACTION NOTIFICATION TO INFO
    NotificationService.updateNotification({ reference_code: code }, { type: "info", badge: "info" });

    const notificationPayload = {
      company,
      user_id: recipient.id,
      type: `info`,
      badge: `info`,
      title: `Reimbursement Update`,
      message: `${Utils.toTitle(adminUser.firstName)} needs more info to review your reimbursement request of ${currency}${Utils.formatAmount(
        amount
      ).toLocaleString()} on ${Utils.getCurrentDate()}`,
      reference_code: code,
      body: {
        code: reimbursement.code,
        entity: "Reimbursement",
      },
      table: {
        code,
        entity: "Reimbursement",
      },
      event: "reimbursementUpdate",
    };
    NotificationService.saveNotification(notificationPayload);
  },

  async multipleReimbursementAction(payload) {
    const { decision } = payload;
    if (decision === "approve") return Service.approveMultipleReimbursement(payload);
    return Service.declineMultipleReimbursement(payload);
  },

  async declineMultipleReimbursement(payload) {
    const {
      reimbursements,
      note = null,
      reviewer: { id: reviewer },
      company: { id: company },
    } = payload;
    const foundReimbursements = await Promise.all(
      Array.from(reimbursements).map((reimbursement) => {
        return Service.validateReimbursement({
          code: reimbursement,
          company,
          decision: "decline",
        });
      })
    );
    const results = await Promise.allSettled(
      Array.from(foundReimbursements).map(({ reimbursement }) => {
        return Service.declineReimbursement({
          code: reimbursement.code,
          company,
          reviewer,
          note,
          recipient: reimbursement.User,
          amount: reimbursement.amount,
          currency: reimbursement.currency,
        });
      })
    );
    const failedApproval = results.find((result) => result.status === "rejected");

    if (failedApproval) throw new ValidationError(failedApproval?.reason?.message || "An error occurred while approving request");
    return responseUtils.sendObjectResponse("Declined");
  },

  async approveMultipleReimbursement(payload) {
    const {
      reimbursements,
      reviewer,
      company: { id: company, code: companyCode, paymentPlan: companyPaymentPlan },
      actionLater,
    } = payload;

    await Promise.all(
      Array.from(reimbursements).map((reimbursement) => {
        return Service.validateReimbursement({
          code: reimbursement,
          company,
          decision: "approve",
          actionLater,
        });
      })
    );

    const foundReimbursements = await ReimbursementRepo.getReimbursments({ filters: { code: reimbursements, status: STATUSES.PENDING } });
    if (foundReimbursements.length !== reimbursements.length) throw new ValidationError("One or more reimbursement not found");

    const groupedReimbursement = {};
    // eslint-disable-next-line array-callback-return
    Array.from(foundReimbursements).map((reimbursement) => {
      // eslint-disable-next-line no-unused-vars, no-shadow
      const { Budget: BudgetObject = {}, Balance = {}, ...rest } = reimbursement.toJSON();
      const key = BudgetObject?.code || Balance?.code;
      if (!key) throw new ValidationError("One or more Reimbursment isn't tied to a Budget or Balance");
      if (groupedReimbursement[key]) {
        groupedReimbursement[key].reimbursements.push({ ...rest });
        groupedReimbursement[key].totalAmount += rest.amount;
      } else {
        // New reimbursment request
        groupedReimbursement[key] = {
          ...(Balance && { balance: Balance }),
          ...(BudgetObject && { budget: BudgetObject }),
          totalAmount: rest.amount,
        };
        groupedReimbursement[key].reimbursements = [{ ...rest }];
      }
    });
    const groupedReimbursementKeys = Object.keys(groupedReimbursement);
    // Get Balance or Budget Balance
    await Promise.all(
      Array.from(groupedReimbursementKeys).map(async (currentKey) => {
        if (String(currentKey).includes("bdg_")) {
          // Get budget Balance
          groupedReimbursement[currentKey].availableBalance = await BudgetService.getBudgetAvailableBalance(groupedReimbursement[currentKey].budget);
        } else {
          const {
            balance: { company: companyId, currency, code },
          } = groupedReimbursement[currentKey] || {};
          groupedReimbursement[currentKey].availableBalance = await BalanceRepo.getAvailableBalance({ company: companyId, currency, code });
        }
      })
    );

    Array.from(groupedReimbursementKeys).map(async (currentKey) => {
      const { availableBalance, totalAmount } = groupedReimbursement[currentKey];
      if (availableBalance < totalAmount) throw new ValidationError("Insufficient balance");
    });

    const results = await Promise.allSettled(
      Array.from(foundReimbursements).map(async (reimbursement) => {
        const { code, company, budget, currency, amount, category } = reimbursement.toJSON();
        try {
          await Service.approveReimbursement({
            code,
            company,
            reviewer: reviewer.id,
            companyCode,
            recipient: reimbursement.User,
            amount,
            currency,
            budget,
            category,
            plan: companyPaymentPlan.configuration,
            actionLater,
          });
        } catch (error) {
          if (error.message === "Reimbursement pending approval") {
            const { message, success } = await Service.triggerReimbursementApproval({
              code,
              status: "approved",
              user: reviewer.id,
              company,
            });
            return message;
          }
          throw error;
        }
      })
    );

    const failedApproval = results.find((result) => result.status === "rejected");

    if (failedApproval) throw new ValidationError(failedApproval?.reason?.message || "An error occurred while approving request");

    return responseUtils.sendObjectResponse("Approved");
  },

  async triggerReimbursementApproval(payload) {
    const { code, status, user, company, ...rest } = payload;
    const foundReimbursement = await Service.getReimbursement({ code });
    const {
      ApprovalRequest: { code: approvalRequestCode },
    } = foundReimbursement;
    const approvalPayload = {
      company,
      code: approvalRequestCode,
      user,
      status,
      ...rest,
    };
    return ApprovalService.reviewGroupedApprovalRequest(approvalPayload);
  },

  async initiateBulkReimbursementTransactions(payload) {
    const { reimbursements } = payload;
    const response = await Promise.allSettled(
      Array.from(reimbursements).map((reimbursement) => {
        try {
          return Service.initiateReimbursementTransaction(reimbursement);
        } catch (error) {
          return error;
        }
      })
    );
    const failedInitiations = response.filter((result) => result.status === "rejected");

    if (failedInitiations) {
      const errorArray = failedInitiations.map((failedInitiation) => {
        return {
          message: failedInitiation.reason?.message,
          status: failedInitiation.status,
        };
      });
      return responseUtils.BadRequestException("Error occured initiating transactions", errorArray);
    }

    return responseUtils.sendObjectResponse("Reimbursement transactions initiated");
  },

  totalPendingCount(filters) {
    const { company, currency, budget, balance, loggedInUser } = filters;

    const criteria = HelperService.computeStatisticsCriteria({ company, currency, budget, balance });

    const approvalRequests = {
      model: ApprovalRequest,
      attributes: ["id"],
      required: false,
      as: "ReimbursementApprovalRequests",
      where: { entity: "reimbursement" },
      include: [
        {
          model: ApprovalRule,
          attributes: ["id"],
        },
        {
          model: ApprovalStage,
          attributes: ["id"],
          order: [["created_at", "ASC"]],
          include: [
            {
              model: ApproverLevel,
              attributes: ["id"],
              include: [
                {
                  model: Approver,
                  attributes: ["id"],
                  include: [
                    {
                      model: User,
                      attributes: ["id"],
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          model: Approval,
          attributes: ["id"],
          include: [
            {
              model: Approver,
              attributes: ["id"],
              include: [
                {
                  model: User,
                  attributes: ["id"],
                },
              ],
            },
          ],
        },
      ],
    };
    return Reimbursement.count({
      where: {
        ...criteria,
        status: STATUSES.PENDING,
        ...HelperService.returnRelevantRequests("ReimbursementApprovalRequests", loggedInUser),
      },
      group: ["currency"],
      include: [approvalRequests],
      distinct: true,
      col: "id",
    });
  },

  totalPendingAmount(filters) {
    const { company, currency, budget, balance, loggedInUser } = filters;

    const criteria = HelperService.computeStatisticsCriteria({ company, currency, budget, balance });

    const approvalRequests = {
      model: ApprovalRequest,
      attributes: ["id"],
      required: false,
      as: "ReimbursementApprovalRequests",
      where: { entity: "reimbursement" },
      include: [
        {
          model: ApprovalRule,
          attributes: ["id"],
        },
        {
          model: ApprovalStage,
          attributes: ["id"],
          order: [["created_at", "ASC"]],
          include: [
            {
              model: ApproverLevel,
              attributes: ["id"],
              include: [
                {
                  model: Approver,
                  attributes: ["id"],
                  include: [
                    {
                      model: User,
                      attributes: ["id"],
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          model: Approval,
          attributes: ["id"],
          include: [
            {
              model: Approver,
              attributes: ["id"],
              include: [
                {
                  model: User,
                  attributes: ["id"],
                },
              ],
            },
          ],
        },
      ],
    };

    return Reimbursement.findAll({
      where: {
        ...criteria,
        status: STATUSES.PENDING,
        ...HelperService.returnRelevantRequests("ReimbursementApprovalRequests", loggedInUser),
      },
      attributes: [[fn("SUM", col("amount")), "amount"], "currency"],
      group: ["currency"],
      include: [approvalRequests],
    });
  },
};

module.exports = Service;
async function createScheduledTransaction({
  schedule,
  currency,
  amount,
  reimbursement,
  company,
  bankAccount,
  actualRecipient,
  bujetiFee,
  cbnFee,
  narration,
  category,
  description,
  transaction,
}) {
  const { recurring, schedule: diffSchedule, startDate, expiryDate } = schedule;
  // create schedule
  const scheduledTransaction = await ScheduleService.createScheduleTransaction({
    currency,
    amount,
    budget: reimbursement?.Budget?.code,
    balance: reimbursement?.Balance?.id,
    recurring,
    schedule: diffSchedule,
    startDate,
    expiryDate,
    company,
    user: reimbursement.user,
    cronBody: {},
    payload: {
      bank_account: bankAccount.id,
      recipient: actualRecipient.recipient,
      recipientType: actualRecipient.recipient_type,
      bujeti_fee: bujetiFee,
      processor_fee: cbnFee,
      narration,
      category,
      description,
      directDebitId: reimbursement.directDebitId,
    },
  });

  await Transaction.update(
    {
      status: STATUSES.SCHEDULED,
      schedule_id: scheduledTransaction?.data?.id,
    },
    { where: { id: transaction.id } }
  );
}

async function processPayment({
  isDirectDebit,
  reimbursement,
  actionLater,
  narration,
  foundCompany,
  transaction,
  company,
  amount,
  currency,
  budget,
  receiptDetails,
  existingMandate,
}) {
  if (isDirectDebit) {
    // do direct debit
    let directDebit = null;

    if (reimbursement.directDebitId) {
      directDebit = await DirectDebit.findOne({
        where: {
          id: reimbursement.directDebitId,
        },
      });
    }

    if (!directDebit) {
      directDebit = await DirectDebit.create({
        mandate: existingMandate.id,
        company,
        amount: Math.abs(amount),
        beneficiaryAccountNumber: receiptDetails.number,
        beneficiaryBankCode: receiptDetails.bankCode,
        narration,
        status: STATUSES.PENDING,
        reference: Utils.generateRandomString(14),
      });
      await Promise.all([
        Transaction.update(
          {
            directDebitId: directDebit.id,
          },
          { where: { id: transaction.id } }
        ),
        ReimbursementRepo.updateReimbursement({
          queryParams: {
            id: reimbursement.id,
          },
          payload: {
            directDebitId: directDebit.id,
          },
        }),
      ]);
    }

    if (!actionLater) {
      directDebit.narration = narration;
      // initiate direct debit
      await Promise.all([
        BankService.directDebit(foundCompany, {
          bankCode: directDebit.beneficiaryBankCode,
          accountNumber: directDebit.beneficiaryAccountNumber,
          narration: directDebit.narration,
          amount: directDebit.amount,
          createdDirectDebit: directDebit,
        }),
        directDebit.save(),
      ]);
    }

    await Transaction.update(
      {
        status: actionLater ? STATUSES.APPROVED : STATUSES.PROCESSING,
      },
      { where: { id: transaction.id } }
    );
  } else {
    // eslint-disable-next-line no-lonely-if
    if (actionLater) {
      await Transaction.update(
        {
          status: STATUSES.APPROVED,
        },
        { where: { id: transaction.id } }
      );
    } else {
      await Promise.all([
        Transaction.update(
          {
            status: STATUSES.PROCESSING,
          },
          { where: { id: transaction.id } }
        ),
        ApprovalService.addPaymentToQueue(transaction.code),
      ]);
    }
  }
}
