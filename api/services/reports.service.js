const {
  subMonths,
  format,
  subQuarters,
  subDays,
  isSameMonth,
  isSameQuarter,
  isSameYear,
  differenceInQuarters,
  differenceInYears,
  differenceInMonths,
  startOfMonth,
  endOfMonth,
} = require("date-fns");
const { QueryTypes } = require("sequelize");
const { sequelize } = require("../models");
const {
  TransferRepo,
  BalanceLedgerRepo,
  TransactionRepo,
  ReimbursementRepo,
  ApprovalRequestRepo,
  InvoiceRepo,
} = require("../repositories/index.repo");
const { STATUSES } = require("../models/status");
const { getDateSpreadInMonths } = require("../utils");

module.exports = {
  async categoriesBreakdown(company, filters) {
    const { groupBy, from, to = new Date() } = filters;
    const rangeStart = subMonths(new Date(from), getDateSpreadInMonths(filters.from, filters.to));
    const rangeEnd = new Date(to);

    const query = `
    select 
        category,
        ${groupBy === "vendor" ? "v.name as vendor," : "null as vendor,"}
        ${groupBy === "budget" ? "b.name as budget," : "null as budget,"}
        c.name as name,
        c.code as code,
        count(Transactions.id) as transactionsCount,
        SUM(CASE WHEN DATE(paidOn) >= $1 and DATE(paidOn) < $3 THEN Transactions.amount ELSE 0 END) AS previousPeriod,
        SUM(CASE WHEN DATE(paidOn) >= $3 and DATE(paidOn) <= $2 THEN Transactions.amount ELSE 0 END) AS amount
    FROM
        Transactions
    JOIN Categories c on Transactions.category = c.id
    ${groupBy === "vendor" ? "JOIN Vendors v on Transactions.recipient = v.id and Transactions.recipient_type='vendor'" : ""}
    ${groupBy === "budget" ? "JOIN Budgets b on Transactions.budget = b.id" : ""}
    where 
        Transactions.company = ${company}
        and Date(paidOn) >= $1
        AND Date(paidOn) <= $2
        AND Transactions.status = 14
    GROUP by category${groupBy ? `, ${groupBy}` : ""}
    ORDER by paidOn`;
    const data = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      bind: [rangeStart, rangeEnd, from],
    });

    return data;
  },

  async vendorsBreakdown(company, filters) {
    const { groupBy, from, to = new Date() } = filters;
    const rangeStart = subMonths(new Date(from), getDateSpreadInMonths(filters.from, filters.to));
    const rangeEnd = new Date(to);

    const query = `
    select 
        recipient,
        v.name as name,
        v.code as code,
        ${groupBy === "category" ? "c.name as category," : "null as category,"}
        ${groupBy === "budget" ? "b.name as budget," : "null as budget,"}
        count(Transactions.id) as transactionsCount,
        SUM(CASE WHEN DATE(paidOn) >= $1 and DATE(paidOn) < $3 THEN Transactions.amount ELSE 0 END) AS previousPeriod,
        SUM(CASE WHEN DATE(paidOn) >= $3 and DATE(paidOn) < $2 THEN Transactions.amount ELSE 0 END) AS amount
    FROM
        Transactions
    JOIN Vendors v on Transactions.recipient = v.id
    ${groupBy === "category" ? "JOIN Categories c on Transactions.category = c.id" : ""}
    ${groupBy === "budget" ? "JOIN Budgets b on Transactions.budget = b.id" : ""}
    where 
    Transactions.company = ${company}
        and Date(paidOn) >= $1
        AND Date(paidOn) <= $2
        AND Transactions.status = 14
        AND Transactions.recipient_type = "vendor"
    GROUP by recipient${groupBy ? `, ${groupBy}` : ""}
    ORDER by paidOn`;
    return sequelize.query(query, {
      type: QueryTypes.SELECT,
      bind: [rangeStart, rangeEnd, from],
    });
  },

  async spendersBreakdown(company, filters) {
    const { groupBy = null, from, to = new Date() } = filters;
    const rangeStart = subMonths(new Date(from), getDateSpreadInMonths(filters.from, filters.to));
    const rangeEnd = new Date(to);

    const query = `
    select 
        concat(u.firstName, ' ', u.lastName) as name,
        ${groupBy === "category" ? "c.name as category," : "null as category,"}
        ${groupBy === "vendor" ? "v.name as vendor," : "null as vendor,"}
        ${groupBy === "budget" ? "b.name as budget," : "null as budget,"}
        u.code as code,
        count(Transactions.id) as transactionsCount,
        SUM(CASE WHEN DATE(paidOn) >= $1 and DATE(paidOn) < $3 THEN Transactions.amount ELSE 0 END) AS previousPeriod,
        SUM(CASE WHEN DATE(paidOn) >= $3 and DATE(paidOn) <= $2 THEN Transactions.amount ELSE 0 END) AS amount
    FROM
        Transactions
    JOIN Users u on Transactions.payer = u.id
    ${groupBy === "category" ? "JOIN Categories c on Transactions.category = c.id" : ""}
    ${groupBy === "vendor" ? "JOIN Vendors v on Transactions.recipient = v.id and Transactions.recipient_type='vendor'" : ""}
    ${groupBy === "budget" ? "JOIN Budgets b on Transactions.budget = b.id" : ""}
    where 
    Transactions.company = ${company}
        and Date(paidOn) >= $1
        AND Date(paidOn) <= $2
        AND Transactions.status = 14
    GROUP by payer${groupBy ? `, ${groupBy}` : ""}
    ORDER by Transactions.amount`;
    return sequelize.query(query, {
      type: QueryTypes.SELECT,
      bind: [rangeStart, rangeEnd, from],
    });
  },

  async budgetsBreakdown(company, filters) {
    const { groupBy = null, from, to = new Date() } = filters;
    const rangeStart = subMonths(new Date(from), getDateSpreadInMonths(filters.from, filters.to));
    const rangeEnd = new Date(to);

    const query = `
    select
        budget,
        ${groupBy === "category" ? "c.name as category," : "null as category,"}
        ${groupBy === "vendor" ? "v.name as vendor," : "null as vendor,"}
        b.name as name,
        b.code as code,
        count(Transactions.id) as transactionsCount,
        SUM(CASE WHEN DATE(paidOn) >= $1 and DATE(paidOn) < $3 THEN Transactions.amount ELSE 0 END) AS previousPeriod,
        SUM(CASE WHEN DATE(paidOn) >= $3 and DATE(paidOn) <= $2 THEN Transactions.amount ELSE 0 END) AS amount
    FROM
        Transactions
    JOIN Budgets b on Transactions.budget = b.id
    ${groupBy === "category" ? "JOIN Categories c on Transactions.category = c.id" : ""}
    ${groupBy === "vendor" ? "JOIN Vendors v on Transactions.recipient = v.id and Transactions.recipient_type='vendor'" : ""}
    where 
      Transactions.company = ${company}
      and Date(paidOn) >= $1
      AND Date(paidOn) <= $2
      AND Transactions.status = 14
      and Transactions.budget is not null
    GROUP by budget${groupBy ? `, ${groupBy}` : ""}
    ORDER by paidOn`;
    return sequelize.query(query, {
      type: QueryTypes.SELECT,
      bind: [rangeStart, rangeEnd, from],
    });
  },

  async cardsBreakdown(company, filters) {
    const { groupBy, from, to = new Date() } = filters;
    const rangeStart = subMonths(new Date(from), getDateSpreadInMonths(filters.from, filters.to));
    const rangeEnd = new Date(to);

    const query = `
    select 
        card,
        c.name as name,
        c.code as code,
        ${groupBy === "category" ? "c.name as category," : "null as category,"}
        ${groupBy === "vendor" ? "v.name as vendor," : "null as vendor,"}
        ${groupBy === "budget" ? "b.name as budget," : "null as budget,"}
        count(Transactions.id) as transactionsCount,
        SUM(CASE WHEN DATE(paidOn) >= $1 and DATE(paidOn) < $3 THEN Transactions.amount ELSE 0 END) AS previousPeriod,
        SUM(CASE WHEN DATE(paidOn) >= $3 and DATE(paidOn) <= $2 THEN Transactions.amount ELSE 0 END) AS amount
    FROM
        Transactions
    JOIN CreditCards c on Transactions.card = c.id
    ${groupBy === "category" ? "JOIN Categories c on Transactions.category = c.id" : ""}
    ${groupBy === "vendor" ? "JOIN Vendors v on Transactions.recipient = v.id and Transactions.recipient_type='vendor'" : ""}
    ${groupBy === "budget" ? "JOIN Budgets b on Transactions.budget = b.id" : ""}
    where 
    Transactions.company = ${company}
        and Transactions.status=14
        and Date(paidOn) >= $1
        AND Date(paidOn) <= $2
        and card is not null
    GROUP by card${groupBy ? `, ${groupBy}` : ""}
    ORDER by paidOn`;
    return sequelize.query(query, {
      type: QueryTypes.SELECT,
      bind: [rangeStart, rangeEnd, from],
    });
  },

  async teamsBreakdown(company, filters) {
    const { groupBy, from, to = new Date() } = filters;
    const rangeStart = subMonths(new Date(from), getDateSpreadInMonths(filters.from, filters.to));
    const rangeEnd = new Date(to);

    const query = `
    select
        team,
        t.name as name,
        t.code as code,
        ${groupBy === "category" ? "c.name as category," : "null as category,"}
        ${groupBy === "vendor" ? "v.name as vendor," : "null as vendor,"}
        ${groupBy === "budget" ? "b.name as budget," : "null as budget,"}
        count(Transactions.id) as transactionsCount,
        SUM(CASE WHEN DATE(paidOn) >= $1 and DATE(paidOn) < $3 THEN Transactions.amount ELSE 0 END) AS previousPeriod,
        SUM(CASE WHEN DATE(paidOn) >= $3 and DATE(paidOn) <= $2 THEN Transactions.amount ELSE 0 END) AS amount
    FROM
        Transactions
        JOIN Teams t on Transactions.team = t.id
    ${groupBy === "category" ? "JOIN Categories c on Transactions.category = c.id" : ""}
    ${groupBy === "vendor" ? "JOIN Vendors v on Transactions.recipient = v.id and Transactions.recipient_type='vendor'" : ""}
    ${groupBy === "budget" ? "JOIN Budgets b on Transactions.budget = b.id" : ""}
    where 
        Transactions.company = ${company}
        and Transactions.status=14
        and Date(paidOn) >= $1
        AND Date(paidOn) <= $2
        and team is not null
    GROUP by team${groupBy ? `, ${groupBy}` : ""}
    ORDER by paidOn`;
    return sequelize.query(query, {
      type: QueryTypes.SELECT,
      bind: [rangeStart, rangeEnd, from],
    });
  },

  async getCashSummary(company, filters = {}) {
    const { from, to = new Date() } = filters;
    const rangeStart = subMonths(new Date(from), getDateSpreadInMonths(filters.from, filters.to));
    const rangeEnd = new Date(to);

    const query = `
        SELECT
        "credit" as type,
        SUM(CASE WHEN Date(created_at) >= $1 and DATE(created_at) < $3 THEN amount ELSE 0 END) AS previousPeriod,
        SUM(CASE WHEN Date(created_at) >= $3 and DATE(created_at) <= $2 THEN amount ELSE 0 END) AS total
        FROM Transfers
        WHERE
            company=$4
            and amount > 0
            and status in (11,14)
            and created_at >= $1
            AND created_at <= $2
        UNION
        SELECT
        "debit" as type,
        SUM(CASE WHEN Date(paidOn) >= $1 and Date(paidOn) < $3 THEN amount ELSE 0 END) AS previousPeriod,
        SUM(CASE WHEN Date(paidOn) <= $3 and Date(paidOn) <= $2  THEN amount ELSE 0 END) AS total
        FROM Transactions
        WHERE
            company=$4
            and status = 14
            and Date(paidOn) >= $1
            AND Date(paidOn) <= $2
    `;
    const data = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      bind: [rangeStart, rangeEnd, from, company],
    });

    const result = {};
    data.forEach(({ type, total, previousPeriod }) => {
      result[type] = {
        amount: total,
        previousPeriod,
      };
    });
    return result;
  },

  async getOperationCounts(company, filters) {
    const { from, to } = filters;
    const criteria = {
      company,
      from,
      to,
    };
    const [transactions, reimbursements, requests, invoices] = await Promise.all([
      TransactionRepo.breakdownPerStatusesAndPeriod({ filters: criteria }),
      ReimbursementRepo.breakdownPerStatusesAndPeriod({ filters: criteria }),
      ApprovalRequestRepo.breakdownPerStatusesAndPeriod({ filters: criteria }),
      InvoiceRepo.breakdownPerStatusesAndPeriod({ filters: criteria }),
    ]);

    return {
      transactions,
      reimbursements,
      requests,
      invoices,
    };
  },

  async getOperations(company, filters) {
    const { groupBy, ...rest } = filters;
    const credit = await TransferRepo.getGroupedTransfers({
      filters: {
        company,
        amount: { gt: 0 },
        status: [STATUSES.PENDING, STATUSES.SUCCESS],
        ...rest,
      },
      attributes: ["code", "amount", "created_at"],
    });
    const debit = await BalanceLedgerRepo.getGroupedBalanceLedgers({
      filters: { ...filters, amount: { lt: 0 }, company, status: [STATUSES.PROCESSED, STATUSES.SUCCESS] },
      attributes: ["amount", "created_at"],
    });
    return {
      // eslint-disable-next-line camelcase
      credit: credit.map(({ created_at, ...remaining }) => {
        return {
          created_at: format(new Date(created_at), "MMM do"),
          ...remaining,
        };
      }),
      // eslint-disable-next-line camelcase
      debit: debit.map(({ created_at, ...remaining }) => {
        return {
          created_at: format(new Date(created_at), "MMM do"),
          ...remaining,
        };
      }),
    };
  },

  async getBalancesKeyFigures(balance, filters) {
    const to = new Date(filters.to);
    const from = new Date(filters.from || subMonths(to, getDateSpreadInMonths(filters.from, filters.to)));
    const query = `
    with OpeningBalance as (select * from BalanceLedgers where balance=$3 and Date(created_at) >= $1 and Date(created_at) <= $2 order by created_at asc limit 1),
    ClosingBalance as (select * from BalanceLedgers where balance=$3 and Date(created_at) >= $1 and Date(created_at) <= $2 order by created_at desc limit 1)
    select balanceBefore as balance, currency from OpeningBalance
    union
    select balanceAfter as balance, currency from ClosingBalance
    `;
    const [opening = { balance: 0, currency: "NGN" }, closing = { balance: 0, currency: "NGN" }] = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      bind: [from, to, balance],
    });

    return {
      openingBalance: opening.balance,
      closingBalance: closing.balance,
      revenue: 0,
      currency: opening.currency,
    };
  },

  getPeriodBreakdown({ period, from, to }) {
    switch (period) {
      case "This Month":
      case "Last Month":
        // eslint-disable-next-line no-case-declarations
        const previousDateFrom = subMonths(new Date(from), 1);
        // eslint-disable-next-line no-case-declarations
        const dateFormat0 = this.getSpreadAppropriateDateFormat(from, to);
        return {
          period: format(new Date(from), dateFormat0),
          previousPeriod: format(previousDateFrom, dateFormat0),
        };
      case "This Quarter":
      case "Last Quarter":
        // eslint-disable-next-line no-case-declarations
        const startOfPreviousQuarter = subQuarters(new Date(from), 1);
        // eslint-disable-next-line no-case-declarations
        const dateFormat1 = this.getSpreadAppropriateDateFormat(from, to);

        return {
          period: format(new Date(from), dateFormat1),
          previousPeriod: format(startOfPreviousQuarter, dateFormat1),
        };
      case "Last Semester":
        // eslint-disable-next-line no-case-declarations
        const startOfLastSemester = subQuarters(new Date(from), 2);
        // eslint-disable-next-line no-case-declarations
        const dateFormat = this.getSpreadAppropriateDateFormat(startOfLastSemester, to);
        return {
          period: format(new Date(from), dateFormat),
          previousPeriod: format(startOfLastSemester, dateFormat),
        };
      case "Custom range":
      case "Last 30 days":
        // eslint-disable-next-line no-case-declarations
        const previousDate = subDays(new Date(from), getDateSpreadInMonths(new Date(from), new Date(to)));
        // eslint-disable-next-line no-case-declarations
        const dateFormat2 = this.getSpreadAppropriateDateFormat(from, to);
        return {
          period: `${format(new Date(from), dateFormat2)} to ${format(new Date(to), dateFormat2)}`,
          previousPeriod: `${format(previousDate, dateFormat2)} to ${format(new Date(from), dateFormat2)}`,
        };
      default:
        return {
          period: "",
          previousPeriod: "",
        };
    }
  },
  getSpreadAppropriateDateFormat(start, end) {
    const startingDate = new Date(start);
    const endingDate = new Date(end);

    if (isSameMonth(startingDate, endingDate)) {
      if (startOfMonth(startingDate) && endOfMonth(endingDate)) return "MMMM";
      return "MMM do";
    }
    if (isSameQuarter(startingDate, endingDate)) {
      if (differenceInMonths(startingDate, endingDate) > 1) return "MMMM";
      return "MMM do";
    }

    if (isSameYear(startingDate, endingDate)) {
      if (Math.abs(differenceInQuarters(startingDate, endingDate)) > 0) return "QQQ";
      return "MMM do";
    }
    if (Math.abs(differenceInQuarters(startingDate, endingDate)) < 2) return "QQQ yyyy";

    return "MMM do, yy";
  },
};
