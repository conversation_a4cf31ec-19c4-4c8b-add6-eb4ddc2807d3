module.exports = {
  success(res, message, data = null, meta = null) {
    const payload = {
      message,
      status: true,
      error: false,
    };
    if (data) payload.data = data;
    if (meta) payload.meta = meta;
    res.status(200).json(payload);
  },
  failure(res, message, data = undefined) {
    const payload = {
      message,
      status: false,
      error: true,
      data,
    };
    res.status(400).json(payload);
  },
  notFound(res, message) {
    const payload = {
      message,
      status: false,
      error: true,
    };
    res.status(404).json(payload);
  },
  error(res, message, data = null) {
    const payload = {
      message,
      status: false,
      error: true,
      ...(data && { data }),
    };
    res.status(500).json(payload);
  },
  /**
   *
   * @param res
   * @param config
   * @param config.message the message
   * @param config.status the status
   * @param config.statusCode the status code
   * @param config.code the status code
   * @param config.data the data to return
   */
  json(res, config) {
    const payload = {
      message: config.message,
      status: Object.prototype.hasOwnProperty.call(config, "status") ? config.status : true,
    };
    if (config.data) payload.data = config.data;
    res.status(config.statusCode || config.code).json(payload);
  },

  plainJson(res, config) {
    const payload = config;
    res.status(config.statusCode || config.code).json(payload);
  },
};
