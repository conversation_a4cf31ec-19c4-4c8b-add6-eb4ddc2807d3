const { RoleRepo, PermissionRepo } = require("../repositories/index.repo");
const responseUtils = require("../utils/response.utils");
const { ValidationError, NotFoundError } = require("../utils/error.utils");
const Sanitizer = require("../utils/sanitizer");
const Utils = require("../utils/utils");
const { Op } = require("sequelize");
const { STATUSES } = require("../models/status");
const { User } = require("../models");

const Service = {
  /**
   * Gets a Role with it's permissions
   * @param { String } code Role code
   * @returns
   */
  async getRoleWithPermissions(code) {
    const response = await RoleRepo.getRoleWithPermissions(code);
    if (!response) throw new NotFoundError("Role");

    return responseUtils.sendObjectResponse("Role and its permissions successfully retrieved", response);
  },

  /**
   * Gets all Roles with there permissions
   * @param { object } data
   * @param { String } data.company company id
   * @returns
   */
  async getAllRoles(data) {
    const { company } = data;
    const response = await RoleRepo.getAllRoles({
      queryParams: { company, status: { [Op.notIn]: [STATUSES.DELETED] } },
    });
    return responseUtils.sendObjectResponse("Roles successfully retrieved", response);
  },

  /**
   * Gets all System Permissions
   * @returns
   */
  async getAllPermission() {
    const response = await PermissionRepo.getAllPermission({
      queryParams: {},
    });
    return responseUtils.sendObjectResponse("Permission successfully retrieved", response);
  },

  /**
   * Creates a Role for a company
   * @param { object } data
   * @param { number } data.company company id
   * @param { string } data.name role name
   * @param { string[] } data.permissions array of permissions code
   * @returns
   */
  async createRole(data) {
    const { company, permissions, name, description } = data;

    const existingRole = await RoleRepo.getRole({ queryParams: { company, name, status: { [Op.notIn]: [STATUSES.DELETED] } } });
    if (existingRole) throw new ValidationError("Role exists for this company");

    const fetchedPermissions = await PermissionRepo.getAllPermission({
      queryParams: { code: permissions },
      selectOptions: ["bit"],
    });
    if (fetchedPermissions.length === 0) throw new NotFoundError("Permissions");

    const roleValue = Utils.generateRoleValue(Sanitizer.sanitizePermissions(fetchedPermissions));

    const response = await RoleRepo.createRole({
      queryParams: { company, name, value: roleValue, description },
    });
    return responseUtils.sendObjectResponse("Role successfully created", response);
  },

  /**
   * Update the Role of a company
   * @param { object } data
   * @param { number } data.company company id
   * @param { string } data.name role name
   * @param { string } data.code role code
   * @returns
   */
  async updateRole(data) {
    const { company, name, description, code, permissions } = data;

    const updateFields = { name, description };
    if (permissions) {
      const existingRole = await RoleRepo.getRoleWithPermissions(code, company);
      if (!existingRole) throw new NotFoundError("Role");

      const exisitingPermissions = Utils.mapAnArray(existingRole.Permissions, "code");
      const updatedRolePermissions = permissions
        .filter((permission) => !exisitingPermissions.includes(permission)) // filtering out elements present in exisitingPermissions
        .concat(exisitingPermissions.filter((permission) => !permissions.includes(permission))); // Concatenate elements from exisitingPermissions not present in permissions

      const fetchedPermissions = await PermissionRepo.getAllPermission({
        queryParams: { code: updatedRolePermissions },
        selectOptions: ["bit"],
      });
      if (fetchedPermissions.length !== updatedRolePermissions.length) throw new NotFoundError("Permissions");

      updateFields.value = Utils.generateRoleValue(Sanitizer.sanitizePermissions(fetchedPermissions));
    }

    const response = await RoleRepo.updateRole({
      updateFields,
      queryParams: { company, code },
    });
    return responseUtils.sendObjectResponse("Role successfully updated", response);
  },

  /**
   * Add permissions to an existing company role
   * @param { object } data
   * @param { number } data.company company id
   * @param { string } data.permissionCode permission code
   * @param { string } data.code role code
   * @returns
   */
  async addPermissionsToRole(data) {
    const { company, permissionCode, code } = data;

    const existingRole = await RoleRepo.getRoleWithPermissions(code, company);
    if (!existingRole) throw new NotFoundError("Role");

    const { Permissions, value } = existingRole;

    const mapped = Permissions.map(({ code: elementCode }) => elementCode);
    if (mapped.includes(permissionCode)) throw new ValidationError("Permission already exist in role");

    const fetchedPermissions = await PermissionRepo.getPermission({ queryParams: { code: permissionCode }, selectOptions: ["bit"] });
    // New role value
    const roleValue = value + fetchedPermissions.bit;

    await RoleRepo.updateRole({ updateFields: { value: roleValue }, queryParams: { company, code } });
    return responseUtils.sendObjectResponse("Permissions added to Role successfully");
  },

  /**
   * Remove permissions from an existing company's role
   * @param { object } data
   * @param { number } data.company company id
   * @param { string } data.permissionCode permission code
   * @param { string } data.code role code
   * @returns
   */
  async removePermissionFromRole(data) {
    const { company, permissionCode, code } = data;

    const existingRole = await RoleRepo.getRoleWithPermissions(code, company);
    if (!existingRole) throw new NotFoundError("Role");

    const { Permissions, value } = existingRole;

    const mapped = Permissions.map(({ code: elementCode }) => elementCode);
    if (!mapped.includes(permissionCode)) throw new ValidationError("This Role does not have this permissions");

    const fetchedPermissions = await PermissionRepo.getPermission({ queryParams: { code: permissionCode }, selectOptions: ["bit"] });

    const newRoleValue = value - fetchedPermissions.bit;
    const response = await RoleRepo.updateRole({ updateFields: { value: newRoleValue }, queryParams: { company, code } });
    return responseUtils.sendObjectResponse("Permissions removed from Role successfully", response);
  },

  /**
   * Update the [status] of an existing company role to [DELETED]
   * @param { object } data
   * @param { number } data.company company id
   * @param { string } data.permissionCode permission code
   * @param { string } data.code role code
   * @returns
   */
  async removeRole(data) {
    const { company, code } = data;

    const existingRole = await RoleRepo.getRole({ queryParams: { code, company, status: { [Op.notIn]: [STATUSES.DELETED] } } });
    if (!existingRole) throw new NotFoundError("Role");

    await this.roleIsStillAssigned({ role: existingRole.id, company }, true);

    await RoleRepo.deleteRole({ company, code });
    return responseUtils.sendObjectResponse("Role removed successfully");
  },

  async getRole(criteria) {
    const role = await RoleRepo.getRole({ queryParams: { ...criteria, status: { [Op.notIn]: [STATUSES.DELETED] } } });
    if (!role) throw new NotFoundError("Role");
    return role;
  },

  /**
   * Checks if a role is still assigned and throws an error
   * @param { object } criteria
   * @param { number } criteria.role role id
   * @param { number } criteria.company company id
   * @param { boolean } throwError
   * @returns
   */
  async roleIsStillAssigned(criteria, throwError = false) {
    const findUserWithRole = await User.findOne({ where: { company: criteria.company, role_id: criteria.role }, attributes: ["id"], raw: true });

    if (findUserWithRole && throwError) {
      throw new ValidationError("Please unassign this role first");
    }

    return !!findUserWithRole;
  },
};

module.exports = Service;
