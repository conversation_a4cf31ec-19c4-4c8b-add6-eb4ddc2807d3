const mindee = require("mindee");
const mindeeClient = new mindee.Client({ apiKey: process.env.MINDEE_API_KEY });

module.exports = {
  /**
   * Scan an asset
   * @param {Object} asset
   * @param {String} asset.url
   * @param {String} asset.code
   * @param {number} company
   * @returns
   */
  async scan(asset, company) {
    try {
      const inputSource = mindeeClient.docFromUrl(asset.url);
      const apiResponse = (await mindeeClient.parse(mindee.product.FinancialDocumentV1, inputSource)) || {};

      const { document: { inference: { prediction: data = {} } = {} } = {} } = apiResponse;

      if (!data) return null;

      const response = {};
      Object.entries(data).forEach((k, v) => {
        if (Array.isArray(v)) response[k] = v;
        else response[k] = v.value;
      });
      const {
        customerAddress,
        customerName,
        date: paymentDate,
        documentType,
        dueDate,
        invoiceNumber,
        lineItems,
        locale: { currency } = {},
        referenceNumbers,
        subcategory,
        supplierAddress,
        supplierName: vendorName,
        supplierPaymentDetails: paymentDetails,
        supplierPhoneNumber: vendorPhoneNumber,
        taxes,
        totalAmount: total,
        totalNet: amount,
        totalTax: tax,
      } = response;

      const scannedData = {
        customerAddress,
        customerName,
        paymentDate,
        documentType,
        dueDate,
        invoiceNumber,
        lineItems,
        currency,
        referenceNumbers,
        subcategory,
        supplierAddress,
        vendorName,
        paymentDetails,
        vendorPhoneNumber,
        taxes: taxes.map(({ value }) => value),
        total: Number(total) * 100,
        amount: Number(amount) * 100,
        tax: Number(tax) * 100,
        original: response,
      };
      // ThirdPartyLog
      return scannedData;
    } catch (error) {
      return {
        error: String(error),
      };
    }
    return {};
  },
};
