/* eslint-disable no-unused-expressions */
// eslint-disable-next-line import/no-extraneous-dependencies
const { Op } = require("sequelize");
const CronParser = require("cron-parser");
const dateFns = require("date-fns");
const responseUtils = require("../utils/response.utils");
const Utils = require("../utils/utils");
const { CronIntegrator } = require("../integrations");
const { STATUSES } = require("../models/status");
const {
  ScheduledTransactionRepo,
  ScheduleRepo,
  BudgetRepo,
  ScheduledBudgetRepo,
  ScheduledInvoiceRepo,
  ScheduledBillRepo,
} = require("../repositories/index.repo");
const { ValidationError, NotFoundError } = require("../utils/error.utils");
const CronService = require("./cron.service");
const SettingsService = require("./settings");
const { Schedule } = require("../models");
const { SCHEDULED_ENTITY } = require("../mocks/constants.mock");

const Service = {
  convertMonthToNumber(month) {
    const monthMap = {
      january: 1,
      february: 2,
      march: 3,
      april: 4,
      may: 5,
      june: 6,
      july: 7,
      august: 8,
      september: 9,
      october: 10,
      november: 11,
      december: 12,
    };
    return monthMap[month.toLowerCase()];
  },

  convertDayOfWeekToNumber(dayOfWeek) {
    const dayOfWeekMap = {
      sunday: 0,
      monday: 1,
      tuesday: 2,
      wednesday: 3,
      thursday: 4,
      friday: 5,
      saturday: 6,
    };

    return dayOfWeekMap[dayOfWeek.toLowerCase()];
  },

  getDayOfWeekString(dayOfWeek) {
    switch (dayOfWeek) {
      case 0:
        return "sunday";
      case 1:
        return "monday";
      case 2:
        return "tuesday";
      case 3:
        return "wednesday";
      case 4:
        return "thursday";
      case 5:
        return "friday";
      case 6:
        return "saturday";
      default:
        throw new ValidationError("Invalid day of the week");
    }
  },

  getMonthString(dayOfWeek) {
    switch (dayOfWeek) {
      case 0:
        return "sunday";
      case 1:
        return "monday";
      case 2:
        return "tuesday";
      case 3:
        return "wednesday";
      case 4:
        return "thursday";
      case 5:
        return "friday";
      case 6:
        return "saturday";
      default:
        throw new ValidationError("Invalid day of the week");
    }
  },

  convertNumberToMonth(number) {
    const monthMap = {
      1: "January",
      2: "February",
      3: "March",
      4: "April",
      5: "May",
      6: "June",
      7: "July",
      8: "August",
      9: "September",
      10: "October",
      11: "November",
      12: "December",
    };

    const month = monthMap[number];
    if (!month) throw new ValidationError("Invalid month number");

    return month.toLowerCase();
  },

  convertDateToSchedule({ date, timestamp, addDayOfWeek = true, addDayOfMonth = true }) {
    const [hours, minutes, seconds] = timestamp.split(":");
    const [year, month, dayOfMonth] = date.split("-");

    let dayOfWeekString;
    if (addDayOfWeek) {
      dayOfWeekString = new Date(date).getDay();
      dayOfWeekString = Service.getDayOfWeekString(parseInt(dayOfWeekString, 10));
    }

    return {
      minutes: minutes && minutes,
      hours: hours && hours,
      dayOfMonth: addDayOfMonth && parseInt(dayOfMonth, 10),
      month: month && Service.convertNumberToMonth(parseInt(month, 10)),
      dayOfWeek: addDayOfWeek && dayOfWeekString,
    };
  },

  splitDateAndTimeStamp(newDate) {
    const incomingDateObj = new Date(newDate);

    const [updatedDate, updatedTimestamp] = incomingDateObj.toISOString().split("T");

    return {
      date: updatedDate,
      timestamp: updatedTimestamp,
    };
  },

  addMinutes(incomingDate, minutesToAdd, timezone) {
    const { date, timestamp } = incomingDate;

    const allDate = dateFns.addMinutes(Utils.stringToDate(this.combineDateTime(date, timestamp)), minutesToAdd).toISOString();

    const updatedDate = allDate.split("T")[0];
    const updatedTimestamp = allDate.split("T")[1];

    return {
      date: updatedDate,
      timestamp: updatedTimestamp,
    };
  },

  generateCronExpression({ minutes, hours, dayOfMonth, month, dayOfWeek }) {
    const cronExpression = [];

    // Minutes
    minutes ? cronExpression.push(minutes) : cronExpression.push("*");
    // Hours
    hours ? cronExpression.push(hours) : cronExpression.push("*");
    // Day of Month
    dayOfMonth ? cronExpression.push(dayOfMonth) : cronExpression.push("*");

    // Month
    if (month !== undefined) {
      const monthNumber = Service.convertMonthToNumber(month);
      cronExpression.push(monthNumber);
    } else cronExpression.push("*");

    // Day of Week
    if (dayOfWeek !== undefined) {
      const dayOfWeekNumber = Service.convertDayOfWeekToNumber(dayOfWeek);
      cronExpression.push(dayOfWeekNumber);
    } else cronExpression.push("*");

    return cronExpression.join(" ");
  },

  generateCronExpressionAndNextTriggerDate(readableInput) {
    const expression = Service.generateCronExpression(readableInput);

    // Validate and parse the cron expression
    const interval = CronParser.parseExpression(expression);

    // Get the next trigger date
    const nextDate = interval.next();
    const prevDate = interval.prev();

    return {
      expression,
      prevDate: prevDate.toString(),
      nextDate: nextDate.toString(),
      hasNext: interval.hasNext(),
    };
  },

  async validateAndInitiateScheduleRecord({ recurring = true, schedule, startDate, expiryDate, cronExpressionArg = null, status = null }) {
    if (!recurring && !startDate) throw new ValidationError("Start Date has to be added for a schedule");
    if (!recurring && expiryDate) throw new ValidationError("Expiry Date not needed for a schedule");

    // checks if startDate is not today or not
    const isStartDateToday = startDate && Utils.isDateToday(startDate.date);
    const startDateTime = startDate && Service.combineDateTime(startDate.date, startDate.timestamp);
    // Check if startDate is ahead of current time and before the expiry Date
    if (startDate && !Utils.firstDateIsAfterSecondDate(startDateTime)) throw new ValidationError("Start Date has to be ahead of current date");
    if (startDate && expiryDate && !Utils.firstDateIsBeforeSecondDate(startDateTime, Service.combineDateTime(expiryDate.date, expiryDate.timestamp)))
      throw new ValidationError("Start Date has to be ahead of Expiry Date");

    // generate cron expression
    const cronExpression = cronExpressionArg || Service.generateCronExpressionAndNextTriggerDate(schedule);
    // create schedule on schedule table
    const createdSchedule = await ScheduleRepo.createSchedule({
      queryParams: {
        cron_expression: String(cronExpression.expression),
        status: status || STATUSES.ACTIVE,
        nextExecutionDate: Service.generateNextDateFromCronExpression(cronExpression.expression),
      },
    });

    // Here the daily Cron is Started if the schedule has a start date
    if (startDate && !isStartDateToday) {
      const dailySchedule = await ScheduleRepo.getSchedule({ queryParams: { code: SettingsService.get("SCHEDULES").daily } });
      if (dailySchedule && dailySchedule.status !== STATUSES.ACTIVE)
        await Service.updateScheduleStatus({ code: SettingsService.get("SCHEDULES").daily, status: "active" });
    }

    return responseUtils.sendObjectResponse("Schedule Initialized", {
      cronExpression,
      createdSchedule,
      isStartDateToday: isStartDateToday || !!cronExpressionArg,
      startDateTime,
    });
  },

  async generateAndRecordScheduleWithCronJob({
    createdSchedule,
    isStartDateToday,
    cronBody,
    cronExpression,
    startDate,
    expiryDate,
    title,
    customQuarterly,
  }) {
    const generateSchedule = Service.createScheduleFromCronExpression(cronExpression.expression, customQuarterly);

    const createdCron = await CronIntegrator.createAJob({
      generate: false,
      title,
      url: title,
      method: CronIntegrator.METHODS.POST,
      body: cronBody,
      schedule: {
        ...generateSchedule,
        timezone: "Africa/Lagos",
        enabled: true,
        expiresAt: expiryDate ? Service.formatDateString({ date: expiryDate.date, timestamp: expiryDate.timestamp }) : 0,
      },
    });

    // Update Schedule Table with CronId
    if (createdCron?.data.jobId)
      await ScheduleRepo.updateSchedule({ queryParams: { id: createdSchedule.id }, updateFields: { cron_id: createdCron.data.jobId } });

    // silently update the scheduled budget to active if this is for a scheduled budget
    ScheduledBudgetRepo.updateScheduledBudget({
      queryParams: {
        schedule_id: createdSchedule.id,
      },
      updateFields: {
        status: STATUSES.ACTIVE,
      },
    });

    return responseUtils.sendObjectResponse("Get Expression Schedule", {
      createdCron: createdCron?.data,
      cronExpression,
      createdSchedule,
      generateSchedule,
    });
  },

  async createScheduledRecord({ recurring = true, schedule, startDate, expiryDate, title = "schedule/" }) {
    const {
      data: { cronExpression, createdSchedule, isStartDateToday },
    } = await Service.validateAndInitiateScheduleRecord({ recurring, schedule, startDate, expiryDate });

    const {
      data: { createdCron, generateSchedule },
    } = await Service.generateAndRecordScheduleWithCronJob({
      createdSchedule,
      isStartDateToday,
      cronBody: {},
      cronExpression,
      startDate,
      expiryDate,
      title: `${title}${createdSchedule.code}`,
    });

    return responseUtils.sendObjectResponse("Get Expression Schedule", {
      createdCron,
      cronExpression,
      createdSchedule,
      generateSchedule,
    });
  },

  async createScheduleTransaction({
    cronBody,
    recurring = true,
    schedule,
    startDate,
    expiryDate,
    currency = "NGN",
    amount,
    budget,
    balance,
    company,
    user,
    payload,
  }) {
    const foundBudget = budget && (await BudgetRepo.getBudget({ code: budget }));

    let cronExpressionArg;

    if (typeof schedule === "string") {
      const combinedStartDate = startDate && Service.combineDateTime(startDate.date, startDate.timestamp);
      cronExpressionArg = Service.generateCronFromDuration(schedule, true, combinedStartDate);
    }

    const {
      data: { cronExpression, createdSchedule, isStartDateToday },
    } = await Service.validateAndInitiateScheduleRecord({ recurring, schedule, startDate, expiryDate, cronExpressionArg });

    // create schedule on transaction Schedule
    const createdTxSchedule = await ScheduledTransactionRepo.createScheduledTransaction({
      queryParams: {
        amount,
        budget: foundBudget && foundBudget.id,
        balance,
        company,
        currency,
        user,
        ...(typeof schedule === "string" && { frequency: schedule }),
        recipient: payload ? payload.recipient : user.id,
        recipient_type: payload ? payload.recipientType : "user",
        type: recurring ? "recurring" : "schedule",
        schedule_id: createdSchedule.id,
        start_date: startDate && Service.combineDateTime(startDate.date, startDate.timestamp),
        expiry_date: expiryDate && Service.combineDateTime(expiryDate.date, expiryDate.timestamp),
        ...payload,
      },
    });

    await Service.generateAndRecordScheduleWithCronJob({
      createdSchedule,
      isStartDateToday,
      cronBody,
      cronExpression,
      startDate,
      expiryDate,
      title: `payment/${createdTxSchedule.code}`,
      customQuarterly: true,
    });
    return responseUtils.sendObjectResponse("Get Expression Schedule", createdTxSchedule);
  },

  async createExpiryBudgetCron({
    createdSchedule,
    cronBody = {},
    isStartDateToday,
    cronExpression,
    startDate,
    expiryDate,
    title = `budgets/expire`,
  } = {}) {
    let queryDate = new Date();
    if (expiryDate) {
      queryDate = Service.combineDateTime(expiryDate.date, expiryDate.timestamp);
    }

    if (createdSchedule) {
      const scheduledBudget = await ScheduledBudgetRepo.scheduledBudgetExists({
        queryParams: {
          expiry_date: {
            [Op.between]: [dateFns.startOfDay(new Date(queryDate)), dateFns.endOfDay(new Date(queryDate))],
          },
          status: STATUSES.ACTIVE,
          type: "schedule",
        },
      });

      // this indicates that we have a cron that will run on the passed expiry date
      if (scheduledBudget) {
        ScheduledBudgetRepo.updateScheduledBudget({
          queryParams: {
            schedule_id: createdSchedule.id,
          },
          updateFields: {
            status: STATUSES.ACTIVE,
          },
        });
        return;
      }
    } else {
      // this operation runs when we are creating a cron (with a TTL of 10 mins) as part of our kill-switch process
      startDate = Service.splitDateAndTimeStamp(Utils.getUTCPlusOneTime(1));
      const { minutes, hours, dayOfMonth, month, dayOfWeek } = Service.convertDateToSchedule(
        Service.splitDateAndTimeStamp(Utils.getUTCPlusOneTime(5))
      );
      const schedule = { minutes, hours, dayOfMonth, month, dayOfWeek };
      const { data } = await Service.validateAndInitiateScheduleRecord({ recurring: false, schedule, startDate });
      createdSchedule = data.createdSchedule;
      cronExpression = data.cronExpression;
      isStartDateToday = data.isStartDateToday;
      expiryDate = Service.splitDateAndTimeStamp(Utils.getUTCPlusOneTime(15));
    }

    return Service.generateAndRecordScheduleWithCronJob({
      createdSchedule,
      isStartDateToday,
      startDate,
      cronBody,
      cronExpression,
      title,
      expiryDate,
    });
  },

  async createRecurringBudgetCron({
    createdSchedule,
    cronExpression,
    cronBody = {},
    isStartDateToday,
    title = `budgets/recurring`,
    startDate,
    expiryDate,
  } = {}) {
    if (createdSchedule) {
      const scheduledBudget = await ScheduledBudgetRepo.scheduledBudgetExists({
        queryParams: {
          status: STATUSES.ACTIVE,
          type: "recurring",
          expiry_date: {
            [Op.or]: [
              null,
              {
                [Op.gt]: new Date(),
              },
            ],
          },
        },
        include: {
          model: Schedule,
          where: {
            cron_expression: cronExpression.expression,
            status: STATUSES.ACTIVE,
          },
        },
      });

      // this indicates that we have a cron that will run on the passed expiry date
      if (scheduledBudget) {
        ScheduledBudgetRepo.updateScheduledBudget({
          queryParams: {
            schedule_id: createdSchedule.id,
          },
          updateFields: {
            status: STATUSES.ACTIVE,
          },
        });
        return;
      }
    } else {
      // this operation runs when we are creating a cron (with a TTL of 10 mins) as part of our kill-switch process
      startDate = Service.splitDateAndTimeStamp(Utils.getUTCPlusOneTime(1));
      const { minutes, hours, dayOfMonth, month, dayOfWeek } = Service.convertDateToSchedule(
        Service.splitDateAndTimeStamp(Utils.getUTCPlusOneTime(5))
      );
      const schedule = { minutes, hours, dayOfMonth, month, dayOfWeek };
      const { data } = await Service.validateAndInitiateScheduleRecord({ recurring: true, schedule, startDate, cronExpressionArg: cronExpression });
      createdSchedule = data.createdSchedule;
      cronExpression = data.cronExpression;
      isStartDateToday = data.isStartDateToday;
      expiryDate = Service.splitDateAndTimeStamp(Utils.getUTCPlusOneTime(15));
    }

    return Service.generateAndRecordScheduleWithCronJob({
      createdSchedule,
      isStartDateToday,
      startDate,
      cronBody,
      cronExpression,
      title,
      expiryDate,
    });
  },

  formatDateString({ date, timestamp }) {
    // timestamp(hh:mm:ss) is in 24hrs and it stops in seconds
    // date(yyyy-mm:-dd)
    const chosenDate = `${date}${timestamp}`;
    const formattedString = chosenDate.replace(/[-:T.]/g, "").slice(0, 14);

    // returns this format of date YYYYMMDDhhmmss
    return formattedString;
  },

  combineDateTime(date, time) {
    const currentDateTime = new Date();

    const [hours, minutes] = time.split(":");

    const [year, month, day] = date.split("-").map(Number);

    currentDateTime.setMinutes(minutes);
    currentDateTime.setHours(hours);
    currentDateTime.setMonth(month - 1);
    currentDateTime.setFullYear(year, month - 1, day);
    const combinedDateTime = currentDateTime.toISOString();
    return combinedDateTime;
  },

  createScheduleFromCronExpression(cronExpression, customQuarterly) {
    const cronFields = cronExpression.split(" ");
    const [minutes, hours, mdays, months, wdays] = cronFields.map((field, index) => Service.parseCronField(field, index, customQuarterly));

    return {
      hours,
      mdays,
      minutes,
      months,
      wdays,
    };
  },

  parseCronField(field, index, customQuarterly) {
    if (field === "*") return [-1];
    if (field.includes(",")) return field.split(",").map((value) => parseInt(value, 10));
    if (field.startsWith("*/")) {
      const step = parseInt(field.slice(2), 10);
      const max = Service.getMaxValueForField(field, index);
      const values = [];

      // Handle sequential quarterly schedules
      if (index === 3 && customQuarterly) {
        const date = new Date();
        const day = date.getDate();
        values.push(...Utils.generateQuarterlySchedules(day));
      } else {
        for (let index = 1; index <= max; index += step) {
          values.push(index);
        }
      }
      return values;
    }
    return [parseInt(field, 10)];
  },

  getMaxValueForField(field, index) {
    const fieldType = field.charAt(0);
    switch (fieldType) {
      case "*":
        return Service.getDefaultMaxValue(`${index}`);
      case "/":
        return parseInt(field.slice(2), 10);
      default:
        return parseInt(field, 10);
    }
  },

  getDefaultMaxValue(fieldType) {
    switch (fieldType) {
      case "0":
        return 59; // minutes field
      case "1":
        return 23; // hours field
      case "2":
        return 31; // month days field
      case "3":
        return 12; // months field
      case "4":
        return 6; // week days field (0-6, Sunday to Saturday)
      default:
        return 0; // default value
    }
  },

  async updateScheduleStatus(data) {
    const { code: scheduleCode, status: incomingStatus } = data;

    const existingSchedule = await ScheduleRepo.getSchedule({ queryParams: { code: scheduleCode } });
    if (!existingSchedule) throw new NotFoundError("Schedule");

    const status = STATUSES[incomingStatus.toUpperCase()];
    if (existingSchedule.status === STATUSES.SUCCESS) throw new ValidationError("Schedule is completed");
    if (status === STATUSES.PAUSE && existingSchedule.status === STATUSES.PAUSE) throw new ValidationError("Schedule already paused");
    if (status === STATUSES.ACTIVE && existingSchedule.status === STATUSES.ACTIVE) throw new ValidationError("Schedule already active");

    await Promise.all([
      ScheduleRepo.updateSchedule({
        queryParams: { id: existingSchedule.id },
        updateFields: { status: status === STATUSES.ACTIVE ? STATUSES.ACTIVE : STATUSES.INACTIVE },
      }),
      CronService.updateCron(existingSchedule.cron_id, { activate: status === STATUSES.ACTIVE }),
    ]);
    return responseUtils.sendObjectResponse("Schedule updated successfully");
  },

  generateCronFromDuration(duration, generateDay, startDate) {
    let day;
    let month;
    let hour = 0;
    let minute = 0;

    if (generateDay) {
      const date = startDate ? new Date(startDate) : new Date();

      day = duration === "weekly" ? date.getDay() : date.getDate();
      month = date.getMonth();

      if (startDate) {
        hour = date.getHours();
        minute = date.getMinutes();
      }
    } else {
      day = 1;
      month = 1;
    }

    const durationCronExpressionMap = {
      hourly: `${minute} * * * *`,
      weekly: `${minute} ${hour} * * ${day}`,
      monthly: `${minute} ${hour} ${day} * *`,
      quarterly: `${minute} ${hour} ${day} */3 *`,
      yearly: `${minute} ${hour} ${day} ${month} *`,
    };

    return { expression: durationCronExpressionMap[duration] };
  },

  generateNextDateFromCronExpression(expression) {
    return CronParser.parseExpression(expression).next();
  },

  async createScheduledEntity({
    cronBody,
    recurring = true,
    schedule,
    startDate,
    expiryDate,
    currency = "NGN",
    company,
    user,
    description,
    amount,
    vat,
    vatAmount,
    discount,
    discountType,
    customer,
    title,
    balance,
    meta,
    terms,
    status = null,
    entity = SCHEDULED_ENTITY.INVOICE,
    ...rest
  }) {
    const entityToCreate = {
      bill: {
        route: "bills",
        repo: { service: ScheduledBillRepo, handler: "createScheduledBill" },
      },
      invoice: {
        route: "invoices",
        repo: { service: ScheduledInvoiceRepo, handler: "createScheduledInvoice" },
      },
    };

    const foundEntity = entityToCreate[entity];

    if (!foundEntity) return foundEntity;

    const { route, repo } = foundEntity;
    let cronExpressionArg;

    if (typeof schedule === "string") {
      const combinedStartDate = startDate && Service.combineDateTime(startDate.date, startDate.timestamp);
      cronExpressionArg = Service.generateCronFromDuration(schedule, true, combinedStartDate);
    }

    const {
      data: { cronExpression, createdSchedule, isStartDateToday },
    } = await Service.validateAndInitiateScheduleRecord({
      recurring,
      schedule,
      startDate,
      expiryDate,
      cronExpressionArg,
      ...(status && { status }),
    });

    // Create Scheduled Entity
    const scheduledEntityPayload = {
      ...rest,
      company,
      user,
      currency,
      description,
      amount,
      vat,
      vatAmount,
      discount,
      discountType,
      customer,
      balance,
      terms,
      title,
      startDate: startDate && Service.combineDateTime(startDate.date, startDate.timestamp),
      expiryDate: expiryDate && Service.combineDateTime(expiryDate.date, expiryDate.timestamp),
      schedule: createdSchedule.id,
      type: recurring ? "recurring" : "scheduled",
      meta,
      ...(status && { status }),
    };

    const createdScheduledEntity = await repo.service[repo.handler]({ data: scheduledEntityPayload });

    // Generate and create cronjob
    if (createdScheduledEntity.status === STATUSES.ACTIVE)
      await Service.generateAndRecordScheduleWithCronJob({
        createdSchedule,
        isStartDateToday,
        cronBody,
        startDate,
        expiryDate,
        cronExpression,
        title: `${route}/${createdScheduledEntity.code}`,
        customQuarterly: true,
      });
    return responseUtils.sendObjectResponse("Get Expression Schedule", createdScheduledEntity);
  },
};

module.exports = Service;
