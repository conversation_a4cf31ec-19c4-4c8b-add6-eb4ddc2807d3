/* eslint-disable camelcase */
const { ScheduledBillProductRepo, ScheduledBillRepo, ScheduleRepo, CategoryRepo, VendorRepo } = require("../repositories/index.repo");
const { SCHEDULED_ENTITY, METHODS } = require("../mocks/constants.mock");
const ScheduleService = require("./schedule.service");
const ProductService = require("./product.service");
const ResponseUtils = require("../utils/response.utils");
const { NotFoundError, ValidationError } = require("../utils/error.utils");
const { STATUSES } = require("../models/status");
const Sanitizer = require("../utils/sanitizer");
const AuditLogsService = require("./auditLogs");
const Utils = require("../utils/utils");
const CronService = require("./cron.service");
const HelperService = require("./helper.service");
const PaymentService = require("./paymentservice");

const Service = {
  async createScheduledBill(payload) {
    const { billPayload, scheduledBill, products, installments = null } = payload;
    const { company } = billPayload;
    const { recurring, schedule, startDate, expiryDate } = scheduledBill;
    const meta = [];

    const productData = products.map((product) => {
      const { name, currency, unitPrice, quantity, discount = 0, discount_type = null } = product;
      meta.push({ quantity, discount, discount_type, currency, unitPrice, company });
      return { name, company, currency, price: unitPrice };
    });

    const { data: createdScheduledBill } = await ScheduleService.createScheduledEntity({
      ...billPayload,
      schedule,
      recurring,
      startDate,
      currency: products[0].currency || "NGN",
      expiryDate,
      entity: SCHEDULED_ENTITY.BILL,
    });

    const productsResponse = await ProductService.findOrCreate(productData);

    const scheduledBillProducts = productsResponse.map((createdProduct, index) => {
      return { ...meta[index], product: createdProduct.id, scheduledBill: createdScheduledBill.id };
    });

    const callsToMake = [];

    callsToMake.push(ScheduledBillProductRepo.bulkCreateScheduledBillProduct({ data: scheduledBillProducts }));

    const shouldCreateInstallments = installments && Object.keys(installments).length > 0 && installments.payments.length > 0;

    if (shouldCreateInstallments) {
      const { type, payments } = installments;

      const billInstallmentPayload = payments.map((payment) => {
        return {
          ...payment,
          type,
          scheduledBill: createdScheduledBill.id,
          currency: products[0].currency || "NGN",
          company,
          dueDate: payment.due_date,
        };
      });

      callsToMake.push(ScheduledBillRepo.createBulkScheduledBillInstallment({ payload: billInstallmentPayload }));
    }

    await Promise.all([...callsToMake]);

    return ResponseUtils.sendObjectResponse("Scheduled bill created successfully", createdScheduledBill);
  },

  async listScheduledBills(filter) {
    return ScheduledBillRepo.listScheduledBills({ queryParams: filter });
  },

  async getScheduledBill(filter) {
    const foundScheduledBill = await ScheduledBillRepo.getScheduledBill({ queryParams: filter, includeAssets: true });
    if (!foundScheduledBill) throw new NotFoundError("Scheduled Bill");
    return foundScheduledBill;
  },

  async updateScheduleBillProducts({ scheduledBill, company, products }) {
    return Promise.all(
      products.map(async (product) => {
        const {
          code = null,
          discount,
          discount_type: discountType = null,
          name = null,
          quantity,
          unitPrice,
          created_at,
          updated_at,
          ...rest
        } = product;
        if (code)
          return ScheduledBillProductRepo.updateScheduledBillProduct({
            filter: { code },
            payload: { ...rest, ...(discountType && { discountType }) },
          });
        const createdProduct = await ProductService.findOrCreate({ ...rest, name, company, price: unitPrice });

        return ScheduledBillProductRepo.createScheduleBillProduct({
          data: {
            ...rest,
            scheduledBill,
            product: createdProduct.id,
            discount,
            discount_type: discountType,
            company,
            unitPrice,
            quantity,
          },
        });
      })
    );
  },

  calculateSingleProductPrice({ unitPrice, quantity, discount, discountType }) {
    let discountAmount = 0;
    const amountWithoutDiscount = parseInt(unitPrice, 10) * parseInt(quantity, 10);
    if (discount) discountAmount = discountType === "amount" ? parseInt(discount, 10) : amountWithoutDiscount * (parseInt(discount, 10) / 100);
    return amountWithoutDiscount - discountAmount;
  },

  async calculateScheduledBillAmount(scheduledBill) {
    const foundScheduledBill = await ScheduledBillRepo.getScheduledBill({ queryParams: { id: scheduledBill } });
    if (!foundScheduledBill) throw new NotFoundError("Scheduled Bill");

    const { vat, discount, discount_type: discountType, ScheduledBillProducts = [] } = foundScheduledBill;

    let totalBillAmount = ScheduledBillProducts.reduce((prev, scheduledBillProduct) => {
      const { unitPrice, quantity, discount: productDiscount, discount_type: productDiscountType } = scheduledBillProduct;
      const productPrice = Service.calculateSingleProductPrice({ unitPrice, quantity, discount: productDiscount, discountType: productDiscountType });
      return prev + productPrice;
    }, 0);

    if (discount) totalBillAmount -= discountType === "amount" ? parseInt(discount, 10) : totalBillAmount * (discount / 100);

    if (vat) totalBillAmount += totalBillAmount * (vat / 100);
    return totalBillAmount;
  },

  async createScheduledBillWithoutProducts(payload) {
    const { id, code, scheduledBill, status, created_at, updated_at, ...rest } = payload;
    const { recurring, schedule, startDate, expiryDate } = scheduledBill;

    const { data: createdScheduledBill } = await ScheduleService.createScheduledEntity({
      ...rest,
      reference: Utils.generateRandomString(17),
      schedule,
      recurring,
      startDate,
      expiryDate,
      entity: SCHEDULED_ENTITY.BILL,
    });

    return ResponseUtils.sendObjectResponse("Scheduled Bill created successfully", createdScheduledBill);
  },

  // eslint-disable-next-line consistent-return
  async updateScheduledBill({ queryParams, payload, user }) {
    const { scheduledBill, products, method, budget, directDebit, uploads, ...rest } = payload;

    const foundScheduledBill = await ScheduledBillRepo.getScheduledBill({
      queryParams,
    });
    if (!foundScheduledBill) throw new NotFoundError("Scheduled Bill");

    if (foundScheduledBill.status === STATUSES.COMPLETED) throw new ValidationError("Scheduled bill already completed");
    if (foundScheduledBill.status === STATUSES.INACTIVE) throw new ValidationError("Scheduled bill is inactive");

    const status = rest?.status && Utils.getStatusValues(rest.status);
    let scheduleStatus;
    if (scheduledBill) scheduleStatus = STATUSES.INACTIVE; // Deactivate previous schedule if new one is sent

    const isPreviouslyDraft = foundScheduledBill.status === STATUSES.DRAFT;

    if (products) {
      await Service.updateScheduleBillProducts({ scheduledBill: foundScheduledBill.id, company: foundScheduledBill.company, products });
      rest.amount = await Service.calculateScheduledBillAmount(foundScheduledBill.id);
    }

    if (Array.isArray(uploads) && uploads.length) {
      await PaymentService.callService("updateAssetWithEntity", uploads, { entityId: foundScheduledBill.id, entityType: "scheduledBill" });
    }

    if (rest.category) {
      const foundCategory = await CategoryRepo.getCategory({ queryParams: { code: rest.category } });
      if (!foundCategory) throw new NotFoundError("Category");
      rest.category = foundCategory.id;
    }

    if (rest.vendor) {
      const foundVendor = await VendorRepo.getVendor({
        queryParams: {
          code: rest.vendor,
        },
      });

      if (!foundVendor) throw new NotFoundError("Vendor");
      rest.vendor = foundVendor.id;
    }

    if (method) {
      rest.paymentMethodId = await HelperService.deduceAccount({
        method,
        budget,
        company: foundScheduledBill.company,
        balance: rest.balance,
        directDebit,
      });
      rest.paymentMethod = METHODS[method.toUpperCase()];
    }

    const { Schedule } = foundScheduledBill;

    await Promise.all([
      scheduleStatus &&
        ScheduleRepo.updateSchedule({
          queryParams: { id: Schedule.id },
          updateFields: {
            status: scheduleStatus === STATUSES.ACTIVE ? STATUSES.ACTIVE : STATUSES.INACTIVE,
          },
        }),
      ScheduledBillRepo.updateScheduledBill({
        queryParams,
        payload: { ...rest, ...(status && { status }) },
      }),
    ]);

    const jobExists = !!Schedule.cron_id;

    if (scheduleStatus && jobExists) {
      await CronService.updateCron(Schedule.cron_id, {
        activate: scheduleStatus === STATUSES.ACTIVE,
      });
    }

    if (isPreviouslyDraft && status === STATUSES.ACTIVE && !scheduledBill) {
      // Activate and create cron
      const { startDate, expiryDate } = foundScheduledBill;
      const isStartDateToday = startDate && Utils.isDateToday(startDate);
      const { cron_expression: expression } = Schedule;

      await ScheduleService.generateAndRecordScheduleWithCronJob({
        createdSchedule: Schedule.toJSON(),
        isStartDateToday,
        cronBody: null,
        startDate,
        expiryDate,
        cronExpression: { expression },
        title: `bills/${foundScheduledBill.code}`,
        customQuarterly: true,
      });
    }

    if (scheduledBill) {
      const existingScheduledBill = await ScheduledBillRepo.getScheduledBill({ queryParams });
      const {
        company,
        user: billOwner,
        currency,
        reason,
        amount,
        vat,
        discount,
        discount_type: discountType,
        title,
        expiryDate,
        startDate,
        type,
        ScheduledBillProducts,
        ScheduledBillInstallments,
        customer: scheduledBillCustomer,
        status: previousBillStatus,
        balance,
        ...remainingData
      } = Sanitizer.jsonify(existingScheduledBill);
      //   Create new scheduled bill
      const { data } = await Service.createScheduledBillWithoutProducts({
        company,
        user: billOwner,
        currency,
        reason,
        amount,
        vat,
        discount,
        discount_type: discountType,
        title,
        expiryDate,
        startDate,
        type,
        customer: scheduledBillCustomer,
        scheduledBill,
        status: previousBillStatus,
        balance,
        ...remainingData,
      });
      if (Array.isArray(uploads) && uploads.length) {
        await PaymentService.callService("updateAssetWithEntity", uploads, { entityId: data.id, entityType: "scheduledBill" });
      }
      const createdScheduledBillProducts = ScheduledBillProducts.map((product) => {
        const { id, code, Product, created_at, updated_at, ...productData } = Sanitizer.jsonify(product);
        return { ...productData, status: STATUSES.ACTIVE, scheduledBill: data.id };
      });
      const newScheduledBillInstallment =
        ScheduledBillInstallments &&
        ScheduledBillInstallments.length &&
        ScheduledBillInstallments.map((installment) => {
          const { id, code, created_at, updated_at, scheduledBill: ignoredScheduledBill, ...installmentData } = Sanitizer.jsonify(installment);
          return { ...installmentData, scheduledBill: data.id };
        });

      await Promise.all([
        // DEACTIVATE PREVIOUS SCHEDULED BILL
        ScheduledBillRepo.updateScheduledBill({
          queryParams: { id: existingScheduledBill.id },
          payload: { status: STATUSES.INACTIVE },
        }),
        // DEACTIVATE ITS PRODUCT
        ScheduledBillProductRepo.updateScheduledBillProduct({
          filter: { scheduledBill: existingScheduledBill.id },
          payload: { status: STATUSES.INACTIVE },
        }),
        // DEACTIVATE ITS INSTALLMENT
        ScheduledBillRepo.updateScheduledBillInstallment({
          filter: { scheduledBill: existingScheduledBill.id },
          payload: { status: STATUSES.INACTIVE },
        }),
        // CREATE NEW PRODUCTS
        ScheduledBillProductRepo.bulkCreateScheduledBillProduct({ data: createdScheduledBillProducts }),
        // CREATE NEW INSTALLMENT
        newScheduledBillInstallment &&
          newScheduledBillInstallment.length &&
          ScheduledBillRepo.createBulkScheduledBillInstallment({ payload: newScheduledBillInstallment }),
      ]);
    }
    AuditLogsService.createLog({
      event: "updated-a-scheduled-bill",
      user: user.id,
      table_type: "ScheduledBill",
      table_id: foundScheduledBill.id,
      initial_state: Sanitizer.sanitizeScheduledBill(foundScheduledBill),
      delta: { ...payload },
    });
    return ResponseUtils.sendObjectResponse("Scheduled bill updated successfully");
  },

  async deleteScheduledBill({ filter }) {
    const foundScheduledBill = await ScheduledBillRepo.getScheduledBill({ queryParams: filter });
    if (!foundScheduledBill) throw new NotFoundError("Scheduled Bill");

    await Promise.all([
      // DELETE SCHEDULED BILL
      ScheduledBillRepo.updateScheduledBill({
        queryParams: { id: foundScheduledBill.id },
        payload: { status: STATUSES.DELETED },
      }),

      ScheduleRepo.updateSchedule({
        queryParams: { id: foundScheduledBill.schedule },
        updateFields: { status: STATUSES.INACTIVE },
      }),

      // DELETE SCHEDULED BILL PRODUCTS
      ScheduledBillProductRepo.updateScheduledBillProduct({
        filter: { scheduledBill: foundScheduledBill.id, status: STATUSES.ACTIVE },
        payload: { status: STATUSES.DELETED },
      }),

      // DELETE SCHEDULED BILL INSTALLMENT
      ScheduledBillRepo.updateScheduledBillInstallment({
        filter: { scheduledBill: foundScheduledBill.id, status: STATUSES.ACTIVE },
        payload: { status: STATUSES.DELETED },
      }),
    ]);

    const { Schedule } = foundScheduledBill;
    // DEACTIVATE CRON
    const jobExists = !!Schedule.cron_id;

    if (jobExists) {
      await CronService.updateCron(Schedule.cron_id, {
        activate: false,
      });
    }
    return ResponseUtils.sendObjectResponse("Scheduled Bill deleted successfully");
  },
};

module.exports = Service;
