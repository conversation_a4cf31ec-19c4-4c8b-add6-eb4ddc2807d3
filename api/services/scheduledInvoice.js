/* eslint-disable no-unused-expressions */
// eslint-disable-next-line import/no-extraneous-dependencies
const { ScheduledInvoiceRepo, ScheduledInvoiceProductRepo, ScheduleRepo, CustomerRepo, BalanceRepo } = require("../repositories/index.repo");
const { NotFoundError, ValidationError } = require("../utils/error.utils");
const ScheduleService = require("./schedule.service");
const ProductService = require("./product.service");
const ResponseUtils = require("../utils/response.utils");
const { STATUSES } = require("../models/status");
const Utils = require("../utils/utils");
const CronService = require("./cron.service");
const AuditLogsService = require("./auditLogs");
const Sanitizer = require("../utils/sanitizer");
const { SCHEDULED_ENTITY } = require("../mocks/constants.mock");

const Service = {
  async createScheduledInvoice(payload) {
    const { invoicePayload, scheduleInvoice, products, installments } = payload;
    const { company } = invoicePayload;
    const { recurring, schedule, startDate, expiryDate } = scheduleInvoice;
    const meta = [];
    const productData = products.map((product) => {
      const { name, description, currency, unitPrice, quantity, discount = 0, discount_type: discountType = null } = product;
      meta.push({ quantity, discount, discountType, currency, unitPrice });
      return { name, company, currency, price: unitPrice, description };
    });

    const { data: createdScheduledInvoice } = await ScheduleService.createScheduledEntity({
      ...invoicePayload,
      schedule,
      recurring,
      startDate,
      currency: products[0].currency || "NGN",
      expiryDate,
      entity: SCHEDULED_ENTITY.INVOICE,
    });

    const productsResponse = await ProductService.createProducts(productData);
    const scheduledInvoiceProducts = productsResponse.map((createdProduct, index) => {
      return { ...meta[index], product: createdProduct.id, scheduledInvoice: createdScheduledInvoice.id };
    });

    const callsToMake = [];
    callsToMake.push(ScheduledInvoiceProductRepo.bulkCreateScheduledInvoiceProduct({ data: scheduledInvoiceProducts }));

    if (installments) {
      const { type, payments } = installments;
      const invoiceInstallmentPayload = payments.map((payment) => {
        return { ...payment, type, scheduledInvoice: createdScheduledInvoice.id };
      });
      callsToMake.push(ScheduledInvoiceRepo.createBulkScheduledInvoiceInstallment({ payload: invoiceInstallmentPayload }));
    }
    await Promise.all([...callsToMake]);
    return ResponseUtils.sendObjectResponse("Scheduled Invoice created successfully", createdScheduledInvoice);
  },

  async listScheduledInvoices(filter) {
    return ScheduledInvoiceRepo.listScheduledInvoices({ queryParams: filter });
  },

  async getScheduledInvoice(filter) {
    const foundScheduledInvoice = await ScheduledInvoiceRepo.getScheduledInvoice({ queryParams: filter });
    if (!foundScheduledInvoice) throw new NotFoundError("Scheduled Invoice");
    return foundScheduledInvoice;
  },

  async updateScheduleInvoiceProducts({ scheduledInvoice, company, products }) {
    return Promise.all(
      products.map(async (product) => {
        const { code = null, discount_type: discountType = null, name = null, ...rest } = product;
        if (code)
          return ScheduledInvoiceProductRepo.updateScehduledInvoiceProduct({
            filter: { code },
            payload: { ...rest, ...(discountType && { discountType }) },
          });
        const createdProduct = await ProductService.createProduct({ data: { ...rest, name, company, price: rest.unitPrice } });
        return ScheduledInvoiceProductRepo.createScheduleInvoiceProduct({
          data: { ...rest, scheduledInvoice, product: createdProduct.id, discountType },
        });
      })
    );
  },

  calculateSingleProductPrice({ unitPrice, quantity, discount, discountType }) {
    let discountAmount = 0;
    const amountWithoutDiscount = parseInt(unitPrice, 10) * parseInt(quantity, 10);
    if (discount) discountAmount = discountType === "amount" ? parseInt(discount, 10) : amountWithoutDiscount * (parseInt(discount, 10) / 100);
    return amountWithoutDiscount - discountAmount;
  },

  async calculateScheduledInvoiceAmount(scheduledInvoice) {
    const foundScheduledInvoice = await ScheduledInvoiceRepo.getScheduledInvoice({ queryParams: { id: scheduledInvoice } });
    if (!foundScheduledInvoice) throw new NotFoundError("Scheduled Invoice");

    const { vat, discount, discountType, ScheduledInvoiceProducts = [] } = foundScheduledInvoice;

    let totalInvoiceAmount = ScheduledInvoiceProducts.reduce((prev, scheduledInvoiceProduct) => {
      const { unitPrice, quantity, discount: productDiscount, discountType: productDiscountType } = scheduledInvoiceProduct;
      const productPrice = Service.calculateSingleProductPrice({ unitPrice, quantity, discount: productDiscount, discountType: productDiscountType });
      return prev + productPrice;
    }, 0);

    if (discount) totalInvoiceAmount -= discountType === "amount" ? parseInt(discount, 10) : totalInvoiceAmount * (parseInt(discount, 10) / 100);

    if (vat) totalInvoiceAmount += totalInvoiceAmount * (parseInt(vat, 10) / 100);
    return totalInvoiceAmount;
  },

  async createScheduledInvoiceWithoutProducts(payload) {
    const { scheduleInvoice, ...rest } = payload;
    const { recurring, schedule, startDate, expiryDate } = scheduleInvoice;

    const { data: createdScheduledInvoice } = await ScheduleService.createScheduledEntity({
      ...rest,
      schedule,
      recurring,
      startDate,
      expiryDate,
      entity: SCHEDULED_ENTITY.INVOICE,
    });

    return ResponseUtils.sendObjectResponse("Scheduled Invoice created successfully", createdScheduledInvoice);
  },

  // eslint-disable-next-line consistent-return
  async updateScheduledInvoice({ queryParams, payload, user }) {
    const { scheduleInvoice, settlementAccount, customer, products, ...rest } = payload;

    const foundScheduledInvoice = await ScheduledInvoiceRepo.getScheduledInvoice({
      queryParams,
    });
    if (!foundScheduledInvoice) throw new NotFoundError("Scheduled Invoice");

    if (foundScheduledInvoice.status === STATUSES.COMPLETED) throw new ValidationError("Scheduled invoice already completed");

    const status = rest?.status && Utils.getStatusValues(rest.status);
    let scheduleStatus;
    if (scheduleInvoice) scheduleStatus = STATUSES.INACTIVE; // Deactivate previous schedule if new one is sent

    const isPreviouslyDraft = foundScheduledInvoice.status === STATUSES.DRAFT;

    if (products) {
      await Service.updateScheduleInvoiceProducts({ scheduledInvoice: foundScheduledInvoice.id, company: foundScheduledInvoice.company, products });
      rest.amount = await Service.calculateScheduledInvoiceAmount(foundScheduledInvoice.id);
    }

    if (settlementAccount) {
      const foundBalance = await BalanceRepo.getBalance({
        filter: { code: settlementAccount, company: foundScheduledInvoice.company, status: STATUSES.ACTIVE },
      });
      if (!foundBalance) throw new NotFoundError("Balance");
      rest.balance = foundBalance.id;
    }

    if (customer) {
      const foundCustomer = await CustomerRepo.getCustomer({
        filter: { company: queryParams.company, code: customer },
      });
      if (!foundCustomer) throw new NotFoundError("Customer");
      rest.customer = foundCustomer.id;
    }

    const { Schedule } = foundScheduledInvoice;

    await Promise.all([
      scheduleStatus &&
        ScheduleRepo.updateSchedule({
          queryParams: { id: Schedule.id },
          updateFields: {
            status: scheduleStatus === STATUSES.ACTIVE ? STATUSES.ACTIVE : STATUSES.INACTIVE,
          },
        }),
      ScheduledInvoiceRepo.updateScheduledInvoice({
        queryParams,
        payload: { ...rest, ...(status && { status }) },
      }),
    ]);

    const jobExists = !!Schedule.cron_id;

    if (scheduleStatus && jobExists) {
      await CronService.updateCron(Schedule.cron_id, {
        activate: scheduleStatus === STATUSES.ACTIVE,
      });
    }

    if (isPreviouslyDraft && status === STATUSES.ACTIVE && !scheduleInvoice) {
      // Activate and create cron
      const { startDate, expiryDate } = foundScheduledInvoice;
      const isStartDateToday = startDate && Utils.isDateToday(startDate);
      const { cron_expression: expression } = Schedule;

      await ScheduleService.generateAndRecordScheduleWithCronJob({
        createdSchedule: Schedule.toJSON(),
        isStartDateToday,
        cronBody: null,
        startDate,
        expiryDate,
        cronExpression: { expression },
        title: `invoices/${foundScheduledInvoice.code}`,
        customQuarterly: true,
      });
    }

    if (scheduleInvoice) {
      const existingScheduledInvoice = await ScheduledInvoiceRepo.getScheduledInvoice({ queryParams });
      const {
        company,
        user: invoiceOwner,
        currency,
        description,
        amount,
        vat,
        discount,
        discountType,
        title,
        expiryDate,
        startDate,
        type,
        ScheduledInvoiceProducts,
        ScheduledInvoiceInstallments,
        customer: scheduledInvoiceCustomer,
        status: previousInvoiceStatus,
        balance,
        ...remainingData
      } = existingScheduledInvoice;
      //   Create new scheduled invoice
      const { data } = await Service.createScheduledInvoiceWithoutProducts({
        company,
        user: invoiceOwner,
        currency,
        description,
        amount,
        vat,
        discount,
        discountType,
        title,
        expiryDate,
        startDate,
        type,
        customer: scheduledInvoiceCustomer,
        scheduleInvoice,
        status: previousInvoiceStatus,
        balance,
        ...remainingData,
      });
      const createdScheduledInvoiceProducts = ScheduledInvoiceProducts.map((product) => {
        const { id, code, Product, ...productData } = product.toJSON();
        return { ...productData, status: STATUSES.ACTIVE, scheduledInvoice: data.id };
      });
      const newScheduledInvoiceInstallment =
        ScheduledInvoiceInstallments &&
        ScheduledInvoiceInstallments.length &&
        ScheduledInvoiceInstallments.map((installment) => {
          const { scheduledInvoice, ...installmentData } = installment.toJSON();
          return { ...installmentData, scheduledInvoice: data.id };
        });

      await Promise.all([
        // DEACTIVATE PREVIOUS SCHEDULED INVOICE
        ScheduledInvoiceRepo.updateScheduledInvoice({
          queryParams: { id: existingScheduledInvoice.id },
          payload: { status: STATUSES.INACTIVE },
        }),
        // DEACTIVATE ITS PRODUCT
        ScheduledInvoiceProductRepo.updateScehduledInvoiceProduct({
          filter: { scheduledInvoice: existingScheduledInvoice.id },
          payload: { status: STATUSES.INACTIVE },
        }),
        // DEACTIVE ITS INSTALLMENT
        ScheduledInvoiceRepo.updateScheduledInvoiceInstallment({
          filter: { scheduledInvoice: existingScheduledInvoice.id },
          payload: { status: STATUSES.INACTIVE },
        }),
        // CREATE NEW PRODUCTS
        ScheduledInvoiceProductRepo.bulkCreateScheduledInvoiceProduct({ data: createdScheduledInvoiceProducts }),
        // CREATE NEW INSTALLMENT
        newScheduledInvoiceInstallment &&
          newScheduledInvoiceInstallment.length &&
          ScheduledInvoiceRepo.createBulkScheduledInvoiceInstallment({ payload: newScheduledInvoiceInstallment }),
      ]);
    }
    AuditLogsService.createLog({
      event: "updated-a-scheduled-invoice",
      user: user.id,
      table_type: "ScheduledInvoice",
      table_id: foundScheduledInvoice.id,
      initial_state: Sanitizer.sanitizeScheduledInvoice(foundScheduledInvoice),
      delta: { ...payload },
    });
    return ResponseUtils.sendObjectResponse("Scheduled invoice updated successfully");
  },

  async deleteScheduledInvoice({ filter }) {
    const foundScheduledInvoice = await ScheduledInvoiceRepo.getScheduledInvoice({ queryParams: filter });
    if (!foundScheduledInvoice) throw new NotFoundError("Scheduled Invoice");

    await Promise.all([
      // DELETE SCHEDULED INVOICE
      ScheduledInvoiceRepo.updateScheduledInvoice({
        queryParams: { id: foundScheduledInvoice.id },
        payload: { status: STATUSES.DELETED },
      }),

      ScheduleRepo.updateSchedule({
        queryParams: { id: foundScheduledInvoice.schedule },
        updateFields: { status: STATUSES.INACTIVE },
      }),

      // DELETE SCHEDULED INVOICE PRODUCTS
      ScheduledInvoiceProductRepo.updateScehduledInvoiceProduct({
        filter: { scheduledInvoice: foundScheduledInvoice.id, status: STATUSES.ACTIVE },
        payload: { status: STATUSES.DELETED },
      }),

      // DELETE SCHEDULED INVOICE INSTALLMENT
      ScheduledInvoiceRepo.updateScheduledInvoiceInstallment({
        filter: { scheduledInvoice: foundScheduledInvoice.id, status: STATUSES.ACTIVE },
        payload: { status: STATUSES.DELETED },
      }),
    ]);

    const { Schedule } = foundScheduledInvoice;
    // DEACTIVATE CRON
    const jobExists = !!Schedule.cron_id;

    if (jobExists) {
      await CronService.updateCron(Schedule.cron_id, {
        activate: false,
      });
    }
    return ResponseUtils.sendObjectResponse("Scheduled Invoice deleted successfully");
  },
};

module.exports = Service;
