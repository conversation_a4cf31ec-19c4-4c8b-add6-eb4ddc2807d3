const cron = require("node-cron");
const { Setting: Settings, PolicyType, Role, TransactionType } = require("../models");
const Sanitize = require("../utils/sanitizer");
const { STATUSES } = require("../models/status");

const settings = {
  CONFIG: {
    fund_reference_expiry: 604800,
  },
  budgetRevampDeploymentDate: "2023-01-25",
  customer_experience_email: "<EMAIL>",
  support_email: "<EMAIL>",
  notification_email: "<EMAIL>",
  invalidBudgetStatus: [STATUSES.DELETED],
  FBO_Account: "***************-anc_acc",
  BUJETI_ANCHOR_CUSTOMER_ID: "***************-anc_bus_cst",
  DISPOSABLE_ACCOUNT_PROVIDER: "providus",
  LEVEL_1_ONBOARDING_MAX_AMOUNT: ********,
  LEVEL_2_ONBOARDING_MAX_AMOUNT: *********,
  MAX_OTP_TRIAL: 5,
  MAX_CARD_FUNDING_ATTEMPT: 3,
  MAX_CARD_CREATION_ATTEMPT: 3,
  feature_flags: {
    subaccount: true,
  },
  TIER_PERMISSIONS: {
    invoice: {
      level_1: 10,
      level_2: 50,
    },
    bills: {
      level_1: 3,
      level_2: 10,
    },
    cards: {
      level_1: 1,
      level_2: 5,
    },
    spend: {
      level_1: 0,
      level_2: 5,
    },
    approvals: {
      level_1: 1,
      level_2: 1,
    },
  },
  providers: {
    multiCurrencyProviders: {
      anchor: {
        USD: {
          accountId: "**************-anc_acc",
        },
      },
    },
    payment: {
      defaultProvider: "anchor",
      cmp_xyk9NCX3ivTmHZhOC: "anchor",
      1: "anchor",
      cardPaymentProvider: "paystack",
      usd: {
        defaultProvider: "graph",
      },
    },
    virtual_accounts: {
      usd: {
        defaultProvider: "graph",
      },
    },
    card_issuing: {
      usd: {
        defaultIssuer: "sudo",
        issuing_fee: 200, // Value is in cents... This is === $2
        "funding_fee(%)": 0.02,
        funding_fee: 200,
      },
      ngn: {
        defaultIssuer: "sudo",
        issuing_fee: 10000,
        "funding_fee(%)": 0.005,
        funding_fee: 5000,
        maintenance_fee: 10000,
      },
      defaultProvider: "sudo",
      sudoFundingSource: {
        default: "648311f7e311a0f3b2e4b552",
        account: "6483140ce311a0f3b2e4b707",
        gateway: "6483146de311a0f3b2e4b713",
      },
      issuing_fees: 10000,
      "funding_fee(%)": 0.005,
      funding_fee: 10000,
      maintenance_fee: 10000,
    },
    connect: {
      defaultProvider: "mono",
    },
    ledger: {
      defaultProvider: "blnk",
    },
    accountVerificationProviders: [
      {
        provider: "mono",
        isDefault: false,
      },
      {
        provider: "anchor",
        isDefault: true,
      },
    ],
  },
  speciallyPricedCompanies: {
    default: 1,
    cmp_T238e2bV5kFUCvNXN: 0.55,
  },
  APPROVERS_THRESHOLD: {
    all: -1,
    any: 1,
  },
  cancelled_transaction_expiry: 3600,
  pricing_config: {
    transactionFee: 1,
    NGN: {
      maximumFee: 100000,
      minimumAmount: 10000,
      directDebitFee: 30000,
    },
    USD: {
      maximumFee: 100,
      minimumAmount: 1000,
    },
    models: {
      start: {
        beneficiaries: {
          max_users: 5,
        },
        bank_account: {
          allowed: false,
        },
        budgets: {
          max_subaccounts: 5,
          max_budgets: 5,
        },
        approval_levels: 1,
        role_management: "system",
        cards: {
          max_number: 5,
          free_cards: 0,
          NGN: {
            issuing_fees: 20000,
            "funding_fee(%)": 0.014,
            funding_fee: 5000,
            maintenance_fee: 10000,
          },
          USD: {
            issuing_fees: 300,
            "funding_fee(%)": 0.02,
            funding_fees: 500,
            maintenance_fee: 250,
          },
        },
        virtual_accounts: {
          NGN: {
            allowed: false,
            max_number: 0,
            deposit_fee: 0,
          },
          USD: {
            allowed: false,
            max_number: 0,
            deposit_fee: 0,
          },
        },
        monthly_user_fee: {
          NGN: 0,
          USD: 0,
        },
        annual_user_fee: {
          NGN: 0,
          USD: 0,
        },
        connectedAccounts: {
          canSync: false,
          syncWindow: 1440, // 24 hours,
          canRequestRealTimeData: false,
          maxRealTimeDataPerDay: 0,
        },
      },
      growth: {
        beneficiaries: {
          max_users: 50,
        },
        bank_account: {
          allowed: true,
        },
        budgets: {
          max_subaccounts: 25,
          max_budgets: 25,
        },
        approval_levels: 3,
        role_management: "custom",
        cards: {
          max_number: 50,
          free_cards: 1,
          NGN: {
            issuing_fees: 10000,
            funding_fees: 5000,
            maintenance_fee: 10000,
          },
          USD: {
            issuing_fees: 200,
            "funding_fee(%)": 0.015,
            funding_fees: 500,
            maintenance_fee: 250,
          },
        },
        virtual_accounts: {
          NGN: {
            allowed: true,
            max_number: 1,
            deposit_fee: 0,
          },
          USD: {
            allowed: false,
            max_number: 0,
            deposit_fee: 0,
          },
        },
        monthly_user_fee: {
          NGN: 500000,
          USD: 0,
        },
        annual_user_fee: {
          NGN: 5000000,
          USD: 0,
        },
        connectedAccounts: {
          canSync: true,
          syncWindow: 120, // 2 hours,
          canRequestRealTimeData: true,
          maxRealTimeDataPerDay: 24, // 1 per hour
        },
      },
      scale: {
        beneficiaries: {
          max_users: 1000000,
        },
        bank_account: {
          allowed: true,
        },
        budgets: {
          max_subaccounts: 1000000,
          max_budgets: 1000000,
        },
        approval_levels: 10,
        role_management: "custom",
        cards: {
          max_number: 1000000,
          free_cards: 5,
          NGN: {
            issuing_fees: 10000,
            funding_fees: 5000,
            maintenance_fee: 0,
          },
          USD: {
            issuing_fees: 200,
            "funding_fee(%)": 0.015,
            funding_fees: 500,
            maintenance_fee: 0,
          },
        },
        virtual_accounts: {
          NGN: {
            allowed: true,
            max_number: 1000,
            deposit_fee: 0,
          },
          USD: {
            allowed: true,
            max_number: 1000,
            deposit_fee: 0,
          },
        },
        monthly_user_fee: {
          NGN: 800000,
          USD: 0,
        },
        annual_user_fee: {
          NGN: 8000000,
          USD: 0,
        },
        connectedAccounts: {
          canSync: true,
          syncWindow: 30, //30 minutes
          canRequestRealTimeData: true,
          maxRealTimeDataPerDay: 48, // 1 per 30 mins
        },
      },
    },
  },
  BULK_TRANSACTIONS: {
    delay: 3000,
  },
  APPROVAL_EXEMPTION: ["cmp_T238e2bV5kFUCvNXN"],
  COMPANIES_WITH_SEPERATE_CREDIT_AND_DEBIT_STATEMENT: ["cmp_T238e2bV5kFUCvNXN"],
  GROUPED_BATCH_TRANSACTION_STATEMENT_COMPANIES: ["cmp_7uqvYQjA3S2BOoP0u", "cmp_fJi5UK5ZsEK4JCWHe11"], // For companies that want their Batch transaction to show as one transaction on statement
  MAXIMUM_TRANSACTION_RETRY_ATTEMPT: 2,
  cardIssuingAccounts: {
    flutter: {
      account: {
        bankName: "Wema Bank",
        accountName: "Bujeti Inc FLWFLW",
        number: "**********",
        bankCode: "035",
      },
      default_genders: {
        gender: "M",
        title: "MR",
      },
    },
    bridgecard: {
      account: {
        bankName: "Wema Bank",
        accountName: "Bujeti Inc FLWFLW",
        number: "**********",
        bankCode: "035",
      },
      default_genders: {
        gender: "M",
        title: "MR",
      },
    },
    sudo: {
      account: {
        bankName: "Wema Bank",
        accountName: "Bujeti Inc FLWFLW",
        number: "**********",
        bankCode: "035",
      },
      accountId: "6480fee2ee8cad920145f19e",
      // accountId: {
      //   ngn: "6480fee2ee8cad920145f19e",
      //   usd: "648061a6ee8cad920144f2ef",
      // },
      sandboxAccount: {
        bankCode: "999240",
        accountNumber: "**********",
      },
      default_genders: {
        gender: "M",
        title: "MR",
      },
      address: {
        address: "8 The Green Ste R",
        city: "Dover County",
        state: "Delaware",
        country: "United States",
        postal_code: 19901,
      },
    },
  },
  default_genders: {
    gender: "M",
    title: "MR",
  },
  SCHEDULES: {
    daily: "sch_k1nT7v3pWfKCAgqKO",
  },
  sudoFundingSources: {
    ngn: { default: "648311f7e311a0f3b2e4b552" },
    usd: { default: "6483146de311a0f3b2e4b713" },
  },
  sudoGatewayFundingSources: {
    ngn: { default: "682723bd45eb036680f7a921" },
    usd: { default: "682723bd45eb036680f7a921" },
  },
  requeryJobId: 4327419,
  invoiceInstallmentReminderJobId: 5134219,
  billInstallmentReminderJobId: Infinity,
  subscriptionCollectionAccounts: {
    NGN: {
      bankName: "Wema Bank",
      accountName: "Bujeti Inc FLWFLW",
      number: "**********",
      bankCode: "035",
    },
  },
  collectionAccounts: {
    cardRequestCharge: "**************-anc_acc",
    subscriptionCharge: "**************-anc_acc",
  },
  annualSubscriptionDiscount: {
    default: 16,
  },
  excludedFromFundingFees: { excluded: ["**********"] },
  googleapis: {
    token: {},
    credentials: {},
  },
  directDebitFees: {
    ngn: 0,
  },
};

// const flutter_account1 = {
//   bankName: "Wema Bank",
//   accountName: "Bujeti Inc FLWFLW",
//   number: "**********",
// };
let pusher;

const mapper = (payload, key, value, response) => {
  if (!Array.isArray(payload)) return Sanitize.jsonify(payload);
  payload.forEach((item) => {
    const data = Sanitize.jsonify(item);
    response[data[key]] = data[value];
    return response;
  });
};

const loadSettings = async () => {
  const foundSettings = await Settings.findAll();
  foundSettings.forEach((setting) => {
    try {
      settings[setting.key] = JSON.parse(setting.value);
    } catch (error) {
      settings[setting.key] = setting.value;
    }
  });
  return settings;
};

const loadPolicyTypes = async () => {
  const policyTypesSettings = {};
  const response = await PolicyType.findAll();
  mapper(response, "name", "id", policyTypesSettings);
  settings["POLICY_TYPES"] = policyTypesSettings;
  return policyTypesSettings;
};

const loadTransactionTypes = async () => {
  const transactionTypes = {};
  const response = await TransactionType.findAll();
  mapper(response, "feature_name", "id", transactionTypes);
  settings.TRANSACTION_TYPES = transactionTypes;
  return transactionTypes;
};

const loadRoles = async () => {
  let systemRoles = {};
  const response = await Role.findAll({
    where: { company: null },
  });
  mapper(response, "name", "id", systemRoles);
  settings["SYSTEM_ROLES"] = systemRoles;
};

const Service = {
  async init() {
    await Promise.all([loadSettings(), loadPolicyTypes(), loadRoles(), loadTransactionTypes()]);
  },
  get(key) {
    return settings[key];
  },
  async dbGet() {
    await Settings.findAll().forEach((setting) => {
      settings[setting.key] = JSON.parse(JSON.stringify(setting.value));
    });
    return settings;
  },

  timeChecker(timer) {
    let newTime;
    if (timer >= 1000 && timer <= 59000) newTime = `*/${Math.floor(timer / 1000)} * * * * *`;
    if (timer >= 60000 && timer <= 3540000) newTime = `*/${Math.floor(timer / 60000)} * * * *`;
    if (timer >= 3600000 && timer <= 82800000) newTime = `* */${Math.floor(timer / 3600000)} * * *`;
    return newTime;
  },

  pusher,
  settings,
};

// This cron helps to make the code run like every 5 minutes
const refreshCron = () => {
  if (settings.refreshKeys) {
    for (let key in settings.refreshKeys) {
      const interval = Service.timeChecker(Number(key.every));
      cron.schedule(
        interval,
        async () => {
          const variable = await Settings.findOne({
            where: { key },
          });
          settings[key] = JSON.parse(JSON.stringify(variable.value));
        },
        {
          scheduled: true,
          timezone: "Africa/Algiers",
        }
      );
    }
  } /** run by 11:59pm */ else
    cron.schedule("59 23 * * *", Service.init, {
      scheduled: true,
      timezone: "Africa/Algiers",
    });
};

// refreshCron()

module.exports = Service;
