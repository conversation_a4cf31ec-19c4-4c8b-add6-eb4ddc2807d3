const { fn, col, Op } = require("sequelize");
const { Transfer, Settlement } = require("../models");
const { STATUSES } = require("../models/status");
const { ValidationError, NotFoundError } = require("../utils/error.utils");
const {
  SettlementAccountRepo,
  SettlementRepo,
  BalanceRepo,
  PendingSettlementRepo,
  InvoiceRepo,
  TransferRepo,
} = require("../repositories/index.repo");
const SettingsService = require("./settings");
const { CARD_ISSUER } = require("../models/cardissuer");
const OTPService = require("./otp");
const CompanyService = require("./company");
const NotificationService = require("./notification");
const ChargeService = require("./charge");
const Sanitizer = require("../utils/sanitizer");
const { getStatusValues, isProd } = require("../utils/utils");
const { log, Log } = require("../utils/logger.utils");
const Providers = require("./providers");
const ResponseUtils = require("../utils/response.utils");
const QueueService = require("./queue.service");
const RedisService = require("./redis");

const Service = {
  async getCollectionSummary(company) {
    const [[totalCollection] = [], [totalWithdrawal] = []] = await Promise.all([
      Transfer.findAll({
        where: { company, status: STATUSES.SUCCESS, invoice: { [Op.ne]: null } },
        attributes: [[fn("sum", col("amount")), "amount"]],
      }),
      Settlement.findAll({
        where: { company, status: { [Op.in]: [STATUSES.PENDING, STATUSES.PROCESSED] } },
        attribute: [[fn("sum", col("amount")), "amount"]],
      }),
    ]);
    return { totalCollection: (totalCollection && totalCollection.amount) || 0, totalWithdrawal: (totalWithdrawal && totalWithdrawal.amount) || 0 };
  },

  async requestSettlement(payload) {
    const { amount, currency = "NGN", company, user: initiatedBy, balance } = payload;

    const collectionBalance = await BalanceRepo.getAvailableBalance({ company, currency, code: balance });
    if (collectionBalance < amount) throw new ValidationError("Insufficient balance left");

    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[company] || payment.defaultProvider;

    const account = await SettlementAccountRepo.getSingleSettlementAccount({ filter: { company, code: payload.account } });

    const data = {
      initiatedBy,
      company,
      amount,
      currency,
      settlementAccount: account.id,
      provider: CARD_ISSUER[providerToUse],
    };

    const withdrawal = await SettlementRepo.createSettlement({ data });

    const companyWithAdmins = await CompanyService.getCompanyWithAdmins({ id: company });
    const [firstAdmin] = companyWithAdmins.Users;

    const { code, hash } = await OTPService.createOTPRedisCode({ code: withdrawal.code });
    NotificationService.notifyUser(
      firstAdmin,
      "otp-verification-login",
      { firstName: firstAdmin.firstName, code: code.split("") },
      {
        from: "Bujeti Security Team",
        subject: `Your OTP to sign in is ${code}`,
      }
    );
    return { hash, name: firstAdmin.id === initiatedBy ? `you` : `${firstAdmin.firstName} ${firstAdmin.lastName}` };
  },

  async verifyOTP({ code, hash }) {
    const verifyOTP = await OTPService.verifyRedis({ hash, code }, "withdrawal");
    if (!verifyOTP.success) throw new ValidationError("Invalid OTP sent");

    const withdrawal = await SettlementRepo.getSettlemenmt({ filter: { code: verifyOTP.data.entityCode } });

    if (!withdrawal) throw new NotFoundError("Withdrawal Request");

    const {
      code: withdrawalCode,
      company,
      amount,
      currency,
      SettlementAccount: {
        BankAccount: { bankCode, bankName, number },
      },
    } = withdrawal;

    const { payment } = SettingsService.get("providers");
    const provider = payment[company] || payment.defaultProvider;

    const payload = {
      company,
      amount,
      currency,
      provider,
      debitAccountType: "Revenue",
      recipient: {
        bankCode,
        bankName,
        number,
        reason: "payout",
        withdrawal: withdrawalCode,
      },
      narration: "Payout from Bujeti",
    };
    const response = await ChargeService.chargeCompany(payload);
    if (response.error) {
      const { errors = [] } = response.data || {};
      const [{ detail: message = "Could not initiate transfer" } = {}] = errors;
      throw new ValidationError(message);
    }
    await SettlementRepo.update({ queryParams: { code: verifyOTP.data.entityCode }, data: { status: STATUSES.PROCESSING } });
    return { code: withdrawalCode, amount, currency };
  },

  async finalize({ code, status = STATUSES.PROCESSED }) {
    await SettlementRepo.update({ queryParams: { code }, data: { status } });
    // TODO ***** NOTIFY ADMINS *****
    return;
  },

  async list(filter) {
    const { page = 1, perPage = 20, status, ...rest } = filter;
    const queryParams = { ...rest };
    if (status) queryParams.status = getStatusValues(status);
    const { settlements, meta } = await SettlementRepo.list({ filter: queryParams });
    const sanitizedSettlement = Sanitizer.sanitizeModelInstances(settlements, "Settlement");
    return { settlements: sanitizedSettlement, meta };
  },

  async getInvoiceSettlementDetails(invoiceId) {
    const foundInvoice = await InvoiceRepo.getSingleInvoice({ filter: { id: invoiceId } });
    if (!foundInvoice) throw new NotFoundError("Invoice");

    const { Customer: { BankAccounts: customerBankAccounts = [] } = {} } = foundInvoice;

    if (!customerBankAccounts.length) throw new NotFoundError("Customer Bank Account");

    const activeBankAccount = customerBankAccounts.find((bankAccount) => bankAccount.status === STATUSES.ACTIVE && bankAccount.type === "virtual");

    return {
      bankAccount: activeBankAccount,
    };
  },

  async processPaystackPendingSettlement({ filter, payload }) {
    const { trial } = payload;
    if (trial >= 4) {
      // Stop retrying
      await PendingSettlementRepo.updatePendingSettlement({ filter, payload: { status: STATUSES.FAILED } });
      return ResponseUtils.BadRequestException("Transfer could not be initiated");
    }
    const foundSettlement = await PendingSettlementRepo.getPendingSettlement({ filter });
    if (!foundSettlement) throw new NotFoundError("Pending Settlement");

    if (foundSettlement.status !== STATUSES.PENDING)
      throw new ValidationError(`Only pending settlements can be processed, settlement status is ${foundSettlement.Status?.value} `);

    try {
      // Initiate Payment
      const { entityType, entity, amount, reference, company } = foundSettlement;
      let response = {};
      switch (entityType) {
        case "invoice":
          response = await Service.getInvoiceSettlementDetails(entity);
          break;

        default:
          response = await Service.getInvoiceSettlementDetails(entity);
          break;
      }
      const { bankAccount: { accountName, number, bankCode, currency, externalIdentifier: externalAccountIdentifier } = {} } = response;

      if (!(accountName && number && bankCode)) throw new ValidationError("Account name and number is required");
      const providerTransferPayload = { accountName, number, bankCode, amount, currency, reference, reason: "Invoice Settlement" };

      const { payment } = SettingsService.get("providers");
      const { data } = await Providers[payment.cardPaymentProvider || "paystack"].payments.initiateTransfer(providerTransferPayload);

      const { transfer_code: externalIdentifier } = data;
      await PendingSettlementRepo.updatePendingSettlement({ filter, payload: { externalIdentifier, status: STATUSES.PROCESSING } });

      // Create Transfer
      const transferPayload = {
        currency,
        description: "Card Invoice settlement into customer account",
        status: STATUSES.PENDING,
        amount: -1 * amount,
        company,
        reference,
        narration: "Card Invoice settlement into customer account",
      };
      await TransferRepo.createTransfer({
        data: transferPayload,
      });

      // Mock Funding on staging
      if (!isProd()) {
        const providerToUse = payment[company] || payment.defaultProvider;
        await Providers[providerToUse].fundBank(externalAccountIdentifier, amount);
      }

      await RedisService.setex(`pending_customer_settlement:${externalAccountIdentifier}`, JSON.stringify({ entity, entityType }), 86400);

      return ResponseUtils.sendObjectResponse("Transfer initiated successfully");
    } catch (error) {
      // Add to queue
      log(Log.fg.red, error?.message);
      const SQSPayload = {
        idempotencyKey: `pending_settlement:${foundSettlement.code}`,
        path: `/settlements/${foundSettlement.code}/process`,
        key: process.env.INTRA_SERVICE_TOKEN,
        trial: trial + 1,
      };
      return QueueService.addJob({}, SQSPayload, `pending_settlement:${foundSettlement.code}`, isProd() ? 10 : 60);
    }
  },
};

module.exports = Service;
