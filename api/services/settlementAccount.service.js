const { STATUSES } = require("../models/status");
const BankService = require("./bank");
const CompanyService = require("./company");
const OTPService = require("./otp");
const NotificationService = require("./notification");
const { ValidationError, ExistsError, NotFoundError } = require("../utils/error.utils");
const BankAccountRepo = require("../repositories/bankAccount.repo");
const SettlementAccountRepo = require("../repositories/settlementAccount.repo");
const UserRepo = require("../repositories/user.repo");

const CompanyRepo = require("../repositories/company.repo");
const { Op } = require("sequelize");
const { getBank } = require("../utils");

const Service = {
  async createSettlementAccount(payload) {
    const { addedBy, bankCode, ...rest } = payload;
    const { label: bankName } = getBank(bankCode);
    const bankAccount = await BankService.createBankAccount({
      ...rest,
      bankName,
      bankCode,
      ownerType: "company",
      type: "real",
      company: rest.owner,
    });

    let settlementAccount = await SettlementAccountRepo.getSingleSettlementAccount({ filter: { bankAccount: bankAccount.id } });
    if (settlementAccount && [STATUSES.ACTIVE, STATUSES.INACTIVE].includes(settlementAccount.status)) throw new ExistsError("Settlement Account");

    if (!settlementAccount) {
      settlementAccount = await SettlementAccountRepo.createSettlementAccount({
        data: {
          addedBy,
          company: rest.owner,
          bankAccount: bankAccount.id,
          status: STATUSES.UNVERIFIED,
        },
      });
    }

    const companyWithAdmins = await CompanyService.getCompanyWithAdmins({ id: rest.owner });
    const [firstAdmin] = companyWithAdmins.Users;

    const creator = await UserRepo.getOneUser({ queryParams: { id: addedBy }, selectOptions: ["firstName", "lastName"] });

    const { code, hash } = await OTPService.createOTPRedisCode({ code: settlementAccount.code });
    const emailPayload = {
      adminFirstName: firstAdmin.firstName,
      creatorName: `${creator.firstName} ${creator.lastName}`,
      bankName: bankAccount.bankName,
      accountName: bankAccount.accountName,
      accountNumber: bankAccount.number,
      otp: code.split(""),
      year: new Date().getFullYear(),
    };
    NotificationService.notifyUser(firstAdmin, "settlement-otp", emailPayload, {
      from: "Bujeti Security Team",
      subject: `A new Settlement Account has been added for ${companyWithAdmins.name}`,
    });
    return { hash, name: firstAdmin.id === addedBy ? `you` : `${firstAdmin.firstName} ${firstAdmin.lastName}` };
  },

  async verifyOTP({ code, hash }) {
    const verifyOTP = await OTPService.verifyRedis({ hash, code }, "settlementAccount");
    if (!verifyOTP.success) throw new ValidationError("Invalid OTP sent");

    await SettlementAccountRepo.update({ filter: { code: verifyOTP.data.entityCode }, data: { status: STATUSES.INACTIVE } });
    return verifyOTP.data;
  },

  async update(filter, body) {
    const { status } = body;
    let settlementAccount = await SettlementAccountRepo.getSingleSettlementAccount({ filter });
    if (!settlementAccount) throw new NotFoundError("Settlement Account");

    if (settlementAccount.status === STATUSES.UNVERIFIED) throw new ValidationError(`This account has not been verified`);

    // DDeactivate all other setttllement account if an attemp to activate another
    if (status === "active")
      await SettlementAccountRepo.update({ filter: { company: filter.company, status: STATUSES.ACTIVE }, data: { status: STATUSES.INACTIVE } });

    await SettlementAccountRepo.update({ filter, data: { status: STATUSES[String(status).toUpperCase()] } });
    const companyPayload = {
      settlementAccount: String(status).toLowerCase() === "active" ? settlementAccount.id : null,
    };
    await CompanyRepo.updateACompany({ queryParams: { id: settlementAccount.company }, updateFields: companyPayload });
    const foundCompany = await CompanyRepo.getOneCompany({ queryParams: { id: settlementAccount.company }, selectOptions: ["settlementAccount"] });
    settlementAccount.status = foundCompany.settlementAccount === settlementAccount.id ? STATUSES.ACTIVE : STATUSES.INACTIVE;
    return settlementAccount;
  },

  async list(filter) {
    return SettlementAccountRepo.listSettlementAccount({ filter });
  },
};

module.exports = Service;
