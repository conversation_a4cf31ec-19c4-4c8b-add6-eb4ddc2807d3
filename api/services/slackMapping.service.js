const { WebClient } = require("@slack/web-api");
const { Beneficiary, User, Integration } = require("../models");
const { STATUSES } = require("../models/status");
const ThirdPartyLogRepo = require("../repositories/thirdpartylog.repo");
const { CARD_ISSUER } = require("../models/cardissuer");
const { log, Log } = require("../utils/logger.utils");
const { PLATFORM } = require("../constants/platforms");

const Service = {
  /**
   * Maps Slack users to Bujeti beneficiaries by matching email addresses
   * @param {Object} options - Options for mapping
   * @param {number} options.companyId - Company ID
   * @param {string} options.accessToken - Slack access token
   * @returns {Promise<Object>} - Mapping results
   */
  async mapSlackUsersToBeneficiaries({ companyId, accessToken }) {
    try {
      if (!companyId || !accessToken) {
        log(Log.bg.red, {
          message: "Missing required parameters for Slack mapping",
        });
        return {
          success: false,
          message: "Missing company ID or access token",
          mappedCount: 0,
        };
      }

      const client = new WebClient(accessToken);

      // Get all company users with their email addresses
      const beneficiaries = await Beneficiary.findAll({
        where: {
          company: companyId,
          status: STATUSES.ACTIVE,
        },
        include: [
          {
            model: User,
            as: "User",
            attributes: ["id", "email", "firstName", "lastName"],
          },
        ],
      });

      if (!beneficiaries.length) {
        log(Log.fg.yellow, {
          message: `No beneficiaries found for company ${companyId}`,
        });
        return {
          success: true,
          message: "No beneficiaries found to map",
          mappedCount: 0,
        };
      }

      // Create a map of email addresses to beneficiaries for faster lookup
      const emailToBeneficiaryMap = {};
      beneficiaries.forEach((beneficiary) => {
        if (beneficiary.User && beneficiary.User.email) {
          emailToBeneficiaryMap[beneficiary.User.email.toLowerCase()] = beneficiary;
        }
      });

      // Fetch Slack users
      log(Log.fg.blue, {
        message: `Fetching Slack users for company ${companyId}`,
      });
      const slackResponse = await client.users.list();

      if (!slackResponse.ok) {
        log(Log.bg.red, { message: `Slack API error: ${slackResponse.error}` });

        // Log the error
        await ThirdPartyLogRepo.createLog({
          message: "Slack User Mapping Error",
          company: companyId,
          event: "slack_api_request_error",
          provider: CARD_ISSUER.Slack,
          providerType: "slack",
          response: JSON.stringify(slackResponse),
          statusCode: 500,
          endpoint: "https://slack.com/api/users.list",
          method: "get",
        });

        return {
          success: false,
          message: `Failed to fetch Slack users: ${slackResponse.error}`,
          mappedCount: 0,
        };
      }

      // Process each Slack user
      const updates = [];
      let mappedCount = 0;

      slackResponse.members.forEach((slackUser) => {
        if (!slackUser.is_bot && !slackUser.deleted && slackUser.profile && slackUser.profile.email) {
          const slackEmail = slackUser.profile.email.toLowerCase();
          const beneficiary = emailToBeneficiaryMap[slackEmail];

          if (beneficiary && (!beneficiary.slackId || beneficiary.slackId !== slackUser.id)) {
            updates.push(Beneficiary.update({ slackId: slackUser.id }, { where: { id: beneficiary.id } }));
            mappedCount += 1;
          }
        }
      });

      // Execute all updates
      if (updates.length > 0) {
        await Promise.all(updates);
      }

      // Log successful mapping
      await ThirdPartyLogRepo.createLog({
        message: `Slack User Mapping Completed - ${mappedCount} users mapped`,
        company: companyId,
        event: "slack_api_user_mapping",
        provider: CARD_ISSUER.Slack,
        providerType: "workspace",
        response: JSON.stringify({ mappedCount }),
        statusCode: 200,
        endpoint: "https://slack.com/api/users.list",
        method: "get",
      });

      log(Log.fg.green, {
        message: `Successfully mapped ${mappedCount} Slack users for company ${companyId}`,
      });

      return {
        success: true,
        message: `Successfully mapped ${mappedCount} Slack users to beneficiaries`,
        mappedCount,
      };
    } catch (error) {
      log(Log.bg.red, {
        message: `Error mapping Slack users: ${error.message}`,
        err: String(error),
      });

      // Log the error
      await ThirdPartyLogRepo.createLog({
        message: "Slack User Mapping Error",
        company: companyId,
        event: "slack_api_request_error",
        provider: CARD_ISSUER.Slack,
        providerType: "workspace",
        response: error.message,
        statusCode: 500,
        endpoint: "https://slack.com/api/users.list",
        method: "get",
      });

      return {
        success: false,
        message: `Failed to map Slack users: ${error.message}`,
        mappedCount: 0,
      };
    }
  },

  /**
   * Scheduled job to sync all active Slack integrations
   * @returns {Promise<void>}
   */
  async syncAllSlackIntegrations() {
    try {
      // Get all active Slack integrations
      const integrations = await Integration.findAll({
        where: {
          platform: PLATFORM.SLACK,
          status: STATUSES.ACTIVE,
        },
        attributes: ["company", "access_token"],
      });

      log(Log.fg.blue, {
        message: `Syncing ${integrations.length} active Slack integrations`,
      });

      // Process integrations in parallel
      await Promise.all(
        integrations.map((integration) =>
          Service.mapSlackUsersToBeneficiaries({
            companyId: integration.company,
            accessToken: integration.access_token,
          }).catch((integrationError) => {
            log(Log.bg.red, {
              message: `Error syncing Slack integration for company ${integration.company}`,
              err: String(integrationError),
            });
          })
        )
      );

      log(Log.fg.green, {
        message: "Completed syncing all Slack integrations",
      });
    } catch (error) {
      log(Log.bg.red, {
        message: "Error in syncAllSlackIntegrations",
        err: String(error),
      });
    }
  },
};

module.exports = Service;
