const axios = require("axios");
const { Op } = require("sequelize");
const { isValidPhoneNumber } = require("libphonenumber-js");
const { CARD_ISSUER } = require("../models/cardissuer");
const { STATUSES } = require("../models/status");
const { ValidationError, NotFoundError } = require("../utils/error.utils");
const ThirdPartyLogService = require("./thirdPartyLog");
const TransactionService = require("./transaction");
const {
  CompanyRepo,
  CardHolderRepo,
  UserRepo,
  VirtualCardRepo,
  VendorRepo,
  BudgetLedgerRepo,
  TransactionRepo,
  BalanceLedgerRepo,
  BudgetRepo,
  UserBudgetRepo,
} = require("../repositories/index.repo");
const NotificationService = require("./notification");
const { getDashboardURL, cardType, getCardType, formatHumanReadableDate, getApiURL, formatPhoneNumber } = require("../utils");
const SettingsService = require("./settings");
const RedisService = require("./redis");
const Utils = require("../utils");
const { findSaaSMatch } = require("../utils/saas.utils");
const BackgroundService = require("./backgroundService");
const { BACKGROUND } = require("../mocks/constants.mock");

const channelDescriptionSwitch = ({ channel, reference, merchantName }) => {
  let description;
  switch (channel.toLowerCase()) {
    case "web":
      description = `Online Payment to ${merchantName} (Ref: ${reference}).`;
      break;
    case "pos":
      description = `POS Payment to ${merchantName} (Ref: ${reference})`;
      break;
    case "atm":
      description = `ATM Payment made to ${merchantName} (Ref: ${reference})`;
      break;
    default:
      description = `Payment made to ${merchantName} (Ref: ${reference})`;
      break;
  }
  return description;
};

class SudoService {
  static async apicall({ url, method, payload = {}, timeout, vaultUrl = false, cardToken = null }) {
    try {
      let call;
      if (method === "get") {
        call = await axios[method](`${!vaultUrl ? process.env.SUDO_BASE_URL : process.env.SUDO_BASE_URL_VAULT}${url}`, {
          headers: {
            Authorization: `Bearer ${!cardToken ? process.env.SUDO_AUTH_TOKEN : cardToken}`,
            "Content-Type": "application/json",
          },
          timeout,
        });
        //
      } else {
        call = await axios[method](`${!vaultUrl ? process.env.SUDO_BASE_URL : process.env.SUDO_BASE_URL_VAULT}${url}`, payload, {
          headers: {
            Authorization: `Bearer ${!cardToken ? process.env.SUDO_AUTH_TOKEN : cardToken}`,
            "Content-Type": "application/json",
          },
        });
      }
      const {
        data: { responseCode, message, statusCode, ...rest },
      } = call;
      return { statusCode, message, responseCode, ...rest };
    } catch (error) {
      const { response } = error;
      return { error: true, ...(response && { data: response?.data }) };
    }
  }

  static async createCardHolder(payload, company, user) {
    const cardHolderPayload = this.cardHolderWrapper(payload);
    const request = await this.apicall({
      url: `/customers`,
      method: "post",
      payload: cardHolderPayload,
    });
    const { error, message, status, data } = request;
    if (error) {
      throw new ValidationError(message || "Could not profile this user for a Card. Please try again later.");
    }
    await ThirdPartyLogService.createLog({
      company,
      event: "sudo.create.cardholder",
      payload: JSON.stringify(cardHolderPayload),
      message: `creating.card.holder`,
      provider: CARD_ISSUER.sudo,
      providerType: "issuer",
      response: JSON.stringify(request),
      statusCode: 200,
      endpoint: `${process.env.SUDO_BASE_URL}/customers`,
      method: "POST",
    });
    await CardHolderRepo.create({
      provider: CARD_ISSUER.sudo,
      user,
      company,
      status: STATUSES.ACTIVE,
      // eslint-disable-next-line no-underscore-dangle
      externalIdentifier: data._id,
    });
    return {
      message,
      error,
      data,
    };
  }

  static async updateCardSetting({ payload, cardExternalId, company }) {
    const request = await this.apicall({
      url: `/cards/${cardExternalId}`,
      method: "put",
      payload,
    });
    const { error, message, data } = request;
    if (error) {
      throw new ValidationError(message || "Could not update card.");
    }
    await ThirdPartyLogService.createLog({
      company,
      event: "sudo.update.cardSettings",
      payload: JSON.stringify(payload),
      message: `updating.card.cardSettings`,
      provider: CARD_ISSUER.sudo,
      providerType: "issuer",
      response: JSON.stringify(request),
      statusCode: 200,
      endpoint: `${process.env.SUDO_BASE_URL}/cards/${cardExternalId}`,
      method: "POST",
    });
    return {
      message,
      error,
      data,
    };
  }

  static cardHolderWrapper(payload) {
    return {
      type: "individual",
      name: `${payload.first_name} ${payload.last_name}`,
      status: "active",
      phoneNumber: payload.phone,
      emailAddress: payload.email_address,
      individual: {
        firstName: payload.first_name,
        lastName: payload.last_name,
        dob: payload.identity.dob,
        identity: {
          type: "BVN",
          number: payload.identity.bvn,
        },
      },
      billingAddress: {
        line1: payload.address.address,
        line2: payload.address.address,
        city: payload.address.city,
        state: payload.address.state,
        country: payload.address.country,
        postalCode: payload.address.postal_code || "123009",
      },
    };
  }

  static async fundVACard(payload) {
    const request = await this.apicall({
      url: "/accounts/simulator/fund",
      method: "post",
      payload: {
        accountId: payload.accountId,
        amount: payload.amount / 100,
      },
    });
    return {};
  }

  static async createVirtualCard({ payload, cardObject = {}, hasPolicy = false }) {
    // eslint-disable-next-line camelcase
    const { currency, amount, card_type, cardHolder, accountId, reference } = payload;
    const cardBrandMap = {
      ngn: "Verve",
      usd: "MasterCard",
    };
    const lowerCasedCurrency = currency.toLowerCase();

    // const { accountId } = Setting.get("cardIssuingAccounts").sudo;
    const createCardPayload = {
      customerId: cardHolder.externalIdentifier,
      debitAccountId: accountId,
      brand: cardBrandMap[lowerCasedCurrency],
      // eslint-disable-next-line camelcase
      type: card_type.toLowerCase() === "flash" ? "virtual" : card_type.toLowerCase(),
      currency,
      status: "active",
      issuerCountry: "NGA",
      ...(lowerCasedCurrency === "usd" && {
        issuerCountry: "USA",
        amount: amount / 100,
      }),
    };
    if (!payload.spendingControls) {
      createCardPayload.spendingControls = {
        allowedCategories: [],
        blockedCategories: [],
        channels: {
          atm: true,
          pos: true,
          web: true,
          mobile: true,
        },
        spendingLimits: [],
      };
    } else {
      createCardPayload.spendingControls = payload.spendingControls;
    }
    if (!payload.fundingSourceId) {
      const fundingSourceKey = hasPolicy ? "sudoGatewayFundingSources" : "sudoFundingSources";
      createCardPayload.fundingSourceId = SettingsService.get(fundingSourceKey)[lowerCasedCurrency].default;
    }
    const request = await this.apicall({
      url: "/cards",
      method: "post",
      payload: createCardPayload,
    });

    const { statusCode, responseCode, message, error, data } = request;

    const user = await UserRepo.getOneUser({
      queryParams: { id: cardHolder.user },
      selectOptions: ["firstName", "lastName", "email", "company"],
    });

    await ThirdPartyLogService.createLog({
      company: user.company,
      event: "sudo.create.card",
      payload: JSON.stringify(createCardPayload),
      message,
      provider: CARD_ISSUER.sudo,
      providerType: "issuer",
      response: JSON.stringify(request),
      statusCode,
      endpoint: `${process.env.SUDO_BASE_URL}/cards`,
      method: "POST",
    });

    const check = this.sudoStatusCheck(statusCode);
    if (!check) {
      throw new ValidationError(message);
    }
    // get user with cardHolder externalId

    const emailPayload = {
      lastName: user.lastName,
      firstName: user.firstName,
      failed: false,
      dashboardUrl: `${getDashboardURL()}/cards/${cardObject.code}/details`,
    };
    NotificationService.notifyUser({ email: user.email }, "success-card", emailPayload, {
      subject: `Hey ${user.firstName}! Your Bujeti Card is ready to be used 💳.`,
    });

    return {
      status: [200, 201].includes(statusCode) ? "success" : "Failed",
      message,
      data: {
        // eslint-disable-next-line no-underscore-dangle
        externalIdentifier: data._id,
        currency: data.currency,
        // card_pan: null,
        masked_pan: data.maskedPan,
        // expiration: null,
        // cvv: null,
        // name: null,
        card_type: data.brand.toLowerCase(),
        exp_month: data.expiryMonth,
        exp_year: data.expiryYear,
        accountTransfer: {
          // eslint-disable-next-line no-underscore-dangle
          receipientId: data.account,
          accountId,
          amount,
          narration: `card creation for ${user.firstName}`,
          reference,
        },
      },
    };
  }

  static async getVirtualCard({ id, reveal = false }) {
    const card = await VirtualCardRepo.find({ externalIdentifier: id });
    const request = await this.apicall({
      url: `/cards/${id}?reveal=${reveal}`,
      method: "get",
      payload: null,
    });
    const { data, statusCode, message } = request;
    await ThirdPartyLogService.createLog({
      company: card.company,
      event: "sudo.get.card",
      payload: JSON.stringify({}),
      message: "Getting virtual cards.",
      provider: CARD_ISSUER.sudo,
      providerType: "issuer",
      response: JSON.stringify(request),
      statusCode,
      endpoint: `${process.env.SUDO_BASE_URL}/cards/${id}?reveal=${reveal}`,
      method: "GET",
    });
    const check = this.sudoStatusCheck(statusCode);
    if (!check) {
      return {
        status: "Failed",
        error: true,
        message,
      };
    }
    return { data, status: "success", message };
  }

  static async fundVirtualCard({ id, amount, currency, reference = null, company = null, accountId }) {
    // get the cards wallet id
    const card = await this.getVirtualCard({ id });
    // move funds from settlement account to card wallet
    const accountTransfer = await this.accountTransfer({
      // eslint-disable-next-line no-underscore-dangle
      receipientId: card.data.account._id,
      debitAccountId: accountId,
      currency,
      amount,
      reference,
      narration: `funding of card with id: ${id}`,
      cardId: id,
      event: "sudo.card.fund",
    });

    if (accountTransfer.error) {
      throw new ValidationError(accountTransfer.message);
    }
    return accountTransfer;
  }

  static async accountTransfer({
    receipientId,
    debitAccountId,
    amount,
    narration,
    reference,
    beneficiaryBankCode,
    beneficiaryAccountNumber,
    cardId,
    event,
  }) {
    let payload;
    let request;

    const { sandboxAccount } = SettingsService.get("cardIssuingAccounts").sudo;
    const isLive = Utils.isProd();

    const card = await VirtualCardRepo.find({
      [Op.or]: [
        {
          externalIdentifier: cardId,
        },
        {
          code: cardId,
        },
      ],
    });
    if (!beneficiaryBankCode && !beneficiaryAccountNumber) {
      payload = {
        debitAccountId,
        creditAccountId: receipientId,
        amount: amount / 100,
        narration,
        paymentReference: reference,
      };
    } else {
      payload = {
        debitAccountId,
        beneficiaryBankCode: isLive ? beneficiaryBankCode : sandboxAccount.bankCode,
        beneficiaryAccountNumber: isLive ? beneficiaryAccountNumber : sandboxAccount.accountNumber,
        amount: amount / 100,
        narration,
        paymentReference: reference,
      };
    }
    // eslint-disable-next-line prefer-const
    request = await this.apicall({
      url: "/accounts/transfer",
      method: "post",
      payload,
    });

    const { responseCode, statusCode, message, data } = request;

    await ThirdPartyLogService.createLog({
      company: card.company,
      event,
      payload: JSON.stringify(payload),
      message: narration,
      provider: CARD_ISSUER.sudo,
      providerType: "issuer",
      response: JSON.stringify(request),
      statusCode,
      endpoint: `${process.env.SUDO_BASE_URL}accounts/transfer`,
      method: "POST",
    });
    const check = this.sudoStatusCheck(statusCode);
    if (!check) {
      if ((responseCode && Number(responseCode) === 94) || String(message) === "Duplicate transaction") {
        RedisService.delete(`credit_card:funding:${card.code}`);
        return { status: "success", message, error: false };
      }
      return {
        status: "failed",
        message,
        error: true,
        responseCode,
      };
    }
    RedisService.delete(`credit_card:funding:${card.code}`);
    return {
      status: "success",
      message,
      data,
    };
  }

  static async liquidateVirtualCard(payload) {
    // get Card account
    const card = await this.getVirtualCard({ id: payload.id });
    const accountTransfer = await this.accountTransfer({
      receipientId: payload.accountId,
      // eslint-disable-next-line no-underscore-dangle
      debitAccountId: card.data.account._id,
      amount: payload.amount,
      narration: `Liquidate Card id:${payload.id}`,
      reference: payload.reference,
      event: "initiate.sudo.liquidate.card",
      cardId: payload.id,
    });
    // do a bank transfer
    if (accountTransfer.error) {
      throw new ValidationError("Could not Fund Card");
    }
    return accountTransfer;
  }

  static async getTransaction({ externalIdentifier, company }) {
    const request = await this.apicall({
      url: `accounts/transfers/${externalIdentifier}`,
      method: "get",
    });
    const { data, statusCode, message } = request;
    await ThirdPartyLogService.createLog({
      company,
      event: "sudo.get.transaction",
      payload: JSON.stringify({ externalIdentifier }),
      message: "Get single transaction.",
      provider: CARD_ISSUER.sudo,
      providerType: "issuer",
      response: JSON.stringify(request),
      statusCode,
      endpoint: `${process.env.SUDO_BASE_URL}accounts/transfers/${externalIdentifier}`,
      method: "GET",
    });
    const check = this.sudoStatusCheck(statusCode);
    if (!check) {
      return {
        status: "Failed",
        error: true,
        message,
      };
    }
    return { data, status: "success", message };
  }

  static async getCard(id) {
    const { message, status, data, error } = await this.getVirtualCard({
      id,
      reveal: false,
    });

    if (error) {
      throw new ValidationError("Could not fetch card");
    }
    const {
      customer,
      business,
      account,
      fundingSource,
      isDeleted,
      createdAt,
      updatedAt,
      __v,
      is2FAEnrolled,
      isDefaultPINChanged,
      disposable,
      refundAccount,
      ...rest
    } = data;
    return { data: { ...rest } };
  }

  static async freezeVirtualCard(id) {
    const card = await VirtualCardRepo.find({ externalIdentifier: id });
    const request = await this.apicall({
      url: `/cards/${id}`,
      method: "put",
      payload: {
        status: "inactive",
      },
    });
    const { data, statusCode, responseCode, message, error } = request;
    await ThirdPartyLogService.createLog({
      company: card.company,
      event: "sudo.card.freeze",
      payload: JSON.stringify({
        status: "inactive",
      }),
      message: message || "Freeze Card",
      provider: CARD_ISSUER.sudo,
      providerType: "issuer",
      response: JSON.stringify({ ...request }),
      statusCode,
      endpoint: `${process.env.SUDO_BASE_URL}/cards/${id}`,
      method: "PUT",
    });
    const check = this.sudoStatusCheck(statusCode);
    if (!check) throw new ValidationError("Could not block card");
    return {
      status: check ? "success" : "failed",
      data,
      message,
    };
  }

  static async unfreezeVirtualCard(id) {
    const card = await VirtualCardRepo.find({ externalIdentifier: id });
    const request = await this.apicall({
      url: `/cards/${id}`,
      method: "put",
      payload: {
        status: "active",
      },
    });
    const { data, statusCode, responseCode, message, error } = request;
    await ThirdPartyLogService.createLog({
      company: card.company,
      event: "sudo.card.unfreeze",
      payload: JSON.stringify({
        status: "active",
      }),
      message: "Unfreeze Card",
      provider: CARD_ISSUER.sudo,
      providerType: "issuer",
      response: JSON.stringify({ ...request }),
      statusCode,
      endpoint: `${process.env.SUDO_BASE_URL}/cards/${id}`,
      method: "PUT",
    });
    const check = this.sudoStatusCheck(statusCode);
    if (!check) throw new ValidationError("Could not block card");
    return {
      status: check ? "success" : "failed",
      data,
      message,
    };
  }

  static sudoStatusCheck(statusCode) {
    if (![200, 201, 202].includes(statusCode)) {
      return false;
    }
    return true;
  }

  static async generateCardToken(id, company = -1) {
    const response = await this.apicall({
      url: `/cards/${id}/token`,
      method: "get",
      timeout: 3000, // 3 secs,
    });

    const { data, message, statusCode, error = false } = response;

    const maskedResponse = {
      ...response,
      data: {
        ...response.data,
        token: "****",
      },
    };

    await ThirdPartyLogService.createLog({
      company,
      event: "sudo.card.getToken",
      payload: JSON.stringify({ id }),
      message: message || `${error ? "Card token generated successfully" : "Error generating card token"}`,
      provider: CARD_ISSUER.sudo,
      providerType: "issuer",
      response: JSON.stringify({ maskedResponse }),
      statusCode,
      endpoint: `${process.env.SUDO_BASE_URL}/cards/${id}`,
      method: "GET",
    });
    return {
      data,
      message,
      error,
    };
  }

  static decryptCard(cards) {
    return {
      ...cards,
      last_4: null,
      cvv: null,
      number: null,
      pin: null,
    };
  }

  static async simulateCardTransaction({ cardId, amount, currency }) {
    const { message, statusCode, responseCode, ...rest } = await this.apicall({
      url: `/cards/simulator/authorization`,
      method: "post",
      payload: {
        cardId,
        channel: "web",
        type: "purchase",
        amount,
        currency,
        merchant: {
          category: "7399",
          merchantId: "*********",
          name: "Acme Inc",
          city: "Barnawa",
          state: "KD",
          country: "NG",
        },
      },
    });
    const check = this.sudoStatusCheck(statusCode);
    if (!check) throw new ValidationError("Unable to Charge Card");
    return {
      message,
      statusCode,
      responseCode,
      ...rest,
    };
  }

  static async getDefaultCardPin(id, company) {
    const { message: tokenMessage, data: tokenData, error: tokenError } = await this.generateCardToken(id, company);
    const { token: cardToken } = tokenData;

    if (tokenError) throw new ValidationError(tokenMessage || "Error occurred generating card token");
    const response = await this.apicall({
      url: `/cards/${id}/secure-data/defaultPin`,
      method: "get",
      vaultUrl: true,
      cardToken,
    });

    const maskedResponse = {
      ...response,
      data: {
        ...response.data,
        defaultPin: "****",
      },
    };

    const { statusCode, message, error, data } = response;

    await ThirdPartyLogService.createLog({
      company,
      event: "sudo.get.DefaultPin",
      payload: JSON.stringify({ id }),
      message,
      provider: CARD_ISSUER.sudo,
      providerType: "issuer",
      response: JSON.stringify(maskedResponse),
      statusCode,
      endpoint: `${process.env.SUDO_BASE_URL}/cards/${id}/secure-data/defaultPin`,
      method: "GET",
    });

    if (error) throw new ValidationError(message || "Error occurred fetching card pin");
    return {
      ...data,
    };
  }

  static async webhook({ data, type }, meta) {
    switch (type) {
      case "authorization.request":
        await cardAuthorized(data);
        break;
      case "transaction.created":
        await cardTransactionCreated(data, meta);
        break;
      case "transaction.refund":
        await cardTransactionRefund(data);
        break;
      case "card.updated":
        await handleCardUpdateWebhook(data);
        break;
      default:
        break;
    }
  }

  static async getSecureDetails({ id, cardToken, requiredDetail }) {
    const { statusCode, data, message } = await this.apicall({
      url: `/cards/${id}/secure-data/${requiredDetail}`,
      method: "get",
      cardToken,
      vaultUrl: true,
    });
    const check = this.sudoStatusCheck(statusCode);
    if (!check) throw new ValidationError(`Unable to get ${requiredDetail} at the moment.`);
    return { data };
  }

  static async changeCardPin({ id, oldPin, newPin, company }) {
    const response = await this.apicall({
      url: `/cards/${id}/pin`,
      method: "put",
      payload: { oldPin, newPin },
    });
    const { message, statusCode } = response;
    await ThirdPartyLogService.createLog({
      company,
      event: "sudo.change.pin",
      payload: JSON.stringify({ oldPin: "****", newPin: "****" }),
      message: message || `Change card pin request`,
      provider: CARD_ISSUER.sudo,
      providerType: "issuer",
      response: JSON.stringify(response),
      statusCode: parseInt(statusCode, 10),
      endpoint: `${process.env.SUDO_BASE_URL}/cards/${id}/pin`,
      method: "PUT",
    });
    const check = this.sudoStatusCheck(statusCode);
    if (!check) throw new ValidationError("Unable to change pin");
    return { message, error: false };
  }

  static async getExchangeRate(company) {
    const { error, message, status, data } = await this.apicall({
      url: "/accounts/transfer/rate/USDNGN",
      method: "get",
    });
    if (error || !data) {
      ThirdPartyLogService.createLog({
        company: company?.id || -1,
        event: "sudo.rates.exchange",
        payload: null,
        message: "sudo.rates.exchange",
        provider: CARD_ISSUER.sudo,
        providerType: "issuer",
        response: JSON.stringify({ data, message, status, error }),
        statusCode: 400,
        endpoint: `${process.env.SUDO_BASE_URL}/accounts/transfer/rate/USDNGN`,
        method: "GET",
      });
      throw new ValidationError("Error getting exchange rate");
    }
    return Number(data.buy);
  }

  static async getUSDLIquidationRate(company) {
    const { error, data, message, status } = await this.apicall({
      url: "/accounts/transfer/rate/USDNGN",
      method: "get",
    });
    if (error || !data) {
      ThirdPartyLogService.createLog({
        company: company?.id || -1,
        event: "sudo.rates.liquidate",
        payload: null,
        message: "sudo.rates.liquidate",
        provider: CARD_ISSUER.sudo,
        providerType: "issuer",
        response: JSON.stringify({ data, message, status, error }),
        statusCode: 400,
        endpoint: `${process.env.SUDO_BASE_URL}/accounts/transfer/rate/USDNGN`,
        method: "GET",
      });
      throw new ValidationError("Error getting exchange rate");
    }
    return Number(data.sell);
  }

  static async getDefaultProviderAccount() {
    return SettingsService.get("cardIssuingAccounts").sudo.account;
  }

  static async generateCardHolderPayload({ user, company, bvn }) {
    if (!(user && company)) throw new ValidationError("Please specify user and company value");
    if (typeof user !== "number" || typeof company !== "number") throw new ValidationError("Both user and company values should be number");
    const [foundUser, foundCompany] = await Promise.all([
      UserRepo.getOneUser({
        queryParams: { id: user },
        includePhonenumber: true,
        includeAddress: true,
      }),
      CompanyRepo.getOneCompany({
        queryParams: { id: company },
        addPhoneNumber: true,
        addAddress: true,
      }),
    ]);

    if (!foundUser) throw new NotFoundError("User");
    if (!foundCompany) throw new NotFoundError("Company");
    const { firstName, email, lastName, Address: userAddress, PhoneNumber: userPhoneNumber, dob = null } = foundUser;
    const { Address: companyAddress } = foundCompany;

    if (!userPhoneNumber) throw new NotFoundError("User's Phone Number");
    if (!dob) throw new ValidationError("Please update your Date of Birth");

    const internationalPhoneNumber =
      userPhoneNumber.internationalFormat || formatPhoneNumber(userPhoneNumber.localFormat, userPhoneNumber.countryCode);

    const cardHolderPayload = {
      first_name: firstName,
      last_name: lastName,
      address: {
        address: userAddress?.street || companyAddress?.street,
        city: userAddress?.city || companyAddress?.city,
        state: userAddress?.state || companyAddress?.state,
        country: userAddress?.country || companyAddress?.country,
        postal_code: userAddress?.postalCode || companyAddress?.postalCode,
        house_no: userAddress?.street || companyAddress?.street,
      },
      phone: internationalPhoneNumber,
      email_address: email,
      identity: {
        id_type: "NIGERIAN_BVN_OTP_VERIFICATION",
        bvn,
        dob,
      },
    };

    return cardHolderPayload;
  }

  static async updateCardHolder({ externalIdentifier, payload, company }) {
    let { phoneNumber = null } = payload;
    if (phoneNumber) {
      phoneNumber = String(phoneNumber).startsWith("+") ? phoneNumber : `+${phoneNumber}`;
    }
    if (phoneNumber && !isValidPhoneNumber(phoneNumber)) throw new ValidationError("Please provide a valid phone number in international format");
    const providerPayload = {
      type: "individual",
      ...(phoneNumber && { phoneNumber }),
    };

    const response = await this.apicall({
      url: `/customers/${externalIdentifier}`,
      method: "put",
      payload: providerPayload,
    });
    const { error, message, statusCode } = response;
    await ThirdPartyLogService.createLog({
      company,
      event: "sudo.update.cardholder",
      payload: JSON.stringify({ payload: providerPayload }),
      message: message || `${error ? "Error occured while updating CardHolder" : "CardHolder updated successfully"}`,
      provider: CARD_ISSUER.sudo,
      providerType: "issuer",
      response: JSON.stringify(response),
      statusCode: parseInt(statusCode, 10),
      endpoint: `${process.env.SUDO_BASE_URL}/customers/${externalIdentifier}`,
      method: "PUT",
    });
  }

  static async getCardBalance({ externalIdentifier, company }) {
    const { message, data, error } = await this.getVirtualCard({
      id: externalIdentifier,
      reveal: false,
    });

    if (error) throw new ValidationError(`${message || "Could not fetch card"}`);

    // eslint-disable-next-line no-underscore-dangle
    const accountId = data?.account?._id;

    if (!accountId) throw new ValidationError("Couldn't get account ID");

    const request = await this.apicall({
      url: `/accounts/${accountId}/balance`,
      method: "get",
    });
    const { data: accountBalanceData, statusCode: accountBalanceStatus, message: accountBalanceMessage } = request;
    await ThirdPartyLogService.createLog({
      company,
      event: "sudo.account.balance",
      payload: JSON.stringify({ externalIdentifier }),
      message: accountBalanceMessage || "Get card account balance.",
      provider: CARD_ISSUER.sudo,
      providerType: "issuer",
      response: JSON.stringify({ data, message }),
      statusCode: accountBalanceStatus,
      endpoint: `${process.env.SUDO_BASE_URL}/accounts/${accountId}/balance`,
      method: "GET",
    });

    const isSuccessful = this.sudoStatusCheck(accountBalanceStatus);

    if (!isSuccessful) throw new ValidationError(message || "Error getting account balance");
    const { availableBalance } = accountBalanceData;
    const balance = Number(availableBalance) * 100;
    return { balance, status: "success", message };
  }
}

const cardAuthorized = async ({ object }) => {
  const { card, pendingRequest, transactionMetadata, status, fee, merchant } = object;
  const findCard = await VirtualCardRepo.find({
    // eslint-disable-next-line no-underscore-dangle
    externalIdentifier: card._id,
  });
  if (status === "active") {
    // eslint-disable-next-line camelcase
    const get_card_type = cardType();
    // eslint-disable-next-line camelcase
    const card_type = getCardType(get_card_type, findCard.type);
    // eslint-disable-next-line camelcase
    if (card_type === "flash") {
      // deactivate card
      await findCard.update({ status: STATUSES.INACTIVE });
    } else {
      VirtualCardRepo.arithmetic({
        // eslint-disable-next-line no-underscore-dangle
        conditions: { where: { externalIdentifier: card._id } },
        action: "increment",
        field: { spent: Number(pendingRequest.amount) },
      });
    }
    const transactionDescription = channelDescriptionSwitch({
      channel: transactionMetadata.channel,
      reference: transactionMetadata.reference,
      merchantName: merchant.name,
    });
    // create Transaction
    const existingTransaction = await TransactionRepo.getTransaction({
      queryParams: {
        reference: transactionMetadata.transaction_reference,
        status: STATUSES[status.toUpperCase()],
      },
    });
    if (existingTransaction) return;

    const transaction = await TransactionService.createTransaction({
      amount: Number(pendingRequest.amount),
      currency: findCard.currency,
      company: findCard.company,
      card: findCard.id,
      budget: findCard.budget,
      payer: findCard.user,
      reference: transactionMetadata.transaction_reference,
      narration: transactionDescription,
      description: transactionDescription,
      processor_fee: fee,
      bujeti_fee: 0,
      status: STATUSES[status.toUpperCase()],
    });
    const lastLedger = await BudgetLedgerRepo.getLastLedger({
      criteria: { card: findCard.id },
    });
    const balanceBefore = (lastLedger && lastLedger.balanceAfter) || findCard.amount - (findCard.spent - pendingRequest.amount);
    const totalAmount = Number(pendingRequest.amount) + fee;
    // create BudgetLedger
    const existingLedgerCriteria = {
      card: findCard.id,
      transaction: transaction.id,
    };
    const existingLedger = await BudgetLedgerRepo.getBudgetLedger({
      criteria: existingLedgerCriteria,
    });
    if (existingLedger) return;

    await BudgetLedgerRepo.createBudgetLedger({
      payload: {
        currency: findCard.currency,
        amount: -1 * totalAmount,
        budget: findCard.budget,
        transaction: transaction.id,
        description: pendingRequest.description,
        status: STATUSES.PROCESSED,
        user: findCard.user,
        balanceBefore,
        balanceAfter: balanceBefore - Number(totalAmount),
        card: findCard.id,
      },
      isSpent: true,
    });
  }
  await ThirdPartyLogService.createLog({
    company: findCard.company,
    event: "sudo.card.authorized",
    payload: JSON.stringify({ object }),
    message: `Card Debit Successful card:${findCard.code}, amount: ${pendingRequest.amount}`,
    provider: CARD_ISSUER.sudo,
    providerType: "issuer",
    response: JSON.stringify({ object }),
    statusCode: 200,
    endpoint: `${getApiURL}/webhook/sudo`,
    method: "POST",
  });
};

const isPOSorCheckout = (text) => {
  const regex = /\b(POS|checko?u?t?)\b[^a-zA-Z]*/i;
  return regex.test(text);
};

const handleCardUpdateWebhook = async (payload) => {
  const {
    object: { _id: externalIdentifier, brand, maskedPan: pan, expiryMonth, expiryYear },
  } = payload || {};

  const foundCard = await VirtualCardRepo.find({ externalIdentifier });

  if (!foundCard) throw new NotFoundError("Card");

  await ThirdPartyLogService.createLog({
    company: foundCard.company,
    event: "sudo.card.update",
    payload: JSON.stringify(payload),
    message: `Card Details Updated`,
    provider: CARD_ISSUER.sudo,
    providerType: "issuer",
    response: JSON.stringify({ message: "ok" }),
    statusCode: 200,
    endpoint: `${getApiURL()}/webhook/sudo`,
    method: "POST",
  });

  const updatePayload = {
    pan,
    exp_month: expiryMonth,
    exp_year: expiryYear,
    brand: brand.toLowerCase(),
  };

  await VirtualCardRepo.update({
    conditions: { id: foundCard.id },
    changes: updatePayload,
  });
};

const cardTransactionRefund = async (payload) => {
  const { object: { transactionMetadata = {} } = {} } = payload || {};
  const { reference } = transactionMetadata;

  if (!reference) return; // Transaction will not be found

  const foundTransaction = await TransactionRepo.getTransaction({
    queryParams: { reference },
    selectOptions: ["id", "amount", "budget", "card", "company", "code", "currency"],
  });

  if (!foundTransaction) throw new NotFoundError("Card Transaction");

  const foundCard = await VirtualCardRepo.find({ id: foundTransaction.card });

  if (!foundCard) throw new NotFoundError(`Card`);

  await ThirdPartyLogService.createLog({
    company: foundTransaction.company,
    event: "sudo.card.transaction.refund",
    payload: JSON.stringify(payload),
    message: `Card Transaction Refund`,
    provider: CARD_ISSUER.sudo,
    providerType: "issuer",
    response: JSON.stringify({ message: "ok" }),
    statusCode: 200,
    endpoint: `${getApiURL()}/webhook/sudo`,
    method: "POST",
  });

  // Mark Transaction as failed
  await TransactionRepo.updateTransaction({
    queryParams: { id: foundTransaction.id },
    updateFields: { status: STATUSES.FAILED },
  });

  const { budget, balance } = foundCard;

  const ledgerCriteria = {
    card: foundTransaction.card,
    transaction: foundTransaction.id,
    status: STATUSES.PROCESSED,
  };

  let exisitingLedger;

  if (budget) {
    exisitingLedger = await BudgetLedgerRepo.getBudgetLedger({
      criteria: ledgerCriteria,
    });
  } else {
    exisitingLedger = await BalanceLedgerRepo.getBalanceLedger({
      filter: ledgerCriteria,
    });
  }

  if (!exisitingLedger) throw new NotFoundError("Card transaction ledger");

  if (String(exisitingLedger.description).toLowerCase().includes(`[reversal]`)) return; // Reversal already created

  const balanceBefore = await VirtualCardRepo.getAvailableBalance(foundCard);

  const reversalLedgerPayload = {
    currency: foundCard.currency,
    amount: Math.abs(exisitingLedger.amount),
    ...(balance && { balance, company: foundCard.company }),
    ...(budget && { budget }),
    user: foundCard.user,
    transaction: foundTransaction.id,
    description: `[Reversal] ${exisitingLedger.description}`,
    status: STATUSES.PROCESSED,
    balanceBefore,
    balanceAfter: Utils.money(exisitingLedger.amount).abs().plus(balanceBefore),
    card: foundCard.id,
  };

  if (budget) {
    // Mark Ledger as failed
    await BudgetLedgerRepo.updateBudgetLedger({
      criteria: { id: exisitingLedger.id },
      payload: { status: STATUSES.FAILED },
    });

    // Create reversal ledger
    await BudgetLedgerRepo.createBudgetLedger({
      payload: { ...reversalLedgerPayload },
      isSpent: false,
    });
  } else {
    await BalanceLedgerRepo.updateLedger({
      filter: { id: exisitingLedger.id },
      payload: { status: STATUSES.FAILED },
    });

    await BalanceLedgerRepo.createLedger(reversalLedgerPayload);
  }

  // Reduce Card spent
  await VirtualCardRepo.arithmetic({
    conditions: { where: { id: foundCard.id } },
    action: "decrement",
    field: { spent: Math.abs(exisitingLedger.amount) },
  });
};

const cardTransactionCreated = async ({ object }, { PolicyService } = {}) => {
  const { card, transactionMetadata, feeDetails, merchant, amount, merchantAmount } = object;
  const findCard = await VirtualCardRepo.find({
    externalIdentifier: card,
  });

  await ThirdPartyLogService.createLog({
    company: findCard.company,
    event: "sudo.card.transaction.created",
    payload: JSON.stringify({ object }),
    message: `${findCard.code} debited Successful. Amount: ${amount}`,
    provider: CARD_ISSUER.sudo,
    providerType: "issuer",
    response: JSON.stringify({ object }),
    statusCode: 200,
    endpoint: `${getApiURL()}/webhook/sudo`,
    method: "POST",
  });
  if (amount === 0 && feeDetails.length === 0) return;

  const cardTypes = cardType();
  const findCardType = getCardType(cardTypes, findCard.type);
  if (findCardType === "flash") {
    await findCard.update({ status: STATUSES.INACTIVE });
  }

  const findUser = await UserRepo.getOneUser({
    queryParams: {
      id: findCard.user,
    },
    selectOptions: ["email", "firstName", "lastName"],
  });

  const [{ amount: feeAmount = 0 } = {}] = feeDetails || [];

  const totalAmount = Number(Math.abs(merchantAmount)) + Number(feeAmount);

  const scaleFactor = Utils.getScaleFactor(totalAmount);

  const scale = Utils.money(10).pow(scaleFactor).toNumber();

  const scaledValue = Utils.money(totalAmount).mul(scale).toNumber();

  await VirtualCardRepo.arithmetic({
    conditions: { where: { externalIdentifier: card } },
    action: "increment",
    field: { spent: scaledValue },
  });

  // create vendor
  const { id: recipientId, name } = await VendorRepo.findOrCreateVendor({
    name: findSaaSMatch(merchant.name),
    company: findCard.company,
    industry: 1,
    ignoreable: isPOSorCheckout(merchant.name),
  });

  const transactionDescription = channelDescriptionSwitch({
    channel: transactionMetadata.channel,
    reference: transactionMetadata.reference,
    merchantName: merchant.name,
  });

  const existingTransaction = await TransactionRepo.getTransaction({
    queryParams: { reference: transactionMetadata.reference },
  });
  if (existingTransaction) return;

  // create Transaction
  const transactionPayload = {
    amount: Number(Math.abs(merchantAmount) * 100),
    currency: findCard.currency,
    company: findCard.company,
    card: findCard.id,
    ...(findCard.balance && { balance: findCard.balance }),
    ...(findCard.budget && { budget: findCard.budget }),
    payer: findCard.user,
    reference: transactionMetadata.reference,
    narration: `Card Payment`,
    description: transactionDescription,
    processor_fee: Number(feeAmount) * 100,
    // eslint-disable-next-line no-underscore-dangle
    externalIdentifier: object._id,
    bujeti_fee: 0,
    status: STATUSES.SUCCESS,
    recipient: recipientId,
    recipient_type: "vendor",
  };

  if (findCard.impactBudget) transactionPayload.budget = findCard.impactBudget;

  const transaction = await TransactionService.createTransaction(transactionPayload);

  const availableBalance = await VirtualCardRepo.getAvailableBalance(findCard);

  // create BudgetLedger
  const existingLedgerCriteria = {
    card: findCard.id,
    transaction: transaction.id,
    ...(findCard.balance && { balance: findCard.balance }),
    ...(findCard.budget && { budget: findCard.budget }),
  };

  let existingLedger;

  if (findCard.budget) {
    existingLedger = await BudgetLedgerRepo.getBudgetLedger({
      criteria: existingLedgerCriteria,
    });
  } else {
    existingLedger = await BalanceLedgerRepo.getBalanceLedger({
      filter: existingLedgerCriteria,
    });
  }

  if (existingLedger) return;

  const ledgerPayload = {
    currency: findCard.currency,
    amount: -1 * scaledValue,
    ...(findCard.balance && { balance: findCard.balance, company: findCard.company }),
    ...(findCard.budget && { budget: findCard.budget }),
    user: findCard.user,
    transaction: transaction.id,
    description: `Card Payment from ${merchant.name}.`,
    status: STATUSES.PROCESSED,
    balanceBefore: availableBalance,
    balanceAfter: availableBalance - scaledValue,
    card: findCard.id,
    scale: scaleFactor,
  };

  if (findCard.budget) {
    await BudgetLedgerRepo.createBudgetLedger({
      payload: ledgerPayload,
      isSpent: !findCard.impactBudget,
    });
  } else {
    await BalanceLedgerRepo.createLedger(ledgerPayload);
  }

  // If Impact source, record payment there too
  if (findCard.impactBudget) {
    // This is where spent will be
    const foundBudget = await BudgetRepo.getOneBudget({
      queryParams: { id: findCard.impactBudget },
    });
    if (!foundBudget) throw new NotFoundError("Budget");
    const newBalance = ledgerPayload.balanceAfter;
    const spentLedgerPayload = { ...ledgerPayload, budget: foundBudget.id, card: findCard.id, balanceBefore: newBalance, balanceAfter: newBalance };
    await BudgetLedgerRepo.createBudgetLedger({
      payload: spentLedgerPayload,
      isSpent: true,
    });
  }

  // Update User Budget Spent
  if (findCard.budget || findCard.impactBudget) {
    const budget = findCard.impactBudget || findCard.budget;
    await UserBudgetRepo.increaseUserBudgetSpent({
      filter: { user: findCard.user, budget },
      amount: Math.abs(ledgerPayload.amount),
    });
  }

  const emailPayload = {
    dashboardUrl: `${getDashboardURL()}/transactions/${transaction.code}`,
    cardPan: findCard.pan,
    amount: parseInt(totalAmount, 10).toLocaleString(),
    currency: findCard.currency,
    last4: findCard.pan.slice(-4),
    description: transaction.description,
    paidOn: formatHumanReadableDate(new Date()),
    paidBy: `${findUser.firstName} ${findUser.lastName}`,
    paidTo: `${name}`,
    companyName: name,
    referenceId: transactionMetadata.reference,
    firstName: findUser.firstName,
    card: true,
    brand: findCard.brand,
  };
  NotificationService.notifyUser({ email: findUser.email }, "transaction-successful", emailPayload, {
    subject: `Hey ${findUser.firstName}, your card payment of ${findCard.currency}${emailPayload.amount.toLocaleString()} has been made`,
  });

  if (findCard.impactBudget || findCard.budget) {
    // enforce policy
    await PolicyService.policyEnforcer({
      company: findCard.company,
      overrideStrictness: true,
      entity: {
        description: `Card Payment`,
        budget: findCard.impactBudget || findCard.budget,
        amount: transaction.amount,
        user: transaction.payer,
        currency: transaction.currency,
        vendor: transaction.vendor,
        type: "payment",
      },
    });
  }

  BackgroundService.addToQueue(BACKGROUND.QUEUES.CARD_BALANCE_RECONCILIATION, BACKGROUND.JOBS.CARD_BALANCE_RECONCILIATION, object, {
    delay: 10 * 1000, // 10 secs delay
  });
};

module.exports = SudoService;
