const { TaxRepo, TaxVersionRepo } = require("../repositories/index.repo");
const { TaxVersion, TaxGroupTax } = require("../models");
const { NotFoundError, ValidationError, ExistsError } = require("../utils/error.utils");
const ResponseUtils = require("../utils/response.utils");
const { TAX_TYPES, TAX_CALCULATION_TYPES } = require("../constants/taxTypes");
const { validateTaxSequenceNumber } = require("../utils/utils");

const Service = {
  async createTax(payload) {
    const { company, user, rate, effectiveFrom, name, ...rest } = payload;

    // Check if tax with same name exists in the company
    const existingTax = await TaxRepo.getTax({
      filter: { name, company },
    });

    if (existingTax) {
      throw new ExistsError("Tax");
    }

    const taxPayload = {
      ...rest,
      name,
      company,
      created_by: user,
    };

    const createdTax = await TaxRepo.create({ payload: taxPayload });

    const versionPayload = {
      tax: createdTax.id,
      rate,
      effective_from: effectiveFrom || new Date(),
      effective_to: null,
      created_by: user,
    };

    await TaxVersionRepo.create({ payload: versionPayload });

    const foundTax = await TaxRepo.getTax({
      filter: {
        id: createdTax.id,
      },
      include: ["versions"],
    });

    return ResponseUtils.sendObjectResponse("Tax created successfully", { tax: foundTax });
  },

  async createVersion(filter, payload) {
    const { user, rate, effectiveFrom } = payload;

    const foundTax = await TaxRepo.getTax({ filter, include: ["versions"] });
    if (!foundTax) throw new NotFoundError("Tax");

    const latestVersion = await TaxVersionRepo.getActiveTaxVersion({ taxId: foundTax.id });
    if (latestVersion && !latestVersion.effective_to) {
      await TaxVersion.update({ effective_to: effectiveFrom }, { where: { id: latestVersion.id } });
    }

    const versionPayload = {
      tax: foundTax.id,
      rate,
      effective_from: effectiveFrom || new Date(),
      effective_to: null,
      created_by: user,
    };

    await TaxVersionRepo.create({ payload: versionPayload });

    // Refresh tax with updated versions
    const updatedTax = await TaxRepo.getTax({
      filter: { id: foundTax.id },
      include: ["versions"],
    });

    return ResponseUtils.sendObjectResponse("Tax version created successfully", {
      tax: {
        code: updatedTax.code,
        name: updatedTax.name,
        type: updatedTax.type,
        calculation_type: updatedTax.calculation_type,
        scope: updatedTax.scope,
        country_code: updatedTax.country_code,
        is_active: updatedTax.is_active,
      },
      versions: updatedTax.versions,
    });
  },

  async list(filter) {
    const { company, ...rest } = filter;
    const query = { company, ...rest };
    const { taxes, meta } = await TaxRepo.listTaxes({ filter: query });

    return ResponseUtils.sendObjectResponse("Taxes retrieved successfully", { taxes, meta });
  },

  async view(filter) {
    const foundTax = await TaxRepo.getTax({
      filter,
      include: ["versions"],
    });

    if (!foundTax) throw new NotFoundError("Tax");

    return ResponseUtils.sendObjectResponse("Tax retrieved successfully", { tax: foundTax });
  },

  async listVersions(filter) {
    const foundTax = await TaxRepo.getTax({ filter });
    if (!foundTax) throw new NotFoundError("Tax");

    const versions = await TaxVersionRepo.listTaxVersions({ taxId: foundTax.id });

    return ResponseUtils.sendObjectResponse("Tax versions retrieved successfully", {
      tax: {
        code: foundTax.code,
        name: foundTax.name,
        type: foundTax.type,
        calculation_type: foundTax.calculation_type,
        scope: foundTax.scope,
        country_code: foundTax.country_code,
        is_active: foundTax.is_active,
      },
      versions,
    });
  },

  async deactivateTax(filter) {
    const foundTax = await TaxRepo.getTax({ filter });
    if (!foundTax) throw new NotFoundError("Tax");

    await TaxRepo.update({
      filter: { id: foundTax.id },
      data: { is_active: false },
    });

    return ResponseUtils.sendObjectResponse("Tax deactivated successfully");
  },

  async activateTax(filter) {
    const foundTax = await TaxRepo.getTax({ filter });
    if (!foundTax) throw new NotFoundError("Tax");

    await TaxRepo.update({
      filter: { id: foundTax.id },
      data: { is_active: true },
    });

    return ResponseUtils.sendObjectResponse("Tax activated successfully");
  },

  async calculateTax({ amount, taxes, calculationType = TAX_CALCULATION_TYPES.PARALLEL }) {
    if (!Array.isArray(taxes) || taxes.length === 0) {
      return { totalTax: 0, breakdown: [] };
    }

    const { totalTax, breakdown } = taxes.reduce(
      (acc, tax) => {
        const { rate, type } = tax;
        const taxAmount = type === TAX_TYPES.PERCENTAGE ? (acc.baseAmount * rate) / 100 : rate;

        acc.breakdown.push({
          tax_id: tax.id,
          name: tax.name,
          rate,
          type,
          base_amount: acc.baseAmount,
          tax_amount: taxAmount,
        });

        acc.totalTax += taxAmount;

        if (calculationType === TAX_CALCULATION_TYPES.SEQUENTIAL) {
          acc.baseAmount += taxAmount;
        }

        return acc;
      },
      { totalTax: 0, breakdown: [], baseAmount: amount }
    );

    return {
      totalTax,
      breakdown,
    };
  },

  async createTaxGroup(payload, user, company) {
    const { name } = payload;

    // Check if tax group with same name exists in the company
    const existingGroup = await TaxRepo.getTaxGroup({
      name,
      company
    });

    if (existingGroup) {
      throw new ExistsError("Tax group");
    }

    const taxGroup = await TaxRepo.createTaxGroup({
      ...payload,
      company,
      user,
    });

    return ResponseUtils.sendObjectResponse("Tax group created successfully", { taxGroup });
  },

  async getTaxGroup({ filter }) {
    const taxGroup = await TaxRepo.getTaxGroup(filter);
    return ResponseUtils.sendObjectResponse("Tax group retrieved successfully", { taxGroup });
  },

  async getTaxGroups(query) {
    const { taxGroups, meta } = await TaxRepo.findTaxGroups(query);
    return ResponseUtils.sendObjectResponse("Tax groups retrieved successfully", { taxGroups, meta });
  },

  async updateTaxGroup({ filter, payload, transaction }) {
    const taxGroup = await TaxRepo.getTaxGroup(filter);
    const updatedTaxGroup = await TaxRepo.updateTaxGroup(filter, payload, transaction);
    return ResponseUtils.sendObjectResponse("Tax group updated successfully", { taxGroup: updatedTaxGroup });
  },

  async addTaxToGroup({ code, tax, user, company, sequenceNumber }) {
    // First verify the tax group exists
    const taxGroup = await TaxRepo.getTaxGroup({ code, company });
    if (!taxGroup) {
      throw new NotFoundError("Tax group");
    }

    // Then verify the tax exists and belongs to the company
    const foundTax = await TaxRepo.getTax({
      filter: { code: tax, company },
    });
    if (!foundTax) {
      throw new NotFoundError("Tax");
    }

    // We also need to check that the tax does not already exist in the taxgroup
    const existingTaxes = await TaxRepo.listTaxesInAGroup(taxGroup.id);
    const taxAlreadyExists = existingTaxes.some(t => t.tax === foundTax.id);
    if (taxAlreadyExists) {
      throw new ExistsError("Tax");
    }

    const maxSequence = Math.max(...existingTaxes.map((t) => t.sequence_number), 0);
    
    // Validate the sequence number
    validateTaxSequenceNumber(sequenceNumber, existingTaxes);

    // Add the new tax with the desired sequence number
    const taxGroupTax = await TaxRepo.addTaxToGroup({
      tax_group: taxGroup.id,
      tax: foundTax.id,
      sequence_number: sequenceNumber || maxSequence + 1,
      user,
    });

    // Reorder all sequences to ensure they are sequential, starting from the insertion point
    await TaxRepo.reorderTaxGroupSequences(
      taxGroup.id, 
      sequenceNumber || maxSequence + 1,
      taxGroupTax.id
    );

    // Get the updated tax group with all taxes
    const updatedTaxGroup = await TaxRepo.getTaxGroup({ code, company });

    return ResponseUtils.sendObjectResponse("Tax added to group successfully", { 
      taxGroup: updatedTaxGroup,
      taxGroupTax 
    });
  },

  async removeTaxFromGroup({ code, tax, company }) {
    const sequelize = TaxGroupTax.sequelize;
    const transaction = await sequelize.transaction();

    try {
      const [taxGroup, foundTax] = await Promise.all([
        TaxRepo.getTaxGroup({ code, company }),
        TaxRepo.getTax({ filter: { code: tax, company } })
      ]);

      if (!taxGroup) {
        throw new NotFoundError("Tax group");
      }

      if (!foundTax) {
        throw new NotFoundError("Tax");
      }

      await TaxRepo.removeTaxFromGroup(taxGroup.id, foundTax.id);

      // After removal, reorder the remaining sequences to be sequential
      const remainingTaxes = await TaxRepo.listTaxesInAGroup(taxGroup.id);
      if (remainingTaxes.length > 0) {
        // Sort by current sequence and reassign sequences from 1
        const sortedTaxes = remainingTaxes.sort((a, b) => a.sequence_number - b.sequence_number);
        for (let i = 0; i < sortedTaxes.length; i++) {
          await TaxRepo.updateTaxGroupSequence({
            taxGroup: taxGroup.id,
            tax: sortedTaxes[i].tax,
            sequenceNumber: i + 1,
            transaction
          });
        }
      }

      await transaction.commit();
      return ResponseUtils.sendObjectResponse("Tax removed from group successfully");
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  // Tax Application Methods
  async createTaxApplication(payload) {
    const { company, user, applicableType, applicableId, taxVersion: taxVersionId, baseAmount } = payload;

    // Get the tax version to calculate tax amount
    const taxVersion = await TaxVersionRepo.getTaxVersion({ id: taxVersionId });
    if (!taxVersion) throw new NotFoundError("Tax version");

    // Calculate tax amount
    const taxAmount = taxVersion.type === TAX_TYPES.PERCENTAGE ? (baseAmount * taxVersion.rate) / 100 : taxVersion.rate;

    const applicationPayload = {
      applicable_type: applicableType,
      applicable_id: applicableId,
      tax_version: taxVersionId,
      base_amount: baseAmount,
      tax_amount: taxAmount,
      final_amount: baseAmount + taxAmount,
      created_by: user,
      company,
    };

    const taxApplication = await TaxRepo.createTaxApplication(applicationPayload);

    return ResponseUtils.sendObjectResponse("Tax application created successfully", { taxApplication });
  },

  async listTaxApplications(criteria) {
    const { applications, meta } = await TaxRepo.listTaxApplications(criteria);
    return ResponseUtils.sendObjectResponse("Tax applications retrieved successfully", { applications, meta });
  },

  async getTaxApplication(filter) {
    const application = await TaxRepo.getTaxApplication(filter);
    if (!application) throw new NotFoundError("Tax application");

    return ResponseUtils.sendObjectResponse("Tax application retrieved successfully", { application });
  },

  async listTaxApplicationsByApplicable(filter) {
    const { applications, meta } = await TaxRepo.listTaxApplicationsByApplicable(filter);
    return ResponseUtils.sendObjectResponse("Tax applications retrieved successfully", { applications, meta });
  },
};

module.exports = Service;
