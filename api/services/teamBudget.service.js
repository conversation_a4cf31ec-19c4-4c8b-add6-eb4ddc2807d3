const TeamBudgetRepo = require('../repositories/teamBudget.repo');
const TeamMemberRepo = require('../repositories/teamMember.repo');
const BudgetRepo = require('../repositories/budget.repo');
const ValidationError = require('../utils/validation-error');
const BudgetService = require('./budget');
const BeneficiaryService = require('./beneficiary.service');
const { STATUSES } = require('../models/status');
const { sequelize } = require('../models');


const Service = {
    async createTeamBudgets(team, company, budgetCodes, users) {
        const transaction = await sequelize.transaction();
        const foundBudgets = await BudgetRepo.getAllBudgets({ queryParams: { code: budgetCodes }, selectOptions: ['owner'], transaction });
        if(foundBudgets.length !== budgetCodes.length) throw new ValidationError(`Invalid budget code sent`);
        let beneficiariesCodes = [];
        
        if(users.length){
            // Checks if Users is an array of Benefifiary or Users
            if(String(users[0]).startsWith('bnf_')) beneficiariesCodes = users;
            else beneficiariesCodes = await BeneficiaryService.getBeneficiariesCodeWithUserCodes(company, users)

        }else {
                
            const { rows: teamMembers } = await TeamMemberRepo.getTeamMembers({ team: team.id, status: STATUSES.ACTIVE });
            beneficiariesCodes = Array.from(teamMembers).map(teamMember => teamMember.User.Beneficiary.code);
        }
        const data = foundBudgets.map(budget => {
            BudgetService.addBeneficiaries(budget.owner, budget, beneficiariesCodes);
            return {
                budget: budget.id,
                team: team.id,
            }
        })

        const teamBudgetResponse = await TeamBudgetRepo.createTeamBudgets({ data, transaction });
        await transaction.commit();
        return teamBudgetResponse;
    },

    async addBudgetsToTeam({ team, budgets }) {
        const { rows: teamMembers } = await TeamMemberRepo.getTeamMembers({ team, status: STATUSES.ACTIVE });
        const beneficiaryCodes = Array.from(teamMembers).map(teamMember => teamMember.User.Beneficiary.code);
        const data = beneficiaryCodes.length && Array.from(budgets).map(budget => {
            BudgetService.addBeneficiaries(budget.owner, budget, beneficiaryCodes);
            return {
                team,
                budget: budget.id
            }
        })

        return TeamBudgetRepo.createTeamBudgets({ data })

    },

    async removeBudgetsFromTeam({ team, budgets }) {
        const { rows: teamMembers } = await TeamMemberRepo.getTeamMembers({ team, status: STATUSES.ACTIVE });
        const userIds  = Array.from(teamMembers).map(teamMember => teamMember.User.id);
        userIds.length && Array.from(userIds).forEach(user => {
            BudgetService.removeBeneficiaryFromBudgets(budgets, user)
        })
        const budgetIds = Array.from(budgets).map(({ id }) => id);
        return TeamBudgetRepo.remove({ criteria: { budget: budgetIds, team } });
    },

    async addMemberToTeamBudgets({ team, user }){
        const teamBudgets = await TeamBudgetRepo.getTeamsBudgets({ filter: { team } });
        if(!teamBudgets) return;
        
        const budgets = Array.from(teamBudgets).map(teamBudget => teamBudget.Budget);
        return BudgetService.addBeneficiaryToBudgets(budgets, user)
    },
}

module.exports = Service;
