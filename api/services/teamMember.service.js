const { STATUSES } = require("../models/status");
const TeamMemberRepo = require("../repositories/teamMember.repo");
const TeamBudgetRepo = require("../repositories/teamBudget.repo");
const BudgetService = require("./budget");
const ValidationError = require("../utils/validation-error");

const Service = {
  async createTeamMembers(team, teamMembers, transaction = null) {
    const data = teamMembers.map((teamMember) => {
      return {
        team,
        ...teamMember,
      };
    });
    return TeamMemberRepo.createOrUpdateTeamMembers({ data, transaction });
  },

  async getUserTeamIds(filter) {
    const teamIds = await TeamMemberRepo.getUserTeams({ filter, selectOptions: ["team"] });
    return Array.from(teamIds).map(({ team }) => team);
  },

  async removeTeamMembers(team, teamMembersIds) {
    // REMOVE THE FROM ALL BUDGET IN THE TEAM
    let budgets = await TeamBudgetRepo.getTeamsBudgets({ filter: { team } });
    if (budgets.length) {
      budgets = Array.from(budgets).map(({ Budget }) => Budget);
      teamMembersIds.length && Promise.all(Array.from(teamMembersIds).map((user) => BudgetService.removeBeneficiaryFromBudgets(budgets, user)));
    }
    // REMOVE FROM TEAM
    const filter = { team, user: teamMembersIds };
    return TeamMemberRepo.updateTeamMembers({ filter, payload: { status: STATUSES.INACTIVE } });
  },

  async getTeamMembers(filter) {
    return TeamMemberRepo.getTeamMembers(filter);
  },

  async getTeamMembersUserCodes(filter) {
    const criteria = { ...filter, status: STATUSES.ACTIVE };
    const { rows: teamMembers } = await TeamMemberRepo.getTeamMembers(criteria);
    return Array.from(teamMembers).map((teamMember) => teamMember.User.code);
  },

  async filterMembersToAddAndRemove({ company, team, members, role }) {
    const { rows: teamMembers } = await TeamMemberRepo.getTeamMembers({ company, team, role, status: STATUSES.ACTIVE });
    const previousMembersUserCode = Array.from(teamMembers).map((teamMember) => teamMember.User.code);
    const membersToRemove = Array.from(previousMembersUserCode).filter((previousMember) => !members.includes(previousMember));
    const membersToAdd = Array.from(members).filter((member) => !previousMembersUserCode.includes(member));
    return { membersToRemove, membersToAdd };
  },

  async getTeamMember(filter) {
    const teamMember = await TeamMemberRepo.getTeamMember(filter);
    if (!teamMember) throw new NotFoundError("Team Member");
    return teamMember;
  },

  async addTeamMembersToBudgets(team, userIds) {
    let budgets = await TeamBudgetRepo.getTeamsBudgets({ filter: { team } });
    if (budgets.length) {
      budgets = Array.from(budgets).map(({ Budget }) => Budget);
      userIds.length && Promise.all(Array.from(userIds).map((user) => BudgetService.addBeneficiaryToBudgets(budgets, user)));
    }
  },

  async updateTeamMembers(team, userIds, payload, company) {
    const { status } = payload;
    let response;
    if (status === STATUSES.DELETED || status === STATUSES.INACTIVE) response = await Service.removeTeamMembers(team, userIds);

    if (status === STATUSES.ACTIVE) {
      const { rows: foundTeamMembers } = await TeamMemberRepo.getMembersAcrossTeams({ status: "active", company, user: userIds });
      if (foundTeamMembers.length)
        throw new ValidationError(`${foundTeamMembers[0].User.firstName} ${foundTeamMembers[0].User.lastName} already belongs to another team`);

      response = await TeamMemberRepo.updateTeamMembers({ filter: { team, user: userIds }, payload });
      await Service.addTeamMembersToBudgets(team, userIds);
    }

    return response;
  },
};
module.exports = Service;
