const { Op } = require("sequelize");
const { sequelize } = require("../models");
const { SYSTEM_ROLES } = require("../models/role");
const { STATUSES } = require("../models/status");
const { UserRepo, BudgetRepo } = require("../repositories/index.repo");
const TeamRepo = require("../repositories/team.repo");
const { ValidationError, NotFoundError } = require("../utils/error.utils");
const ExistsError = require("../utils/exists-error");
const Sanitizer = require("../utils/sanitizer");
const Utils = require("../utils/utils");
const BudgetService = require("./budget");
const TransactionService = require("./transaction");
const TeamBudgetRepo = require("../repositories/teamBudget.repo");

const Service = {
  /**
   * Creates a Team for a company
   * @param { object } data
   * @param { String } data.name Name of the comany
   * @param { String } data.description Description of the comppany
   * @param { Integer } data.company Company ID of the company this team belongs to
   * @returns
   */
  async createTeam(data, transaction = null) {
    const { name, company } = data;
    const filter = { name, company };
    const team = await TeamRepo.getTeam({ filter });
    if (team) throw new ExistsError("Team");
    return TeamRepo.createTeam({ data, transaction });
  },

  /**
   * List teams that fit the passed criteria
   * @param filters
   * @param filters.user the beneficiary attached to the team
   * @param filters.company the company that owns the budgets
   * @param filters.search any search string to search with
   * @param filters.from the team's creation date starting date
   * @param filters.to the teams's creation date ending date
   * @param filters.status the teams's status
   * @returns {Promise<{teams: ([]|*), meta: {total, perPage: number, nextPage: number, hasMore: boolean, page: number}}>}
   */
  async list(filters) {
    let { status, user, page = 1, perPage = 20, company, from, to, search, manager, member, budget } = filters;
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    const skip = Math.max(page - 1, 0);
    const criteria = { company, skip, limit: perPage, ...(user && { user }), ...(budget && { budget }) };

    if (search) criteria[Op.or] = [{ name: { [Op.like]: `%${search}%` } }, { description: { [Op.like]: `%${search}%` } }];

    if (status) {
      criteria.status = Array.isArray(status) ? status.map((val) => STATUSES[val.toUpperCase()]) : (criteria.status = STATUSES[status.toUpperCase()]);
    } else {
      criteria.status = {
        [Op.ne]: STATUSES.DELETED,
      };
    }

    if (from || to) {
      criteria.created_at = {};
      if (from && Utils.isValidDate(from)) {
        criteria.created_at[Op.gte] = from;
      }
      if (to && Utils.isValidDate(to)) {
        criteria["where"] = sequelize.where(sequelize.fn("date", sequelize.col("Team.created_at")), "<=", to);
      }
    }

    if (manager || member) {
      const user = await UserRepo.getOneUser({ queryParams: { code: manager || member } });
      if (!user) throw new NotFoundError("User");
      if (manager) criteria.manager = user.id;
      if (member) criteria.member = user.id;
    }

    if (budget) {
      const foundBudget = await BudgetRepo.getOneBudget({ queryParams: { code: budget } });
      if (!foundBudget) throw new NotFoundError("Budget");
      criteria.budget = foundBudget.id;
    }

    let { rows: teams, count: total } = await TeamRepo.getAllTeams({ filter: criteria, includeMembers: true });

    return {
      teams,
      meta: {
        page,
        total,
        perPage,
        nextPage: page + 1,
        hasMore: total > page * perPage,
      },
    };
  },

  async getTeamBudget(filter, includeBudget = false, includeTeam = false) {
    return TeamBudgetRepo.getTeamBudget({
      filter,
      includeBudget,
      includeTeam,
    });
  },

  /**
   * Get's a single team based on filter
   * @param filters
   * @param filters
   * @returns {Promise<{team: (object)}>}
   */
  async getTeam(filter) {
    if (typeof filter !== "object") throw new ValidationError(`Invalid filter`);

    const team = await TeamRepo.getTeam({ filter });
    if (!team) throw new NotFoundError(`Team`);
    return team;
  },

  /**
   * Gets budgets associated with an array of teams
   * @param { number[] } teamIds
   * @returns
   */
  async getTeamsBudgets(teamIds) {
    const teamBudgets = await TeamRepo.getTeamsBudgets(teamIds);
    return teamBudgets;
  },

  async getSingleTeamWithMembers(filter) {
    if (typeof filter !== "object") throw new ValidationError(`Invalid filter`);

    const team = await TeamRepo.getTeam({ filter, includeMembers: true });
    if (!team) throw new NotFoundError(`Team`);
    return team;
  },

  async update(filter, payload) {
    return TeamRepo.updateTeam({ filter, payload });
  },

  async overview(criteria) {
    const { company } = criteria;
    const team = await TeamRepo.getTeam({ filter: { id: criteria.team }, includeBudget: true });
    const chartTransactions = await Service.getTransactionsChart(criteria);
    const { transactions } = await TransactionService.listTransactions({ ...criteria, team: team.code });
    const spent = transactions.reduce((total, transaction) => total + transaction.amount, 0);

    return {
      team: Sanitizer.sanitizeTeam(team),
      chart: Utils.groupByKey(chartTransactions, "created_at"),
      transactions: Sanitizer.sanitizeTransactions(transactions),
    };
  },

  async getTransactionsChart(filters) {
    const { company } = filters;
    const { from, to, groupBy, currency, team } = Utils.buildAnalyticsFilters(filters);
    const transactions = await TransactionService.listTransactionsRaw(company, { from, to, groupBy, currency, team });
    return transactions.map((transaction) => ({
      ...transaction,
      amount: parseInt(transaction.amount, 10),
    }));
  },
};

module.exports = Service;
