const { Op } = require("sequelize");
const { ThirdPartyLog, CardIssuer } = require("../models");

module.exports = {
  async createLog({ externalIdentifier, company, event, payload, message, provider, providerType, statusCode, endpoint, method, response = {} }) {
    /* if (! (company && event && payload && provider && providerType)) {
            throw new ValidationError(
                'event, company, payload, provider and providerType are required'
            );
        } */
    try {
      await ThirdPartyLog.create({
        externalIdentifier,
        event,
        message,
        company,
        payload,
        provider,
        providerType,
        response,
        statusCode,
        endpoint,
        method,
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(error);
    }
  },

  updateLog(code, payload) {
    return ThirdPartyLog.update(payload, { where: { code } });
  },

  async list(filter = {}) {
    const { page = 1, perPage = 20, ...rest } = filter;

    const total = await ThirdPartyLog.count({
      where: rest,
    });

    const skip = (parseInt(page, 10) - 1) * perPage;

    return ThirdPartyLog.findAll({
      where: rest,
      include: [
        {
          model: CardIssuer,
          required: false,
        },
      ],
      offset: skip,
      limit: parseInt(perPage, 10),
      order: [["created_at", "DESC"]],
    });
  },

  getLog(criteria) {
    const { search, ...rest } = criteria;

    const filter = { ...rest };
    if (search) {
      filter[Op.or] = [
        {
          payload: { [Op.like]: `%${search}%` },
        },
      ];
    }
    return ThirdPartyLog.findOne({
      where: filter,
    });
  },

  async eventProcessed(eventId) {
    return ThirdPartyLog.findOne({
      where: {
        externalIdentifier: eventId,
      },
      attributes: ["id", "code", "event"],
    });
  },
};
