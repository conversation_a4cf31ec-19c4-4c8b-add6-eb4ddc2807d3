const dateFns = require("date-fns");
const { Op, QueryTypes } = require("sequelize");
const NotFoundError = require("../utils/not-found-error");
const Utils = require("../utils");
const TransferRepo = require("../repositories/transfer.repo");
const HelperService = require("./helper.service");
const RedisService = require("./redis");
const QueueService = require("./queue.service");

const { Transfer, sequelize, Status, Company } = require("../models");
const { STATUSES } = require("../models/status");
require("../utils/error.utils");

module.exports = {
  async listTransfers(filters) {
    let { page = 1, perPage = 50 } = filters;
    const { search, company, from, to } = filters;

    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    const skip = Math.max(page - 1, 0) * perPage;
    const criteria = {
      company,
    };

    if (from && Utils.isValidDate(from)) {
      criteria.createdAt[">="] = from;
    }
    if (to && Utils.isValidDate(to)) {
      criteria.createdAt["<="] = to;
    }

    if (search) {
      criteria[Op.or] = [{ description: { [Op.like]: `%${search}%` } }];
    }
    const [total = 0, transfers = []] = await Promise.all([
      Transfer.count({ where: criteria }),
      Transfer.findAll({
        where: criteria,
        include: [Status],
        offset: skip,
        limit: perPage,
        order: [["created_at", "DESC"]],
      }),
    ]);
    return {
      transfers,
      meta: {
        page,
        total,
        perPage,
        hasMore: total > page * perPage,
        nextPage: page + 1,
      },
    };
  },
  async getTransfer(transferCode, criteria) {
    const transfer = await Transfer.findOne({
      where: {
        code: transferCode,
        ...criteria,
      },
    });
    if (!transfer) throw new NotFoundError("Transfer not found");
    return transfer;
  },
  async createTransfer(payload) {
    return Transfer.create(payload);
  },
  async listTransfersRaw(company, filters) {
    const today = new Date();
    const { from = today, to = dateFns.subDays(today, 7), currency = "NGN", groupBy = "days" } = filters;
    const formatMap = {
      days: "%a",
      weeks: "Week %u",
      default: "%b",
    };
    const format = formatMap[groupBy] || formatMap.default;
    let query = `
        select sum(t.amount) as amount, t.currency, DATE_FORMAT(t.created_at, '${format}') as created_at
        from Transfers as t
        where t.company = $1 and t.status = $2
        `;
    const bindings = [parseInt(company, 10), STATUSES.SUCCESS];
    if (filters) {
      if (from && Utils.isValidDate(from)) {
        bindings.push(from);
        query = `${query} and t.created_at >= $${bindings.length}`;
      }
      if (to && Utils.isValidDate(to)) {
        bindings.push(to);
        query = `${query} and t.created_at <= $${bindings.length}`;
      }
      if (currency) {
        bindings.push(currency);
        query = `${query} and t.currency = $${bindings.length}`;
      }
    }
    query = `${query} group by DATE_FORMAT(t.created_at, '${format}'), t.currency limit 20`;
    return sequelize.query(query, {
      bind: bindings,
      type: QueryTypes.SELECT,
    });
  },
  async findAndCountAllTransfer({ conditions, paginate, inclusions, raw = false }) {
    let include = [];
    if (inclusions) {
      include = [Company];
    }
    const transfers = await Transfer.findAndCountAll({
      ...paginate,
      raw,
      where: conditions,
      include,
    });

    return {
      ...transfers,
      number_of_pages: Math.ceil(transfers.count / paginate.limit),
    };
  },
  async findAllTransfer({ conditions, inclusions, raw = false }) {
    let include = [];
    if (inclusions) {
      include = [Company];
    }
    const transfers = await Transfer.findAll({
      raw,
      where: conditions,
      include,
    });
    return transfers;
  },

  async updateTransfer(filter, payload) {
    return Transfer.update(payload, { where: filter });
  },

  async handleSQSBookTransfer(payload) {
    const { data, ProviderService } = payload;
    const { providerToUse, ...rest } = data;

    const foundTranfer = await TransferRepo.getTransfer({ filter: { reference: rest.reference, status: STATUSES.PENDING } });
    if (!foundTranfer) return foundTranfer;

    // check if balance reconciliation is happening simultaneously
    const externalIdentifier = data.senderId;
    const isCurrentlyReconciling = await RedisService.get(`balance_reconcile:${externalIdentifier}`);

    if (isCurrentlyReconciling) {
      const bookTransferPayload = data;

      const computedPayload = {
        data: bookTransferPayload,
        id: Utils.generateRandomString(17),
        path: `/transfers/bookTransfer/${foundTranfer.code}/process`,
        key: process.env.INTRA_SERVICE_TOKEN,
      };
      return QueueService.addDelayedJob({}, computedPayload, `BookTransferReference:${bookTransferPayload.reference}`, 5);
    }

    const { error, data: transferData } = await ProviderService[providerToUse].virtualAccount.bookTransfer(rest);
    // eslint-disable-next-line new-cap
    if (error) ProviderService[providerToUse].throwProviderError(transferData);
    return TransferRepo.update({
      filter: { id: foundTranfer.id },
      payload: { externalIdentifier: transferData.id },
    });
  },

  async getTotalReceivedOverDuration(company, filters = { currency: "NGN" }) {
    const bindings = [parseInt(company, 10), STATUSES.SUCCESS];
    let query;
    let groupByClause;
    let dateDiffInDays = null;

    if (filters.from && filters.to && Utils.isValidDate(filters.from) && Utils.isValidDate(filters.to)) {
      const fromDate = dateFns.parseISO(filters.from);
      const toDate = dateFns.parseISO(filters.to);
      dateDiffInDays = dateFns.differenceInDays(toDate, fromDate);
    }

    if (dateDiffInDays !== null && dateDiffInDays <= 30) {
      query = `SELECT DATE_FORMAT(t.created_at, "%a %D") AS weekday, currency, SUM(amount) AS totalAmount
            FROM Transfers as t
            WHERE t.amount > 0 AND t.company = $1 and (t.status = $2)`;
      groupByClause = `GROUP BY DATE_FORMAT(t.created_at, "%a %D"), t.currency`;
    } else {
      query = `SELECT YEAR(t.created_at) AS year, DATE_FORMAT(t.created_at, '%b') AS month, currency, SUM(amount) AS totalAmount
			FROM 
			Transfers as t
			WHERE t.amount > 0 AND t.company = $1 and (t.status = $2)`;
      groupByClause = `GROUP BY YEAR(t.created_at), DATE_FORMAT(t.created_at, '%b'), t.currency`;
    }

    if (filters) {
      if (filters.from && Utils.isValidDate(filters.from)) {
        bindings.push(Utils.formatDateWithStartTime(filters.from));
        query = `${query} and t.created_at >= $${bindings.length}`;
      }

      if (filters.to && Utils.isValidDate(filters.to)) {
        bindings.push(Utils.formatDateWithEndTime(filters.to));
        query = `${query} and t.created_at <= $${bindings.length}`;
      }

      if (filters.currency) {
        bindings.push(filters.currency);
        query = `${query} and t.currency = $${bindings.length}`;
      }
    }
    query = `${query} ${groupByClause}`;
    return sequelize.query(query, {
      bind: bindings,
      type: QueryTypes.SELECT,
    });
  },

  async getTopSources(company, filters = { currency: "NGN" }) {
    const { from: startDate, to: endDate, currency = "NGN" } = filters;
    const bindings = [parseInt(company, 10), STATUSES.SUCCESS, currency];

    let query = `
	 SELECT SUM(trf.amount) AS amount, trf.currency, cp.accountName FROM Transfers trf
	 INNER JOIN  CounterParties cp ON trf.counterParty = cp.id
	 WHERE trf.company = $1 AND trf.status = $2 AND trf.currency = $3
	 ${startDate ? HelperService.addDateToBaseQuery({ bindings, date: startDate, table: "trf", timestamp: "00:00:00", direction: "start" }) : ""}
	 ${endDate ? HelperService.addDateToBaseQuery({ bindings, date: endDate, table: "trf", timestamp: "23:59:59" }) : ""}
	  `;

    query = `${query} GROUP BY trf.counterParty, trf.currency, cp.accountName ORDER BY amount DESC LIMIT 10`;

    return sequelize.query(query, {
      bind: bindings,
      type: QueryTypes.SELECT,
    });
  },

  async totalReceived(company, filters = { currency: "NGN" }) {
    const { from: startDate, to: endDate, currency = "NGN" } = filters;
    const bindings = [parseInt(company, 10), STATUSES.SUCCESS];
    let query = `SELECT SUM(trf.amount) AS amount, trf.currency FROM Transfers trf WHERE trf.amount > 0 AND trf.company = $1 AND trf.status = $2`;

    if (currency) {
      bindings.push(currency);
      query = `${query} AND trf.currency = $${bindings.length}`;
    }

    if (startDate) {
      query = `${query} ${HelperService.addDateToBaseQuery({ bindings, date: startDate, table: "trf", timestamp: "00:00:00", direction: "start" })}`;
    }

    if (endDate) {
      query = `${query} ${HelperService.addDateToBaseQuery({ bindings, date: endDate, table: "trf", timestamp: "23:59:59" })}`;
    }

    query = `${query} GROUP BY trf.currency`;

    return sequelize.query(query, {
      bind: bindings,
      type: QueryTypes.SELECT,
    });
  },
};
