/* eslint-disable no-param-reassign */
const { Op, QueryTypes } = require("sequelize");
const ValidationError = require("../utils/validation-error");
const NotFoundError = require("../utils/not-found-error");
const {
  User,
  PhoneNumber,
  PasswordResetRequest,
  sequelize,
  Company,
  Status,
  Address,
  Role,
  PaymentPlan,
  Subscription,
  BillingHistory,
} = require("../models");
const NotificationService = require("./notification");
const AuthenticationService = require("./authentication");
const BeneficiaryService = require("./beneficiary.service");
const BudgetService = require("./budget");
const Helper = require("./helper.service");
const OTPService = require("./otp");

const UserValidator = require("../validators/user.validator");
const Utils = require("../utils");
const { STATUSES } = require("../models/status");
const UserRepo = require("../repositories/user.repo");
const AddressRepo = require("../repositories/address.repo");
const Redis = require("./redis");
const TooManyConnectionError = require("../utils/too-many-log-in-error");
const { findOrCreatePhoneNumber } = require("./helper.service");
const { RoleRepo, CompanyRepo, CardHolderRepo, PhoneNumberRepo } = require("../repositories/index.repo");
const Providers = require("./providers");
const ResponseUtils = require("../utils/response.utils");

const UserService = {
  validateLoginPayload(payload) {
    const { error } = UserValidator.login.validate(payload);
    if (error) throw new ValidationError(error.message);
    return {
      error: false,
      message: "Valid payload",
    };
  },
  validatePasswordResetPayload(payload) {
    const { error } = UserValidator.reset.validate(payload);
    if (error) throw new ValidationError(error.message);
    return {
      error: false,
      message: "Valid payload",
    };
  },
  getUserBaseCriteria({ company = null, email, phoneNumber }) {
    const criteria = {
      ...(company && typeof company !== "string" && { company }),
    };
    if (email) criteria.email = email;
    else {
      criteria["$PhoneNumber.countryCode$"] = phoneNumber.countryCode;
      criteria["$PhoneNumber.localFormat$"] = phoneNumber.localFormat;
    }
    return criteria;
  },
  async validateRegistrationPayload(payload) {
    const { email } = payload;
    const criteria = { email };
    const user = await User.findOne({
      where: criteria,
    });
    if (user) {
      const passwordResetRequest = await PasswordResetRequest.create({
        user: user.id,
      });
      const notificationPayload = {
        firstName: user.firstName,
        password_reset_url: `${Utils.getDashboardURL()}/reset_link/${passwordResetRequest.hash}`,
        hash: passwordResetRequest.hash,
      };

      NotificationService.notifyUser(user, "registered-user", notificationPayload);
      throw new ValidationError("An account with this email already exist, Please cheeck your email.");
    }
    return { error: false, message: "Valid payload" };
  },

  async validateUpdatePayload(payload) {
    const { error } = UserValidator.update.validate(payload);
    if (error) throw new ValidationError(error.message);
    return {
      error: false,
      message: "Valid payload",
    };
  },

  async validateNewBeneficiaryPasswordPayload(payload) {
    const { error } = UserValidator.setBeneficiaryPassword.validate(payload);
    if (error) throw new ValidationError(error.message);

    return { error: false, message: "Valid payload" };
  },

  async registerUser(data) {
    const { phoneNumber: userPhoneNumber, email, ...payload } = data;
    const hashedPassword = await AuthenticationService.encryptPassword(payload.password || Utils.generateRandomString());

    const newUser = await UserRepo.createAUser({
      queryParams: { ...payload, email, password: hashedPassword },
    });
    let phoneNumber = null;
    if (userPhoneNumber) {
      const phoneNumberPayload = { ...userPhoneNumber };
      phoneNumber = await PhoneNumberRepo.findOrCreate({
        queryParams: { ...phoneNumberPayload },
        defaults: { ...phoneNumberPayload },
      });
    }

    if (phoneNumber) await UserRepo.updateUser({ queryParams: { id: newUser.id }, updateFields: { phoneNumber: phoneNumber.id } });

    newUser.PhoneNumber = phoneNumber;

    if (!payload.password) return ResponseUtils.sendObjectResponse("User successfully created", newUser); // User is a beneficiary

    const otp = await OTPService.createOTPRedisCode(newUser);
    const { hash, code } = otp;

    // Send notification
    NotificationService.notifyUser({ email, phoneNumber }, "otp-verification", {
      dashboardUrl: `${Utils.getDashboardURL()}/verification?code=${code}`,
      firstName: newUser.firstName || "there",
      name: newUser.firstName || "there",
      code: code.split(""),
      hash,
    });
    return ResponseUtils.sendObjectResponse("User successfully created. Please verify your email", { newUser, hash });
  },

  async authenticate({ email, phoneNumber, password }) {
    await UserService.trackConnectionAttempt(email);
    const criteria = UserService.getUserBaseCriteria({ email, phoneNumber });
    const includeRequired = false;
    const user = await User.findOne({
      where: {
        ...criteria,
        status: { [Op.in]: [STATUSES.ACTIVE, STATUSES.PENDING, STATUSES.UNVERIFIED] },
      },
      include: [
        {
          model: PhoneNumber,
          required: includeRequired,
        },
        {
          model: User,
          required: false,
          as: "Manager",
        },
        Company,
        Status,
        Role,
      ],
      order: [["company", "DESC"]],
    });
    const message = `invalid ${email ? "email" : "phone number"} or password`;
    if (!user) {
      UserService.trackConnectionAttempt(email);
      throw new ValidationError(message);
    }
    if (user.Company && [STATUSES.INACTIVE, STATUSES.BLOCKED].includes(user.Company.status)) {
      throw new ValidationError("Account deactivated");
    }
    const validAccount = await AuthenticationService.comparePassword(password, user.password);
    if (!validAccount) {
      UserService.trackConnectionAttempt(email);
      throw new ValidationError(message);
    }
    // If successfull delete any attempt count
    const key = `auth:${email}`;
    Redis.delete(key);

    return user;
  },
  async fetchUserByCodeOrId(idOrCode) {
    return this.fetchUser(idOrCode);
  },
  async fetchUser(idOrCode) {
    const criteria = {};
    if (Number(idOrCode)) criteria.id = idOrCode;
    else {
      // Check cache here actually!!
      criteria.code = idOrCode;
    }
    const user = await User.findOne({
      where: criteria,
      include: [
        PhoneNumber,
        Status,
        Role,
        Address,
        {
          model: Company,
          include: [
            PaymentPlan,
            {
              model: Status,
              via: "active",
            },
          ],
        },
        {
          model: User,
          as: "Manager",
        },
      ],
    });
    if (!user) throw new NotFoundError("User");
    if (user.Company) user.Company.paymentPlan = user.Company.PaymentPlan;
    const paymentPlan = user.Company?.paymentPlan;
    if (paymentPlan) {
      const activeSubscription = await Subscription.findOne({
        where: {
          plan: paymentPlan.id,
          status: STATUSES.ACTIVE,
          company: user.company,
        },
        attributes: ["isFreeTrial", "additionalSeats"],
        include: [
          {
            model: BillingHistory,
            required: true,
            where: {
              status: STATUSES.PAID,
              company: user.company,
            },
            order: [["id", "DESC"]],
            limit: 1,
          },
        ],
      });

      const billingAddress = await Subscription.findOne({
        where: {
          address: {
            [Op.ne]: null,
          },
          company: user.company,
        },
        attributes: ["address", "state", "country", "companyName", "city", "firstName", "lastName"],
        order: [["created_at", "DESC"]],
      });

      const [billingHistory] = activeSubscription?.BillingHistories || [];

      const configuration = Utils.parseJSON(paymentPlan.configuration);
      const maxTransactionLimit = configuration?.payables?.maxTransactionLimit?.[paymentPlan.currency || "NGN"];

      user.Company.paymentPlan = {
        ...paymentPlan.toJSON(),
        configuration,
        expiryDate: billingHistory?.dueDate || null,
        paidOn: billingHistory?.created_at,
        billingAddress,
        isFreeTrial: Boolean(activeSubscription?.isFreeTrial),
        isNonPayingCompany: Boolean(maxTransactionLimit) && (!activeSubscription || activeSubscription?.isFreeTrial),
        additionalSeats: activeSubscription?.additionalSeats || 0,
        isActiveSubscription: Boolean(activeSubscription),
      };
    }
    if (user.role_id) {
      const userRole = await RoleRepo.getRoleWithPermissions(user.role_id);
      user.role = userRole.name;
      user.permissions = userRole.Permissions.map(({ name }) => name);
    }
    return user;
  },
  async updateUser(id, payload) {
    const user = await UserService.fetchUser(id);

    const { address = undefined, phoneNumber = undefined, old_password: oldPassword, ...remainingPayload } = payload;
    if (address && typeof address === "object") {
      // eslint-disable-next-line prefer-const
      let { id: addressId, ...rest } = address;
      const { data: foundCountry } = await Helper.CountryChecker({
        code: rest.country,
      });
      if (addressId || user.address) {
        addressId = addressId || user.address;
        await Address.update(rest, { where: { id: addressId } });
        delete payload.address;
      } else {
        const newAddress = await AddressRepo.createAnAddress({
          queryParams: {
            ...rest,
            country: foundCountry.name,
            address: user.id,
            countryIso: foundCountry.iso_code,
            company: user.company,
          },
        });
        remainingPayload.address = newAddress.id;
      }
    }

    if (phoneNumber && typeof phoneNumber === "object") {
      const { id: newPhoneNumber } = await findOrCreatePhoneNumber(phoneNumber);
      remainingPayload.phoneNumber = newPhoneNumber;
    } else remainingPayload.phoneNumber = phoneNumber;
    // if (phoneNumber && typeof phoneNumber === "object") {
    //   let { id: phoneNumberId, ...rest } = phoneNumber;
    //   if (phoneNumberId || user.phoneNumber) {
    //     phoneNumberId = phoneNumberId || user.phoneNumber;
    //     await PhoneNumber.update(rest, { where: { id: phoneNumberId } });
    //     delete payload.phoneNumber;
    //   } else {
    //     const newPhoneNumber = await PhoneNumber.create({ ...rest });
    //     payload.phoneNumber = newPhoneNumber.id;
    //   }
    // }

    if (oldPassword) {
      await UserService.internalResetPassword({ id, oldPassword, newPassword: remainingPayload.password });
      delete remainingPayload.password;
    }

    if ([STATUSES.PENDING, STATUSES.UNVERIFIED].includes(user.status)) {
      const expectedUser = {
        phoneNumber: payload.phoneNumber || user.phoneNumber,
        dob: payload.dob || user.dob,
        address: payload.address || user.address,
      };
      if (expectedUser.phoneNumber && expectedUser.address && expectedUser.dob) {
        remainingPayload.status = STATUSES.ACTIVE;
        BudgetService.activateBeneficiaryBudgets(user.id);
        BeneficiaryService.updateBeneficiary({ user: user.id }, STATUSES.ACTIVE);
      }
      const { Users: companyAdmins = [] } = (await CompanyRepo.getCompanyWithAdmins({ id: user.company }, true)) || {};
      Array.from(companyAdmins).forEach((admin) => {
        const notificationPayload = {
          company: user.company,
          user_id: admin.id,
          type: `info`,
          badge: `info`,
          title: "New teammate alert 🎉",
          message: `${Utils.toTitle(user.firstName)} ${Utils.toTitle(
            user.lastName
          )} has completed their onboarding process and now has access to their Bujeti dashboard.`,
          table: {
            code: user.code,
            entity: "User",
          },
          event: "kycCompleted",
        };

        NotificationService.saveNotification(notificationPayload);
      });
    }
    await User.update(remainingPayload, { where: { id } });
    return user;
  },

  async setNewBeneficiaryPassword(payload) {
    const { code, password } = payload;
    const user = await UserService.fetchUser(code);
    if (!user) throw new NotFoundError("User");
    if (user.status !== STATUSES.PENDING) throw new ValidationError(`Invalid or expired link`);
    const hashedPassword = await AuthenticationService.encryptPassword(password);
    await user.update({ password: hashedPassword }, { where: { id: user.id } });
    return user;
  },

  async resetPassword({ hash, oldPassword, newPassword }) {
    if (!hash) throw new ValidationError("This link is expired or invalid");
    const passwordResetRequest = await PasswordResetRequest.findOne({
      where: {
        hash,
        expirationDate: {
          [Op.gt]: new Date(),
        },
      },
      include: {
        model: User,
        attributes: ["id", "password", "firstName", "email"],
      },
    });
    if (!passwordResetRequest) throw new ValidationError("This link is expired or invalid");
    const { User: user } = passwordResetRequest;
    if (oldPassword) {
      const validOldPassword = await AuthenticationService.comparePassword(oldPassword, user.password);
      if (!validOldPassword) throw new ValidationError("Could not update your password, please try again");
    }
    const hashedPassword = await AuthenticationService.encryptPassword(newPassword);
    const updatedUser = await UserService.updateUser(user.id, {
      password: hashedPassword,
    });
    if (!updatedUser) throw new ValidationError("Could not update your password, please try again");

    passwordResetRequest.expirationTime = new Date();
    passwordResetRequest.save();

    // If successfull delete any attempt count
    const key = `auth:${user.email}`;
    Redis.delete(key);

    return {
      passwordUpdated: true,
      email: user.email,
      firstName: user.firstName,
    };
  },

  async internalResetPassword({ id, oldPassword, newPassword }) {
    if (!(oldPassword && newPassword)) throw new ValidationError("Please specify old password and new Password");
    const { data: user } = await Helper.UserChecker({ id, password: true });

    const validOldPassword = await AuthenticationService.comparePassword(oldPassword, user.password);
    if (!validOldPassword) throw new ValidationError("Could not update your password, please try again");

    const hashedPassword = await AuthenticationService.encryptPassword(newPassword);
    const updatedUser = await UserService.updateUser(user.id, {
      password: hashedPassword,
    });
    if (!updatedUser) throw new ValidationError("Could not update your password, please try again");

    return {
      passwordUpdated: true,
      email: user.email,
      firstName: user.firstName,
    };
  },

  async sendPasswordResetLink(user) {
    try {
      const passwordResetRequest = await PasswordResetRequest.create({
        user: user.id,
      });
      return NotificationService.notifyUser(user, "password-reset", {
        firstName: user.firstName || "there",
        reset_link: `${Utils.getDashboardURL()}/reset_link/${passwordResetRequest.hash}`,
        hash: passwordResetRequest.hash,
      });
    } catch (e) {
      throw new ValidationError("Reset Link could not be created");
    }
  },
  async sendPasswordChangedEmail(user) {
    try {
      return NotificationService.notifyUser(user, "password-changed", {
        firstName: user.firstName || "there",
        dashboard_link: Utils.getDashboardURL(),
      });
    } catch (e) {
      throw new ValidationError("Reset Link could not be created");
    }
  },
  // eslint-disable-next-line no-unused-vars
  async getUserStats(id, query) {
    const dbQuery =
      "select " +
      "sum(ub.amount)/100 as total_disbursed, " +
      "sum(ub.spent)/100 as total_spent, " +
      "sum(case when ub.status = 1 then 1 else 0 end) as active_budgets " +
      "from UserBudgets ub " +
      "join Budgets b on b.id = ub.budget " +
      "where ub.user = $1";
    const bindings = [parseInt(id, 10)];
    return sequelize.query(dbQuery, {
      bind: bindings,
      type: QueryTypes.SELECT,
    });
  },
  async fetchUserByEmailOrPhoneNumber(company, email, phoneNumber) {
    const criteria = UserService.getUserBaseCriteria({
      company,
      email,
      phoneNumber,
    });
    const includeRequired = phoneNumber && phoneNumber.localFormat;
    return User.findOne({
      where: { ...criteria, status: { [Op.ne]: STATUSES.DELETED } },
      include: [
        {
          model: PhoneNumber,
          required: includeRequired,
        },
        Company,
      ],
    });
  },

  // eslint-disable-next-line no-empty-function
  async getCompanyStats() {},

  async getUsers(criteria) {
    return UserRepo.getAllUsers({ queryParams: criteria });
  },
  async trackConnectionAttempt(email) {
    const isTest = !(Utils.isProd() && Utils.isStaging());
    if (isTest) return;
    const key = `auth:${email}`;
    // if failed attempt track attempt
    const attemptCount = parseInt(await Redis.get(key), 10) || 0;
    if (attemptCount >= 3) {
      NotificationService.notifyUser({ email }, "login-attempt");
      throw new TooManyConnectionError();
    } else if (attemptCount < 3) {
      Redis.incr(key, 1);
      Redis.expiresIn(key, 3600);
    } else {
      Redis.setex(key, 0, 3600);
      Redis.incr(key, 1);
    }
  },

  /**
   * This function is meant to update Third party providers user details when invoked
   * It can be invoked when users update their profile.. e.g Phone number or address and we want to update it in other providers
   * @param { id } id The user id on Bujeti
   * @param { payload } payload An  Object of what you want to update
   */
  async updateUserProviderDetails({ id, payload }) {
    const cardHolderPayload = {};
    const { phoneNumber = null } = payload;
    if (phoneNumber) {
      const foundPhoneNumber = await PhoneNumberRepo.getOnePhoneNumber({
        queryParams: { id: phoneNumber },
      });
      if (!foundPhoneNumber) throw new NotFoundError("Phone Number");
      cardHolderPayload.phoneNumber =
        foundPhoneNumber.internationalFormat || Utils.formatPhoneNumber(foundPhoneNumber.localFormat, foundPhoneNumber.countryCode);
    }
    // Get all providers
    const [cardHolders] = await Promise.all([CardHolderRepo.getDistinctCardHolder({ filter: { user: id, status: STATUSES.ACTIVE } })]);

    // Update all Card Holders
    if (cardHolders && Object.keys(cardHolderPayload).length) {
      await Promise.all(
        Array.from(cardHolders).map((cardHolder) => {
          const {
            externalIdentifier,
            company,
            CardIssuer: { name: providerToUse },
          } = cardHolder;
          return Providers[String(providerToUse).toLowerCase()].updateCardHolder({ externalIdentifier, payload: cardHolderPayload, company });
        })
      );
    }
  },
  killSession(sessionId, expirationDate) {
    const currentTime = new Date().getTime() / 1000;
    const duration = Math.round(parseInt(expirationDate, 10) - currentTime);
    Redis.setex(sessionId, 1, duration);
  },
};

module.exports = UserService;
