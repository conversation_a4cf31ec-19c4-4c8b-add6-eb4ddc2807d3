const {industryRepo, CountryRepo, UserBudgetRepo} = require("../repositories/index.repo");
const responseUtils = require("../utils/response.utils");
const { ValidationError, HttpException, HttpStatus, NotFoundError } = require("../utils/error.utils");
const { UserBudgetValidator } = require("../validators");
const { BusinessSizeMock, UserIdTypeMock, CompanyTypeMock } = require("../mocks/index");
const Helper = require("./helper.service");

const Service = {
    async getUserBudget(payload) {
        if (payload.budget) if (!payload.budget.includes('bdg_')) throw new ValidationError('Not a Budget code');

        const { error } = UserBudgetValidator.getAllUserBudget.validate({ ...payload });
        if (error) throw new ValidationError(error.message);

        await Helper.UserChecker({ id: payload.employee });
        let budget;
        if (payload.budget) budget = await Helper.BudgetChecker({ budget_code: payload.budget });

        const UserBudget = await UserBudgetRepo.getAllUserBudgets({ 
            queryParams: { user : payload.employee, ...(budget && { budget: budget.data.id }), ...( payload.status && { status: payload.status } ) },
            selectOptions: ['amount','spent', 'id'],
            available: true,
        });
        if (!UserBudget) throw new NotFoundError('Something went wrong'); 

        return responseUtils.sendObjectResponse('User budget successfully retrieved', UserBudget);
    },

    async getAUserBudget({ id, country_code }) {
        const { error } = UserBudgetValidator.getAllUserBudget.validate({ id, country_code });
        if (error) throw new ValidationError(error.message);

        await Helper.UserChecker({ id: payload.employee });
        // await Helper.BudgetChecker({ id: payload.employee });
        const UserBudget = await UserBudgetRepo.getAllUserBudgets({ 
            queryParams: { user : payload.employee },
            selectOptions: ['amount','spent'],
            available: true,
        });
        if (!UserBudget) throw new NotFoundError('Something went wrong'); 
        
        return responseUtils.sendObjectResponse('Country successfully retrieved', country);
    },

}

module.exports = Service;
