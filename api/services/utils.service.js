const { where, fn, col, Op } = require("sequelize");
const { CompanyLookupRepo } = require("../repositories/index.repo");
const responseUtils = require("../utils/response.utils");
const Providers = require("../services/providers");
const { NotFoundError } = require("../utils/error.utils");

const UtilsService = {
  async getCompanyLookup(query) {
    let foundCompanies = await CompanyLookupRepo.listCompanies({
      queryParams: {
        [Op.or]: [
          {
            name: where(fn("LOWER", col("name")), "LIKE", `%${query.toLowerCase()}%`),
          },
          {
            rcNumber: query,
          },
        ],
      },
    });
    if (foundCompanies.length) return responseUtils.sendObjectResponse("Company lookup successful", foundCompanies);
    const { data } = await Providers.mono.company.searchCompany(query);
    const formattedCompanyList = Array.from(data).map((company) => {
      const {
        branch_address: branchAddress,
        registration_date: registrationDate,
        email,
        state,
        city,
        id: externalIdentifier,
        address,
        approved_name: approvedName,
        rc_number: rcNumber,
      } = company;
      return {
        name: approvedName,
        externalIdentifier,
        state,
        address,
        branchAddress,
        rcNumber,
        email,
        registrationDate,
        city,
      };
    });
    foundCompanies = await CompanyLookupRepo.bulkCreateCompanyLookUp({
      data: formattedCompanyList,
    });
    return responseUtils.sendObjectResponse("Company lookup successful", foundCompanies);
  },

  async getLookedUpCompany(filter) {
    const foundLookedUpCompany = await CompanyLookupRepo.getCompany({ queryParams: filter });
    if (!foundLookedUpCompany) throw new NotFoundError("Selected Company");
    return responseUtils.sendObjectResponse("Company retrieved", foundLookedUpCompany);
  },
};

module.exports = UtilsService;
