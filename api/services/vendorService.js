const { Op } = require("sequelize");
const dateFns = require("date-fns");
const ValidationError = require("../utils/validation-error");
const VendorValidator = require("../validators/vendor");
const { PhoneNumber, Address, Vendor, Status, Company, VendorCategory, Category, BankAccount } = require("../models");
const { TransactionRepo, CategoryRepo, BankAccountRepo, VendorRepo, BillRepo, CompanyRepo } = require("../repositories/index.repo");
const { STATUSES } = require("../models/status");
const NotFoundError = require("../utils/not-found-error");
const responseUtils = require("../utils/response.utils");
const Utils = require("../utils/utils");
const { ExistsError } = require("../utils/error.utils");
const NotificationService = require("./notification");
const { default: parsePhoneNumberFromString } = require("libphonenumber-js");
const { findOrCreatePhoneNumber } = require("./helper.service");
const { VENDORS } = require("../mocks/constants.mock");
const { getDashboardURL } = require("../utils/utils");
const { syncVendorToZoho } = require("../utils/zoho.utils");

const Service = {
  validateVendorCreationPayload(payload) {
    const { error, value } = VendorValidator.create.validate(payload, {
      allowUnknown: true,
    });
    if (error) throw new ValidationError(`${error.message}`);
    return { error: false, status: false, message: "Valid payload", value };
  },
  validateVendorsBulkCreationPayload(payload) {
    const { error } = VendorValidator.bulkCreate.validate(payload, {
      allowUnknown: true,
    });
    if (error) throw new ValidationError(`${error.message}`);
    return { error: false, status: false, message: "Valid payload" };
  },
  validateVendorUpdatePayload(payload) {
    const { error } = VendorValidator.update.validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: false, message: "Valid payload" };
  },

  validateVendorMassUpdatePayload(payload) {
    const { error } = VendorValidator.massUpdate.validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: false, message: "Valid payload" };
  },
  validateSyncZoho(payload) {
    const { error } = VendorValidator.syncZoho.validate(payload);
    if (error) throw new ValidationError(error.message);
    return { error: false, status: false, message: "Valid payload" };
  },
  async createVendor(payload) {
    const { bankAccount, ...rest } = payload;

    const vendor = await Vendor.findOne({
      where: { name: payload.name, company: payload.company },
    });
    if (vendor) throw new ExistsError("Vendor");
    rest.hash = Utils.generateUniqueId(Utils.alphabet.alphanumeric, 32);
    const createdVendor = await Vendor.create(rest);

    if (bankAccount) {
      await BankAccountRepo.createABankAccount({
        queryParams: {
          number: bankAccount.number,
          accountName: bankAccount.accountName,
          bankName: bankAccount.bankName,
          bankCode: bankAccount.bankCode,
          currency: bankAccount.currency,
          ownerType: "vendor",
          owner: createdVendor.id,
          type: "real",
          subtype: "deposit",
          company: payload.company,
          routingNumber: bankAccount.routingNumber,
          accountType: bankAccount.accountType,
          routingType: bankAccount.routingType,
          beneficiaryAddress: bankAccount.beneficiaryAddress,
          bankAddress: bankAccount.bankAddress,
          wireType: bankAccount.wireType,
        },
      });
    }

    return createdVendor;
  },

  async createVendorsBulk(company, data) {
    const createdVendors = await Promise.all(
      data.map(async ({ phoneNumber, bankName, accountNumber, currency, ...rest }) => {
        const vendorPayload = {
          ...rest,
          company,
        };
        if (phoneNumber) {
          /* parse number */
          const parsedPhoneNumber = parsePhoneNumberFromString(phoneNumber);
          // save phone number
          const phoneOnDatabase = await findOrCreatePhoneNumber({
            localFormat: parsedPhoneNumber.nationalNumber,
            countryCode: parsedPhoneNumber.countryCallingCode,
          });
          vendorPayload.phoneNumber = phoneOnDatabase.id;
        }
        const [newVendor] = await Vendor.findOrCreate({ where: vendorPayload });
        // if bank account data avail, save to DB incomplete is fine
        // needs to be resolved during payment and refresh post resolution
        if (bankName && accountNumber) {
          await BankAccountRepo.createABankAccount({
            queryParams: {
              number: accountNumber,
              accountName: rest.name,
              bankName,
              ownerType: "vendor",
              owner: newVendor.id,
              type: "real",
              subtype: "deposit",
              currency,
              company,
            },
          });
        }
        return newVendor;
      })
    );
    return createdVendors;
  },

  async listVendors(criteria) {
    const { from, to, name, company, status = "active", perPage = 50, page = 1, search, categories = [], ...filters } = criteria;

    const skip = (parseInt(page, 10) - 1) * parseInt(perPage, 10);
    filters.ignoreable = false;

    if (status) filters.status = Utils.getStatusValues(status);
    if (name) filters.name = { [Op.like]: `%${name}%` };
    if (search) {
      filters[Op.or] = {
        name: { [Op.like]: `%${search}%` },
        description: { [Op.like]: `%${search}%` },
      };
    }
    filters.company = { [Op.or]: [null, company] };
    const includeCategories = {
      required: false,
      model: VendorCategory,
      as: "categories",
      include: {
        model: Category,
      },
      where: { status: STATUSES.ACTIVE },
    };
    if (categories.length > 0) {
      includeCategories.include.where = {
        code: { [Op.in]: categories },
      };
    }

    if (categories.length > 0) {
      includeCategories.include.where = {
        ...includeCategories.include.where,
        code: categories,
      };
    }

    const modelsToInclude = [
      Company,
      Status,
      Address,
      {
        model: PhoneNumber,
        required: false,
      },
      includeCategories,
      {
        model: BankAccount,
        attributes: [
          "id",
          "code",
          "bankCode",
          "number",
          "bankName",
          "currency",
          "balance",
          "status",
          "meta",
          "routingType",
          "routingNumber",
          "accountType",
          "beneficiaryAddress",
          "bankAddress",
          "wireType",
        ],
        where: { type: "real", ownerType: "vendor" },
        required: false,
      },
    ];

    if (from || to) filters.createdAt = {};
    if (from) filters.created_at[Op.gte] = from;
    if (to) filters.created_at[Op.lte] = to;
    const [total = 0, vendors = []] = await Promise.all([
      Vendor.count({ where: { ...filters }, include: modelsToInclude }),
      Vendor.findAll({
        where: { ...filters },
        include: modelsToInclude,
        order: [
          ["status", "ASC"],
          ["id", "DESC"],
        ],
        offset: skip,
        limit: parseInt(perPage, 10),
      }),
    ]);
    return {
      meta: {
        total,
        page: parseInt(page, 10),
        perPage: parseInt(perPage, 10),
        hasMore: total > page * parseInt(perPage, 10),
        nextPage: total > page * parseInt(perPage, 10) ? parseInt(page, 10) + 1 : null,
      },
      vendors,
    };
  },

  async getVendor(criteria, { includeBankAccount = false } = {}) {
    const include = [
      Company,
      Status,
      Address,
      {
        required: false,
        model: VendorCategory,
        as: "categories",
        include: Category,
        where: { status: STATUSES.ACTIVE },
      },
    ];

    if (includeBankAccount) {
      include.push({
        model: BankAccount,
        attributes: [
          "id",
          "code",
          "bankCode",
          "number",
          "bankName",
          "currency",
          "balance",
          "status",
          "meta",
          "routingType",
          "routingNumber",
          "accountType",
          "beneficiaryAddress",
          "bankAddress",
          "wireType",
        ],
        where: { type: "real", ownerType: "vendor" },
        required: false,
      });
    }
    const vendor = await Vendor.findOne({
      where: criteria,
      include,
    });
    if (!vendor) throw new NotFoundError("Vendor");
    return vendor;
  },

  async updateVendor(criteria, payload) {
    const { bankAccount, isVendorPortalRequest, ...rest } = payload;

    const foundVendor = await Vendor.findOne({
      where: criteria,
    });

    if (foundVendor) {
      await foundVendor.update(rest);

      if (bankAccount) {
        const vendorHasBankAccount = await BankAccountRepo.getOneBankAccount({
          queryParams: {
            number: bankAccount.number,
            ownerType: "vendor",
            owner: foundVendor.id,
            status: {
              [Op.ne]: STATUSES.DELETED,
            },
          },
        });

        if (!vendorHasBankAccount) {
          await BankAccountRepo.createABankAccount({
            queryParams: {
              number: bankAccount.number,
              accountName: bankAccount.accountName,
              bankName: bankAccount.bankName,
              bankCode: bankAccount.bankCode,
              currency: bankAccount.currency,
              ownerType: "vendor",
              owner: foundVendor.id,
              type: "real",
              subtype: "deposit",
              company: foundVendor.company,
            },
          });

          // notify company if they wanna pay vendor or not;
          const shouldNotifyCompanyViaEmail = isVendorPortalRequest;

          const oustandingBillsToPay = await BillRepo.countBills({
            filter: {
              vendor: foundVendor.id,
              status: [STATUSES.PENDING, STATUSES.APPROVED],
            },
          });

          if (!oustandingBillsToPay) return null;

          if (!shouldNotifyCompanyViaEmail) {
            const billsToPay = await BillRepo.fetchBills({
              filter: {
                vendor: foundVendor.id,
                status: [STATUSES.PENDING, STATUSES.APPROVED],
              },
              meta: {
                limit: 5,
                order: [["created_at", "DESC"]],
                attributes: ["code", "amount", "dueDate", "balanceDue", "currency"],
              },
            });
            return {
              prompt: {
                bills: {
                  data: billsToPay,
                  count: oustandingBillsToPay,
                },
              },
            };
          }

          // send email notification
          const companyWithAdmin = await CompanyRepo.getCompanyWithAdmins({ id: foundVendor.company }, true);

          const admins = companyWithAdmin?.Users || [];

          const notificationPayload = {
            login: `${getDashboardURL()}/login`,
            companyName: companyWithAdmin?.name,
            dashboardUrl: Utils.getDashboardURL(),
            vendorName: foundVendor.name,
          };

          Array.from(admins).forEach((admin) => {
            NotificationService.notifyUser(admin, "vendor-bank-account-added", notificationPayload);

            const systemNotificationPayload = {
              company: foundVendor.company,
              user_id: admin.id,
              type: `info`,
              badge: `info`,
              title: `Vendor Bank Account Added`,
              message: `A new bank account has been added to ${foundVendor.name}'s profile. You can now pay them`,
              table: {
                code: foundVendor.code,
                entity: "Vendor",
              },
              event: "vendorBankAccountAdded",
            };

            NotificationService.saveNotification(systemNotificationPayload);
          });
        }
      }
    }
    return null;
    // if (!updatedVendor) throw new NotFoundError("Vendor");
  },

  async disableVendor(criteria) {
    const payload = {
      status: STATUSES.DELETED,
    };
    return Service.updateVendor(criteria, payload);
  },

  async activateVendor(criteria) {
    const payload = {
      status: STATUSES.ACTIVE,
    };
    return Service.updateVendor(criteria, payload);
  },

  /**
   * get statistics of transactions by vendor
   * @param {Object} criteria
   * @param {Object} payload
   * @returns
   */
  async vendorTransactionAnalytics(criteria) {
    const today = new Date();
    const { to = today, from = dateFns.subDays(today, 30), ...query } = criteria;
    const vendorTransactionAnalytics = await TransactionRepo.analysisOfTransactionByVendor({
      queryParams: {
        ...query,
      },
      from,
      to,
    });
    const message = "Vendors breakdown successfully retrieved";
    if (!vendorTransactionAnalytics.length) {
      return responseUtils.sendObjectResponse(message, {
        highestTransactionCount: null,
        vendorTransactionAnalytics: [],
        highestTransactionVolume: null,
      });
    }

    const highestTransactionVolume = vendorTransactionAnalytics[0];
    const highestTransactionCount = Utils.maxElementFromAnArray(vendorTransactionAnalytics, "transactionCount");

    return responseUtils.sendObjectResponse(message, {
      highestTransactionCount,
      vendorTransactionAnalytics,
      highestTransactionVolume,
    });
  },
  async createVendorCategories(vendor, categories) {
    if (!(vendor && vendor.id && categories && categories.length)) return;
    // eslint-disable-next-line consistent-return
    return VendorCategory.bulkCreate(categories.map((category) => ({ category, vendor: vendor.id })));
  },

  async createVendorCategory(vendor, category) {
    if (!(vendor && category)) return;
    // eslint-disable-next-line consistent-return
    return VendorCategory.create({ category, vendor });
  },

  async updateVendorCategories(vendor, categories) {
    if (!(vendor && vendor.id && categories && categories.length)) return;
    const existingCategories = await VendorCategory.findAll({
      where: { vendor: vendor.id, category: { [Op.in]: categories } },
    });

    if (existingCategories && existingCategories.length) {
      const inactiveCategories = existingCategories.filter((category) => category.status === STATUSES.INACTIVE);
      if (inactiveCategories.length)
        // eslint-disable-next-line consistent-return
        return VendorCategory.update(
          { status: STATUSES.ACTIVE },
          {
            where: {
              vendor: vendor.id,
              category: {
                [Op.in]: inactiveCategories.map(({ category }) => category),
              },
            },
          }
        );
      return;
    }

    // eslint-disable-next-line consistent-return
    await VendorCategory.update(
      { status: STATUSES.INACTIVE },
      {
        where: {
          vendor: vendor.id,
          category: {
            [Op.notIn]: categories,
          },
        },
      }
    );
    return VendorCategory.bulkCreate(categories.map((category) => ({ category, vendor: vendor.id })));
  },
  /**
   * List of category codes
   * @param {*} categories
   */
  async getVendorCategoriesIds(categories) {
    if (!(categories && categories.length)) return [];
    const categoriesFound = await CategoryRepo.listCategories({
      queryParams: { code: categories },
    });
    return categoriesFound.map(({ id }) => id);
  },

  async inviteVendor(vendor, company) {
    return NotificationService.notifyUser(
      { email: vendor.email },
      "vendor-invitation",
      {
        name: vendor.name,
        companyName: company.name,
        inviteUrl: `${Utils.getDashboardURL().replace("dashboard", "vendors")}/invite/${vendor.hash}`,
      },
      { subject: `${company.name} has invited you to onboard on Bujeti for a better collaboration` }
    );
  },

  async massUpdateVendors({ vendors, action, company }) {
    const { id: companyId } = company;

    const actionStatusMap = {
      [VENDORS.ACTIONS.DELETE]: STATUSES.DELETED,
      [VENDORS.ACTIONS.BLOCK]: STATUSES.BLOCKED,
    };

    const foundVendors = await VendorRepo.getVendors({
      queryParams: {
        code: vendors,
        company: companyId,
      },
    });

    if (foundVendors.length !== vendors.length) {
      throw new ValidationError(`Invalid vendor code sent`);
    }

    const promises = foundVendors.map((vendor) => {
      return vendor.update({ status: actionStatusMap[action] });
    });

    return Promise.all(promises);
  },

  async syncZoho({ codes, company }) {
    const foundVendors = await VendorRepo.getVendors({
      queryParams: {
        code: codes,
        company,
      },
    });

    if (!foundVendors.length) return null;

    const promises = foundVendors.map((vendor) => {
      return syncVendorToZoho(vendor.code, company);
    });

    return Promise.all(promises);
  },
};

module.exports = Service;
