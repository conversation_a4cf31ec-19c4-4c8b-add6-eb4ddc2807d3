// const CompanyService = require("./company");
const MonoService = require("./mono");
// const BudgetService = require("./budget");
// const BeneficiaryService = require("./beneficiary.service");
const { DocumentRepo, CompanyRepo, IndividualRepo } = require("../repositories/index.repo");
const { Op } = require("sequelize");
const { MonoIdentityMock } = require("../mocks/index");
const NotFoundError = require("../utils/not-found-error");
const { format } = require("date-fns");
const { log, Log } = require("../utils/logger.utils");
const Provider = require("./providers");
const { ValidationError } = require("../utils/error.utils");

const Service = {
  startVerification(params) {},
  /**
   *
   * @param {*} payload
   * @returns
   */
  async concludeVerification(payload) {
    const { company: companyCode, companyId, provider = "mono" } = payload;
    const queryParams = {
      ...(companyId && { id: companyId }),
      ...(companyCode && { code: companyCode }),
    };
    const existingCompany = await CompanyRepo.getCompany({
      queryParams,
      addIndustry: true,
    });
    if (!existingCompany) throw new NotFoundError("Company");

    if (provider === "anchor") {
      const customerPayload = await Provider[provider].customer.getPayloadForBusinessCustomer(existingCompany.id);
      const { error, data } = await Provider[provider].customer.createBusinessCustomer(existingCompany.id, customerPayload);
      if (error) throw new ValidationError(`Provider error: ${data.errors.map(({ detail }) => detail || "").join("\n")}`);
      return Provider[provider].customer.verifyBusinessCustomer(data.externalIdentifier);
    }

    const company = existingCompany.dataValues;
    const directorsBvn = {
      reference: company.document_reference,
      table_type: "individual",
      type: "bvn",
      number: { [Op.not]: null },
    };
    const businessCAC = {
      reference: company.document_reference,
      table_type: "business",
      number: { [Op.not]: null },
      type: { [Op.or]: ["rcNumber", "bnNumber", "cac"] },
    };
    const directorsIdNumber = {
      reference: company.document_reference,
      table_type: "individual",
      number: { [Op.not]: null },
      type: { [Op.not]: "bvn" },
    };

    let selectOptions = [
      "code",
      "type",
      "number",
      "url",
      "country",
      "status",
      "table_type",
      "table_id",
      "metadata",
      "reference",
      "type",
      "issuing_date",
    ];
    const [docsBvn, docs, docsCAC] = await Promise.all([
      DocumentRepo.getOneDocument({ selectOptions, queryParams: directorsBvn }),
      DocumentRepo.getOneDocument({
        selectOptions,
        queryParams: directorsIdNumber,
      }),
      DocumentRepo.getOneDocument({ selectOptions, queryParams: businessCAC }),
    ]);

    if (provider === "mono") {
      const directorId = (company.director && company.director.id) || (company.Director && company.Director.id) || company.director;
      const companyDirector = await IndividualRepo.getCompanyDirectorFullDetails({
        queryParams: { id: directorId },
      });

      const { firstName: director_first_name, lastName: director_last_name, ...rest } = companyDirector;
      const businessDetails = {
        business_type: MonoService.businessIdentity[company.businessType],
        industry_type: MonoService.industryType[company.Industry.name],
        ...(docsCAC && {
          rc_number: String(docsCAC.number).toLowerCase().replace("rc", ""),
          registration_date: format(new Date(docsCAC.issuing_date), "dd-MM-yyyy"),
        }),
        company_name: company.name,
        director_first_name,
        director_last_name,
      };

      const nameArray = company.name.split(" ");
      const companyFirstName = nameArray.shift();
      return MonoService.createAccountHolder({
        user: {
          firstName: companyFirstName,
          lastName: nameArray.join(" "),
          ...rest,
          bvn: docsBvn.number,
        },
        company,
        identity: {
          type: MonoIdentityMock[docs.type],
          number: docs.number,
          url: docs.url,
        },
        business_details: businessDetails,
        subType: "main",
      });
    }
    return {
      error: true,
      message: "Unknown provider",
    };
  },
};

module.exports = Service;
