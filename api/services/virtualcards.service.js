/* eslint-disable no-underscore-dangle */
/* eslint-disable camelcase */
const { Op, QueryTypes } = require("sequelize");
const providers = require("./providers");
const { sequelize, Transaction } = require("../models");
const {
  VirtualCardRepo,
  CompanyRepo,
  BudgetRepo,
  BankAccountRepo,
  UserRepo,
  UserBudgetRepo,
  CardHolderRepo,
  PhoneNumberRepo,
  BudgetLedgerRepo,
  TransferRepo,
  CategoryRepo,
  BeneficiaryRepo,
  MccRepo,
  CardPolicyRepo,
  BalanceRepo,
  BudgetAccountRepo,
  BalanceLedgerRepo,
  CardAssignmentRepo,
  TransactionAttemptRepo,
} = require("../repositories/index.repo");
const TransferService = require("./transfer");
const BudgetService = require("./budget");
const Settings = require("./settings");
const { STATUSES } = require("../models/status");
const { CARD_ISSUER } = require("../models/cardissuer");
const { NotFound<PERSON>rror, Exists<PERSON><PERSON>r, HttpException, HttpStatus } = require("../utils/error.utils");
const { formatPhoneNumber, getCardCodeFromHash, getCardHash } = require("../utils");
const userRepo = require("../repositories/user.repo");
const ChargeService = require("./charge");
const RedisService = require("./redis");
const NotificationService = require("./notification");
const { getStatusById } = require("../utils/sanitizer");
const responseUtils = require("../utils/response.utils");
const Utils = require("../utils/utils");
const QueueService = require("./queue.service");
const UserService = require("./user");
const ValidationError = require("../utils/validation-error");
const HelperService = require("./helper.service");
const { ONBOARDING_LEVEL } = require("../models/company");
const BalanceService = require("./balance");
const Sanitizer = require("../utils/sanitizer");
const PolicyService = require("./policy.service");

class VirtualCardService {
  static async createCard(payload) {
    const {
      company,
      amount,
      budget: budgetCode,
      balance: balanceCode,
      user,
      currency,
      team,
      name,
      type,
      beneficiary: beneficiaryCode,
      issuer,
      settings,
      paymentPlan,
    } = payload;

    const lowerCasedCurrency = String(currency).toLowerCase();
    const { card_issuing: cardIssuing } = Settings.get("providers");
    const { defaultIssuer } = cardIssuing[lowerCasedCurrency];
    const cardIssuer = (issuer || defaultIssuer).toLowerCase();

    let foundBeneficiary;

    let budget;
    let balance;
    let availableBalance;
    if (budgetCode) {
      budget = await BudgetRepo.getOneBudget({
        queryParams: {
          status: STATUSES.ACTIVE,
          company,
          code: budgetCode,
        },
        selectOptions: ["id", "currency", "amount", "created_at", "isNewBudget"],
      });
      if (!budget) throw new NotFoundError("Budget");
    }

    if (balanceCode) {
      balance = await BalanceRepo.getBalance({
        filter: {
          status: STATUSES.ACTIVE,
          company,
          code: balanceCode,
        },
      });
      if (!balance) throw new NotFoundError("Balance");
    }

    if (beneficiaryCode) {
      foundBeneficiary = await BeneficiaryRepo.getOneBeneficiary({
        queryParams: { code: beneficiaryCode },
        addBudgets: true,
        selectOptions: ["status", "user"],
      });
      if (!foundBeneficiary) throw new NotFoundError("Beneficiary");

      if (foundBeneficiary.status !== STATUSES.ACTIVE || foundBeneficiary.User.status !== STATUSES.ACTIVE)
        throw new ValidationError("Cannot generate card for non-active beneficiaries");

      if (budgetCode) {
        const { User: foundBeneficiaryUser = {} } = foundBeneficiary;
        const { UserBudgets = [] } = foundBeneficiaryUser;
        const beneficiaryUserBudget = UserBudgets.find(({ Budget }) => Budget.code === budgetCode);
        if (!beneficiaryUserBudget) throw new ValidationError("Beneficiary is not on this budget");
        if (beneficiaryUserBudget.status !== STATUSES.ACTIVE) throw new ValidationError("Cannot generate card for inactive/blocked beneficiary");
        if (beneficiaryUserBudget.Budget.status !== STATUSES.ACTIVE) throw new ValidationError("Budget is not active");
      }
    }

    const [foundUser, foundCompany] = await Promise.all([
      UserRepo.getOneUser({
        queryParams: {
          id: foundBeneficiary?.User?.id || user,
        },
        selectOptions: ["firstName", "lastName", "email", "dob", "role", "phoneNumber", "address"],
        includePhonenumber: true,
        includeAddress: true,
      }),

      CompanyRepo.getOneCompany({
        queryParams: {
          id: company,
        },
        selectOptions: ["contact_email", "contact_phone", "paymentPlan", "address", "onboardingLevel", "amountSpent", "maxTransactionAmount"],
        addAddress: true,
        addPhoneNumber: true,
        addPaymentPlan: true,
      }),
    ]);

    if (budgetCode) {
      availableBalance = await BudgetLedgerRepo.getBudgetAvailableBalance(budget);
    } else {
      availableBalance = await BalanceRepo.getAvailableBalance({
        company,
        currency: balance.currency,
        code: balanceCode,
      });
    }

    const { expenseCategory, allowedMerchants, spendingLimitPolicy, ...otherSettings } = settings || {};

    let mccs;
    let spendingLimit;

    if (expenseCategory) {
      const foundCategory = await CategoryRepo.getCategory({
        queryParams: { code: expenseCategory },
      });
      if (!foundCategory) throw new NotFoundError("Expense category");
      otherSettings.expenseCategory = foundCategory.id;
    }

    if (allowedMerchants) {
      mccs = await HelperService.validateMccs(allowedMerchants);
    }

    if (spendingLimitPolicy) {
      spendingLimit = await HelperService.validateSpendingPolicy(spendingLimitPolicy);

      if (spendingLimit.frequency === "weekends") {
        throw new ValidationError("Spending controls do not apply to policies with 'weekends' frequency");
      }
    }

    const { totalAmount, rate, fundingFee, charges, issuingFee, percentageFundingFee } = await this.getCardChargesByAmount({
      amount,
      currency: lowerCasedCurrency,
      action: "create",
      cardIssuing,
      issuer: cardIssuer,
      company,
      paymentPlan,
    });

    if (availableBalance < totalAmount) throw new ValidationError("Insufficient funds in Budget to create card");

    // check plan payout limit
    await Utils.checkPlanPayoutLimit({ company: foundCompany, amount: totalAmount, currency, Transaction });

    if (foundCompany.onboardingLevel === ONBOARDING_LEVEL.LEVEL_1) {
      const willReachLimit = Utils.checkTransactionLimit({ company: foundCompany, amount: totalAmount });
      if (willReachLimit) throw new ValidationError("Transaction limit reached, Please complete your onboarding");
    }

    const cardPayload = {
      name,
      currency,
      amount: 0,
      company,
      budget: budget?.id,
      balance: balance?.id,
      type: this.cardType()[type.toLowerCase()],
      issuer: CARD_ISSUER[cardIssuer],
      status: STATUSES.PROCESSING,
      user: foundBeneficiary?.User?.id || user,
      requiresPinChange: cardIssuer === "sudo" && type.toLowerCase() !== "physical" && lowerCasedCurrency === "ngn",
      ...(team && { team }),
      ...otherSettings,
    };
    const createdCard = await VirtualCardRepo.create(cardPayload);

    if (mccs) {
      await MccRepo.createMccCards(createdCard.id, mccs);
    }

    if (spendingLimit) {
      await CardPolicyRepo.create({
        payload: {
          policy: spendingLimit.id,
          card: createdCard.id,
        },
      });
    }

    const cardHolder = await CardHolderRepo.find({
      condition: {
        user: createdCard.user,
        status: STATUSES.ACTIVE,
        provider: CARD_ISSUER[cardIssuer],
      },
    });

    if (!cardHolder) {
      // Send email
      const redisPayload = {
        card: createdCard.code,
        amount: totalAmount,
        cardAmount: amount,
        rate,
        currency,
        company,
        bujetiFee: percentageFundingFee === 0 ? fundingFee : Math.min(percentageFundingFee, fundingFee),
        processorFee: issuingFee,
        description: `Card creation funding(${createdCard.name})`,
      };
      await RedisService.setex(`cardholder_pending_card:${createdCard.code}`, JSON.stringify(redisPayload), 259200); // Set this to expire in three days if the user doesn't use the bvn
      const cardHolderEmail = await this.cardHolderEmail({
        user,
        getUser: foundUser,
        getCompany: foundCompany,
        amount,
        currency,
        card: createdCard.code,
      });
      return responseUtils.sendObjectResponse(cardHolderEmail.message, cardHolderEmail);
    }

    await this.initiateCardProviderFunding({
      payload: {
        card: createdCard.code,
        amount: totalAmount,
        cardAmount: amount,
        rate,
        currency,
        company,
        bujetiFee: percentageFundingFee === 0 ? fundingFee : Math.min(percentageFundingFee, fundingFee),
        processorFee: issuingFee,
        description: `Card creation funding(${createdCard.name})`,
      },
    });

    return responseUtils.sendObjectResponse("Your card creation request is processing");
  }

  static async initiateCardProviderFunding({ payload }) {
    const { card, amount, cardAmount, rate, currency, company, bujetiFee, processorFee, description } = payload;

    const reference = Utils.generateRandomString(17);
    const pendingTransfer = await TransferService.createTransfer({
      amount: -1 * amount,
      currency: "NGN",
      company,
      bujeti_fee: bujetiFee,
      processor_fee: processorFee,
      description,
      narration: description,
      reference,
      status: STATUSES.PENDING,
    });

    const SQSPayload = {
      id: `crd_funding:${card}`,
      idempotencyKey: `crd_funding:${card}`,
      path: `/cards/${card}/process-funding`,
      key: process.env.INTRA_SERVICE_TOKEN,
      data: {
        trial: 0,
        card,
        currency,
        cardAmount,
        rate,
        reason: "create-card",
        transfer: pendingTransfer.code,
      },
    };

    return QueueService.addDelayedJob({ tag: "card:funding" }, SQSPayload, `card:${card}`, 10);
  }

  static async getCardChargesByAmount({ amount, currency, action = "create", cardIssuing = {}, issuer, company, paymentPlan = {} }) {
    const lowerCasedCurrency = currency.toLowerCase();
    const { cards: { free_cards: freeCards = 0 } = {} } = paymentPlan;

    let cardIssuingObject = cardIssuing;
    let charges = 0;
    let totalAmount = 0;
    let markedUpRate = 0;
    let percentageCharged = 0;

    if (lowerCasedCurrency === "ngn") {
      const totalCardCount = await VirtualCardRepo.count({
        filter: { company, currency },
      });
      if (totalCardCount < freeCards) {
        // Company can create free card
        return {
          charges: 0,
          totalAmount: amount,
          rate: markedUpRate,
          issuingFee: 0,
          fundingFee: 0,
          percentageFundingFee: 0,
        };
      }
    }

    const cardChargesConfigurations = paymentPlan.cards[String(currency).toUpperCase()] || {};
    const { issuing_fee: planCardIssuingFees, funding_fee: planFundingFee } = cardChargesConfigurations;

    if (Object.keys(cardIssuingObject).length === 0) {
      const { card_issuing: settingsCardIssuingObject } = Settings.get("providers");
      cardIssuingObject = settingsCardIssuingObject;
    }

    const { defaultIssuer, issuing_fee: defaultIssuingFee, funding_fee: defaultFundingFee } = cardIssuingObject[lowerCasedCurrency];

    const cardIssuingFees = planCardIssuingFees || defaultIssuingFee;
    const fundingFee = planFundingFee || defaultFundingFee;
    if (lowerCasedCurrency === "ngn") {
      console.log("Payload is ", { amount, cardIssuingFees, percentageCharged, fundingFee });
      // percentageCharged = parseInt(amount * cardIssuingObject[lowerCasedCurrency]["funding_fee(%)"], 10);
      charges = parseInt(cardIssuingFees, 10) + percentageCharged + fundingFee;
      totalAmount = parseInt(amount, 10) + charges;
    } else {
      const cardIssuer = (issuer || defaultIssuer).toLowerCase();
      const providerRate = await providers[cardIssuer].getExchangeRate();
      markedUpRate = providerRate + 5;
      const dollarAmount = Utils.formatAmount(amount); // Amount is converted to USD
      const issuingFeeAmount = Utils.formatAmount(cardIssuingFees); // Amount is converted to USD
      const percentageFundingFee = cardChargesConfigurations["funding_fee(%)"] || cardIssuingObject[lowerCasedCurrency]["funding_fee(%)"];
      percentageCharged = Number(dollarAmount * percentageFundingFee);
      charges = Math.min(percentageCharged, Utils.formatMoney(fundingFee)) + issuingFeeAmount;
      const currencyTotalAmount = dollarAmount + charges;

      totalAmount = currencyTotalAmount * markedUpRate * 100; // Amount is converted to lowest denomination
      charges = charges * markedUpRate * 100; // Convert charges to lowest denomination
    }
    return {
      charges,
      totalAmount,
      rate: markedUpRate,
      issuingFee: lowerCasedCurrency === "ngn" ? cardIssuingFees : Utils.money(cardIssuingFees).div(100).mul(markedUpRate).mul(100),
      fundingFee: lowerCasedCurrency === "ngn" ? fundingFee : Utils.money(fundingFee).div(100).mul(markedUpRate).mul(100),
      percentageFundingFee: lowerCasedCurrency === "ngn" ? percentageCharged : Utils.money(percentageCharged).mul(markedUpRate).mul(100),
    };
  }

  static async processCardProviderFunding({ data }) {
    const { card, currency, cardAmount, rate, reason, transfer, trial } = data;
    // Maximum attempt
    if (parseInt(trial, 10) + 1 >= parseInt(Settings.get("MAX_CARD_FUNDING_ATTEMPT"), 10)) {
      await VirtualCardRepo.update({
        conditions: { code: card },
        changes: { status: STATUSES.FAILED, failureReason: "Unable to process card charges" },
      });
      return responseUtils.sendObjectResponse("Ok");
    }

    const [foundCard, foundTransfer] = await Promise.all([
      VirtualCardRepo.find({ code: card }),
      TransferRepo.getTransfer({ filter: { code: transfer } }),
    ]);

    if (!foundCard) throw new NotFoundError("Virtual Card");
    if (!foundTransfer) throw new NotFoundError("Transfer");

    if (foundCard.status !== STATUSES.PROCESSING) return responseUtils.sendObjectResponse("Ok"); // Don't make this call again
    // If there's external identifier, transfer has been initiated
    if (foundTransfer.externalIdentifier) return responseUtils.sendObjectResponse("Ok");

    const {
      CardIssuer: { name: issuer },
      company,
      budget,
      Budget,
      user,
      balance,
      Balance,
    } = foundCard;

    const { amount } = foundTransfer;
    const lowerCasedIssuer = issuer.toLowerCase();

    const { payment } = Settings.get("providers");
    const receivingAccount = await providers[lowerCasedIssuer].getDefaultProviderAccount();
    const provider = payment[company] || payment.defaultProvider;

    try {
      const { status, data: cardFundingData } = await this.cardFunding({
        payload: {
          company,
          amount: Math.abs(parseInt(amount, 10)),
          currency: "NGN",
          budget,
          balance,
          recipient: {
            bankCode: receivingAccount.bankCode,
            accountName: receivingAccount.accountName,
            number: receivingAccount.number,
            reason,
            transfer: foundTransfer.code,
            cardCode: card,
            budget: Budget?.code,
            ...(Balance && { balance: Balance.code }),
            user,
            markedUpRate: rate,
            cardAmount,
          },
          narration: `Card creation funding(${foundCard.name})`,
          provider,
        },
      });
      if ([200, 201].includes(status)) {
        await TransferRepo.update({ filter: { code: foundTransfer.code }, payload: { externalIdentifier: cardFundingData.id } });
        return responseUtils.sendObjectResponse("Ok");
      }
      // If you don't get a success, send to queue again
      const SQSPayload = {
        id: `crd_funding:${card}`,
        idempotencyKey: `crd_funding:${card}`,
        path: `/cards/${card}/process-funding`,
        key: process.env.INTRA_SERVICE_TOKEN,
        data: {
          trial: trial + 1,
          card,
          currency,
          cardAmount,
          rate,
          reason: "create-card",
          transfer: foundTransfer.code,
        },
      };
      return QueueService.addDelayedJob({ tag: "card:funding" }, SQSPayload, `card:${card}`, 10);
    } catch (error) {
      // Send to queue again, increasing trial by 1
      const SQSPayload = {
        id: `crd_funding:${card}`,
        idempotencyKey: `crd_funding:${card}`,
        path: `/cards/${card}/process-funding`,
        key: process.env.INTRA_SERVICE_TOKEN,
        data: {
          trial: trial + 1,
          card,
          currency,
          cardAmount,
          rate,
          reason: "create-card",
          transfer: foundTransfer.code,
        },
      };
      return QueueService.addDelayedJob({ tag: "card:funding" }, SQSPayload, `card:${card}`, 10);
    }
  }

  static async updateCard({ filter, payload }) {
    const { settings = {}, impactBudget, ...rest } = payload;

    const createdCard = await VirtualCardRepo.find({ ...filter });
    if (!createdCard) throw new NotFoundError("Card");

    if (impactBudget && createdCard.budget) throw new ValidationError("Only Cards on Balance can impact spends on Budget"); // Cards on Budgets cannot impact spent on another budget

    let foundCategory;
    let mccs;
    let spendingLimit;
    if (settings.expenseCategory) {
      foundCategory = await CategoryRepo.getCategory({
        queryParams: { code: settings.expenseCategory },
      });
      if (!foundCategory) throw new NotFoundError("Expense category");
    }

    if (settings.allowedMerchants) {
      mccs = await HelperService.validateMccs(settings.allowedMerchants);
    }

    if (settings.spendingLimitPolicy) {
      spendingLimit = await HelperService.validateSpendingPolicy(settings.spendingLimitPolicy);

      if (spendingLimit.frequency === "weekends") {
        throw new ValidationError("Spending controls do not apply to policies with 'weekends' frequency");
      }
    }

    const sudoUpdateRequest = {
      status: "active",
      spendingControls: {
        allowedCategories: mccs?.map(({ mcc }) => mcc) || [],
        blockedCategories: [],
        channels: {
          atm: (Utils.propertyExistsOnObject(settings, "atmWithdrawals") && settings.atmWithdrawals) || !!createdCard.atmWithdrawals,
          pos: (Utils.propertyExistsOnObject(settings, "posTransaction") && settings.posTransaction) || !!createdCard.posTransaction,
          web: (Utils.propertyExistsOnObject(settings, "onlineTransaction") && settings.onlineTransaction) || !!createdCard.onlineTransaction,
          mobile: false,
        },
        spendingLimits: spendingLimit
          ? [
              {
                amount: spendingLimit.maxAmount / 100,
                interval: spendingLimit.frequency,
              },
            ]
          : [],
      },
    };

    const cardIssuer = createdCard.CardIssuer.name;
    const { error } = await providers[cardIssuer.toLowerCase()].updateCardSetting({
      payload: sudoUpdateRequest,
      cardExternalId: createdCard.externalIdentifier,
      company: filter.company,
    });

    if (error) throw new ValidationError("An error occurred while trying to update your card, Please try again later");

    if (mccs) {
      await MccRepo.updateMccCards({
        queryParams: {
          card: createdCard.id,
          mcc: {
            [Op.notIn]: Utils.mapAnArray(mccs, "id"),
          },
        },
        payload: {
          status: STATUSES.DELETED,
        },
      });
      await MccRepo.findOrCreateMccCards(createdCard.id, mccs);
    }

    if (spendingLimit) {
      await CardPolicyRepo.update({
        payload: {
          status: STATUSES.DELETED,
        },
        queryParams: {
          card: createdCard.id,
          policy: {
            [Op.ne]: spendingLimit.id,
          },
        },
      });
      await CardPolicyRepo.findOrCreate({
        payload: {
          policy: spendingLimit.id,
          card: createdCard.id,
          status: STATUSES.ACTIVE,
        },
      });
    }

    const updateRequest = {
      ...(Utils.propertyExistsOnObject(settings, "onlineTransaction") && { onlineTransaction: settings.onlineTransaction }),
      ...(Utils.propertyExistsOnObject(settings, "atmWithdrawals") && { atmWithdrawals: settings.atmWithdrawals }),
      ...(Utils.propertyExistsOnObject(settings, "posTransaction") && { posTransaction: settings.posTransaction }),
      ...(Utils.propertyExistsOnObject(settings, "contactlessTransaction") && { contactlessTransaction: settings.contactlessTransaction }),
      ...(foundCategory && { expenseCategory: foundCategory.id }),
    };

    if (impactBudget) {
      const foundBudget = await BudgetRepo.getOneBudget({ queryParams: { code: impactBudget } });
      if (!foundBudget) throw new NotFoundError("Budget");
      updateRequest.impactBudget = foundBudget.id;
    }

    await VirtualCardRepo.update({
      conditions: { ...filter },
      changes: updateRequest,
    });
    return responseUtils.sendObjectResponse("Your Card has been updated");
  }

  /**
   * Fund Card
   * @param {*} param0
   * @returns
   */
  static async fundCard({ cardCode, user, company, amount, budget: budgetCode, balance: balanceCode, paymentPlan = {} }) {
    const card = await VirtualCardRepo.find({
      code: cardCode,
      company,
      status: STATUSES.ACTIVE,
    });
    if (!card) throw new NotFoundError("Card");
    const cardIssuer = this.getIssuserById(CARD_ISSUER, card.issuer).toLowerCase();
    // check if there is sufficient balance
    // get budget
    let budget;
    let balance;
    let availableBalance;
    if (budgetCode) {
      budget = await BudgetRepo.getOneBudget({
        queryParams: { code: budgetCode },
        selectOptions: ["owner", "isNewBudget", "available", "currency", "created_at"],
      });
      if (!budget) throw new NotFoundError("Budget");
      const userBudget = await UserBudgetRepo.getOneUserBudget({
        queryParams: {
          user,
          budget: budget.id,
          status: STATUSES.ACTIVE,
        },
      });
      if (card.budget !== budget.id && !userBudget) {
        throw new ValidationError("Cannot fund card from the selected budget");
      }
    }

    if (balanceCode) {
      balance = await BalanceRepo.getBalance({
        filter: {
          status: STATUSES.ACTIVE,
          company,
          code: balanceCode,
        },
      });
      if (!balance) throw new NotFoundError("Balance");
    }

    if (budgetCode) {
      availableBalance = await BudgetService.getBudgetAvailableBalance(budget);
    } else {
      availableBalance = await BalanceRepo.getAvailableBalance({
        company,
        currency: balance.currency,
        code: balanceCode,
      });
    }

    const { currency } = card;
    const lowerCasedCurrency = String(currency).toLowerCase();

    const { payment, card_issuing: cardIssuing } = Settings.get("providers");
    // get charges to apply
    let totalAmount;
    let charge;

    const cardPlanConfiguration = paymentPlan.cards[String(currency).toUpperCase()];
    const { funding_fee: planFundingFee } = cardPlanConfiguration;
    const { funding_fee: defaultFundingFee } = cardIssuing[lowerCasedCurrency];

    const fundingFee = planFundingFee || defaultFundingFee;
    const fundingFeePercentage = cardPlanConfiguration["funding_fee(%)"] || cardIssuing[lowerCasedCurrency]["funding_fee(%)"];
    if (lowerCasedCurrency === "ngn") {
      charge = Utils.calculateCardFundingCharges({
        amount,
        currency: lowerCasedCurrency,
        fundingFee,
      });
      totalAmount = Number(amount) + charge;
    }

    let markedUpRate = 0;
    if (lowerCasedCurrency === "usd") {
      const providerRate = await providers[cardIssuer].getExchangeRate({ id: company });
      markedUpRate = providerRate + 5;
      const dollarAmount = amount / 100;

      charge = Utils.calculateCardFundingCharges({
        amount,
        currency: lowerCasedCurrency,
        fundingFeePercentage,
        fundingFee,
        rate: markedUpRate,
      });

      totalAmount = dollarAmount * markedUpRate * 100;
      totalAmount += charge;
    }

    if (Number(totalAmount) > availableBalance) throw new ValidationError("Insufficient Balance");
    // debit via anchor

    const providerToUse = payment[company] || payment.defaultProvider;

    let accountProvidus;

    if (cardIssuer === "bridgecard") {
      accountProvidus = await BankAccountRepo.getOneBankAccount({
        queryParams: {
          owner: card.id,
          ownerType: "card",
          type: "virtual",
          status: STATUSES.ACTIVE,
          issuer: card.issuer,
          // externalIdentifier: card.externalIdentifier,
        },
        selectOptions: ["bankCode", "accountName", "number", "externalIdentifier"],
      });
    } else accountProvidus = Settings.get("cardIssuingAccounts")[cardIssuer].account;

    const reference = Utils.generateRandomString(17);
    const pendingTransfer = await TransferService.createTransfer({
      amount: -1 * Number(totalAmount),
      currency: "NGN", // Used NGN here because we're transfering Naira
      company,
      description: "Fund Card",
      reference,
      status: STATUSES.PENDING,
      bujeti_fee: 0,
      processor_fee: charge,
    });
    // call anchor Transfer
    await this.cardFunding({
      payload: {
        company,
        amount: totalAmount,
        currency: "NGN",
        budget: budget?.id,
        balance: balance?.id,
        recipient: {
          bankCode: accountProvidus.bankCode,
          accountName: accountProvidus.accountName,
          number: accountProvidus.number,
          reason: "fund-card",
          transfer: pendingTransfer.code,
          cardCode: card.code,
          cardAmount: amount,
          markedUpRate,
          budget: budget?.code,
          balance: balance?.code,
        },
        narration: "fund-card",
        provider: providerToUse,
      },
      cardIssuer,
      amount,
      currency,
      cardHolder: accountProvidus,
    });
  }
  /**
   * Query to get Cards
   * @param {*} param0
   */

  static async getAllCards({ query, paginateOptions }) {
    const { budget, company, team, user, search, type, status, currency, "owner/holder": owner } = query;
    const companyObj = await CompanyRepo.getOneCompany({
      queryParams: { code: company },
    });
    const conditions = {
      status: {
        [Op.in]: [STATUSES.ACTIVE, STATUSES.PROCESSING, STATUSES.PENDING, STATUSES.BLOCKED],
      },
      isExternal: false,
    };
    let userConditions = {};
    let budgetConditions = {};
    let statusConditions = {};
    if (search) {
      conditions[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { "$User.firstName$": { [Op.like]: `%${search}%` } },
        { "$User.lastName$": { [Op.like]: `%${search}%` } },
      ];
    }
    if (user) {
      const userObj = await userRepo.getOneUser({
        queryParams: {
          code: user,
        },
        selectOptions: ["role"],
      });
      if (userObj.role === "admin") {
        conditions.company = companyObj.id;
      } else {
        userConditions = { code: user };
      }
    }

    if (budget) {
      budgetConditions = {
        code: budget,
      };
    }
    if (type) {
      const cardTypes = this.cardType();
      const values = [];
      for (const item of type) {
        values.push(cardTypes[item.toLowerCase()]);
      }
      conditions.type = values;
    }
    if (status) {
      statusConditions = {
        value: status,
      };
    }
    if (currency) {
      conditions.currency = currency;
    }
    if (owner) {
      userConditions.code = owner;
    }

    const cards = await VirtualCardRepo.findAndCount(conditions, paginateOptions, false, userConditions, budgetConditions, statusConditions);
    // decrypt each card.

    const { payment, card_issuing } = Settings.get("providers");

    const rows = [];
    for (let item of cards.rows) {
      item = item.toJSON();
      rows.push(providers[String(item.CardIssuer?.name || card_issuing.defaultIssuer)?.toLowerCase()]?.decryptCard(item));
    }
    cards.rows = rows;
    return cards;
  }

  static async liquidateCard({ user, criteria, payload: { amount = 0 } = {} }) {
    const foundCard = await VirtualCardRepo.find(criteria);
    if (!foundCard) throw new NotFoundError("Card");
    const {
      budget: cardBudgetId,
      balance: cardBalanceId,
      currency,
      company,
      CardIssuer: { name },
    } = foundCard;
    let amountToLiquidate = 0;
    const ledgerAmount = await VirtualCardRepo.getAvailableBalance(foundCard);

    if (amount && amount > ledgerAmount) throw new ValidationError("Insufficient balance on card");

    amountToLiquidate = amount || ledgerAmount;

    if (!amountToLiquidate || amountToLiquidate < 0) throw new ValidationError("Invalid liquidation amount");

    let cardSource;
    let cardAccount;

    if (cardBudgetId) {
      cardSource = await BudgetRepo.getBudget({ id: cardBudgetId }, true);
      if (!cardSource) throw new NotFoundError("Budget");
      if (!cardSource.BudgetAccount) throw new NotFoundError("Budget Account");
    } else {
      cardSource = await BalanceRepo.getBalance({
        filter: { id: cardBalanceId },
        includeAccount: true,
      });
      if (!cardSource) throw new NotFoundError("Balance");
      if (!cardSource.BankAccount) throw new NotFoundError("Bank Account");
    }

    const pendingTransfer = await TransferService.createTransfer({
      amount: Number(amountToLiquidate),
      currency,
      company,
      description: `Card Liquidation (${foundCard.code})`,
      reference: Utils.generateRandomString(17),
      status: STATUSES.PENDING,
    });

    // Decrement Card amount and create ledger
    const budgetLedgerPayload = {
      currency,
      amount: -1 * amountToLiquidate,
      budget: cardBudgetId,
      transfer: pendingTransfer.id,
      description: pendingTransfer.description,
      status: STATUSES.PENDING,
      user: criteria.user || user || foundCard.user,
      balanceBefore: ledgerAmount,
      balanceAfter: ledgerAmount - Number(amountToLiquidate),
      card: foundCard.id,
    };

    const ledgerPayload = {
      currency,
      amount: -1 * amountToLiquidate,
      ...(cardBudgetId && { budget: cardBudgetId }),
      ...(cardBalanceId && { balance: cardBalanceId, company }),
      transfer: pendingTransfer.id,
      description: pendingTransfer.description,
      status: STATUSES.PENDING,
      user: criteria.user || user || foundCard.user,
      balanceBefore: ledgerAmount,
      balanceAfter: ledgerAmount - Number(amountToLiquidate),
      card: foundCard.id,
    };

    if (cardBudgetId) {
      await BudgetLedgerRepo.createBudgetLedger({
        payload: ledgerPayload,
        isSpent: false,
      });
    } else {
      await BalanceLedgerRepo.createLedger(ledgerPayload);
    }

    await VirtualCardRepo.arithmetic({
      conditions: { where: { id: foundCard.id } },
      action: "decrement",
      field: { amount: amountToLiquidate },
    });

    if (currency === "USD") {
      const rate = await providers[String(name).toLowerCase()].getUSDLIquidationRate({ id: company });
      amountToLiquidate *= rate;
    }

    // Add to queue
    const SQSPayload = {
      id: `initiate_liquidation:${foundCard.code}`,
      idempotencyKey: `initiate_liquidation:${foundCard.code}`,
      path: `/cards/${foundCard.code}/initiate-liquidation`,
      key: process.env.INTRA_SERVICE_TOKEN,
      trial: 0,
      transfer: pendingTransfer.code,
      card: foundCard.code,
      amount: amountToLiquidate,
    };
    await QueueService.addDelayedJob({}, SQSPayload, `initiate_liquidation:${foundCard.code}`, 5);

    return responseUtils.sendObjectResponse("Processing card liquidation");
  }

  static async getOneCard({ cardCode, user, isAdmin }) {
    const conditions = { code: cardCode };

    if (!isAdmin) {
      conditions.user = user;
    }
    const card = await VirtualCardRepo.getCard({ filter: conditions, includeTransactions: true });
    if (!card) throw new NotFoundError("Card");
    if (card && !card.externalIdentifier) return card.toJSON();
    const cardProvider = providers[card.CardIssuer.name.toLowerCase()];
    const { data: cards } = await cardProvider.getCard(card.externalIdentifier);
    const cardStatus = getStatusById(STATUSES, card.status);

    let cardAddress = null;
    const { User: cardUser, CardIssuer: { name: provider } = {} } = card;

    ({ Address: cardAddress = null } = cardUser || {});

    if (!cardAddress) {
      // Company address was used
      const foundCompany = await CompanyRepo.getOneCompany({
        queryParams: { id: card.company },
        addAddress: true,
      });

      ({ Address: cardAddress = {} } = foundCompany);
    }

    const lowerCasedCurrency = card.currency.toLowerCase();

    if (lowerCasedCurrency === "ngn") {
      cardAddress = {
        address: cardAddress?.street,
        city: cardAddress?.city,
        state: cardAddress?.state,
        country: cardAddress?.country,
        postal_code: cardAddress?.postalCode,
        house_no: cardAddress?.street,
      };
    } else {
      const { address: defaultAddress } = Utils.parseJSON(Settings.get("cardIssuingAccounts")[provider.toLowerCase()]) || {};
      cardAddress = { ...defaultAddress };
    }

    const cardResponse = {
      ...card.toJSON(),
      number: cards?.card_number,
      last_4: cards?.card_number,
      exp_month: cards?.expiry_month || cards?.expiryMonth,
      exp_year: cards?.expiry_year || cards?.expiryYear,
      cvv: cards?.cvv,
      ...(cardStatus &&
        cardStatus.toLowerCase() === "processing" && {
          reason: "Pending BVN verification",
        }),
      address: cardAddress,
    };

    if (card.issuer === CARD_ISSUER.sudo) {
      const { data: { token } = {} } = await cardProvider.generateCardToken(card.externalIdentifier);
      if (!token) {
        throw new HttpException("This is taking longer than it should, please retry again in one or 2 minutes", HttpStatus.SERVICE_UNAVAILABLE);
      }
      cardResponse.token = token;
      cardResponse.cardId = card.externalIdentifier;
    }

    return cardResponse;
  }

  static async updateStatusOfCard({ type, code: cardCode, user, card }) {
    if (type === "block") {
      const cardIssuer = this.getIssuserById(CARD_ISSUER, card.issuer);
      const { error, message, body, data } = await providers[cardIssuer].freezeVirtualCard(card.externalIdentifier);
      if (error) throw new ValidationError("Unable to block card");
      const updatedCard = await VirtualCardRepo.update({
        conditions: {
          code: cardCode,
        },
        changes: {
          status: STATUSES.BLOCKED,
        },
        returning: true,
        plain: true,
      });
      return updatedCard;
    }
    if (type === "unblock") {
      const cardIssuer = this.getIssuserById(CARD_ISSUER, card.issuer);
      const { error, message, body, data } = await providers[cardIssuer].unfreezeVirtualCard(card.externalIdentifier);
      if (error) throw new ValidationError("Unable to unblock card");
      const updatedCard = await VirtualCardRepo.update({
        conditions: {
          code: cardCode,
        },
        changes: {
          status: STATUSES.ACTIVE,
        },
        returning: true,
        plain: true,
      });
      return updatedCard;
    }
  }

  static async terminateCard({ initiator, filter, extras: { trial = 0 } = {}, transaction = null }) {
    const foundCard = await VirtualCardRepo.getCard({
      filter: { ...filter },
      transaction,
    });

    if (!foundCard) throw new NotFoundError("Card");
    if (foundCard.status === STATUSES.DELETED) throw new ValidationError("This card has already been deleted");

    const { amount, spent, externalIdentifier } = foundCard;

    const amountleftOnCard = parseInt(amount, 10) - parseInt(spent, 10);

    if (amountleftOnCard && externalIdentifier) {
      const { success, error = null } = await this.liquidateCard({
        user: initiator,
        criteria: { id: foundCard.id },
        payload: { amount: amountleftOnCard },
      });
      if (!success) throw new ValidationError(error);
    }

    if (externalIdentifier) {
      const cardIssuer = this.getIssuserById(CARD_ISSUER, foundCard.issuer);
      // Delete Card on provider
      const terminateCardResponse = providers[cardIssuer].freezeVirtualCard(foundCard.externalIdentifier);

      if (terminateCardResponse.status === "failed") {
        const SQSPayload = {
          id: `terminate_card:${foundCard.code}`,
          idempotencyKey: `terminate_card:${foundCard.code}`,
          path: `/cards/${foundCard.code}/terminate`,
          key: process.env.INTRA_SERVICE_TOKEN,
          trial: trial + 1,
          initiator,
        };
        return QueueService.addDelayedJob({}, SQSPayload, `terminate_card:${foundCard.code}`);
      }
    }

    // Delete Card
    await VirtualCardRepo.update({
      conditions: { id: foundCard.id },
      changes: { status: STATUSES.DELETED },
    });

    return responseUtils.sendObjectResponse("Card deleted successfully");
  }

  static balanceCheck(type, id) {
    let balance;
    if (type === "budget") {
      // check bujet balance
      balance = 50000 / 10;
    } else {
      balance = 50000 / 10;
    }
    return balance;
  }

  static getIssuserById(object, value) {
    return Object.keys(object).find((key) => object[key] === value);
  }

  static encryptCard = (cards) => {
    const issuer = this.getIssuserById(CARD_ISSUER, cards.issuer);
    const hash = Utils.getEncryptionkeys(issuer);
    return {
      ...cards,
      last_4: Utils.encrypt({ text: cards.last_4, hash }),
      cvv: Utils.encrypt({ text: cards.cvv, hash }),
      exp_year: Utils.encrypt({ text: cards.exp_year, hash }),
      exp_month: Utils.encrypt({ text: cards.exp_month, hash }),
      ...(cards?.number && {
        number: Utils.encrypt({ text: cards.number, hash }),
      }),
      ...(cards?.pin && {
        pin: Utils.encrypt({ text: cards.pin, hash }),
      }),
    };
  };

  static cardType = () => {
    return Utils.cardType();
  };

  static createCardHolder = async ({ bvn, redisKey, phoneNumber = null }) => {
    const cardCode = getCardCodeFromHash(redisKey);
    if (!cardCode) throw new ValidationError("Can't create card holder for this request");
    const foundCard = await VirtualCardRepo.find({ code: cardCode });
    if (!foundCard) throw new NotFoundError("Card");
    const {
      user,
      company,
      status,
      issuer,
      currency,
      team,
      type,
      code,
      name,
      CardIssuer: { name: cardIssuerName },
    } = foundCard;
    if (status !== STATUSES.PROCESSING) throw new ValidationError("Can't create card holder for this request");

    const foundCardHolder = await CardHolderRepo.find({
      condition: { user, provider: issuer, status: STATUSES.ACTIVE },
    });
    if (foundCardHolder) throw new ExistsError("Card Holder");

    if (phoneNumber && Object.keys(phoneNumber).length) {
      const foundPhoneNumber = await PhoneNumberRepo.findOrCreate({
        queryParams: {
          countryCode: phoneNumber.countryCode || "234",
          localFormat: phoneNumber.localFormat,
          user,
        },
        defaults: {
          countryCode: phoneNumber.countryCode || "234",
          localFormat: phoneNumber.localFormat,
        },
      });
      // update Phonenumber column on user Table
      await UserRepo.updateUser({
        queryParams: { id: user },
        updateFields: {
          phoneNumber: foundPhoneNumber.id,
        },
      });
    }

    const cardHolderPayload = await providers[String(cardIssuerName).toLowerCase()].generateCardHolderPayload({ user, company, bvn });
    // const foundCompany
    const { data, error, message } = await providers[String(cardIssuerName).toLowerCase()].createCardHolder(cardHolderPayload, company, user);
    if (error) throw new ValidationError(message);
    if (String(cardIssuerName).toLowerCase() === "sudo") {
      const cardTypes = Utils.cardType();
      const redisValue = JSON.parse(await RedisService.get(`cardholder_pending_card:${cardCode}`));
      if (!redisValue) {
        await this.markProcessingCardAsFailed({
          company,
          ...(foundCard.balance && { balance: foundCard.balance }),
          ...(foundCard.budget && { budget: foundCard.budget }),
          type: Utils.getCardType(cardTypes, type),
          currency,
          user,
          name,
          reason: `Card holder request expired`,
        });
        throw new ValidationError("Card request is noo longer valid, Please create a new Card");
      }
      await this.initiateCardProviderFunding({
        payload: {
          ...redisValue,
        },
      });
      await RedisService.delete(`cardholder_pending_card:${cardCode}`); // Delete key from redis
      return { message: "Card creation Processing", responseCode: "02" };
    }
    return { message: "An Otp has been sent to you.", responseCode: "01" };
  };

  static otpVerification = async (bvn, otp) => {
    const { card_issuing } = Settings.get("providers");
    const getRedis = await RedisService.get(`validate-otp:${otp}`);

    const { cardholder_id = null } = JSON.parse(getRedis);
    const cardIssuer = card_issuing.usd.defaultIssuer;
    const { data, message, status } = await providers[cardIssuer].verifyOtp({
      cardholder_id,
      otp,
    });
    if (status !== "success") {
      throw new ValidationError("Invalid OTP");
    }
    // update cardHolder
    await CardHolderRepo.update({
      condition: {
        externalIdentifier: cardholder_id,
      },
      changes: { status: STATUSES.ACTIVE },
    });
    // call createCard
    const { create_card } = JSON.parse(await RedisService.get(`${cardholder_id}`));
    const createCard = await this.createCard({
      ...create_card,
      cardHolderFlag: false,
      issuer: cardIssuer,
    });
    await RedisService.delete(`validate-otp:${otp}`);
    await RedisService.delete(`${cardholder_id}`);
    return createCard;
  };

  static async listCards(filter) {
    const { status, user } = filter;
    const { card_issuing } = Settings.get("providers");

    if (status) {
      filter.status = typeof status === "object" ? status.map((item) => STATUSES[item.toUpperCase()]) : STATUSES[status.toUpperCase()];
    }

    if (String(user).startsWith("usr_")) {
      const foundUser = await UserService.fetchUser(user);
      if (!foundUser) throw new NotFoundError("User");
      filter.user = foundUser.id;
    }

    filter.isExternal = false; // exclude external cards for now

    const { cards, meta } = await VirtualCardRepo.list({ filter });
    const decryptedCard = cards.map(
      (card) =>
        providers[String((card.CardIssuer && card.CardIssuer.name) || card_issuing.defaultIssuer).toLowerCase()]?.decryptCard(card.toJSON()) ||
        card.toJSON()
    );

    return { cards: decryptedCard, meta };
  }

  static deactivateCardHolder = async (cardholder_id) => {
    await CardHolderRepo._delete({
      condition: {
        externalIdentifier: cardholder_id,
      },
    });
  };

  static getAllCardHolders = async () => {
    const cardholders = await CardHolderRepo.findAll();
  };

  static getExchangeRate = async (targetCurrency, company) => {
    const { card_issuing } = Settings.get("providers");
    const currencyToLowerCase = String(targetCurrency).toLowerCase();
    const exchangeProvider = card_issuing[currencyToLowerCase || "usd"].defaultIssuer;
    if (currencyToLowerCase === "ngn") return providers[exchangeProvider].getUSDLIquidationRate(company);
    return providers[exchangeProvider].getExchangeRate(company);
  };

  static cardFunding = async ({ payload }) => {
    const { error, message: chargeResponse, data, ...rest } = await ChargeService.chargeCompany(payload);
    if (error) {
      const { errors = [] } = data || {};
      const [{ detail: message = "Error funding card" } = {}] = errors;
      throw new ValidationError(chargeResponse || message);
    }
    return { data, message: chargeResponse, ...rest };
  };

  static resendOtp = async ({ redisKey }) => {
    // get the cardholder attached to the bvn
    const { card_issuing } = Settings.get("providers");
    const cardIssuer = card_issuing.usd.defaultIssuer;
    const redisCall = await RedisService.get(redisKey);
    const cardHolderId = JSON.parse(redisCall);
    if (!cardHolderId) throw new ValidationError("This session key is either invalid or expired.");
    const { status } = await providers["bridgecard"].resendOtp(cardHolderId.cardholder_id);
    if (status !== "success") throw ValidationError("Otp resend failed, please try again.");
    return status;
  };

  static cardHolderEmail = async ({ user, getUser, getCompany, amount, currency, card }) => {
    // create cardHolder Payload
    const admin = await UserRepo.getOneUser({
      queryParams: {
        id: user,
      },
      selectOptions: ["firstName"],
    });
    const internationalPhoneNumber =
      getUser?.PhoneNumber?.internationalFormat || formatPhoneNumber(getUser?.PhoneNumber?.localFormat, getUser?.PhoneNumber?.countryCode);

    const key = getCardHash(card);
    const currencyName = currency.toLowerCase() === "usd" ? "Dollar" : "Naira";
    NotificationService.notifyUser(
      { email: getUser.email || getCompany.email },
      "bvn-request",
      {
        dashboardUrl: `${Utils.getDashboardURL()}/bvn-request/${key}${!internationalPhoneNumber ? "/phoneNumber" : ""}`,
        firstName: getUser.firstName,
        currency: currencyName,
        admin: admin.firstName,
      },
      {
        subject: `[Action Required] Hey ${getUser.firstName}, a ${currencyName} card is waiting for you 💳.`,
      }
    );
    await RedisService.set(key, JSON.stringify({ amount }));
    return { message: "Please check your email to verify your bvn" };
  };

  static getBankaccount = async ({
    cardIssuer,
    cardHolder,
    currency,
    getUser,
    company,
    processingCardCode,
    _beneficiary,
    amount,
    user,
    precardCode,
    name,
    type,
    budget,
  }) => {
    let accountDetails;
    const lowerCasedCardIssuerName = cardIssuer;
    switch (lowerCasedCardIssuerName) {
      case "sudo": {
        // code block
        accountDetails = Settings.get("cardIssuingAccounts")[lowerCasedCardIssuerName].account;
        break;
      }
      case "bridgecard": {
        // code block
        if (currency.toLowerCase() === "usd") {
          accountDetails = Settings.get("cardIssuingAccounts")[lowerCasedCardIssuerName].account;
          // accountDetails = accountProvidus;
        } else {
          // check if bankAccount belongs to cardholder
          let bridgeCardAccount = await BankAccountRepo.getOneBankAccount({
            queryParams: {
              externalIdentifier: cardHolder.externalIdentifier,
              status: STATUSES.ACTIVE,
            },
            selectOptions: ["number", "accountName", "bankName", "bankCode"],
          });
          // if it exisrs clone cardHolder
          if (bridgeCardAccount) {
            const cloneCardHolder = await providers.bridgecard.cloneCardHolder(cardHolder.externalIdentifier);
            // create on the Table
            // eslint-disable-next-line no-param-reassign
            cardHolder = await CardHolderRepo.create({
              provider: CARD_ISSUER.bridgecard,
              user: getUser.id,
              company,
              status: STATUSES.ACTIVE,
              externalIdentifier: cloneCardHolder.data.cardholder_id,
            });
          }
          // create account with cardHolder
          const { status, message, data } = await providers.bridgecard.virtualAccount(cardHolder);
          // save account
          if (status !== "success") {
            throw new ValidationError("Could not create Account");
          }
          let findCard;
          if (!processingCardCode) {
            // create card
            const presaveCard = {
              code: precardCode,
              currency,
              user: _beneficiary?.User?.id || user,
              amount,
              name,
              type: this.cardType()[type.toLowerCase()],
              company,
              budget: budget.id,
              issuer: CARD_ISSUER[cardIssuer],
              status: STATUSES.PROCESSING,
              brand: "mastercard",
            };

            findCard = await VirtualCardRepo.create(presaveCard);
            // eslint-disable-next-line no-param-reassign
            processingCardCode = findCard.code;
          } else {
            findCard = await VirtualCardRepo.find({ code: processingCardCode });
          }
          bridgeCardAccount = await BankAccountRepo.createABankAccount({
            queryParams: {
              externalIdentifier: cardHolder.externalIdentifier,
              number: data.account_nuban,
              accountName: data.account_name,
              bankCode: data.bank_code,
              owner: findCard.id,
              issuer: CARD_ISSUER.Bridgecard,
              currency: "NGN",
              isVerified: true,
              ownerType: "card",
              type: "virtual",
              company,
            },
          });
          // }
          // const { accountWema, default_genders } = Settings.get(
          //   "FLW_ISSUING_ACCOUNTS"
          // );
          accountDetails = bridgeCardAccount;
        }
        break;
      }
      default: {
        accountDetails = null;
      }
    }
    return accountDetails;
  };

  static async simulateChargeCard({ amount, card }) {
    const foundcard = await VirtualCardRepo.find({ code: card });
    if (!foundcard) throw new NotFoundError("Card");
    const charge = await providers[String(foundcard.CardIssuer.name).toLowerCase()].simulateCardTransaction({
      amount,
      cardId: foundcard.externalIdentifier,
      currency: foundcard.currency,
    });
    return charge;
  }

  static async processCreateCardHolderObject({ phoneNumber = null, cardHolderUser, create_card_holder, internationalPhoneNumber }) {
    // find phoneNumber
    const { phone, ...rest } = create_card_holder;
    if (!internationalPhoneNumber) {
      const foundPhoneNumber = await PhoneNumberRepo.findOrCreate({
        queryParams: {
          countryCode: (phoneNumber && phoneNumber?.countryCode) || "234",
          localFormat: phoneNumber && phoneNumber?.localFormat,
          user: cardHolderUser,
        },
        defaults: {
          countryCode: (phoneNumber && phoneNumber?.countryCode) || "234",
          localFormat: phoneNumber && phoneNumber?.localFormat,
        },
      });
      // update Phonenumber column on user Table
      await UserRepo.updateUser({
        queryParams: { id: cardHolderUser },
        updateFields: {
          phoneNumber: foundPhoneNumber.id,
        },
      });
      return { ...rest, phone: formatPhoneNumber(foundPhoneNumber.localFormat, foundPhoneNumber.countryCode) };
    }
    return { ...rest, phone: internationalPhoneNumber };
  }

  static async transferToCard(payload) {
    const { transfer, card, cardAmount, trial } = payload;
    const foundCard = await VirtualCardRepo.find({ code: card });
    if (!foundCard) return;

    // eslint-disable-next-line consistent-return
    if (foundCard.CardPolicies.length) return { message: "Card is using gateway funding source" };

    // eslint-disable-next-line consistent-return
    if (!(await RedisService.get(`credit_card:funding:${foundCard.code}`))) return { message: "Already processed" };

    const reference = Utils.generateRandomString(17);

    try {
      const {
        externalIdentifier,
        currency,
        CardIssuer: { name: cardIssuer },
      } = foundCard;
      const { accountId } = Settings.get("cardIssuingAccounts").sudo;
      const foundTransfer = await TransferRepo.getTransfer({ filter: { code: transfer } });

      // Create Transaction attempt

      await TransactionAttemptRepo.createTransactionAttempt({
        payload: {
          reference,
          status: STATUSES.PENDING,
          transfer: foundTransfer.id,
          card: foundCard.id,
          externalIdentifier,
        },
      });

      const { status, message: cardProviderMessage } = await providers[String(cardIssuer).toLowerCase()].fundVirtualCard({
        id: externalIdentifier,
        amount: Math.abs(cardAmount),
        currency,
        ...(String(cardIssuer).toLowerCase() === "sudo" && {
          accountId,
        }),
        reference,
      });

      if (status !== "success") {
        throw new ValidationError(`Error from Provider: ${cardProviderMessage}`);
      }

      await TransactionAttemptRepo.updateTransactionAttempt({
        filter: { reference },
        payload: { status: STATUSES.SUCCESS },
      });
      // eslint-disable-next-line consistent-return
      return cardProviderMessage;
    } catch (error) {
      await TransactionAttemptRepo.updateTransactionAttempt({
        filter: { reference },
        payload: { status: STATUSES.FAILED },
      });

      if (trial + 1 > 4) throw new ValidationError(`Error funding card: ${error.message}`);
      else {
        const SQSPayload = {
          id: Utils.generateRandomString(17),
          cardAmount,
          currency: foundCard.currency,
          path: `/cards/credit-card`,
          key: process.env.INTRA_SERVICE_TOKEN,
          card,
          transfer,
          trial: Number(trial) + 1,
        };

        const delay = HelperService.generateDelayInMinutes(trial || 1);
        // eslint-disable-next-line consistent-return
        await QueueService.addDelayedJob({}, SQSPayload, `credit_card:${card}`, delay * 60);
        throw new ValidationError(error.message);
      }
    }
  }

  static async initializeCreateUSDCard({ payload }) {
    const { card, type, cardAmount, reference, user, budget, budgetCode, currency, amount, cardIssuer, markedUpRate } = payload;

    const { payment, card_issuing } = Settings.get("providers");
    const { issuing_fees } = card_issuing;

    const transferNarration = `Funding of ${String(currency).toLowerCase() === "ngn" ? "Naira" : "Dollar"} ${type} card (${card.name})`;
    const nairaEquivalent = (cardAmount / 100) * markedUpRate;
    const koboAmount = nairaEquivalent * 100; // Converted markedupRate to kobo
    const transfer = await TransferRepo.createTransfer({
      data: {
        amount,
        currency: "NGN",
        company: card.company,
        description: transferNarration,
        narration: transferNarration,
        reference,
        status: STATUSES.PENDING,
        bujeti_fee: parseInt(koboAmount * card_issuing["funding_fee(%)"], 10),
        processor_fee: issuing_fees,
      },
    });

    const provider = payment[card.company] || payment.defaultProvider;

    const receivingAccount = await providers[cardIssuer].getDefaultProviderAccount();
    return this.cardFunding({
      payload: {
        company: card.company,
        amount,
        currency: "NGN",
        budget,
        recipient: {
          bankCode: receivingAccount.bankCode,
          accountName: receivingAccount.accountName,
          number: receivingAccount.number,
          reason: "create-card",
          transfer: transfer.code,
          cardCode: card.code,
          budget: budgetCode,
          cardAmount,
          markedUpRate,
          user,
        },
        narration: `Card creation funding(${card.name})`,
        provider,
      },
    });
  }

  static async completeCardCreation(payload) {
    const { card, transfer, trial = 0, cardAmount } = payload;
    const foundCard = await VirtualCardRepo.find({
      code: card,
    });

    if (!foundCard) throw new NotFoundError("Card");

    if (!(await RedisService.get(`credit_card:funding:${foundCard.code}`))) return { message: "Already processed" };

    if (foundCard.status !== STATUSES.PROCESSING) return responseUtils.sendObjectResponse(`Cannot create card in ${foundCard.Status.name} state`);
    const {
      currency,
      company,
      type,
      user,
      User,
      CardIssuer: { name: cardIssuer },
      Balance,
      Budget,
    } = foundCard;
    const lowerCasedCardIssuerName = String(cardIssuer).toLowerCase();
    const { firstName, lastName, dob, email, PhoneNumber } = User;

    const foundCompany = await CompanyRepo.getOneCompany({
      queryParams: {
        id: company,
      },
      selectOptions: ["contact_email", "contact_phone", "paymentPlan", "address"],
      addAddress: true,
      addPhoneNumber: true,
    });

    const foundTransfer = await TransferRepo.getTransfer({
      filter: { code: transfer },
    });

    const cardHolder = await CardHolderRepo.find({
      condition: {
        user,
        status: STATUSES.ACTIVE,
        provider: CARD_ISSUER[lowerCasedCardIssuerName],
      },
    });

    const cardTypes = Utils.cardType();
    const { default_genders: defaultGenders, accountId } = Settings.get("cardIssuingAccounts")[lowerCasedCardIssuerName];
    const getMccCards = await MccRepo.getMccCards({
      queryParams: {
        card: foundCard.id,
        status: STATUSES.ACTIVE,
      },
    });

    const mccs = getMccCards?.map((mccCard) => mccCard?.Mcc).filter(Boolean);
    const cardPolicy = await CardPolicyRepo.findOne({
      queryParams: {
        status: STATUSES.ACTIVE,
        card: foundCard.id,
      },
    });
    const spendingLimit = cardPolicy?.Policy;
    const createCardPayload = {
      currency,
      amount: String(currency).toLowerCase() === "usd" ? cardAmount : 0,
      first_name: firstName,
      last_name: lastName,
      date_of_birth: Utils.formatDate(dob, "yyyy/mm/dd"),
      email,
      phone: PhoneNumber?.localFormat || foundCompany.contact_phone,
      title: defaultGenders.title,
      gender: defaultGenders.gender,
      card_type: Utils.getCardType(cardTypes, type),
      reference: foundTransfer.reference,
      cardHolder,
      accountId,
      spendingControls: {
        allowedCategories: mccs?.map(({ mcc }) => mcc) || [],
        blockedCategories: [],
        channels: {
          atm: foundCard.atmWithdrawals || true,
          pos: foundCard.posTransaction || true,
          web: foundCard.onlineTransaction || true,
          mobile: true,
        },
        spendingLimits: [],
        // spendingLimits: spendingLimit
        //   ? [
        //       {
        //         amount: spendingLimit.maxAmount / 100,
        //         interval: spendingLimit.frequency,
        //       },
        //     ]
        //   : [],
      },
    };

    try {
      const { status, message, data } = await providers[lowerCasedCardIssuerName].createVirtualCard({
        payload: createCardPayload,
        cardObject: foundCard,
        hasPolicy: !!spendingLimit,
      });
      if (status !== "success") throw new ValidationError(message || "Error creating card");

      const { accountTransfer, ...rest } = data;
      await VirtualCardRepo.update({
        conditions: {
          id: foundCard.id,
        },
        changes: {
          ...rest,
          pan: rest.masked_pan,
          brand: rest.card_type,
          status: currency.toLowerCase() === "ngn" ? STATUSES.INACTIVE : STATUSES.ACTIVE,
        },
      });

      VirtualCardService.notifyCardHolder({
        cardHolder,
        foundCard,
        User,
        user,
        currency,
        cardAmount,
        foundCompany,
        firstName,
        lastName,
        Budget,
        Balance,
        rest,
        company,
        email,
      });

      return this.creditCardAndCreateLedger({
        foundCard,
        foundTransfer,
        cardAmount,
      });
    } catch (error) {
      if (trial + 1 >= Settings.get("MAX_CARD_CREATION_ATTEMPT")) {
        // Mark card as failed and handle reversal
        return this.markCardCreationAsFailedAndHandleReversal({
          card: foundCard.code,
          transfer: foundTransfer.code,
          reason: error.message,
        });
      }
      const SQSPayload = {
        ...payload,
        id: foundTransfer.code,
        path: `/cards/create-card`,
        key: process.env.INTRA_SERVICE_TOKEN,
        trial: trial + 1,
      };

      return QueueService.addDelayedJob({}, SQSPayload, `create_card:${foundCard.code}`);
    }
  }

  static async notifyCardHolder({
    cardHolder,
    foundCard,
    User,
    user,
    currency,
    cardAmount,
    foundCompany,
    firstName,
    lastName,
    Budget,
    Balance,
    rest,
    company,
    email,
  }) {
    let internationalPhoneNumber;
    const redisKey = !cardHolder && getCardHash(foundCard.code);

    if (redisKey) {
      const getUser = await UserRepo.getOneUser({
        queryParams: {
          id: User?.id || user,
        },
        selectOptions: ["firstName", "lastName", "email", "dob", "role", "phoneNumber", "address"],
        includePhonenumber: true,
        includeAddress: true,
      });
      internationalPhoneNumber =
        getUser?.PhoneNumber?.internationalFormat || formatPhoneNumber(getUser?.PhoneNumber?.localFormat, getUser?.PhoneNumber?.countryCode);
      await RedisService.set(redisKey, JSON.stringify({ amount: String(currency).toLowerCase() === "usd" ? cardAmount : 0 }));
    }

    // notify card holder
    const payload = {
      companyName: foundCompany.name,
      cardType: "virtual",
      currencyName: Utils.getCurrencyNameFromIso3(currency),
      firstName,
      cardUsage: "debit card",
      cardHolder: `${firstName} ${lastName}`,
      ...(Budget && { budget: Budget.name }),
      ...(Balance && { balance: Balance.name }),
      validity: Utils.getLastDayOfMonth(rest.exp_month, rest.exp_year),
      isPhysicalCard: false,
      bvnRequired: !cardHolder,
      verificationUrl: redisKey ? `${Utils.getDashboardURL()}/bvn-request/${redisKey}${!internationalPhoneNumber ? "/phoneNumber" : ""}` : null,
    };

    const sourceConfig = {
      subject: `Virtual Card creation`,
      from_name: `Bujeti`,
    };

    const notificationPayload = {
      company,
      user_id: User.id,
      type: `info`,
      badge: `info`,
      title: `Your virtual ${payload.currencyName} card has been created`,
      message: `Your virtual ${payload.currencyName} card has been created`,
      body: {
        code: foundCard.code,
        entity: "CreditCard",
      },
      table: {
        code: foundCard.code,
        entity: "CreditCard",
      },
      reference_code: foundCard.code,
      event: "cardCreated",
    };
    NotificationService.saveNotification(notificationPayload);
    NotificationService.notifyUser(email, "card-created-successfully", payload, sourceConfig);
  }

  static async creditCardAndCreateLedger({ foundCard, foundTransfer, cardAmount }) {
    let ledgerPayload;
    const { Budget: foundBudget, Balance: foundBalance, currency } = foundCard;
    const cardLedgerCriteria = {
      card: foundCard.id,
    };

    if (String(currency).toLowerCase() === "ngn") {
      await VirtualCardService.transferToCard({ transfer: foundTransfer.code, card: foundCard.code, cardAmount });
    }

    RedisService.delete(`credit_card:funding:${foundCard.code}`);

    if (foundBudget) {
      const { balanceAfter: cardBalanceAfter = 0 } = (await BudgetLedgerRepo.getLastLedger({ criteria: cardLedgerCriteria })) || {
        balanceAfter: 0,
      };
      ledgerPayload = {
        currency: foundCard.currency,
        description: foundTransfer.description,
        amount: Math.abs(cardAmount),
        user: foundCard.user,
        budget: foundBudget.id,
        card: foundCard.id,
        status: STATUSES.PROCESSED,
        balanceBefore: cardBalanceAfter,
        balanceAfter: cardBalanceAfter + Math.abs(cardAmount),
        transfer: foundTransfer.id,
      };
    } else {
      const { balanceAfter: cardBalanceAfter = 0 } = (await BalanceLedgerRepo.getBalanceLedger({ filter: cardLedgerCriteria })) || {
        balanceAfter: 0,
      };
      ledgerPayload = {
        amount: cardAmount,
        currency: foundCard.currency || foundTransfer.currency,
        description: foundTransfer.description,
        company: foundTransfer.company,
        balanceBefore: cardBalanceAfter,
        balanceAfter: cardBalanceAfter + Number(Math.abs(cardAmount)),
        transfer: foundTransfer.id,
        balance: foundBalance.id,
        status: STATUSES.PROCESSED,
        card: foundCard.id,
      };
    }

    const promisesToExecute = [
      VirtualCardRepo.arithmetic({
        conditions: { where: { id: foundCard.id } },
        field: { amount: Math.abs(Number(cardAmount)) },
        action: "increment",
      }),
    ];

    if (foundBudget) {
      promisesToExecute.push(BudgetLedgerRepo.createBudgetLedger({ payload: ledgerPayload, isSpent: false }));
    } else {
      promisesToExecute.push(BalanceLedgerRepo.createLedger(ledgerPayload));
    }

    return Promise.all(promisesToExecute);
  }

  static async markCardCreationAsFailedAndHandleReversal({ card, transfer, reason }) {
    const [foundCard, foundTransfer] = await Promise.all([
      VirtualCardRepo.find({
        code: card,
      }),
      TransferRepo.getTransfer({ filter: { code: transfer } }),
    ]);
    if (!foundCard) throw new NotFoundError("Card");
    if (foundCard.status !== STATUSES.PROCESSING) throw new ValidationError("Can only perform reversal on processing cards");

    const {
      User: { email, firstName },
      currency,
    } = foundCard;

    VirtualCardRepo.update({
      conditions: {
        id: foundCard.id,
      },
      changes: {
        status: STATUSES.FAILED,
        failureReason: reason,
      },
    });
    NotificationService.notifyUser({ email }, "card-creation-failed", {
      currency,
      reason: "An error occured while creating your card. A reversal will be processed if you have been debited",
      firstName,
      linkToDashboard: `${Utils.getDashboardURL()}/cards`,
      dashboardUrl: `${Utils.getDashboardURL()}/cards`,
    });

    const reversalPayload = {
      card: foundCard,
      transfer: foundTransfer,
      narration: "Reversal for failed card creation",
      event: "card.creation.reversal",
    };

    if (foundCard.budget) reversalPayload.budget = foundCard.budget;
    else reversalPayload.balance = foundCard.balance;

    await this.reverseCardFundingMoney({ ...reversalPayload });
  }

  static async changeCardPin({ id, oldPin, newPin, issuer, company }) {
    const issuerName = this.getIssuserById(CARD_ISSUER, issuer).toLocaleLowerCase();
    return providers[issuerName].changeCardPin({ id, oldPin, newPin, company });
  }

  static async markProcessingCardAsFailed(payload) {
    const { beneficiary = null, company, budget, balance, type, currency, user, name, reason } = payload;
    const cardType = Utils.cardType()[type || "virtual"];
    const criteria = {
      type: cardType,
      currency,
      name,
      company,
    };

    if (budget) {
      const foundBudget = await BudgetRepo.getOneBudget({
        queryParams: { code: budget },
      });
      if (!foundBudget) throw new NotFoundError("Budget");
      criteria.budget = foundBudget.id;
    }

    if (balance) {
      const foundBalance = await BalanceRepo.getBalance({
        filter: {
          code: balance,
        },
      });
      if (!foundBalance) throw new NotFoundError("Balance");
      criteria.balance = foundBalance.id;
    }

    if (user) criteria.user = user;
    else {
      const foundBeneficiary = await BeneficiaryRepo.getOneBeneficiary({
        queryParams: { code: beneficiary },
        selectOptions: ["user"],
      });
      if (!foundBeneficiary) throw new NotFoundError("Beneficiary");
      criteria.user = foundBeneficiary.user;
    }

    const foundCard = await VirtualCardRepo.find(criteria);
    if (!foundCard) return;

    VirtualCardRepo.update({ conditions: { id: foundCard.id }, changes: { status: STATUSES.FAILED, failureReason: reason } });
  }

  static async topUpCard(payload) {
    const { card, cardAmount, transfer, trial = 0 } = payload;
    if (!(await RedisService.get(`credit_card:funding:${card}`))) return { message: "Already processed" };

    const [foundCard, foundTransfer] = await Promise.all([
      VirtualCardRepo.find({ code: card }),
      TransferRepo.getTransfer({ filter: { code: transfer } }),
    ]);
    if (!foundCard) throw new NotFoundError("Card");
    if (!foundTransfer) throw new NotFoundError("Transfer");

    const foundCompany = await CompanyRepo.getOneCompany({
      queryParams: { id: foundCard.company },
    });

    if (trial + 1 >= 4) {
      const reversalPayload = {
        card: foundCard,
        transfer: foundTransfer,
        narration: "Reversal for failed card funding",
        event: "card.funding.reversal",
      };

      if (foundCard.budget) reversalPayload.budget = foundCard.budget;
      else reversalPayload.balance = foundCard.balance;

      await this.reverseCardFundingMoney({ ...reversalPayload });

      // Notify engineers
      const emailPayload = {
        code: transfer,
        type: "transfer",
        externalIdentifier: foundCard.externalIdentifier,
        amount: `${Utils.getSymbolFromCurrency(foundTransfer?.currency)}${Utils.formatMoney(foundTransfer?.amount)}`,
        companyName: foundCompany?.name,
        action: "Fund Card",
      };

      return NotificationService.notifyUser({ email: Settings.get("notification_email") }, "failed-transaction-attempt", emailPayload);
    }

    await VirtualCardService.transferToCard(payload); // Throws error if there is
    RedisService.delete(`credit_card:funding:${foundCard.code}`);
    const previousBalance = await VirtualCardRepo.getAvailableBalance(foundCard);
    const cardledgerPayload = {
      currency: foundCard.currency,
      description: `Card topup (${foundCard.name})`,
      amount: cardAmount,
      user: foundCard.user,
      ...(foundCard.budget && { budget: foundCard.budget }),
      ...(foundCard.balance && { balance: foundCard.balance, company: foundCard.company }),
      status: STATUSES.PROCESSED,
      balanceBefore: previousBalance,
      balanceAfter: previousBalance + Number(cardAmount),
      transfer: foundTransfer.id,
      card: foundCard.id,
    };

    if (foundCard.budget) {
      await BudgetLedgerRepo.createBudgetLedger({ payload: cardledgerPayload });
    } else {
      await BalanceLedgerRepo.createLedger(cardledgerPayload);
    }

    await VirtualCardRepo.arithmetic({
      conditions: { where: { id: foundCard.id } },
      field: { amount: Math.abs(Number(cardAmount)) },
      action: "increment",
    });

    const { User: foundUser, currency } = foundCard;

    const notificationPayload = {
      currency,
      amount: Utils.formatMoney(cardAmount),
      login: `${Utils.getDashboardURL()}/cards`,
      cardName: foundCard.name,
      firstName: foundUser.firstName,
      companyName: foundCompany?.name,
      dashboardUrl: `${Utils.getDashboardURL()}/cards/${foundCard.code}/details`,
    };

    NotificationService.notifyUser(foundUser, "card-topup", notificationPayload);
  }

  static async getMccs(filters) {
    const { status, search, ...remainingQuery } = filters;
    let { page = 1, perPage = 50 } = remainingQuery;
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    const skip = Math.max(page - 1, 0) * perPage;

    const criteria = {};

    if (status) {
      criteria.status = Utils.getStatusValues(status);
    }

    if (search) {
      criteria[Op.or] = [
        { mcc: { [Op.like]: `%${search}%` } },
        { edited_description: { [Op.like]: `%${search}%` } },
        { combined_description: { [Op.like]: `%${search}%` } },
        { usda_description: { [Op.like]: `%${search}%` } },
        { irs_description: { [Op.like]: `%${search}%` } },
      ];
    }

    const [total = 0, mccs] = await Promise.all([
      MccRepo.count({
        queryParams: criteria,
        meta: {
          distinct: true,
          col: "id",
        },
      }),
      MccRepo.getMccs({
        queryParams: criteria,
        selectOptions: ["edited_description"],
        meta: {
          distinct: true,
          col: "id",
          offset: skip,
          limit: perPage,
          order: [["created_at", "DESC"]],
        },
      }),
    ]);

    return {
      mccs,
      meta: {
        page,
        total,
        perPage,
        hasMore: total > page * perPage,
        nextPage: page + 1,
      },
    };
  }

  static async reassignCard({ filter, payload }) {
    const { beneficiary, assignedBy } = payload;

    const [foundCard, foundBeneficiary] = await Promise.all([
      VirtualCardRepo.getCard({
        filter,
      }),
      BeneficiaryRepo.getOneBeneficiary({
        queryParams: { code: beneficiary, company: filter.company },
        selectOptions: ["status"],
        addBudgets: true,
      }),
    ]);

    if (!foundCard) throw new NotFoundError("Card");
    if (foundCard.status !== STATUSES.ACTIVE) throw new ValidationError("Cannot reassign card that is not active");

    if (!foundBeneficiary) throw new NotFoundError("Beneficiary");
    if (foundBeneficiary.status !== STATUSES.ACTIVE) throw new ValidationError("Cannot reassign card to a beneficiary that is not active");

    // Check if Beneficiary is on the budget
    const { User: foundBeneficiaryUser = {} } = foundBeneficiary;
    const { UserBudgets = [] } = foundBeneficiaryUser;
    const isUserOnBudget = UserBudgets.find((userBudget) => userBudget.budget === foundCard.budget && userBudget.status === STATUSES.ACTIVE);

    const systemRole = Settings.get("SYSTEM_ROLES");
    const { Admin, Manager } = systemRole;
    const isAdminOrManager = foundBeneficiaryUser.role_id === Admin || foundBeneficiaryUser.role_id === Manager;

    if (!(isUserOnBudget || isAdminOrManager)) throw new ValidationError("This Beneficiary is not on this budget");

    // Reasign the card
    const reassignPayload = {
      card: foundCard.id,
      company: foundCard.company,
      assignedBy,
      previousOwner: foundCard.user,
      newOwner: foundBeneficiaryUser.id,
    };

    const isVirtualCard = Utils.cardType().virtual === foundCard.type;

    await Promise.all([
      CardAssignmentRepo.createCardAssignment({ data: reassignPayload }),
      VirtualCardRepo.update({
        conditions: filter,
        changes: { user: foundBeneficiaryUser.id, ...(isVirtualCard && { requiresPinChange: true, status: STATUSES.INACTIVE }) },
      }),
    ]);

    return responseUtils.sendObjectResponse("Card reassigned successfully");
  }

  // eslint-disable-next-line consistent-return
  static async handleFinalizeLiquidationFailure(payload) {
    const { card, transfer } = payload;
    // Get Transfer and Card
    const [foundTransfer, foundCard] = await Promise.all([
      TransferRepo.getTransfer({ filter: { code: transfer } }),
      VirtualCardRepo.find({ code: card }),
    ]);

    if (!foundCard) return responseUtils.sendObjectResponse("Card not found");

    if (!foundTransfer) return responseUtils.sendObjectResponse("Transfer not found"); // Return instead of throwing an error so it doesn't call again
    if (foundTransfer.status !== STATUSES.PENDING) return responseUtils.sendObjectResponse("Transfer is not pending");

    const {
      externalIdentifier,
      CardIssuer: { name: cardIssuer },
      currency,
    } = foundCard;
    const { accountId } = Settings.get("cardIssuingAccounts")[String(cardIssuer).toLowerCase()];
    // Fund card back.
    const { status, message: cardProviderMessage } = await providers[String(cardIssuer).toLowerCase()].fundVirtualCard({
      id: externalIdentifier,
      amount: Math.abs(foundTransfer.amount),
      currency,
      accountId,
      reference: foundTransfer.reference,
    });

    if (status !== "success") {
      throw new ValidationError(`Error from Provider: ${cardProviderMessage}`);
    }

    const lastTransactionAttempt = await TransactionAttemptRepo.getTransactionAttempt({
      filter: { transfer: foundTransfer.id },
    });

    const foundCompany = await CompanyRepo.getOneCompany({
      queryParams: { id: foundCard.company },
      selectOptions: ["name"],
    });

    const emailPayload = {
      code: transfer,
      type: "transfer",
      externalIdentifier: lastTransactionAttempt?.externalIdentifier,
      amount: `${Utils.getSymbolFromCurrency(foundTransfer?.currency)}${Utils.formatMoney(foundTransfer?.amount)}`,
      companyName: foundCompany?.name,
      action: "Finalize Card Liquidation",
    };

    NotificationService.notifyUser({ email: Settings.get("notification_email") }, "failed-transaction-attempt", emailPayload);

    await Promise.all([
      TransferRepo.update({
        filter: { code: transfer },
        payload: { status: STATUSES.FAILED },
      }),

      VirtualCardRepo.arithmetic({
        conditions: { code: card },
        field: { amount: foundTransfer.amount },
        action: "increment",
      }),
    ]);

    // Create reversal ledger
    const cardAmount = await VirtualCardRepo.getAvailableBalance(foundCard);
    const transferAmount = Math.abs(foundTransfer.amount);
    const { budget, balance } = foundCard;

    let existingLedger;

    const existingLedgerCriteria = {
      transfer: foundTransfer.id,
      status: STATUSES.PENDING,
      ...(balance && { balance }),
      ...(budget && { budget }),
      card: foundCard.id,
    };

    if (budget) {
      existingLedger = await BudgetLedgerRepo.getBudgetLedger({
        criteria: existingLedgerCriteria,
        selectedOptions: ["code", "description"],
      });
    } else {
      existingLedger = await BalanceLedgerRepo.getBalanceLedger({
        filter: existingLedgerCriteria,
        selectedOptions: ["code", "description"],
      });
    }

    // eslint-disable-next-line consistent-return
    if (!existingLedger) return; // No debit ledgers, hence no need to create a reversal ledger

    const reversalLedgerPayload = {
      currency,
      amount: transferAmount,
      ...(budget && { budget }),
      ...(balance && { balance, company: foundCard.company }),
      transfer: foundTransfer.id,
      description: `[Reversal]${existingLedger.description}`,
      status: STATUSES.PROCESSED,
      user: foundCard.user,
      balanceBefore: cardAmount,
      balanceAfter: parseInt(transferAmount, 10) + cardAmount,
      card: foundCard.id,
    };

    // Update pending ledger to failed
    if (budget) {
      await BudgetLedgerRepo.updateBudgetLedger({
        criteria: existingLedger,
        payload: { status: STATUSES.FAILED },
      });
    } else {
      await BalanceLedgerRepo.updateLedger({
        filter: existingLedger,
        payload: { status: STATUSES.FAILED },
      });
    }

    // Create reversal ledgers
    if (budget) {
      await BudgetLedgerRepo.createBudgetLedger({ payload: reversalLedgerPayload, isSpent: false });
    } else {
      await BalanceLedgerRepo.createLedger(reversalLedgerPayload);
    }
  }

  static async handleInitiateLiquidationFailure(payload) {
    const { card, transfer } = payload;

    const [foundTransfer, foundCard] = await Promise.all([
      TransferRepo.getTransfer({ filter: { code: transfer } }),
      VirtualCardRepo.find({ code: card }),
    ]);

    if (!foundCard) throw new NotFoundError("Card");
    if (!foundTransfer) throw new NotFoundError("Transfer");

    const lastTransactionAttempt = await TransactionAttemptRepo.getTransactionAttempt({
      filter: { transfer: foundTransfer.id },
    });

    const foundCompany = await CompanyRepo.getOneCompany({
      queryParams: { id: foundCard.company },
      selectOptions: ["name"],
    });

    const emailPayload = {
      code: transfer,
      type: "transfer",
      externalIdentifier: lastTransactionAttempt?.externalIdentifier,
      amount: `${Utils.getSymbolFromCurrency(foundTransfer.currency)}${Utils.formatMoney(foundTransfer.amount)}`,
      companyName: foundCompany?.name,
      action: "Initiate Card Liquidation",
    };

    NotificationService.notifyUser({ email: Settings.get("notification_email") }, "failed-transaction-attempt", emailPayload);

    // Mark Transfer as failed
    // Top up card
    await Promise.all([
      TransferRepo.update({
        filter: { code: transfer },
        payload: { status: STATUSES.FAILED },
      }),
      VirtualCardRepo.arithmetic({
        conditions: { where: { code: card } },
        field: { amount: Math.abs(Number(foundTransfer.amount)) },
        action: "increment",
      }),
    ]);

    // Create reversal ledger
    const cardAmount = await VirtualCardRepo.getAvailableBalance(foundCard);
    const transferAmount = Math.abs(foundTransfer.amount);
    const { currency, budget, balance } = foundCard;

    let existingLedger;

    const existingLedgerCriteria = {
      transfer: foundTransfer.id,
      status: STATUSES.PENDING,
      ...(balance && { balance }),
      ...(budget && { budget }),
      card: foundCard.id,
    };

    if (budget) {
      existingLedger = await BudgetLedgerRepo.getBudgetLedger({
        criteria: existingLedgerCriteria,
        selectedOptions: ["code", "description"],
      });
    } else {
      existingLedger = await BalanceLedgerRepo.getBalanceLedger({
        filter: existingLedgerCriteria,
        selectedOptions: ["code", "description"],
      });
    }

    if (!existingLedger) return; // No debit ledgers, hence no need to create a reversal ledger

    const reversalLedgerPayload = {
      currency,
      amount: transferAmount,
      ...(budget && { budget }),
      ...(balance && { balance, company: foundCard.company }),
      transfer: foundTransfer.id,
      description: `[Reversal]${existingLedger.description}`,
      status: STATUSES.PROCESSED,
      user: foundCard.user,
      balanceBefore: cardAmount,
      balanceAfter: parseInt(transferAmount, 10) + cardAmount,
      card: foundCard.id,
    };

    // Update pending ledger to failed
    if (budget) {
      await BudgetLedgerRepo.updateBudgetLedger({
        criteria: existingLedgerCriteria,
        payload: { status: STATUSES.FAILED },
      });
    } else {
      await BalanceLedgerRepo.updateLedger({
        filter: existingLedgerCriteria,
        payload: { status: STATUSES.FAILED },
      });
    }

    // Create reversal ledgers
    if (budget) {
      await BudgetLedgerRepo.createBudgetLedger({ payload: reversalLedgerPayload, isSpent: false });
    } else {
      await BalanceLedgerRepo.createLedger(reversalLedgerPayload);
    }
  }

  static async initiateLiquidation({ payload }) {
    const { trial = 1, transfer, card, ...rest } = payload;

    const redisKey = `initiate_liquidation:${card}`;

    const isAlreadyInitiated = await RedisService.get(redisKey);
    if (isAlreadyInitiated) return responseUtils.sendObjectResponse("Already processed"); // Call has already been made

    await RedisService.setex(redisKey, 1, 86400); // SET TO EXPIRE IN A DAY

    if (trial + 1 >= 4) {
      await RedisService.delete(redisKey);
      return VirtualCardService.handleInitiateLiquidationFailure(payload);
    }

    const [foundTransfer, foundCard] = await Promise.all([
      TransferRepo.getTransfer({ filter: { code: transfer } }),
      VirtualCardRepo.find({ code: card }),
    ]);

    if (!foundCard) return responseUtils.sendObjectResponse("Card not found");

    if (!foundTransfer) return responseUtils.sendObjectResponse("Transfer not found"); // Return instead of throwing an error so it doesn't call again
    if (foundTransfer.status !== STATUSES.PENDING) return responseUtils.sendObjectResponse("Transfer is not pending");

    const {
      CardIssuer: { name },
      externalIdentifier,
      company,
    } = foundCard;

    const { accountId } = Settings.get("cardIssuingAccounts")[String(name).toLowerCase()];
    try {
      // Liquidate card to Settlement account
      const { error = false, data } = await providers[String(name).toLowerCase()].liquidateVirtualCard({
        id: externalIdentifier,
        amount: foundTransfer.amount,
        accountId,
        reference: foundTransfer.reference,
      });
      if (error) {
        // add to queue
        const SQSPayload = {
          id: `initiate_liquidation:${foundCard.code}`,
          idempotencyKey: `initiate_liquidation:${foundCard.code}`,
          path: `/cards/${foundCard.code}/initiate-liquidation`,
          key: process.env.INTRA_SERVICE_TOKEN,
          trial: trial + 1,
          transfer: foundTransfer.code,
          card: foundCard.code,
          ...rest,
        };
        return QueueService.addDelayedJob({}, SQSPayload, `initiate_liquidation:${foundCard.code}`, 5);
      }
      // Confirm transaction status
      const SQSPayload = {
        id: `confirm_liquidation:${foundCard.code}`,
        idempotencyKey: `confirm_liquidation:${foundCard.code}`,
        path: `/cards/${foundCard.code}/confirm-liquidation`,
        key: process.env.INTRA_SERVICE_TOKEN,
        trial: 1,
        transfer: foundTransfer.code,
        card: foundCard.code,
        externalIdentifier: data?._id,
        company,
        ...rest,
      };
      return QueueService.addDelayedJob({}, SQSPayload, `confirm_liquidation:${foundCard.code}`, 5);
    } catch (error) {
      // Add back to queue
      const SQSPayload = {
        id: `initiate_liquidation:${foundCard.code}`,
        idempotencyKey: `initiate_liquidation:${foundCard.code}`,
        path: `/cards/${foundCard.code}/initiate-liquidation`,
        key: process.env.INTRA_SERVICE_TOKEN,
        trial: trial + 1,
        transfer: foundTransfer.code,
        card: foundCard.code,
        ...rest,
      };
      return QueueService.addDelayedJob({}, SQSPayload, `initiate_liquidation:${foundCard.code}`, 5);
    } finally {
      await RedisService.delete(redisKey);
    }
  }

  static async finalizeLiquidation({ payload }) {
    const { trial = 1, transfer, card, amount, reference } = payload;

    const redisKey = `finalize_liquidation:${card}`;

    const isAlreadyInitiated = await RedisService.get(redisKey);
    if (isAlreadyInitiated) return responseUtils.sendObjectResponse("Already processed"); // Call has already been made

    if (trial + 1 >= 4) {
      await RedisService.delete(redisKey);
      return VirtualCardService.handleFinalizeLiquidationFailure(payload);
    }

    const [foundTransfer, foundCard] = await Promise.all([
      TransferRepo.getTransfer({ filter: { code: transfer } }),
      VirtualCardRepo.find({ code: card }),
    ]);

    if (!foundCard) return responseUtils.sendObjectResponse("Card not found");

    if (!foundTransfer) return responseUtils.sendObjectResponse("Transfer not found"); // Return instead of throwing an error so it doesn't call again
    if (foundTransfer.status !== STATUSES.PENDING) return responseUtils.sendObjectResponse("Transfer is not pending");

    const {
      budget: cardBudgetId,
      balance: cardBalanceId,
      CardIssuer: { name },
      company,
    } = foundCard;

    // Get recipient account
    const accountToCredit = {};
    if (cardBudgetId) {
      const foundBudget = await BudgetRepo.getBudget({ id: cardBudgetId }, true);
      if (!foundBudget) throw new NotFoundError("Budget");
      if (!foundBudget.BudgetAccount) throw new NotFoundError("Budget Account");
      const { BudgetAccount } = foundBudget;
      accountToCredit.number = BudgetAccount.number;
      accountToCredit.bankCode = BudgetAccount.bankCode;
      accountToCredit.accountId = BudgetAccount.accountId;
    } else {
      const foundBalance = await BalanceRepo.getBalance({
        filter: { id: cardBalanceId },
        includeAccount: true,
      });
      if (!foundBalance) throw new NotFoundError("Balance");
      if (!foundBalance.BankAccount) throw new NotFoundError("Bank Account");
      accountToCredit.number = foundBalance.BankAccount.number;
      accountToCredit.bankCode = foundBalance.BankAccount.bankCode;
      accountToCredit.accountId = foundBalance.BankAccount.externalIdentifier;
    }

    const { accountId } = Settings.get("cardIssuingAccounts")[String(name).toLowerCase()];

    try {
      const {
        error = false,
        message,
        responseCode,
      } = await providers[String(name).toLowerCase()].accountTransfer({
        debitAccountId: accountId,
        amount: parseInt(amount, 10),
        narration: foundTransfer.description,
        reference,
        beneficiaryAccountNumber: accountToCredit.number,
        beneficiaryBankCode: accountToCredit.bankCode,
        cardId: foundCard.code,
        event: "sudo.liquidate.card",
      });

      if (error) {
        // Add back to queue
        if ((responseCode && Number(responseCode) === 51) || String(message).toLowerCase() === "no sufficient funds") {
          // Send Notification
          const lastTransactionAttempt = await TransactionAttemptRepo.getTransactionAttempt({
            filter: { transfer: foundTransfer.id },
          });

          const foundCompany = await CompanyRepo.getOneCompany({
            queryParams: { id: foundCard.company },
            selectOptions: ["name"],
          });

          const emailPayload = {
            code: transfer,
            type: "transfer",
            externalIdentifier: lastTransactionAttempt?.externalIdentifier,
            amount: `${Utils.getSymbolFromCurrency(foundTransfer?.currency)}${Utils.formatMoney(foundTransfer?.amount)}`,
            companyName: foundCompany?.name,
            action: "Finalize Card Liquidation",
          };

          NotificationService.notifyUser({ email: Settings.get("notification_email") }, "failed-transaction-attempt", emailPayload);
        }

        const SQSPayload = {
          id: `finalize_liquidation:${foundCard.code}`,
          idempotencyKey: `finalize_liquidation:${foundCard.code}`,
          path: `/cards/${foundCard.code}/finalize-liquidation`,
          key: process.env.INTRA_SERVICE_TOKEN,
          trial: trial + 1,
          transfer: foundTransfer.code,
          reference,
          card: foundCard.code,
          amount,
        };
        return QueueService.addDelayedJob({}, SQSPayload, `finalize_liquidation:${foundCard.code}`, 5);
      }

      await Promise.all([
        BudgetLedgerRepo.updateBudgetLedger({
          criteria: { card: foundCard.id, transfer: foundTransfer.id },
          payload: { status: STATUSES.PROCESSED },
        }),
        TransferService.updateTransfer({ id: foundTransfer.id }, { status: STATUSES.SUCCESS }),
      ]);

      // Mock funding on staging
      if (!Utils.isProd()) {
        const { payment } = Settings.get("providers");
        const providerToUse = payment[company] || payment.defaultProvider;
        await providers[providerToUse].fundBank(accountToCredit.accountId, amount);
      }

      // Sets a key in redis so that when the transfers comes in, we don't send topup emails for it
      const key = `card_transaction:${accountToCredit.accountId}`;
      return RedisService.setex(key, 1, 86400); // Key to expire after 1 day... To carter for any delays that might exist during transfers
    } catch (error) {
      const SQSPayload = {
        id: `finalize_liquidation:${foundCard.code}`,
        idempotencyKey: `finalize_liquidation:${foundCard.code}`,
        path: `/cards/${foundCard.code}/finalize-liquidation`,
        key: process.env.INTRA_SERVICE_TOKEN,
        trial: trial + 1,
        transfer: foundTransfer.code,
        card: foundCard.code,
        reference,
        amount,
      };
      return QueueService.addDelayedJob({}, SQSPayload, `finalize_liquidation:${foundCard.code}`, 5);
    } finally {
      await RedisService.delete(redisKey);
    }
  }

  static async getAccountTransaction({ payload }) {
    const { externalIdentifier, company, provider = "sudo", trial, card, ...rest } = payload;
    if (trial + 1 >= 10) {
      // After max attempts, if it's not completed yet. Attempt to use funds left in account to process it
      const SQSPayload = {
        id: `finalize_liquidation:${card}`,
        idempotencyKey: `finalize_liquidation:${card}`,
        path: `/cards/${card}/finalize-liquidation`,
        key: process.env.INTRA_SERVICE_TOKEN,
        reference: externalIdentifier,
        trial: 0,
        card,
        ...rest,
      };
      return QueueService.addDelayedJob({}, SQSPayload, `finalize_liquidation:${card}`, 5);
    }

    // Get Transaction
    const { status, data, error } = await providers[provider].getTransaction({ externalIdentifier, company });

    if (error) {
      const SQSPayload = {
        id: `confirm_liquidation:${card}`,
        idempotencyKey: `confirm_liquidation:${card}`,
        path: `/cards/${card}/confirm-liquidation`,
        key: process.env.INTRA_SERVICE_TOKEN,
        trial: trial + 1,
        card,
        externalIdentifier,
        company,
        provider,
        ...rest,
      };
      return QueueService.addDelayedJob({}, SQSPayload, `confirm_liquidation:${card}`, SQSPayload.trial * 5);
    }

    if (status === "success" && String(data?.status).toLowerCase() === "completed") {
      // if success, initiate finalization
      const SQSPayload = {
        id: `finalize_liquidation:${card}`,
        idempotencyKey: `finalize_liquidation:${card}`,
        path: `/cards/${card}/finalize-liquidation`,
        key: process.env.INTRA_SERVICE_TOKEN,
        reference: externalIdentifier,
        trial: 0,
        card,
        ...rest,
      };
      return QueueService.addDelayedJob({}, SQSPayload, `finalize_liquidation:${card}`, 5);
    }

    // The status isn't completed
    const SQSPayload = {
      id: `confirm_liquidation:${card}`,
      idempotencyKey: `confirm_liquidation:${card}`,
      path: `/cards/${card}/confirm-liquidation`,
      key: process.env.INTRA_SERVICE_TOKEN,
      trial: trial + 1,
      card,
      externalIdentifier,
      company,
      provider,
      ...rest,
    };
    return QueueService.addDelayedJob({}, SQSPayload, `confirm_liquidation:${card}`, SQSPayload.trial * 5);
  }

  static async getCardBalances(company, filters = {}) {
    const bindings = [company];

    let query = `
		SELECT
			bl.balanceAfter AS amount,
			bl.card,
			bl.currency
		FROM (
			SELECT
				card,
				MAX(id) AS latestLedgerId
			FROM
				BudgetLedgers
			WHERE
				card IS NOT NULL
				AND status IN (${STATUSES.PROCESSED}, ${STATUSES.PENDING}, ${STATUSES.PROCESSING})
			GROUP BY
				card) latestLedgers
			INNER JOIN BudgetLedgers bl ON latestLedgers.latestLedgerId = bl.id
			LEFT JOIN CreditCards cc ON bl.card = cc.id
		WHERE
			cc.company = $1
		`;

    if (filters.currency) {
      bindings.push(filters.currency);
      query = `${query} AND bl.currency = $${bindings.length}`;
    }

    query = `${query} GROUP BY
			bl.card,
			bl.currency,
			bl.balanceAfter;`;

    return sequelize.query(query, {
      bind: bindings,
      type: QueryTypes.SELECT,
    });
  }

  static async reverseCardFundingMoney({ card, transfer, balance, budget, narration, event }) {
    let cardSource;
    const accountDetails = {};

    if (budget) {
      const budgetCriteria = {
        [Op.or]: [{ id: budget }, { code: budget }],
      };
      cardSource = await BudgetRepo.getBudget(budgetCriteria, true);

      if (!cardSource) throw new NotFoundError("Budget");

      await BudgetService.canBudgetProcessTransaction(cardSource.id);

      accountDetails.number = cardSource?.BudgetAccount?.number;
      accountDetails.bankCode = cardSource?.BudgetAccount?.bankCode;
      accountDetails.externalIdentifier = cardSource?.BudgetAccount?.accountId;
    } else {
      // get balance account
      cardSource = await BalanceRepo.getBalance({
        filter: {
          [Op.or]: [{ id: balance }, { code: balance }],
        },
        includeAccount: true,
      });

      if (!cardSource) throw new NotFoundError("Balance");

      await BalanceService.canBalanceProcessTransaction(cardSource.id);

      accountDetails.number = cardSource?.BankAccount?.number;
      accountDetails.bankCode = cardSource?.BankAccount?.bankCode;
      accountDetails.externalIdentifier = cardSource?.BankAccount?.externalIdentifier;
    }

    const cardIssuerName = String(card?.CardIssuer?.name).toLowerCase() || "sudo";

    const { accountId: debitAccountId, sandboxAccount } = Settings.get("cardIssuingAccounts")[cardIssuerName];
    const isLive = Utils.isProd();
    const reference = Utils.generateRandomString(17);

    const transferPayload = {
      debitAccountId,
      amount: Math.abs(transfer?.amount),
      narration,
      reference,
      beneficiaryBankCode: isLive ? accountDetails.bankCode : sandboxAccount.bankCode,
      beneficiaryAccountNumber: isLive ? accountDetails.number : sandboxAccount.accountNumber,
      cardId: card?.code,
      event,
    };

    const response = await providers[cardIssuerName].accountTransfer(transferPayload);

    // Mock reversal
    if (response.status) {
      if (!isLive) {
        const { payment } = Settings.get("providers");
        const providerToUse = payment[card.company] || payment.defaultProvider;
        await providers[providerToUse].fundBank(accountDetails.externalIdentifier, Math.abs(transfer?.amount));
      }
    }

    const key = `card_transaction:${accountDetails?.externalIdentifier}`;
    return RedisService.setex(key, 1, 86400); // Add to redis to prevent charging for deposit
  }

  static async sudoAuthorizePaymentCheck({ payload }) {
    const { data = {} } = payload || {};
    const { object: { pendingRequest = {}, card = {} } = {} } = data;

    const { _id: externalIdentifier } = card;

    const foundCard = await VirtualCardRepo.find({ externalIdentifier });
    if (!foundCard) throw new NotFoundError("Card");

    const { company, currency, budget, user, CardPolicies: foundPolicies } = foundCard;

    if (!foundPolicies.length) responseUtils.sendObjectResponse("Successful"); // No Policy found, let the payment go

    try {
      await PolicyService.singlePolicyEnforcer({
        policyCode: foundPolicies[0].Policy.code,
        company,
        entity: {
          budget,
          amount: parseInt(pendingRequest.amount, 10) * 100,
          user,
          currency,
          type: "payment",
        },
      });

      return responseUtils.sendObjectResponse("Successful");
    } catch (error) {
      return responseUtils.BadRequestException("Failed");
    }
  }
}
module.exports = VirtualCardService;

/**
 * Things that need to be done.
 * Address Successfull Liquidation and Terminatiom
 * Add Fees
 */
