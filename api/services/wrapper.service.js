class WrapperService {
  static statementWrapper = (data, companyAccount) => {
    let wrap = [];
    if (data.type === "revenue") {
      for (const item of data.result) {
        let description = item.description;
        if (description) {
          description = item.description.split("from")[1].split("(")[1].split(")")[0];
        } else {
          description = "N/A";
        }
        wrap.push({
          budget: item.description.split("from")[1].split("(")[0],
          type: item.amount < 0 ? "debit" : "credit",
          account: data.type,
          amount: Math.abs((item.amount + item.processor_fee + item.bujeti_fee) / 100),
          currency: item.currency || "NGN",
          code: item.code || item.Transaction?.code,
          status: "success",
          created_at: item.Transaction?.created_at || item.created_at,
          source_acc_number: description,
          beneficiary_name: companyAccount?.accountName || "N/A",
          beneficiary_account_number: companyAccount?.number || "N/A",
          description: item.description || item.Transaction?.description || "N/A",
        });
      }
    }
    if (data.type === "expense") {
      for (const item of data.result) {
        wrap.push({
          budget: item.Budget.name,
          type: item.amount < 0 ? "debit" : "credit",
          account: data.type,
          amount: Math.abs(item.amount / 100),
          code: item.Transaction?.code || "N/A",
          status: "success",
          currency: item.Transaction?.currency || item.currency || "NGN",
          created_at: item.Transaction?.created_at || item.created_at,
          source_acc_number: companyAccount?.number || "N/A",
          beneficiary_name: item.Transaction?.BankAccount?.accountName,
          beneficiary_account_number: item.Transaction?.BankAccount?.number,
          description: item.description || item.Transaction?.description || "N/A",
        });
      }
    }
    return wrap;
  };
}

module.exports = WrapperService;
