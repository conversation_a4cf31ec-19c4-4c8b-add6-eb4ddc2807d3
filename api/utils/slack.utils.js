/* eslint-disable no-unsafe-optional-chaining */
const { default: axios } = require("axios");
const { WebClient } = require("@slack/web-api");
const { format } = require("date-fns");
const { Op } = require("sequelize");
const { CARD_ISSUER } = require("../models/cardissuer");
const { log, Log } = require("./logger.utils");
const { ThirdPartyLogRepo, ExternalIntegrationRepo } = require("../repositories/index.repo");
const Redis = require("./redis");
const { STATUSES } = require("../models/status");
const { notifyUserNewClaimedReferral } = require("../services/referral.service");
const UserRepo = require("../repositories/user.repo");
const BeneficiaryRepo = require("../repositories/beneficiary.repo");
const FundRequestRepo = require("../repositories/fundRequest.repo");
const BudgetRepo = require("../repositories/budget.repo");
const { PLATFORM } = require("../constants/platforms");
const { formatMoney, isValidDate } = require("./utils");
const { FUND_REQUEST_TYPES } = require("../constants/fundRequest");
const VendorService = require("../services/vendorService");
const CategoryService = require("../services/category.service");
const BudgetService = require("../services/budget");
const { NOTIFICATION_TYPE } = require("../constants/notifications");
const CompanyService = require("../services/company");
const PROMISE_STATUS = require("../constants/promiseStatus");

const requestTypes = {
  [FUND_REQUEST_TYPES.BUDGET]: {
    title: "Budget Request 🏦",
    type: "Budget Request",
    getText: (requestData) =>
      `New Budget Request: ${requestData?.user?.firstName} ${requestData?.user?.lastName} has requested ${requestData?.currency}${formatMoney(
        requestData?.amount
      )}`,
  },
  [FUND_REQUEST_TYPES.PAYMENT]: {
    title: "Payment Request 💳",
    type: "Payment Request",
    getText: (requestData) =>
      `New Payment Request: ${requestData?.user?.firstName} ${requestData?.user?.lastName} has requested ${requestData?.currency}${formatMoney(
        requestData?.amount
      )}`,
  },
  [FUND_REQUEST_TYPES.TOP_UP]: {
    title: "Top-up Request 💰",
    type: "Top-up Request",
    getText: (requestData) =>
      `New Top-up Request: ${requestData?.user?.firstName} ${requestData?.user?.lastName} has requested ${requestData?.currency}${formatMoney(
        requestData?.amount
      )}`,
  },
};

const Utils = {
  async callAPI(method, url, payload, params) {
    let result = {
      error: false,
      message: "",
      body: {},
      data: {},
      status: "",
      config: {},
    };

    if (url !== "https://accounts.slack.com/oauth/v2/token") {
      const ongoingRequests = await Redis.get(url);
      result.message = "Please wait, we are still cooking!";
      result.error = true;
      if (ongoingRequests) return result;
      Redis.set(url, 1);
    }
    try {
      // eslint-disable-next-line no-shadow
      let status = 200;
      let config;
      let data;

      if (method === "get") ({ data, status, config } = await axios.get(url, payload, params));
      if (method === "post") ({ data, status, config } = await axios.post(url, payload, params));
      const response = data;

      result = {
        error: false,
        message: response.message,
        body: response,
        data: response,
        status,
        config: {
          url: `${config.url}`,
          method: config.method,
          data: config.data,
        },
      };
      if (config.url !== "https://slack.com/api/oauth.v2.access")
        ThirdPartyLogRepo.createLog({
          message: `Slack Request`,
          company: -1,
          event: "slack_api_request",
          payload: JSON.stringify({ ...payload, headers: undefined }),
          provider: CARD_ISSUER.Slack,
          providerType: "accountingSoftware",
          response: (data && JSON.stringify(data)) || "",
          statusCode: status || 200,
          endpoint: config.url,
          method: config.method,
        });
    } catch (error) {
      log(Log.bg.red, { message: "Error in Slack API call", error });
      // eslint-disable-next-line no-shadow
      const { response, message, status, config } = error;
      const { status: statusCode = 400, data: errorData = {} } = response || {};

      result = {
        message: errorData?.message || message,
        status: status || statusCode,
        data: errorData,
        body: errorData,
        error: true,
        config: {
          url: `${config.url}`,
          method: config.method,
          data: config.data,
        },
      };
      if (config.url !== "https://slack.com/api/oauth.v2.access")
        ThirdPartyLogRepo.createLog({
          message: `Slack Request Error`,
          company: -1,
          event: "slack_api_request_error",
          payload: JSON.stringify({ ...payload, headers: undefined }),
          provider: CARD_ISSUER.Slack,
          providerType: "accountingSoftware",
          response: (errorData && JSON.stringify(errorData)) || "",
          statusCode: status || 400,
          endpoint: config.url,
          method: config.method,
        });
    }
    Redis.delete(url);
    return result;
  },
  async getSlackChannels(accessToken) {
    const client = new WebClient(accessToken);
    try {
      // Call the conversations.list method using the WebClient
      const result = await client.conversations.list({
        // The types of conversations to include
        types: "public_channel,private_channel",
        // Exclude archived channels
        exclude_archived: true,
        // Limit the number of results
        limit: 200,
      });

      // Iterate over the channels and log their names and IDs
      // eslint-disable-next-line camelcase
      return (result?.channels || []).map(({ id, name, is_archived, is_general, name_normalized, topic, purpose }) => ({
        id,
        name,
        // eslint-disable-next-line camelcase
        is_archived,
        // eslint-disable-next-line camelcase
        is_general,
        // eslint-disable-next-line camelcase
        name_normalized,
        topic,
        purpose,
      }));
    } catch (error) {
      log(Log.bg.red, { message: "Error fetching Slack conversations", error });
      return [];
    }
  },
  async joinChannel(accessToken, channelId) {
    const client = new WebClient(accessToken);
    const result = await client.conversations.join({ channel: channelId });
    ThirdPartyLogRepo.createLog({
      message: `Slack Request`,
      company: -1,
      event: "slack_api_call",
      payload: JSON.stringify({
        channel: channelId,
      }),
      provider: CARD_ISSUER.Slack,
      providerType: "workspace",
      response: (result && JSON.stringify(result)) || "",
      statusCode: 200,
      endpoint: "https://slack.com/api/conversations.join",
      method: "post",
    });
    return result;
  },
  async getIntegrationData(company) {
    if (!company) {
      log(Log.bg.red, { message: "Missing company ID for Slack integration" });
      throw new Error("Company ID is required");
    }

    try {
      const foundIntegration = await ExternalIntegrationRepo.getIntegration({
        filter: {
          platform: PLATFORM.SLACK.toUpperCase(),
          company,
          status: STATUSES.ACTIVE,
        },
        selectedOptions: ["access_token", "metadata"],
      });

      if (!foundIntegration) {
        log(Log.bg.red, {
          message: `No active Slack integration found for company ${company}`,
        });
        throw new Error("Slack integration not found");
      }

      if (!foundIntegration.dataValues || !foundIntegration.dataValues.access_token || !foundIntegration.dataValues.metadata?.channel) {
        log(Log.bg.red, {
          message: `Invalid Slack integration for company ${company}`,
        });
        throw new Error("Slack integration not properly configured");
      }

      return {
        accessToken: foundIntegration.dataValues.access_token,
        channelId: foundIntegration.dataValues.metadata.channel,
      };
    } catch (error) {
      log(Log.bg.red, {
        message: `Slack integration error: ${error.message}`,
        error,
      });
      throw error;
    }
  },
  async sendReimbursementNotifcation(requestData) {
    try {
      const result = await this.getIntegrationData(requestData.company);
      const client = new WebClient(result.accessToken);
      const baseUrl = this.getBaseUrl();

      const userProfileLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.TEAM_MEMBER(requestData.user?.code));
      const approveLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.FUND_REQUEST(requestData.id), "/approve");
      const declineLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.FUND_REQUEST(requestData.id), "/decline");
      const moreLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.FUND_REQUEST(requestData.id));

      const requestInfo = requestTypes[requestData.type] || {
        title: "Reimbursement Request 💳",
        type: "Reimbursement Request",
        getText: (data) =>
          `New Reimbursement Request: ${data?.user?.firstName} ${data?.user?.lastName} has requested ${data?.currency}${formatMoney(data?.amount)}`,
      };

      const payload = {
        channel: result.channelId,
        attachments: [
          {
            color: "#36a64f", // Green color for the left border
            blocks: [
              {
                type: "header",
                text: {
                  type: "plain_text",
                  text: requestInfo.title,
                  emoji: true,
                },
              },
              {
                type: "section",
                text: {
                  type: "mrkdwn",
                  text: `You have a new request:\n*<${userProfileLink}|${requestData.user?.firstName} ${requestData.user?.lastName}>*`,
                },
              },
              {
                type: "section",
                fields: [
                  {
                    type: "mrkdwn",
                    text: `*Amount:*\n${requestData?.currency}${formatMoney(requestData?.amount)}`,
                  },
                  {
                    type: "mrkdwn",
                    text: `*From:*\n${requestData.user?.firstName} ${requestData.user?.lastName}`,
                  },
                  {
                    type: "mrkdwn",
                    text: `*Submitted:*\n${format(new Date(), "MMMM dd, yyyy")}`,
                  },
                  {
                    type: "mrkdwn",
                    text: `*Reason:*\n${requestData?.description || "No reason provided"}`,
                  },
                ],
              },
              {
                type: "divider",
              },
              {
                type: "actions",
                elements: [
                  {
                    type: "button",
                    text: {
                      type: "plain_text",
                      emoji: true,
                      text: "Approve",
                    },
                    style: "primary",
                    url: approveLink,
                    value: `approve_fund_request_${requestData.requestId}`,
                  },
                  {
                    type: "button",
                    text: {
                      type: "plain_text",
                      emoji: true,
                      text: "Decline",
                    },
                    style: "danger",
                    url: declineLink,
                    value: `decline_fund_request_${requestData.requestId}`,
                  },
                  {
                    type: "button",
                    text: {
                      type: "plain_text",
                      emoji: true,
                      text: "More",
                    },
                    url: moreLink,
                    value: `more_fund_request_${requestData.requestId}`,
                  },
                ],
              },
            ],
          },
        ],
        text: requestInfo.getText(requestData),
        unfurl_links: false,
        unfurl_media: true,
      };

      // Add additional fields based on request type
      if (requestData?.deadLine && isValidDate(requestData.deadLine)) {
        payload.attachments[0].blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Deadline:*\n${format(new Date(requestData.deadLine), "MMMM dd, yyyy")}`,
        });
      }

      if (requestData.source) {
        payload.attachments[0].blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Source of funds:*\n${requestData?.source.name || "Not specified"}`,
        });
      }

      if (requestData.type === FUND_REQUEST_TYPES.BUDGET && requestData.meta?.budget) {
        payload.attachments[0].blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Budget Name:*\n${requestData.meta.budget}`,
        });
      } else if (requestData.type === FUND_REQUEST_TYPES.PAYMENT && requestData.vendor) {
        payload.attachments[0].blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Vendor:*\n${requestData.vendor.name || "Not specified"}`,
        });
      } else if (requestData.type === FUND_REQUEST_TYPES.TOP_UP) {
        if (requestData.budget) {
          payload.attachments[0].blocks[2].fields.push({
            type: "mrkdwn",
            text: `*Target Budget:*\n${requestData.budget.name || "Not specified"}`,
          });
        }
        if (requestData.bankAccount) {
          payload.attachments[0].blocks[2].fields.push({
            type: "mrkdwn",
            text: `*Target Account:*\n${requestData.bankAccount.name || "Not specified"}`,
          });
        }
        if (requestData.card) {
          payload.attachments[0].blocks[2].fields.push({
            type: "mrkdwn",
            text: `*Target Card:*\n${requestData.card.name || "Not specified"}`,
          });
        }
      }

      if (requestData.category) {
        payload.attachments[0].blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Category:*\n${requestData.category.name || "Not categorized"}`,
        });
      }

      const response = await client.chat.postMessage(payload);

      await ThirdPartyLogRepo.createLog({
        message: "Slack Reimbursement Request Notification",
        company: requestData.company,
        event: "slack_api_request",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.Slack,
        providerType: "workspace",
        response: (response && JSON.stringify(response)) || "",
        statusCode: 200,
        endpoint: "https://slack.com/api",
        method: "post",
      });

      log(Log.bg.blue, {
        message: `Successfully sent reimbursement request notification to channel ${result.channelId}`,
      });

      return {
        error: false,
        message: "Reimbursement request notification sent successfully",
      };
    } catch (error) {
      log(Log.bg.red, { message: "Error posting reimbursement request", error });

      await ThirdPartyLogRepo.createLog({
        message: "Slack Reimbursement Request Error",
        company: requestData.company,
        event: "slack_api_request_error",
        payload: JSON.stringify(requestData),
        provider: CARD_ISSUER.Slack,
        providerType: "workspace",
        response: (error && JSON.stringify(error)) || "",
        statusCode: 500,
        endpoint: "https://slack.com/api",
        method: "post",
      });

      return {
        error: true,
        message: `Failed to post reimbursement request: ${error.message}`,
      };
    }
  },

  // Helper functions for deep linking
  constructDeepLink(baseUrl, path, lastPath = "") {
    const url = new URL(`${baseUrl}${path}${lastPath}`);
    return url.toString();
  },

  getBaseUrl() {
    return process.env.DASHBOARD_URL || "https://dashboard-staging.bujeti.com";
  },

  DEEP_LINK_PATHS: {
    TEAM_MEMBER: (code) => `/teams/members/profile/${code}`,
    FUND_REQUEST: (id) => `/requests/funds/${id}`,
    BUDGET: {
      OVERVIEW: (code) => `/expenses/budgets/${code}/overview`,
      TOPUP: (code) => `/expenses/budgets/${code}/topup`,
    },
    INVOICE: (code) => `/invoices/${code}`,
    CARD_REQUEST: (code) => `/cards/all-cards/${code}/details`,
    TRANSACTION: (code) => `/transactions/${code}`,
  },

  async sendEntityRequestNotification(data) {
    try {
      const result = await this.getIntegrationData(data.company);
      const client = new WebClient(result.accessToken);
      const baseUrl = this.getBaseUrl();

      // If there's an approval flow (success is true), send to admins directly
      if (data.success) {
        // Get admins for the company
        const foundCompanyWithAdmins = await CompanyService.getCompanyWithAdmins({
          id: data.company,
        });
        const admins = foundCompanyWithAdmins?.Users || [];

        if (admins.length > 0) {
          // Get user details
          const user = await UserRepo.getOneUser({
            queryParams: { code: data.user.code },
            selectOptions: ["id", "code", "firstName", "lastName"],
          });

          // Notify admins
          await this.notifyAdminsForRequestApproval({
            requestData: data,
            user,
            admins,
            company: data.company,
          });

          // Log success
          log(Log.bg.blue, {
            message: `Successfully sent fund request notifications to ${admins.length} admin(s)`,
          });

          return true;
        }
      }

      // Continue with regular channel notification if success is true or no admins found
      const userProfileLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.TEAM_MEMBER(data.user?.code));
      const approvalLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.FUND_REQUEST(data.requestId));
      const approveLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.FUND_REQUEST(data.requestId), "/approve");
      const declineLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.FUND_REQUEST(data.requestId), "/decline");

      const requestInfo = requestTypes[data.type] || {
        title: "Fund Request 🔔",
        type: "Fund Request",
        getText: (reqData) =>
          `New Fund Request: ${reqData?.user?.firstName} ${reqData?.user?.lastName} has requested ${reqData?.currency}${formatMoney(
            reqData?.amount
          )}`,
      };

      const payload = {
        channel: result.channelId,
        attachments: [
          {
            color: "#36a64f", // Green color for the left border
            blocks: [
              {
                type: "header",
                text: {
                  type: "plain_text",
                  text: requestInfo.title,
                  emoji: true,
                },
              },
              {
                type: "section",
                text: {
                  type: "mrkdwn",
                  text: `You have a new request:\n*<${userProfileLink}|${data.user?.firstName} ${data.user?.lastName}>*`,
                },
              },
              {
                type: "section",
                fields: [
                  {
                    type: "mrkdwn",
                    text: `*Amount:*\n${data?.currency}${formatMoney(data?.amount)}`,
                  },
                  {
                    type: "mrkdwn",
                    text: `*From:*\n${data.user?.firstName} ${data.user?.lastName}`,
                  },
                  {
                    type: "mrkdwn",
                    text: `*Submitted:*\n${format(new Date(), "MMMM dd, yyyy")}`,
                  },
                  {
                    type: "mrkdwn",
                    text: `*Reason:*\n${data?.description || "No reason provided"}`,
                  },
                ],
              },
              {
                type: "divider",
              },
              {
                type: "actions",
                elements: [
                  {
                    type: "button",
                    text: {
                      type: "plain_text",
                      emoji: true,
                      text: "Approve",
                    },
                    style: "primary",
                    url: approveLink,
                    value: `approve_fund_request_${data.requestId}`,
                  },
                  {
                    type: "button",
                    text: {
                      type: "plain_text",
                      emoji: true,
                      text: "Decline",
                    },
                    style: "danger",
                    url: declineLink,
                    value: `decline_fund_request_${data.requestId}`,
                  },
                  {
                    type: "button",
                    text: {
                      type: "plain_text",
                      emoji: true,
                      text: "More",
                    },
                    url: approvalLink,
                    value: `more_fund_request_${data.requestId}`,
                  },
                ],
              },
            ],
          },
        ],
        text: requestInfo.getText(data),
        unfurl_links: false,
        unfurl_media: true,
      };

      const [vendorData, categoryData, budgetData] = await Promise.allSettled([
        data.vendor ? VendorService.getVendor({ code: data.vendor }) : null,
        data.category ? CategoryService.getCategory({ code: data.category }) : null,
        data.budget ? BudgetService.getBudget({ code: data.budget }) : null,
      ]);

      const vendor = vendorData?.status === PROMISE_STATUS.FULFILLED ? vendorData.value : null;
      const category = categoryData?.status === PROMISE_STATUS.FULFILLED ? categoryData.value : null;
      const budget = budgetData?.status === PROMISE_STATUS.FULFILLED ? budgetData.value : null;

      // Get fund request details to access supporting documents
      const fundRequest = await FundRequestRepo.getFundRequest({
        queryParams: { code: data.requestId },
        selectOptions: ["id", "code"],
      });

      if (fundRequest?.FundRequestAssets?.length > 0) {
        // Add supporting documents section
        payload.attachments[0].blocks.push({
          type: "section",
          text: {
            type: "mrkdwn",
            text: "*Supporting Documents:*",
          },
        });

        let requestPath;
        switch (data.type) {
          case NOTIFICATION_TYPE.PAYMENT:
            requestPath = "payments";
            break;
          case NOTIFICATION_TYPE.REIMBURSEMENT:
            requestPath = "reimbursements";
            break;
          default:
            requestPath = "funds";
        }

        const fileUrl = `${baseUrl}/requests/${requestPath}/${fundRequest.code}`;

        payload.attachments[0].blocks.push({
          type: "section",
          text: {
            type: "mrkdwn",
            text: `📎 <${fileUrl}|View Supporting Documents>`,
          },
        });

        // Add a divider after documents
        payload.attachments[0].blocks.push({
          type: "divider",
        });
      }

      // Add deadline if it exists and is valid
      if (data?.deadLine && isValidDate(data.deadLine)) {
        payload.attachments[0].blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Deadline:*\n${format(new Date(data.deadLine), "MMMM dd, yyyy")}`,
        });
      }

      // Add source of funds if it exists
      if (data.source) {
        payload.attachments[0].blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Source of funds:*\n${data?.source.name || "Not specified"}`,
        });
      }

      // Add type-specific information
      if (data.type === FUND_REQUEST_TYPES.BUDGET && data.meta?.budget) {
        payload.attachments[0].blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Budget Name:*\n${budget?.name || "Not specified"}`,
        });
      } else if (data.type === FUND_REQUEST_TYPES.PAYMENT && data.vendor) {
        payload.attachments[0].blocks[2].fields.push( {
          type: "mrkdwn",
          text: `*Vendor:*\n<${this.constructDeepLink(baseUrl, `/expenses/vendors/profile/${vendor.code}`)}|${vendor.name}>`,
        });
      } else if (data.type === FUND_REQUEST_TYPES.TOP_UP) {
        console.log(data, "got here")
        if (data.budget) {
          payload.attachments[0].blocks[2].fields.push({
            type: "mrkdwn",
            text: `*Target Budget:*\n${budget?.name || "Not specified"}`,
          });
        }
        if (data.bankAccount) {
          payload.attachments[0].blocks[2].fields.push({
            type: "mrkdwn",
            text: `*Target Account:*\n${data.bankAccount.name || "Not specified"}`,
          });
        }
        if (data.card) {
          payload.attachments[0].blocks[2].fields.push({
            type: "mrkdwn",
            text: `*Target Card:*\n${data.card.name || "Not specified"}`,
          });
        }
      }

      // Add category if it exists
      if (data.category) {
        payload.attachments[0].blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Category:*\n${category?.name || "Not categorized"}`,
        });
      }

      const response = await client.chat.postMessage(payload);

      await ThirdPartyLogRepo.createLog({
        message: "Slack Request",
        company: data.company,
        event: "slack_api_request",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.Slack,
        providerType: "workspace",
        response: (response && JSON.stringify(response)) || "",
        statusCode: 200,
        endpoint: "https://slack.com/api",
        method: "post",
      });

      log(Log.bg.blue, {
        message: `Successfully sent message ${response.ts} in conversation ${result.channelId}`,
      });

      return {
        error: false,
        message: "Fund request notification sent successfully",
      };
    } catch (error) {
      log(Log.bg.red, { message: "Error posting fund request", error });

      await ThirdPartyLogRepo.createLog({
        message: "Slack Fund Request Error",
        company: data.company,
        event: "slack_api_request_error",
        payload: JSON.stringify(data),
        provider: CARD_ISSUER.Slack,
        providerType: "workspace",
        response: (error && JSON.stringify(error)) || "",
        statusCode: 500,
        endpoint: "https://slack.com/api",
        method: "post",
      });

      return {
        error: true,
        message: `Failed to send fund request notification: ${error.message}`,
      };
    }
  },

  async sendBudgetTopupNotification(data) {
    try {
      const result = await this.getIntegrationData(data.company);
      const client = new WebClient(result.accessToken);
      const baseUrl = this.getBaseUrl();

      const userProfileLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.TEAM_MEMBER(data.user?.code));

      const budgetOverviewLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.BUDGET.OVERVIEW(data.budget?.code || data.budgetCode));

      const approveLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.BUDGET.TOPUP(data.budget?.code || data.budgetCode), "/approve");
      const declineLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.BUDGET.TOPUP(data.budget?.code || data.budgetCode), "/decline");

      const payload = {
        channel: result.channelId,
        blocks: [
          {
            type: "header",
            text: {
              type: "plain_text",
              text: "Budget Top-up Request 🔔",
              emoji: true,
            },
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `You have a new request:\n*<${userProfileLink}|${data.user?.firstName} ${data.user?.lastName}>*`,
            },
          },
          {
            type: "section",
            fields: [
              {
                type: "mrkdwn",
                text: `*Amount:*\n${data?.currency}${formatMoney(data?.amount)}`,
              },
              {
                type: "mrkdwn",
                text: `*From:*\n${data.user?.firstName} ${data.user?.lastName}`,
              },
              {
                type: "mrkdwn",
                text: `*To:*\n${data?.budget?.name || "Budget"}`,
              },
              {
                type: "mrkdwn",
                text: `*Type:*\nBudget Top-up`,
              },
              {
                type: "mrkdwn",
                text: `*When:*\nSubmitted ${format(new Date(), "MMMM dd, yyyy")}`,
              },
              {
                type: "mrkdwn",
                text: `*Reason:*\n${data?.reason || "No reason provided"}`,
              },
            ],
          },
          {
            type: "divider",
          },
          {
            type: "actions",
            elements: [
              {
                type: "button",
                text: {
                  type: "plain_text",
                  emoji: true,
                  text: "Approve",
                },
                style: "primary",
                url: approveLink,
                value: `approve_budget_topup_${data.id}`,
              },
              {
                type: "button",
                text: {
                  type: "plain_text",
                  emoji: true,
                  text: "Decline",
                },
                style: "danger",
                url: declineLink,
                value: `decline_budget_topup_${data.id}`,
              },
              {
                type: "button",
                text: {
                  type: "plain_text",
                  emoji: true,
                  text: "More",
                },
                url: budgetOverviewLink,
                value: `more_budget_topup_${data.id}`,
              },
            ],
          },
        ],
        text: `New Budget Top-up Request: ${data?.user?.firstName} ${data?.user?.lastName} has requested a top-up of ${data?.currency}${formatMoney(
          data?.amount
        )} for ${data?.budget?.name || "a budget"}`,
      };

      // Add deadline if it exists and is valid
      if (data?.deadLine && !Number.isNaN(new Date(data.deadLine).getTime())) {
        payload.blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Deadline:*\n${format(new Date(data.deadLine), "MMMM dd, yyyy")}`,
        });
      }

      // Add source of funds if it exists
      if (data.source) {
        payload.blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Source of funds:*\n${data?.source.name}`,
        });
      }

      // Add team if it exists
      if (data.budget && data.budget.team) {
        payload.blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Team:*\n${data.budget.team.name}`,
        });
      }

      // Add current balance for context
      payload.blocks[2].fields.push({
        type: "mrkdwn",
        text: `*Current Balance:*\n${data?.currency}${formatMoney(data?.budget?.balance)}`,
      });

      const response = await client.chat.postMessage(payload);

      await ThirdPartyLogRepo.createLog({
        message: "Slack Budget Top-up Notification",
        company: data.company,
        event: "slack_api_request",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.Slack,
        providerType: "workspace",
        response: (response && JSON.stringify(response)) || "",
        statusCode: 200,
        endpoint: "https://slack.com/api",
        method: "post",
      });

      log(Log.bg.blue, {
        message: `Successfully sent budget topup notification to channel ${result.channelId}`,
      });

      return {
        error: false,
        message: "Budget topup notification sent successfully",
      };
    } catch (error) {
      log(Log.bg.red, {
        message: "Error posting budget top-up notification",
        error,
      });

      await ThirdPartyLogRepo.createLog({
        message: "Slack Budget Top-up Notification Error",
        company: data.company,
        event: "slack_api_request_error",
        payload: JSON.stringify(data),
        provider: CARD_ISSUER.Slack,
        providerType: "workspace",
        response: (error && JSON.stringify(error)) || "",
        statusCode: 500,
        endpoint: "https://slack.com/api",
        method: "post",
      });

      return {
        error: true,
        message: `Failed to send budget topup notification: ${error.message}`,
      };
    }
  },

  async sendCardTopupNotification(data) {
    try {
      const result = await this.getIntegrationData(data.company);
      const client = new WebClient(result.accessToken);
      const baseUrl = this.getBaseUrl();

      const userProfileLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.TEAM_MEMBER(data.user?.code));

      const cardDetailsLink = data.card?.code ? this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.CARD_REQUEST(data.card.code)) : null;

      const approveLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.CARD_REQUEST(data.card.code), "/approve");
      const declineLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.CARD_REQUEST(data.card.code), "/decline");

      const payload = {
        channel: result.channelId,
        blocks: [
          {
            type: "header",
            text: {
              type: "plain_text",
              text: "Card Top-up Request 🔔",
              emoji: true,
            },
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `You have a new request:\n*<${userProfileLink}|${data.user?.firstName} ${data.user?.lastName}>*`,
            },
          },
          {
            type: "section",
            fields: [
              {
                type: "mrkdwn",
                text: `*Amount:*\n${data?.currency}${formatMoney(data?.amount)}`,
              },
              {
                type: "mrkdwn",
                text: `*From:*\n${data.user?.firstName} ${data.user?.lastName}`,
              },
              {
                type: "mrkdwn",
                text: `*To:*\n${data?.card?.name || "Card"}`,
              },
              {
                type: "mrkdwn",
                text: `*Type:*\nCard Top-up`,
              },
              {
                type: "mrkdwn",
                text: `*When:*\nSubmitted ${format(new Date(), "MMMM dd, yyyy")}`,
              },
              {
                type: "mrkdwn",
                text: `*Reason:*\n${data?.reason || "No reason provided"}`,
              },
            ],
          },
          {
            type: "divider",
          },
          ...(cardDetailsLink
            ? [
                {
                  type: "actions",
                  elements: [
                    {
                      type: "button",
                      text: {
                        type: "plain_text",
                        emoji: true,
                        text: "Approve",
                      },
                      style: "primary",
                      url: approveLink,
                      value: `approve_card_topup_${data.id}`,
                    },
                    {
                      type: "button",
                      text: {
                        type: "plain_text",
                        emoji: true,
                        text: "Decline",
                      },
                      style: "danger",
                      url: declineLink,
                      value: `decline_card_topup_${data.id}`,
                    },
                    {
                      type: "button",
                      text: {
                        type: "plain_text",
                        emoji: true,
                        text: "More",
                      },
                      url: cardDetailsLink,
                      value: `more_card_topup_${data.id}`,
                    },
                  ],
                },
              ]
            : []),
        ],
        text: `New Card Top-up Request: ${data?.user?.firstName} ${data?.user?.lastName} has requested a top-up of ${data?.currency}${formatMoney(
          data?.amount
        )} for ${data?.card?.name || "a card"}`,
      };

      // Add current balance for context
      payload.blocks[2].fields.push({
        type: "mrkdwn",
        text: `*Current Balance:*\n${data?.currency}${formatMoney(data?.card?.balance)}`,
      });

      // Add deadline if it exists and is valid
      if (data?.deadLine && !Number.isNaN(new Date(data.deadLine).getTime())) {
        payload.blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Deadline:*\n${format(new Date(data.deadLine), "MMMM dd, yyyy")}`,
        });
      }

      // Add source of funds if it exists
      if (data.source) {
        payload.blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Source of funds:*\n${data?.source.name}`,
        });
      }

      // Add budget if it exists
      if (data.budget) {
        payload.blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Target budget:*\n${data?.budget.name}`,
        });
      }

      const response = await client.chat.postMessage(payload);

      await ThirdPartyLogRepo.createLog({
        message: "Slack Card Top-up Notification",
        company: data.company,
        event: "slack_api_request",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.Slack,
        providerType: "workspace",
        response: (response && JSON.stringify(response)) || "",
        statusCode: 200,
        endpoint: "https://slack.com/api",
        method: "post",
      });

      log(Log.bg.blue, {
        message: `Successfully sent card topup notification to channel ${result.channelId}`,
      });

      return {
        error: false,
        message: "Card topup notification sent successfully",
      };
    } catch (error) {
      log(Log.bg.red, {
        message: "Error posting card top-up notification",
        error,
      });

      await ThirdPartyLogRepo.createLog({
        message: "Slack Card Top-up Notification Error",
        company: data.company,
        event: "slack_api_request_error",
        payload: JSON.stringify(data),
        provider: CARD_ISSUER.Slack,
        providerType: "workspace",
        response: (error && JSON.stringify(error)) || "",
        statusCode: 500,
        endpoint: "https://slack.com/api",
        method: "post",
      });

      return {
        error: true,
        message: `Failed to send card topup notification: ${error.message}`,
      };
    }
  },

  async sendCardRequestNotification(data) {
    try {
      const result = await this.getIntegrationData(data.company);
      const client = new WebClient(result.accessToken);
      const baseUrl = this.getBaseUrl();

      const userProfileLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.TEAM_MEMBER(data.user?.code));

      // If there's a code or ID to link to
      const cardRequestLink = data.code || data.id ? this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.CARD_REQUEST(data.code || data.id)) : null;

      const approveLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.CARD_REQUEST(data.code || data.id), "/approve");
      const declineLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.CARD_REQUEST(data.code || data.id), "/decline");

      const payload = {
        channel: result.channelId,
        blocks: [
          {
            type: "header",
            text: {
              type: "plain_text",
              text: "Card Request 🔔",
              emoji: true,
            },
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `You have a new request:\n*<${userProfileLink}|${data.user?.firstName} ${data.user?.lastName}>*`,
            },
          },
          {
            type: "section",
            fields: [
              {
                type: "mrkdwn",
                text: `*From:*\n${data.user?.firstName} ${data.user?.lastName}`,
              },
              {
                type: "mrkdwn",
                text: `*Type:*\nCard Request (${data?.cardType || "Standard"})`,
              },
              {
                type: "mrkdwn",
                text: `*Spending Limit:*\n${data?.currency}${data?.spendingLimit ? (data.spendingLimit / 100).toLocaleString() : "Not specified"}`,
              },
              {
                type: "mrkdwn",
                text: `*When:*\nSubmitted ${format(new Date(), "MMMM dd, yyyy")}`,
              },
              {
                type: "mrkdwn",
                text: `*Purpose:*\n${data?.purpose || "No purpose provided"}`,
              },
            ],
          },
          {
            type: "divider",
          },
          ...(cardRequestLink
            ? [
                {
                  type: "actions",
                  elements: [
                    {
                      type: "button",
                      text: {
                        type: "plain_text",
                        emoji: true,
                        text: "Approve",
                      },
                      style: "primary",
                      url: approveLink,
                      value: `approve_card_request_${data.requestId}`,
                    },
                    {
                      type: "button",
                      text: {
                        type: "plain_text",
                        emoji: true,
                        text: "Decline",
                      },
                      style: "danger",
                      url: declineLink,
                      value: `decline_card_request_${data.requestId}`,
                    },
                    {
                      type: "button",
                      text: {
                        type: "plain_text",
                        emoji: true,
                        text: "More",
                      },
                      url: cardRequestLink,
                      value: `more_card_request_${data.requestId}`,
                    },
                  ],
                },
              ]
            : []),
        ],
        text: `New Card Request: ${data?.user?.firstName} ${data?.user?.lastName} has requested a new ${data?.cardType || "standard"} card`,
      };

      // Add deadline if it exists and is valid
      if (data?.deadLine && !Number.isNaN(new Date(data.deadLine).getTime())) {
        payload.blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Deadline:*\n${format(new Date(data.deadLine), "MMMM dd, yyyy")}`,
        });
      }

      // Add target budget if it exists
      if (data.budget) {
        payload.blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Target budget:*\n${data?.budget.name}`,
        });
      }

      // Add team if it exists
      if (data.team) {
        payload.blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Team:*\n${data?.team.name || data?.teamName}`,
        });
      }

      const response = await client.chat.postMessage(payload);

      await ThirdPartyLogRepo.createLog({
        message: "Slack Card Request Notification",
        company: data.company,
        event: "slack_api_request",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.Slack,
        providerType: "workspace",
        response: (response && JSON.stringify(response)) || "",
        statusCode: 200,
        endpoint: "https://slack.com/api",
        method: "post",
      });

      log(Log.bg.blue, {
        message: `Successfully sent card request notification to channel ${result.channelId}`,
      });

      return {
        error: false,
        message: "Card request notification sent successfully",
      };
    } catch (error) {
      log(Log.bg.red, {
        message: "Error posting card request notification",
        error,
      });

      await ThirdPartyLogRepo.createLog({
        message: "Slack Card Request Notification Error",
        company: data.company,
        event: "slack_api_request_error",
        payload: JSON.stringify(data),
        provider: CARD_ISSUER.Slack,
        providerType: "workspace",
        response: (error && JSON.stringify(error)) || "",
        statusCode: 500,
        endpoint: "https://slack.com/api",
        method: "post",
      });

      return {
        error: true,
        message: `Failed to send card request notification: ${error.message}`,
      };
    }
  },

  async sendBudgetRequestNotification(data) {
    try {
      const result = await this.getIntegrationData(data.company);
      const client = new WebClient(result.accessToken);
      const baseUrl = this.getBaseUrl();

      const userProfileLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.TEAM_MEMBER(data.user?.code));

      const budgetOverviewLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.BUDGET.OVERVIEW(data.code));

      const approveLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.BUDGET.OVERVIEW(data.code), "/approve");
      const declineLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.BUDGET.OVERVIEW(data.code), "/decline");

      const payload = {
        channel: result.channelId,
        blocks: [
          {
            type: "header",
            text: {
              type: "plain_text",
              text: "Budget Request 🔔",
              emoji: true,
            },
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `You have a new request:\n*<${userProfileLink}|${data.user?.firstName} ${data.user?.lastName}> - New Budget Request*`,
            },
          },
          {
            type: "section",
            fields: [
              {
                type: "mrkdwn",
                text: `*Amount:*\n${data?.currency}${formatMoney(data?.amount)}`,
              },
              {
                type: "mrkdwn",
                text: `*Budget Name:*\n${data?.name || "Not specified"}`,
              },
              {
                type: "mrkdwn",
                text: `*Submitted:*\n${format(new Date(), "MMMM dd, yyyy")}`,
              },
              {
                type: "mrkdwn",
                text: `*Purpose:*\n${data?.purpose || "No purpose provided"}`,
              },
            ],
          },
          {
            type: "divider",
          },
          {
            type: "actions",
            elements: [
              {
                type: "button",
                text: {
                  type: "plain_text",
                  text: "View",
                },
                style: "primary",
                url: budgetOverviewLink,
                value: `view_request_${data.code}`,
              },
            ],
          },
        ],
        text: `New Budget Request: ${data?.user?.firstName} ${data?.user?.lastName} has requested a new budget of ${data?.currency}${formatMoney(
          data?.amount
        )} named "${data?.name || "Untitled Budget"}"`,
      };

      if (data.deadline)
        payload.blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Deadline:*\n${format(new Date(data?.deadline), "MMMM dd, yyyy")}`,
        });
      if (data.team)
        payload.blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Team:*\n${data?.team?.name || data?.teamName}`,
        });
      if (data.description)
        payload.blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Description:*\n${data.description}`,
        });

      const response = await client.chat.postMessage(payload);

      await ThirdPartyLogRepo.createLog({
        message: "Slack Budget Request Notification",
        company: data.company,
        event: "slack_api_request",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.Slack,
        providerType: "workspace",
        response: (response && JSON.stringify(response)) || "",
        statusCode: 200,
        endpoint: "https://slack.com/api",
        method: "post",
      });

      log(Log.bg.blue, {
        message: `Successfully sent budget request notification to channel ${result.channelId}`,
      });

      return {
        error: false,
        message: "Budget request notification sent successfully",
      };
    } catch (error) {
      log(Log.bg.red, {
        message: "Error posting budget request notification",
        error,
      });

      await ThirdPartyLogRepo.createLog({
        message: "Slack Budget Request Notification Error",
        company: data.company,
        event: "slack_api_request_error",
        payload: JSON.stringify(data),
        provider: CARD_ISSUER.Slack,
        providerType: "workspace",
        response: (error && JSON.stringify(error)) || "",
        statusCode: 500,
        endpoint: "https://slack.com/api",
        method: "post",
      });

      return {
        error: true,
        message: `Failed to send budget request notification: ${error.message}`,
      };
    }
  },

  async sendInvoiceRequestNotification(data) {
    try {
      const result = await this.getIntegrationData(data.company);
      const client = new WebClient(result.accessToken);
      const baseUrl = this.getBaseUrl();

      const userProfileLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.TEAM_MEMBER(data.user?.code));

      const approveLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.INVOICE(data.id || data.code), "/approve");
      const declineLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.INVOICE(data.id || data.code), "/decline");
      const moreLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.INVOICE(data.id || data.code));

      // Safe type handling - ensure type is a string before using replace
      const requestType = typeof data.type === "string" ? data.type.replace("_", " ") : "Invoice Request";

      const payload = {
        channel: result.channelId,
        blocks: [
          {
            type: "header",
            text: {
              type: "plain_text",
              text: "Invoice Request 🔔",
              emoji: true,
            },
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `You have a new request:\n*<${userProfileLink}|${data.user?.firstName} ${data.user?.lastName}>*`,
            },
          },
          {
            type: "section",
            fields: [
              {
                type: "mrkdwn",
                text: `*Amount:*\n${data?.currency}${formatMoney(data?.amount)}`,
              },
              {
                type: "mrkdwn",
                text: `*From:*\n${data.user?.firstName} ${data.user?.lastName}`,
              },
              {
                type: "mrkdwn",
                text: `*To:*\n${data?.vendor?.name || "Not specified"}`,
              },
              {
                type: "mrkdwn",
                text: `*Type:*\n${requestType}`,
              },
              {
                type: "mrkdwn",
                text: `*When:*\nSubmitted ${format(new Date(data?.createdAt), "MMMM dd, yyyy")}`,
              },
              {
                type: "mrkdwn",
                text: `*Invoice Number:*\n${data?.invoiceNumber || "Not specified"}`,
              },
            ],
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `*Reason:*\n${data?.description || "No reason provided"}`,
            },
          },
          {
            type: "divider",
          },
          {
            type: "actions",
            elements: [
              {
                type: "button",
                text: {
                  type: "plain_text",
                  emoji: true,
                  text: "Approve",
                },
                style: "primary",
                url: approveLink,
                value: `approve_invoice_${data.id || data.code}`,
              },
              {
                type: "button",
                text: {
                  type: "plain_text",
                  emoji: true,
                  text: "Decline",
                },
                style: "danger",
                url: declineLink,
                value: `decline_invoice_${data.id || data.code}`,
              },
              {
                type: "button",
                text: {
                  type: "plain_text",
                  emoji: true,
                  text: "More",
                },
                url: moreLink,
                value: `more_invoice_${data.id || data.code}`,
              },
            ],
          },
        ],
        text: `New Invoice Request: ${data?.user?.firstName} ${data?.user?.lastName} has requested approval for invoice ${
          data?.invoiceNumber || ""
        } of ${data?.currency}${formatMoney(data?.amount)} from ${data?.vendor?.name || "a vendor"}`,
      };

      // Add due date if it exists
      if (data.dueDate) {
        payload.blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Due Date:*\n${format(new Date(data.dueDate), "MMMM dd, yyyy")}`,
        });
      }

      // Add invoice date if it exists
      if (data.invoiceDate) {
        payload.blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Invoice Date:*\n${format(new Date(data.invoiceDate), "MMMM dd, yyyy")}`,
        });
      }

      // Add category if it exists
      if (data.category) {
        payload.blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Category:*\n${data.category.name || "Not categorized"}`,
        });
      }

      // Add source of funds if it exists
      if (data.source) {
        payload.blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Source of funds:*\n${data?.source.name}`,
        });
      }

      // Add budget if it exists
      if (data.budget) {
        payload.blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Target budget:*\n${data?.budget.name}`,
        });
      }

      // Add deadline if it exists and is valid
      if (data?.deadLine && !Number.isNaN(new Date(data.deadLine).getTime())) {
        payload.blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Deadline:*\n${format(new Date(data.deadLine), "MMMM dd, yyyy")}`,
        });
      }

      const response = await client.chat.postMessage(payload);

      await ThirdPartyLogRepo.createLog({
        message: "Slack Invoice Request Notification",
        company: data.company,
        event: "slack_api_request",
        payload: JSON.stringify(payload),
        provider: CARD_ISSUER.Slack,
        providerType: "workspace",
        response: (response && JSON.stringify(response)) || "",
        statusCode: 200,
        endpoint: "https://slack.com/api",
        method: "post",
      });

      log(Log.bg.blue, {
        message: `Successfully sent invoice request notification to channel ${result.channelId}`,
      });

      return {
        error: false,
        message: "Invoice request notification sent successfully",
      };
    } catch (error) {
      log(Log.bg.red, {
        message: "Error posting invoice request notification",
        error,
      });

      await ThirdPartyLogRepo.createLog({
        message: "Slack Invoice Request Notification Error",
        company: data.company,
        event: "slack_api_request_error",
        payload: JSON.stringify(data),
        provider: CARD_ISSUER.Slack,
        providerType: "workspace",
        response: (error && JSON.stringify(error)) || "",
        statusCode: 500,
        endpoint: "https://slack.com/api",
        method: "post",
      });

      return {
        error: true,
        message: `Failed to send invoice request notification: ${error.message}`,
      };
    }
  },

  async sendSlackNotification(entityType, entityData) {
    switch (entityType) {
      case "reimbursement":
        return Utils.sendReimbursementNotifcation(entityData);
      case "request":
        return Utils.sendEntityRequestNotification(entityData);
      case "budget_topup":
        return Utils.sendBudgetTopupNotification(entityData);
      case "card_topup":
        return Utils.sendCardTopupNotification(entityData);
      case "card_request":
        return Utils.sendCardRequestNotification(entityData);
      case "budget_request":
        return Utils.sendBudgetRequestNotification(entityData);
      case "invoice_request":
        return Utils.sendInvoiceRequestNotification(entityData);
      default:
        return notifyUserNewClaimedReferral;
    }
  },

  async sendDirectMessageToUser(slackUserId, message, company) {
    if (!slackUserId) {
      log(Log.bg.yellow, {
        message: "No Slack user ID provided, skipping direct message",
      });
      return false;
    }

    try {
      const integrationData = await ExternalIntegrationRepo.getIntegration({
        filter: { company, platform: "SLACK", status: STATUSES.ACTIVE },
        selectedOptions: ["access_token"],
      });
      const client = new WebClient(integrationData.access_token);

      await client.chat.postMessage({
        channel: slackUserId,
        blocks: message.blocks || [],
        text: message.text || "You have a notification from Bujeti",
      });

      log(Log.bg.blue, {
        message: `Successfully sent direct message to user ${slackUserId}`,
      });
      return true;
    } catch (error) {
      log(Log.bg.red, { message: "Error sending direct message", error });

      return false;
    }
  },

  async notifyUserOnSlackForFundRequestApproval(data) {
    const { requestId, amount, currency, approverName, requestType = "fund request", company } = data;

    try {
      // First, verify the fund request exists and is approved
      const fundRequest = await FundRequestRepo.getFundRequest({
        queryParams: {
          id: requestId,
          status: STATUSES.APPROVED, // Ensure it's approved
        },
      });

      if (!fundRequest) {
        log(Log.bg.blue, { message: `Fund request ${requestId} approved` });
        return {
          error: true,
          message: "Fund request approved",
        };
      }

      // Get the user ID from the fund request
      const userId = fundRequest.user;

      // Fetch the beneficiary to get their Slack ID
      const beneficiary = await BeneficiaryRepo.getOneBeneficiary({
        queryParams: { user: userId },
        selectOptions: ["slackId"],
      });

      if (!beneficiary || !beneficiary.slackId) {
        return {
          error: true,
          message: "User does not have a linked Slack account",
        };
      }

      const baseUrl = Utils.getBaseUrl();

      const requestDetailsLink = Utils.constructDeepLink(baseUrl, Utils.DEEP_LINK_PATHS.FUND_REQUEST(requestId));

      const user = await UserRepo.getOneUser({
        queryParams: { id: userId },
        selectOptions: ["firstName", "lastName"],
      });

      const message = {
        blocks: [
          {
            type: "header",
            text: {
              type: "plain_text",
              text: "✅ Your request has been approved!",
              emoji: true,
            },
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `Hello ${user.firstName}, your ${requestType} for *${currency}${formatMoney(amount)}* has been approved by *${approverName}*.`,
            },
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `You can view the details and track the status of your request in the dashboard.`,
            },
          },
          {
            type: "divider",
          },
          {
            type: "actions",
            elements: [
              {
                type: "button",
                text: {
                  type: "plain_text",
                  text: "View Details",
                  emoji: true,
                },
                style: "primary",
                url: requestDetailsLink,
                value: `view_approved_request_${requestId}`,
              },
            ],
          },
        ],
        text: `Your ${requestType} for ${currency}${formatMoney(amount)} has been approved by ${approverName}!`,
      };

      // Send the direct message using the beneficiary's slackId
      const result = await Utils.sendDirectMessageToUser(beneficiary.slackId, message, company);

      if (result) {
        log(Log.bg.blue, {
          message: `Successfully sent approval notification to user ${user.firstName} ${user.lastName} (${beneficiary.slackId})`,
        });

        await ThirdPartyLogRepo.createLog({
          message: "Slack Approval Notification",
          company: fundRequest.company || company || -1,
          event: "slack_approval_notification",
          payload: JSON.stringify({
            userId,
            requestId,
            approverName,
          }),
          provider: CARD_ISSUER.Slack,
          providerType: "slack",
          response: JSON.stringify({ success: true }),
          statusCode: 200,
          endpoint: "https://slack.com/api",
          method: "post",
        });

        return {
          error: false,
          message: "Approval notification sent successfully",
        };
      }

      return {
        error: true,
        message: "Failed to send approval notification",
      };
    } catch (error) {
      log(Log.bg.red, {
        message: "Error sending approval notification",
        error,
      });

      await ThirdPartyLogRepo.createLog({
        message: "Slack Approval Notification Error",
        company: company || -1,
        event: "slack_approval_notification_error",
        payload: JSON.stringify({ requestId, approverName }),
        provider: CARD_ISSUER.Slack,
        providerType: "workspace",
        response: (error && JSON.stringify(error)) || "",
        statusCode: 500,
        endpoint: "https://slack.com/api",
        method: "post",
      });

      return {
        error: true,
        message: `Failed to send approval notification: ${error.message}`,
      };
    }
  },

  async notifyAdminsForRequestApproval(data) {
    const { requestData, user, admins, company } = data;

    try {
      const baseUrl = this.getBaseUrl();

      const adminIds = admins.map((admin) => admin.id || admin.dataValues?.id);

      // Get beneficiaries for all admins to get their Slack IDs
      const adminBeneficiaries = await BeneficiaryRepo.getAllBeneficiaries({
        queryParams: {
          user: { [Op.in]: adminIds },
          company,
          status: STATUSES.ACTIVE,
        },
        selectOptions: ["slackId", "user"],
        addUserAndPhone: true,
      });

      // Create the message payload
      const userProfileLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.TEAM_MEMBER(user.code));
      const requestDetailsLink = this.constructDeepLink(baseUrl, this.DEEP_LINK_PATHS.FUND_REQUEST(requestData.code));

      const [vendorData, categoryData, budgetData] = await Promise.allSettled([
        requestData.vendor ? VendorService.getVendor({ code: requestData.vendor }) : null,
        requestData.category ? CategoryService.getCategory({ code: requestData.category }) : null,
        requestData.budget ? BudgetService.getBudget({ code: requestData.budget }) : null,
      ]);

      const vendor = vendorData?.status === PROMISE_STATUS.FULFILLED ? vendorData.value : null;
      const category = categoryData?.status === PROMISE_STATUS.FULFILLED ? categoryData.value : null;
      const budget = budgetData?.status === PROMISE_STATUS.FULFILLED ? budgetData.value : null;

      const message = {
        blocks: [
          {
            type: "header",
            text: {
              type: "plain_text",
              text: "A Request Needs Approval 🔔",
              emoji: true,
            },
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `You have a new request to review from:\n*<${userProfileLink}|${user.firstName} ${user.lastName}>*`,
            },
          },
          {
            type: "section",
            fields: [
              {
                type: "mrkdwn",
                text: `*Amount:*\n${requestData.currency}${formatMoney(requestData.amount)}`,
              },
              {
                type: "mrkdwn",
                text: `*From:*\n${user.firstName} ${user.lastName}`,
              },
              {
                type: "mrkdwn",
                text: `*Submitted:*\n${format(new Date(), "MMMM dd, yyyy")}`,
              },
              ...(vendor
                ? [
                    {
                      type: "mrkdwn",
                      text: `*Vendor:*\n<${this.constructDeepLink(baseUrl, `/expenses/vendors/profile/${vendor.code}`)}|${vendor.name}>`,
                    },
                  ]
                : []),
            ],
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `*Reason:*\n${requestData.description || "No reason provided"}`,
            },
          },
          {
            type: "divider",
          },
          {
            type: "actions",
            elements: [
              {
                type: "button",
                text: {
                  type: "plain_text",
                  text: "View Details",
                },
                url: requestDetailsLink,
                value: `view_fund_request_${requestData.code}`,
              },
            ],
          },
        ],
        text: `New Approval Request: ${user.firstName} ${user.lastName} has requested ${requestData.currency}${formatMoney(requestData.amount)}`,
      };

      // Add deadline if it exists
      if (requestData.deadLine && isValidDate(requestData.deadLine)) {
        message.blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Deadline:*\n${format(new Date(requestData.deadLine), "MMMM dd, yyyy")}`,
        });
      }

      // Add source of funds if it exists
      if (requestData.source) {
        message.blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Source of funds:*\n${requestData.source.name}`,
        });
      }

      // Add budget if it exists
      if (requestData.budget) {
        message.blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Target budget:*\n${requestData.budget.name}`,
        });
      }

      // Add category if it exists
      if (requestData.category) {
        message.blocks[2].fields.push({
          type: "mrkdwn",
          text: `*Category:*\n${requestData.category.name || "Not categorized"}`,
        });
      }

      // Send direct message to each admin that has a Slack ID
      const notificationPromises = adminBeneficiaries.map(async (beneficiary) => {
        if (beneficiary.slackId) {
          const admin = admins.find((a) => a.id === beneficiary.user);
          if (admin) {
            // Customize the greeting for each admin
            const adminMessage = {
              ...message,
              blocks: [
                ...message.blocks.slice(0, 1),
                {
                  type: "section",
                  text: {
                    type: "mrkdwn",
                    text: `Hi ${admin.firstName}, you have a new request to review:\n*<${userProfileLink}|${user.firstName} ${user.lastName}>*`,
                  },
                },
                ...message.blocks.slice(2),
              ],
            };
            return this.sendDirectMessageToUser(beneficiary.slackId, adminMessage, company);
          }
        }
        return false;
      });

      const results = await Promise.allSettled(notificationPromises);
      const successfulNotifications = results.filter((r) => r.status === PROMISE_STATUS.FULFILLED && r.value === true).length;

      if (successfulNotifications > 0) {
        await ThirdPartyLogRepo.createLog({
          message: "Slack Fund Request Admin Notification",
          company,
          event: "slack_admin_notification",
          payload: JSON.stringify({
            fundRequestId: requestData.requestId,
            adminsNotified: successfulNotifications,
          }),
          provider: CARD_ISSUER.Slack,
          providerType: "slack",
          response: JSON.stringify({ success: true }),
          statusCode: 200,
          endpoint: "https://slack.com/api",
          method: "post",
        });

        log(Log.bg.blue, {
          message: `Successfully sent fund request notifications to ${successfulNotifications} admin(s)`,
        });

        return {
          error: false,
          message: "Fund request notifications sent successfully",
          notifiedAdmins: successfulNotifications,
        };
      }

      return {
        error: true,
        message: "No admins were notified via Slack",
        notifiedAdmins: 0,
      };
    } catch (error) {
      log(Log.bg.red, {
        message: "Error sending admin notifications",
        error,
      });

      await ThirdPartyLogRepo.createLog({
        message: "Slack Fund Request Admin Notification Error",
        company,
        event: "slack_admin_notification_error",
        payload: JSON.stringify({
          fundRequestId: requestData.requestId,
          error: error.message,
        }),
        provider: CARD_ISSUER.Slack,
        providerType: "workspace",
        response: (error && JSON.stringify(error)) || "",
        statusCode: 500,
        endpoint: "https://slack.com/api",
        method: "post",
      });

      return {
        error: true,
        message: `Failed to send admin notifications: ${error.message}`,
        notifiedAdmins: 0,
      };
    }
  },
};

module.exports = Utils;
