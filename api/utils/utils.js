/* eslint-disable no-plusplus */
/* eslint-disable no-prototype-builtins */
/* eslint-disable no-shadow */
/* eslint-disable no-param-reassign */
const { customAlphabet } = require("nanoid");
const { Op } = require("sequelize");
const Big = require("big.js");
const dateFns = require("date-fns");
const cronParser = require("cron-parser");
require("dotenv").config();
const { parsePhoneNumber } = require("libphonenumber-js");
const crypto = require("crypto");
const fs = require("fs");
const path = require("path");
const { format, addMonths, startOfMonth, startOfQuarter, startOfWeek, startOfYear, addDays, addYears, endOfDay } = require("date-fns");
const SymbolCurrencyMap = require("../mocks/currency-symbol.mock");

const ValidationError = require("./validation-error");
const { STATUSES } = require("../models/status");
const { Banks, BanksByBankCode, BanksByNipCode } = require("../mocks/banks.mock");
const { ID_TYPES } = require("../models/document");
const { METHODS_INTERPRETER, PAYMENT_PLANS } = require("../mocks/constants.mock");
const { ONBOARDING_LEVEL } = require("../models/company");
const { PaymentPlan, Company, Subscription } = require("../models");

const alphabet = {
  digits: "**********",
  alphanumeric: "**********ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",
};

const Service = {
  alphabet,

  isAnInteger(value) {
    try {
      return typeof value === "number" || parseInt(value, 10);
    } catch (error) {
      throw new ValidationError("Please provide a positive amount");
    }
  },
  validatePositiveInteger(value) {
    if (parseInt(value, 10) <= 0) {
      throw new ValidationError("Please provide a positive amount");
    }
  },

  validateNegativeInteger(value) {
    if (parseInt(value, 10) >= 0) {
      throw new ValidationError("Please provide a negative amount");
    }
  },

  generateUniqueId(alphabet, length) {
    return customAlphabet(alphabet, length)();
  },

  getDashboardURL() {
    return process.env.DASHBOARD_URL;
  },

  isProd() {
    return process.env.NODE_ENV === "production";
  },

  isTest() {
    return process.env.NODE_ENV === "test";
  },

  canRunCustomMigration() {
    return +process.env.CAN_RUN_CUSTOM_MIGRATION === 1;
  },

  formatMoney(number = 0) {
    return Number(number / 100).toLocaleString();
  },

  formatMoneyWithCurrency(amount = 0, currency = "NGN") {
    const isNegativeAmount = amount < 0;
    return `${isNegativeAmount ? "-" : ""}${Service.getSymbolFromCurrency(currency)}${Service.formatMoney(Math.abs(amount))}`;
  },
  /**
   * Tells if the environment is dev/staging
   * @returns
   */
  isStaging() {
    return ["development", "staging"].includes(process.env.NODE_ENV);
  },

  isLive() {
    return ["production", "test"].includes(process.env.NODE_ENV);
  },

  currentEnvContext() {
    return process.env.NODE_ENV;
  },

  generateRandomString(length = 12) {
    return customAlphabet(alphabet.alphanumeric, length)();
  },

  cleanUpString(string) {
    return string.replace(/([^a-zA-Z0-9\s])/g, "").replace(/\s+/g, "_");
  },

  parseJSON(string) {
    try {
      return JSON.parse(string);
    } catch (error) {
      return string;
    }
  },

  decodeString(string) {
    try {
      return decodeURIComponent(string);
    } catch (error) {
      return string;
    }
  },

  groupByKey(data, key) {
    return data.reduce((collection, element) => {
      const value = element[key];
      delete element[key];
      collection[value] = element;
      return collection;
    }, {});
  },

  buildAnalyticsFilters(filters) {
    let groupBy = "days";
    const today = new Date();
    const { from = dateFns.subDays(today, 7), to = today, currency, budget, payer, team } = filters;
    const difference = dateFns.differenceInCalendarDays(new Date(from), new Date(to));
    if (difference > 31 && difference <= 90) {
      groupBy = "weeks";
    } else if (difference > 90) groupBy = "months";
    return {
      from,
      to,
      groupBy,
      currency,
      budget,
      ...(payer && { payer }),
      ...(team && { team }),
    };
  },

  isValidDate(date) {
    try {
      if (typeof date === "string") {
        return dateFns.isValid(new Date(date));
      }
      return dateFns.isValid(date);
    } catch (e) {
      return false;
    }
  },

  formatDateWithStartTime(date) {
    const formattedDate = typeof date === "string" ? new Date(date) : date;
    return dateFns.format(dateFns.startOfDay(formattedDate), "yyyy-MM-dd HH:mm:ss");
  },

  formatDateWithEndTime(date) {
    const formattedDate = typeof date === "string" ? new Date(date) : date;
    return dateFns.format(dateFns.endOfDay(formattedDate), "yyyy-MM-dd HH:mm:ss");
  },

  toBool(value) {
    if (typeof value === "string" && ["false", "true"].includes(value)) {
      return value === "true";
    }
    if (typeof value === "number" && [0, 1].includes(value)) {
      return value === 1;
    }
    throw new ValidationError(`Expecting boolean got: ${value}`);
  },

  formatAmount(value) {
    return Service.getInteger(value) / 100;
  },

  getInteger(value = 0) {
    return parseInt(value, 10);
  },

  formatMoneyAsBasicUnit(amount = 0) {
    return parseInt(amount * 100, 10);
  },

  stringToInteger(value = 0) {
    return parseInt(value, 10);
  },

  /**
   * Used to format a phone number and create an International number
   * Requirements
   * @param countryCode: string;
   * @param localFormat: string;
   * @returns string;
   * <AUTHOR> - created
   */
  formatPhoneNumber(localFormat, countryCode) {
    if (!localFormat) return null;
    const { number: newInternationalFormat } = parsePhoneNumber(localFormat, {
      defaultCallingCode: countryCode,
    });
    return newInternationalFormat && newInternationalFormat.replace("+", "");
  },

  /**
   * Checks if a currency is dollar
   * @param {String} currency
   */
  isDollar(currency) {
    return currency && String(currency).toUpperCase() === "USD";
  },

  /**
   * Checks if a currency is naira
   * @param {String} currency
   */
  isNaira(currency) {
    return currency && String(currency).toUpperCase() === "NGN";
  },

  getApiURL() {
    return Service.isProd() ? `https://api.bujeti.com` : `https://api-dev.bujeti.com`;
  },

  generateRoleValue(data) {
    const mapped = data.map(({ bit }) => bit);
    return mapped.reduce((prev, next) => prev + next);
  },
  toTitle(text) {
    return text && `${text[0].toUpperCase()}${text.substr(1)}`;
  },
  cleanUpNames(text) {
    return String(text).toLowerCase().split(" ").map(Service.toTitle).join(" ").split("-").map(Service.toTitle).join("-");
  },
  getCurrentDate() {
    return dateFns.format(new Date(), "yyyy-MM-dd");
  },

  firstDateIsBeforeSecondDate(firstDate, secondDate = new Date(), dateOnly = false) {
    if (dateOnly) return dateFns.isBefore(Service.stringToStrictDate(firstDate), Service.stringToStrictDate(secondDate));
    return dateFns.isBefore(Service.stringToDate(firstDate), Service.stringToDate(secondDate));
  },

  firstDateIsAfterSecondDate(firstDate, secondDate = new Date(), dateOnly = false) {
    if (dateOnly) return dateFns.isAfter(Service.stringToStrictDate(firstDate), Service.stringToStrictDate(secondDate));
    return dateFns.isAfter(Service.stringToDate(firstDate), Service.stringToDate(secondDate));
  },

  stringToStrictDate(date) {
    return new Date(dateFns.format(new Date(typeof date === "string" ? new Date(date) : date || new Date()), "yyyy-MM-dd"));
  },

  stringToDate(date) {
    return typeof date === "string" ? new Date(date) : date || new Date();
  },

  isWithinInterval(dateToCheck, firstDate, secondDate = new Date()) {
    return dateFns.isWithinInterval(dateToCheck, {
      start: Service.stringToDate(firstDate),
      end: Service.stringToDate(secondDate),
    });
  },

  isDateToday(date) {
    try {
      const parsedDate = dateFns.parseISO(date);
      return dateFns.isToday(parsedDate);
    } catch (err) {
      return false;
    }
  },

  isLessThanOrEqualToNow(date) {
    try {
      const now = new Date();
      const isEqualToNow = dateFns.isEqual(Service.stringToDate(date), Service.stringToDate(now));
      const isLessThanNow = dateFns.isBefore(Service.stringToDate(date), Service.stringToDate(now));
      return isLessThanNow || isEqualToNow;
    } catch (err) {
      return false;
    }
  },

  isDateTomorrow(date) {
    return dateFns.isTomorrow(new Date(date));
  },

  isSameDay(firstDate, secondDate) {
    return dateFns.isSameDay(firstDate, secondDate);
  },

  parseISO(firstDate) {
    return dateFns.parseISO(firstDate);
  },

  startOfDay(date) {
    return dateFns.startOfDay(date);
  },

  addDays(date, dayCount) {
    return dateFns.addDays(date, dayCount);
  },
  addMinutes(date, minutes) {
    return dateFns.addMinutes(date, minutes);
  },
  removeDays(date, dayCount) {
    return dateFns.subDays(date, dayCount);
  },

  maxElementFromAnArray(arr, key) {
    return arr.reduce((prev, nex) => {
      const previous = Service.jsonify(prev);
      const next = Service.jsonify(nex);
      if (parseInt(previous[key], 10) > parseInt(next[key], 10)) {
        return previous;
      }
      return next;
    });
  },

  mapAnArray(arr, key) {
    return arr.map((item) => {
      const response = Service.jsonify(item);
      return key.includes(".") ? Service.findObjectValue(response, key) : response[key];
    });
  },

  jsonify(payload) {
    try {
      return payload.toJSON();
    } catch (error) {
      /* nothing to see */
    }
    return payload;
  },

  getInitials(name) {
    // Split the name into an array of words
    const words = name.split(" ");

    // Return the initials as a string
    return words
      .map((word) => word.charAt(0))
      .join("")
      .toUpperCase();
  },

  // this method is used to map through a nested
  findObjectValue(object, path) {
    const args = path.split(".");
    let sanitizedObject = Service.jsonify(object);
    // eslint-disable-next-line no-plusplus
    for (let index = 0; index < args.length; index++) {
      if (!sanitizedObject.hasOwnProperty(args[index])) return;
      sanitizedObject = sanitizedObject[args[index]];
    }
    // eslint-disable-next-line consistent-return
    return sanitizedObject;
  },

  jsonify(payload) {
    try {
      return payload.toJSON();
      // eslint-disable-next-line no-empty
    } catch (error) {}
    return payload;
  },

  // eslint-disable-next-line consistent-return
  createObjectFromArray(payload, key, value, path) {
    if (!Array.isArray(payload)) return null;
    const response = {};
    payload.forEach((item) => {
      const data = path ? Service.findObjectValue(item, path) : Service.jsonify(item);
      if (Array.isArray(value)) {
        value.forEach((element) => {
          response[data[key]][element] = data[element];
        });
      } else response[data[key]] = data[value];
    });
    return response;
  },

  getKeyByValue(object, value) {
    return Object.keys(object).find((key) => object[key] === value);
  },

  minElementFromAnArray(arr, key) {
    if (!arr.length) return null;
    return arr.reduce((prev, nex) => {
      const previous = Service.jsonify(prev);
      const next = Service.jsonify(nex);
      if (previous[key] < next[key]) {
        return previous;
      }
      return next;
    });
  },

  getMinNumber(...args) {
    const incomingNumbers = args.map((argument) => {
      if (typeof argument !== "number" || Number.isNaN(argument)) throw new Error("All arguments must be numbers");
      return Number(argument);
    });

    const min = Math.min(...incomingNumbers);
    return min;
  },

  percentCalculator(numerator, denominator) {
    return (numerator / denominator) * 100;
  },

  getStatusValues(status) {
    if (Array.isArray(status)) {
      return status.map((value) => {
        if (typeof value === "number") return value;
        return STATUSES[String(value).toUpperCase()];
      });
    }

    if (typeof status === "number") return status;

    return STATUSES[String(status).toUpperCase()];
  },

  sumNumbers(data) {
    if (!Array.isArray(data)) return [];
    return data.reduce((prev, next) => prev + next);
  },

  /**
   * Get the string value of the passed data
   * @param {String} value
   * @returns {String}
   */
  toString(value) {
    return typeof value === "string" ? value : JSON.stringify(value);
  },

  async requestHandler(request) {
    try {
      const { data: response, status = 200, config } = await request;
      const { message, data, included = null } = response;

      return {
        error: false,
        message,
        body: data ? { data, included } : response,
        data,
        status,
        config: {
          url: `${config.baseURL}${config.url}`,
          method: config.method,
          data: config.data,
        },
      };
    } catch (error) {
      const { response, message = "An error occurred", status, config } = error;
      const errorData = response && response.data;
      return {
        message,
        status: status || 400,
        data: errorData || response,
        body: errorData,
        error: true,
        config: {
          url: `${config.baseURL}${config.url}`,
          method: config.method,
          data: config.data,
        },
      };
    }
  },

  getNIPCodeFromBankCode(bankCode) {
    const foundBank = Banks.find((bank) => bank.bankCode === bankCode || bank.nipCode === bankCode);
    if (!foundBank || !foundBank.nipCode) throw new ValidationError(`NIP Code for bank not found`);
    return foundBank.nipCode;
  },

  getNIPCodeFromBankCodeNoError(bankCode) {
    const nipCode = BanksByBankCode[bankCode]?.nipCode || BanksByNipCode[bankCode]?.nipCode;
    return nipCode;
  },

  /**
   * Get MD5 of passed value
   * @param {String} value the value
   * @returns {String} the md5 of the passed value
   */
  md5(value) {
    return crypto.createHash("md5").update(value).digest("hex");
  },

  getWebsiteURL() {
    return Service.isProd() ? `https://www.bujeti.com` : `https://dashboard-staging.bujeti.com`;
  },

  getInvoiceBaseURL() {
    return Service.isProd() ? `https://invoices.bujeti.com/i` : `https://dashboard-staging.bujeti.com`;
  },

  formatHumanReadableDate(data) {
    if (!data) return null;
    const date = new Date(data);
    return format(date, "MMMM d, yyyy");
  },

  getYear(date) {
    return dateFns.getYear(Service.parseISO(date));
  },

  encrypt({ text, algorithm = "aes-256-cbc", hash }) {
    const key = crypto.createHash("sha256").update(String(hash)).digest("base64").substr(0, 32);
    const cipher = crypto.createCipher(algorithm, key);
    let encrypted = cipher.update(text);
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    return encrypted.toString("hex");
  },

  decrypt({ text, algorithm = "aes-256-cbc", hash, card, decrypted_with }) {
    try {
      const key = crypto.createHash("sha256").update(String(hash)).digest("base64").substr(0, 32);
      const encryptedText = Buffer.from(text, "hex");
      const decipher = crypto.createDecipher(algorithm, key);
      let decrypted = decipher.update(encryptedText);
      decrypted = Buffer.concat([decrypted, decipher.final()]);
      return decrypted.toString();
    } catch (error) {}
  },

  getEncryptionkeys(provider) {
    const keymap = {
      anchor: process.env.ANCHOR_API_KEY,
      mono: process.env.MONO_SECRET_KEY,
      flutterwave: process.env.FLW_ENCRYPTION_KEY,
      bridgecard: process.env.BRIDGE_CARD_SECRET_KEY,
    };
    return keymap[provider];
  },

  getBank(searchString) {
    const foundBank = Banks.find((bank) => bank.bankCode === searchString || bank.nipCode === searchString || bank.label === searchString);
    if (!foundBank) throw new ValidationError(`NIP Code for bank not found`);
    return foundBank;
  },

  paginate(total, page, perPage) {
    const skip = (parseInt(page, 10) - 1) * perPage;
    return {
      total,
      skip,
      hasMore: total > parseInt(perPage, 10) * page,
      limit: parseInt(perPage, 10),
      nextPage: total > parseInt(perPage, 10) * page ? Number(page) + 1 : Number(page),
    };
  },

  anchorDocumentMapper() {
    return {
      CERTIFICATE_OF_BUSINESS_NAME: "cac",
      BN_NUMBER: "bnNumber",
      FORM_CAC_BN_1: "cacBn1",
      PROOF_OF_ADDRESS: "utilityBill",
      RC_NUMBER: "rcNumber",
      TIN: "tin",
      CERTIFICATE_OF_INCORPORATION: "incorp_C",
      MEMORANDUM_OF_ASSOCIATION: "moa",
      CAC_STATUS_REPORT: "cacStatusReport",
      FORM_CAC_3: "cacForm3",
      FORM_CAC_2: "cacForm2",
      FORM_CAC_7: "cacForm7",
      FORM_CAC_1_1: "cacForm1",
      BVN: "bvn",
      CAC_IT_NUMBER: "cacITNumber",
      CAC_IT_1: "cacIT1",
    };
  },

  anchorIDMapper() {
    return {
      PASSPORT: "ip",
      DRIVERS_LICENSE: "dl",
      VOTERS_CARD: "vi",
      NIN_SLIP: "nin",
      NATIONAL_ID: "nin",
    };
  },

  cardType() {
    return {
      physical: 1,
      virtual: 2,
      flash: 3,
    };
  },

  getCardType(object, value) {
    return Object.keys(object).find((key) => object[key] === value);
  },

  /**
   * In case you need a processus to sleep for a few seconds before it resumes processing, it is not native to JS
   * @param {*} ms the milliseconds to sleep
   * @returns null
   */
  async sleep(ms) {
    // eslint-disable-next-line no-promise-executor-return
    return new Promise((resolve) => setTimeout(resolve, ms));
  },

  maskNumber(number, maskChar = "*") {
    if (!number) return null;
    const maskedPart = number.slice(0, -4).replace(/\d/g, maskChar);
    return maskedPart + number.slice(-4);
  },

  getLastString(characters, length) {
    return String(characters).slice(-1 * Math.abs(length));
  },

  generateDates(start, end, offset, dateFormat) {
    const datesArray = [];
    for (const arr = [], dt = new Date(start); dt <= new Date(end); dt.setDate(dt.getDate() + offset)) {
      datesArray.push(format(new Date(dt), dateFormat));
    }
    return datesArray;
  },
  generateDaysInRange(from, to, dateFormat = "YYYY-MM-DD") {
    return Service.generateDates(from, to, 1, dateFormat);
  },

  generateWeeksInRange(from, to, dateFormat = "YYYY-MM-DD") {
    return Service.generateDates(from, to, 7, dateFormat);
  },

  getFirstDayOfNextWeek() {
    const currentDate = new Date();
    const startOfCurrentWeek = startOfWeek(currentDate, { weekStartsOn: 1 }); // start week on monday
    return Service.getUTCPlusOneTime(0, addDays(startOfCurrentWeek, 7));
  },

  getFirstDayOfNextMonth() {
    const currentDate = new Date();
    return Service.getUTCPlusOneTime(0, startOfMonth(addMonths(currentDate, 1)));
  },

  getFirstDayOfNextQuarter() {
    const currentDate = new Date();
    return Service.getUTCPlusOneTime(0, startOfQuarter(addMonths(currentDate, 3)));
  },

  getFirstDayOfNextYear() {
    const currentDate = new Date();
    return Service.getUTCPlusOneTime(0, startOfYear(addYears(currentDate, 1)));
  },

  getUTCPlusOneTime(minutes = 0, date = new Date()) {
    const utcPlusOneTime = new Date(date);

    if (minutes) utcPlusOneTime.setUTCMinutes(date.getUTCMinutes() + minutes);

    return utcPlusOneTime.setUTCHours(date.getUTCHours() + 1);
  },

  isObject(value) {
    return typeof value === "object" && value !== null && !Array.isArray(value);
  },

  getKeyValue(obj, key) {
    if (!String(key).includes(".") && obj[key]) return obj[key];
    const splittedKeys = String(key).split(".");
    let result = obj;
    splittedKeys.map((currentKey) => {
      if (result && typeof result === "object" && currentKey in result) {
        result = result[currentKey];
      } else {
        throw new ValidationError("Key not found in Object");
      }
    });
    return result;
  },

  convertArrayOfObjectToHashMap(arrayOfObject, key) {
    if (!Array.isArray(arrayOfObject)) throw new ValidationError("First parameter should be an array of object");
    if (typeof key !== "string") throw new ValidationError("Key should be a string");
    const castedArray = Array.from(arrayOfObject);
    if (!castedArray.length) return {};
    const createdHash = {};
    castedArray.map((singleObject) => {
      if (!Service.isObject(singleObject)) throw new ValidationError("Elements of array must be an Object");
      const hashKey = Service.getKeyValue(singleObject, key);
      createdHash[hashKey] = singleObject;
      return createdHash;
    });
    return createdHash;
  },

  generateQuarterlySchedules(start) {
    const schedules = [];
    for (let index = 0; index < 4; index++) {
      const schedule = start % 12;
      schedules.push(schedule + 1);
      start += 3;
    }
    return schedules;
  },

  getCardHash(cardCode) {
    const randromString = Service.generateRandomString(6);
    return String(cardCode).replace("crd_", "").padStart(23, randromString);
  },

  getCardCodeFromHash(hash) {
    return `crd_${String(hash).substring(6)}`;
  },

  getDateSpreadInMonths(start, end) {
    const difference = Math.abs(dateFns.differenceInMonths(new Date(start), new Date(end)));
    if (difference === 0 && dateFns.differenceInDays(new Date(start), new Date(end)) <= 30) return 1;
    return difference;
  },

  getYearsAgo(years = 18) {
    const currentDate = new Date();
    return dateFns.subYears(currentDate, years);
  },

  getFutureDate(days, currentDate = new Date()) {
    return dateFns.startOfDay(dateFns.addDays(currentDate, days));
  },

  calculateCardFundingCharges({ amount, currency, fundingFee, rate, fundingFeePercentage = 0 }) {
    if (currency.toLowerCase() === "ngn") return parseInt(amount * fundingFeePercentage, 10) + fundingFee;
    const dollarAmount = Service.formatAmount(amount); // Convert cent to Dollar
    const calculatedCharge = fundingFeePercentage * dollarAmount;
    const finalizedCharge = Math.min(Number(calculatedCharge), Service.formatMoney(fundingFee)); // Get the minimum charge, capped at $5
    const amountInNGN = finalizedCharge * rate; // Converted to Naira
    return amountInNGN * 100; // Converted back to kobo
  },

  compareObjectArray({ previousItems = [], newItems = [], key }) {
    const itemsToDelete = [];
    const itemsToAdd = [];
    const itemsToUpdate = [];

    if (newItems.length === 0) return { itemsToAdd: [], itemsToDelete: previousItems, itemsToUpdate: [] };
    if (previousItems.length === 0) return { itemsToAdd: newItems, itemsToDelete: [], itemsToUpdate: [] };
    // Get the new items to add
    for (let index = newItems.length - 1; index >= 0; index--) {
      const item = newItems[index];
      if (!Object.keys(item).includes(key)) {
        itemsToAdd.push(item);
        newItems.splice(index, 1);
      }
    }
    itemsToAdd.reverse();

    // convert both to Hash
    const previousItemsMap = Service.convertArrayOfObjectToHashMap(previousItems, key);
    const newItemsMap = Service.convertArrayOfObjectToHashMap(newItems, key);

    Object.keys(previousItemsMap).forEach((itemKey, index) => {
      if (!newItemsMap[itemKey]) itemsToDelete.push(previousItems[index]);
      else itemsToUpdate.push({ ...previousItems[index], ...newItemsMap[itemKey] });
    });

    return { itemsToAdd, itemsToUpdate, itemsToDelete };
  },

  getSymbolFromCurrency(currency) {
    return SymbolCurrencyMap[currency];
  },

  splitInBatches(array, length) {
    const batchs = [];
    if (array.length <= 10) return [array];
    batchs.push(array.slice(0, length));
    return [...batchs, ...Service.splitInBatches(array.slice(length), length)];
  },

  indefiniteArticle(word) {
    const lowercaseWord = word.toLowerCase();
    const vowels = ["a", "e", "i", "o", "u"];

    // Special cases where the first few letters sound like a different consonant
    const specialCases = ["europe", "unicorn", "one"];

    // Check if the word starts with a vowel sound or a special case
    return vowels.includes(lowercaseWord[0]) && !specialCases.includes(lowercaseWord) ? "an" : "a";
  },

  validateInstallmentsPayloadForAmount({ amount, payments }) {
    const totalAmount = Array.from(payments).reduce((prev, next, index) => {
      const { amount, due_date: dueDate } = next;
      if (!amount) throw new ValidationError("Please specify an amount");
      if (!Service.isValidDate(dueDate)) throw new ValidationError("Please enter a valid date");
      if (index > 0 && Service.firstDateIsBeforeSecondDate(dueDate, payments[index - 1].dueDate))
        throw new ValidationError("Installment dates must be ahead of each other");
      if (Service.firstDateIsBeforeSecondDate(dueDate, Service.getCurrentDate()))
        throw new ValidationError("Installment due date must be ahead of today");
      return parseInt(prev, 10) + parseInt(amount, 10);
    }, 0);

    if (totalAmount !== amount) throw new ValidationError("Total Installments amount should sum up to invoice amount");
  },

  validateInstallmentsPayloadForPercentage({ payments }) {
    const totalPercentage = Array.from(payments).reduce((prev, next, index) => {
      const { percentage, due_date: dueDate } = next;
      if (!percentage) throw new ValidationError("Please specify percentage");
      if (!Service.isValidDate(dueDate)) throw new ValidationError("Please enter a vvalid date");
      if (index > 0 && Service.firstDateIsBeforeSecondDate(dueDate, payments[index - 1].dueDate))
        throw new ValidationError("Installment dates must be ahead of each other");
      if (Service.firstDateIsBeforeSecondDate(dueDate, Service.getCurrentDate()))
        throw new ValidationError("Installment due date must be ahead of today");
      return parseInt(prev, 10) + parseInt(percentage, 10);
    }, 0);

    if (totalPercentage !== 100) throw new ValidationError("Total percentage should sum up to 100%");
  },

  validateInstallmentsPayload(payload) {
    const { amount, installments } = payload;
    const { type, payments } = installments;
    if (type === "amount") return Service.validateInstallmentsPayloadForAmount({ amount, payments });
    return Service.validateInstallmentsPayloadForPercentage({ payments });
  },

  calculateProductPrice(product) {
    const { discount, discount_type: discountType, quantity, unitPrice } = product;
    let discountAmount = 0;
    const priceWithoutDiscount = unitPrice * quantity;
    if (discount) discountAmount = discountType === "amount" ? discount : priceWithoutDiscount * (discount / 100);
    return priceWithoutDiscount - discountAmount;
  },

  getDifferenceInDates(fromDate, endDate) {
    return dateFns.differenceInCalendarDays(new Date(fromDate), new Date(endDate));
  },

  calculatePercentageAmount(percentage, amount) {
    if (!(percentage && amount)) throw new ValidationError("Please specify percentage and amount");
    const percentageAmount = (Number(percentage) / 100) * amount;
    return percentageAmount;
  },

  formatDate(date, format) {
    if (!(date || format)) throw new ValidationError("Please specify date and format");
    const parsedDate = new Date(date);
    return dateFns.format(parsedDate, format);
  },

  propertyExistsOnObject(object, property) {
    return Object.prototype.hasOwnProperty.call(object, property);
  },

  getNextProvider({ providers, currentProvider }) {
    const currentProviderIndex = Array.from(providers).findIndex((singleProvider) => singleProvider.provider === currentProvider);
    if (currentProviderIndex === -1 || currentProviderIndex === providers.length - 1) return providers[0].provider; // Return the first provider
    return providers[currentProviderIndex + 1].provider;
  },

  calculateDiscount({ discount, discountType, amount }) {
    if (!discountType || discount > 100) return discount || 0;
    return discountType === "amount" ? discount : amount * (discount / 100);
  },

  calculateVat({ vat, amount }) {
    return amount * (vat / 100);
  },

  getIDTypes() {
    return Object.entries(ID_TYPES).map(([_, idType]) => idType.value);
  },

  formatBillingAddress(address) {
    if (typeof address !== "object") return null;
    const { street, city, state, country } = address;
    const formattedAddress = `${street}, ${city}\n${state}\n${country}.`;
    return formattedAddress;
  },

  prepareInvoiceInstallmentPayloadForEmail(installments) {
    const suffixes = ["first", "second", "third", "fourth"];
    const installmentStatusResponse = {};
    const installmentDueDateResponse = {};
    const installmentAmountResponse = {};
    installments.forEach((installment, index) => {
      const key = `${suffixes[index]}Instalment`;
      installmentAmountResponse[key] = (parseInt(installment.amount, 10) / 100).toLocaleString();
      installmentStatusResponse[`${key}Status`] = installment.status === STATUSES.PAID ? "Paid" : "Unpaid";
      installmentDueDateResponse[`${key}Due`] = (installment.due_date && Service.formatHumanReadableDate(installment.due_date)) || null;
    });
    return { installmentAmountResponse, installmentDueDateResponse, installmentStatusResponse };
  },

  getVatAmountFromTotalAmount(amount, vat) {
    const currencyAmount = amount / 100;
    const vatPercentage = vat / 100;
    const baseAmount = currencyAmount / (1 + vatPercentage);
    const vatAmount = currencyAmount - baseAmount;
    return Math.ceil(vatAmount * 100);
  },

  getDiscountAmountFromTotalAmount(initialAmount, discountRate) {
    const discountInLowestValue = Service.money(discountRate).div(100).toNumber();
    const discountValue = Service.money(1).sub(discountInLowestValue).toNumber();
    const originalAmount = Service.money(initialAmount).div(discountValue).toNumber();
    return Service.money(originalAmount).sub(initialAmount).toNumber();
  },

  getInvoiceAmountBreakdown(payload) {
    const { amount, vat = null, discount = 0, discountType } = payload;
    let subTotal = amount;
    let discountAmount = discount;

    if (vat) {
      const vatAmount = Service.getVatAmountFromTotalAmount(amount, vat);
      subTotal -= vatAmount;
    }

    if (discountType === "percentage") {
      discountAmount = Service.getDiscountAmountFromTotalAmount(subTotal, discount);
    }

    subTotal += discountAmount;

    return {
      subTotal: (parseInt(subTotal, 10) / 100).toLocaleString(),
      discount: discountAmount ? (parseInt(discountAmount, 10) / 100).toLocaleString() : null,
    };
  },

  generatePublicId(code) {
    const randomString = Service.generateRandomString(6);
    const newCode = `${randomString}${code.slice(4)}`;
    return newCode;
  },

  businessTypeMapper() {
    return {
      ngo: "incorporated trustees",
      "sole proprietorship": "business name",
      partnership: "business name",
      enterprise: "business name",
      "limited liability": "private limited liability",
    };
  },

  isNotOnNewPlan(paymentPlan = {}) {
    return [PAYMENT_PLANS.START, PAYMENT_PLANS.GROWTH, PAYMENT_PLANS.SCALE, PAYMENT_PLANS.CUSTOM].includes(paymentPlan.name);
  },

  async checkPlanPayoutLimit({ company, amount, currency, Transaction }) {
    const { PaymentPlan: companyPlan } = company;
    let paymentPlan = companyPlan;

    if (!paymentPlan) {
      const foundCompany = await Company.findOne({
        where: {
          id: company.id,
        },
        include: PaymentPlan,
      });
      paymentPlan = foundCompany.PaymentPlan;
    }

    const activeSubscriptions = await Subscription.findAll({
      where: {
        plan: paymentPlan.id,
        company: company.id,
        status: STATUSES.ACTIVE,
      },
    });

    if (activeSubscriptions.length > 0) {
      const [activeSubscription] = activeSubscriptions;

      if (!activeSubscription.isFreeTrial) {
        return true;
      }

      // if free trial, use basic plan's payment limit
      paymentPlan = await PaymentPlan.findOne({
        where: {
          name: PAYMENT_PLANS.BASIC,
          status: STATUSES.ACTIVE,
        },
      });
    } else {
      const isLegacyPlan = Service.isNotOnNewPlan(paymentPlan);
      if (isLegacyPlan) {
        // If the plan is a legacy plan, we don't check limits
        return true;
      }
      // If the plan is not a legacy plan, we check the limits
      paymentPlan = await PaymentPlan.findOne({
        where: {
          name: PAYMENT_PLANS.BASIC,
          status: STATUSES.ACTIVE,
        },
      });
    }

    const configuration = Service.parseJSON(paymentPlan.configuration);
    const maxTransactionLimit = configuration?.payables?.maxTransactionLimit?.[currency];

    if (!maxTransactionLimit) return true;

    const startOfCurrentMonth = startOfMonth(new Date()); // First day of the current month
    const endOfToday = endOfDay(new Date()); // End of today

    const totalSpent = await Transaction.sum("amount", {
      where: {
        company: company.id,
        status: [STATUSES.SUCCESS, STATUSES.PROCESSING, STATUSES.PENDING],
        created_at: {
          [Op.gte]: startOfCurrentMonth,
          [Op.lte]: endOfToday,
        },
        currency: currency || paymentPlan.currency,
      },
    });
    const amountLeft = Service.money(maxTransactionLimit).minus(Service.money(totalSpent)).toNumber();

    const willReachLimit = Service.money(amountLeft).lt(amount);

    if (willReachLimit) {
      throw new ValidationError("Transaction amount exceeds the limit for the month. Please upgrade your plan");
    }

    return true;
  },

  /**
   * Checks if a company transaction will reach limit
   * @param {object} company An object that contains amountSpent, maxTransactionAmount, onboardingLevel
   * @param {number} amount The amount they want to pay
   * @returns boolean
   */
  checkTransactionLimit({ company, amount, currency }) {
    const { onboardingLevel, amountSpent, maxTransactionAmount, PaymentPlan = {} } = company;

    const payables = PaymentPlan?.payables;
    const maxTransactionLimit = payables?.maxTransactionLimit?.[currency];

    const amountLeft = Service.money(maxTransactionLimit || maxTransactionAmount)
      .minus(Service.money(amountSpent))
      .toNumber();

    if (maxTransactionLimit) {
      const willReachLimit = Service.money(amountLeft).lt(amount);
      if (willReachLimit) {
        throw new ValidationError("Transaction will exceed the limit. Please upgrade your plan");
      }
    }

    if (onboardingLevel === ONBOARDING_LEVEL.LEVEL_3) return false;
    return Service.money(amountLeft).lt(amount);
  },

  truncateSentence(string, length = 100) {
    if (!string || typeof string !== "string") return null;
    return string.substring(0, length);
  },

  // Function to ensure that the directory exists
  ensureDirectoryExistence(filePath) {
    const dirname = path.dirname(filePath);
    if (fs.existsSync(dirname)) {
      return true;
    }
    fs.mkdirSync(dirname, { recursive: true });
  },
  stripUndefinedAndNullDeep(obj) {
    if (typeof obj !== "object" || obj === null) {
      return obj;
    }

    return Object.fromEntries(
      Object.entries(obj)
        .filter(([, value]) => value !== undefined && value !== null)
        .map(([key, value]) => [key, Service.stripUndefinedAndNullDeep(value)])
    );
  },

  getCurrencyNameFromIso3(currency = "NGN") {
    const currencyNameMap = {
      NGN: "naira",
      USD: "dollar",
    };

    return currencyNameMap[currency.toUpperCase()];
  },

  getLastDayOfMonth(monthNumber, year) {
    const dateString = `${year}-${monthNumber}-01`;
    const date = dateFns.parse(dateString, "yyyy-MM-dd", new Date());
    const lastDay = dateFns.lastDayOfMonth(date);
    return format(lastDay, "MMMM dd, yyyy");
  },

  money(number) {
    return new Big(number || 0);
  },

  cronToSentence(cronExpression) {
    try {
      const interval = cronParser.parseExpression(cronExpression);
      const { dayOfMonth, month, dayOfWeek } = interval.fields;

      if (Service.isIndefinite(dayOfMonth, month, dayOfWeek)) {
        return "repeats indefinitely";
      }

      if (Service.isWeekly(dayOfMonth, dayOfWeek)) {
        return `repeats weekly on ${dayOfWeek.map(Service.getDayName).join(", ")}`;
      }

      if (Service.isMonthly(dayOfMonth, month)) {
        return Service.handleMonthlySchedule(dayOfMonth[0]);
      }

      if (Service.isAnnually(dayOfMonth, month)) {
        return Service.handleAnnualSchedule(dayOfMonth[0], month[0]);
      }

      return "repeats indefinitely";
    } catch (err) {
      return "";
    }
  },

  isIndefinite(dayOfMonth, month, dayOfWeek) {
    return dayOfMonth.length === 31 && month.length === 12 && dayOfWeek.length === 7;
  },

  isWeekly(dayOfMonth, dayOfWeek) {
    return dayOfMonth.length === 31 && dayOfWeek.length !== 7;
  },

  isMonthly(dayOfMonth, month) {
    return dayOfMonth.length !== 31 && month.length === 12;
  },

  isAnnually(dayOfMonth, month) {
    return dayOfMonth.length !== 31 && month.length !== 12;
  },

  handleMonthlySchedule(day) {
    if (day > 28) {
      return `repeats monthly on the ${Service.ordinalSuffix(day)} (does not run in February and other months without the ${Service.ordinalSuffix(
        day
      )})`;
    }
    return `repeats monthly on the ${Service.ordinalSuffix(day)}`;
  },

  handleAnnualSchedule(day, month) {
    const monthName = Service.getMonthName(month);
    return `repeats annually on the ${Service.ordinalSuffix(day)} of ${monthName}`;
  },

  getDayName(dayIndex) {
    const days = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
    return days[dayIndex];
  },

  getMonthName(monthIndex) {
    const months = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
    return months[monthIndex - 1];
  },

  ordinalSuffix(day) {
    const suffixes = ["th", "st", "nd", "rd"];
    const value = day % 100;
    return (
      day +
      (suffixes[(value - 20) % 10] || // handles 11, 12, 13 => 11th, 12th, 13th
        suffixes[value] ||
        suffixes[0])
    );
  },

  getBillingMethod(method) {
    return METHODS_INTERPRETER[method - 1];
  },

  getNextDateFromBaseDate(initialDate, increment) {
    const currentDate = new Date(initialDate);
    if (increment === "annually") {
      return dateFns.add(currentDate, { years: 1 });
    }
    if (increment === "monthly") {
      return addMonths(currentDate, 1);
    }

    if (typeof increment === "number") {
      return addDays(currentDate, increment);
    }
    return currentDate;
  },

  getAccountType(account) {
    return String(account).includes("subacc") ? "SubAccount" : "DepositAccount";
  },

  validateDate(dateString) {
    if (!dateString) return null;
    // Check if dateString is provided and valid
    const date = new Date(dateString);
    // Return null if the date is invalid or not a real Date object
    return Number.isNaN(date.getTime()) ? null : date;
  },

  /**
   * Calculate the prorated cost based on the number of days left until a given due date.
   *
   * @param {number} cost - The cost per month in basic currency unit.
   * @param {Date} dueDate - The due date for the cost.
   * @returns {number} - The prorated cost rounded (no decimals).
   */
  calculateProratedCost(cost, dueDate) {
    if (+cost === 0) return 0;

    if (cost < 0) {
      return false;
    }

    const today = new Date();

    const parsedDueDate = dateFns.parseISO(new Date(dueDate).toISOString());

    // Ensure the due date is in the future
    if (dateFns.isBefore(parsedDueDate, today)) {
      return false;
    }

    const totalDaysInMonth = dateFns.getDate(dateFns.lastDayOfMonth(today)); // Total days in the current month
    const daysLeft = dateFns.differenceInCalendarDays(parsedDueDate, today);
    const dailyCost = Service.money(cost).div(totalDaysInMonth).toNumber(); // Daily cost based on the current month
    const proratedCost = Service.money(dailyCost).times(daysLeft).toNumber(); // Cost for remaining days

    return Math.round(proratedCost); // Rounded to the nearest basic unit
  },

  getNextDate(increment) {
    const currentDate = new Date();
    if (increment === "annually") {
      return dateFns.add(currentDate, { years: 1 });
    }
    if (increment === "monthly") {
      return dateFns.addMonths(currentDate, 1);
    }

    if (typeof increment === "number") {
      return dateFns.addDays(currentDate, increment);
    }
    return currentDate;
  },

  getScaleFactor(amount) {
    const stringAmount = String(amount);
    if (!stringAmount.includes(".")) return 2;
    return stringAmount.split(".")[1].length;
  },

  getAmountFromScaleValue(amount, scale = 2) {
    const scaleNumber = 10 ** scale;
    return amount / scaleNumber;
  },

  getPhysicalCardRequestCharge(paymentPlan = {}) {
    const serviceConfig = typeof paymentPlan?.configuration === "string" ? Service.parseJSON(paymentPlan.configuration) : paymentPlan.configuration;

    const { addOns: { physicalCards } = {} } = serviceConfig || {};

    const { fee = 300000, free_cards: freeCards = 0, shippingFee = 1000000 } = physicalCards.NGN || {};

    const totalAmount = Service.money(fee).plus(shippingFee).toNumber();

    return totalAmount;
  },

  /**
   * Implements exponential backoff with jitter for retrying operations
   * @param {number} attempt - Current attempt number (0-based)
   * @param {number} baseDelayMs - Base delay in milliseconds (default: 100)
   * @param {number} maxAttempts - Maximum number of attempts (default: 5)
   * @returns {Promise<number>} - Returns the delay used in milliseconds
   * @throws {Error} - If maximum attempts exceeded
   */
  async exponentialBackoff(attempt, { baseDelayMs = 100, maxAttempts = 5 } = {}) {
    if (attempt >= maxAttempts) {
      throw new Error(`Maximum retry attempts (${maxAttempts}) exceeded`);
    }

    // Calculate delay with jitter to avoid thundering herd
    const delayMs = baseDelayMs * Math.pow(2, attempt) * (0.5 + Math.random());
    await new Promise((resolve) => setTimeout(resolve, delayMs));
    return delayMs;
  },

  async waitFor(condition, timeout = 1000, interval = 50) {
    const start = Date.now();
    while (Date.now() - start < timeout) {
      if (condition()) return true;
      // eslint-disable-next-line no-await-in-loop
      await new Promise((resolve) => setTimeout(resolve, interval));
    }
    return false;
  },

  /**
 * Validates a tax sequence number to ensure it doesn't create gaps in sequence
 * 
 * @param {number} sequenceNumber - The sequence number to validate
 * @param {Array} existingTaxes - Array of existing taxes with sequence_number property
 * @throws {ValidationError} If the sequence number would create a gap
 */
async validateTaxSequenceNumber (sequenceNumber, existingTaxes) {
  if (!sequenceNumber) return;
  
  const maxSequence = existingTaxes.length ? 
    Math.max(...existingTaxes.map((t) => t.sequence_number), 0) : 0;
  
  // If sequence number is greater than current max + 1, it would create a gap
  if (sequenceNumber > maxSequence + 1) {
    throw new ValidationError(`Sequence number cannot be greater than ${maxSequence + 1}`);
  }
},
};

module.exports = Service;
