const Joi = require("joi");

module.exports = {
  create: Joi.object({
    currency: Joi.string().required().valid("NGN", "USD"),
    balanceType: Joi.string().required(),
    name: Joi.string().required(),
  }),

  migrate: Joi.object({
    idempotencyKey: Joi.string().optional(),
    account: Joi.alternatives().try(Joi.string().pattern(/blc_.{17}$/), Joi.string().pattern(/bdg_.{17}$/)),
  }),

  processMigration: Joi.object({
    trial: Joi.number().required(),
    idempotencyKey: Joi.string().optional(),
    accountMigration: Joi.string()
      .pattern(/acm_.{17}$/)
      .required(),
  }),

  initiateTransfer: Joi.object({
    trial: Joi.number().required(),
    idempotencyKey: Joi.string().optional(),
    accountMigration: Joi.string()
      .pattern(/acm_.{17}$/)
      .required(),
    providerToUse: Joi.string().required(),
  }),

  finalizeMigration: Joi.object({
    idempotencyKey: Joi.string().optional(),
    accountMigration: Joi.string()
      .pattern(/acm_.{17}$/)
      .required(),
  }),
};
