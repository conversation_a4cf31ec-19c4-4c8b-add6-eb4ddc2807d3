/* eslint-disable prefer-regex-literals */
const Joi = require("joi");

const operator = Joi.string().valid("eq", "gt", "lt", "not:eq", "gte", "lte");
const trigger = Joi.string().valid("vendor", "type", "category", "budget", "amount", "account").required();
const allowedTypes = Joi.string()
  .valid("reimbursement", "payment", "all", "batchPayment", "fundRequest", "cardRequest", "subaccount", "account", "invoice", "bill")
  .required();
const vendor = Joi.string().pattern(new RegExp("vdr_.{17}$")).messages({
  "string.pattern.base": "Invalid vendor code",
});
const category = Joi.string().pattern(new RegExp("ctg_.{17}$")).messages({
  "string.pattern.base": "Invalid category code",
});
const account = Joi.string().pattern(new RegExp("blc_.{17}$")).messages({
  "string.pattern.base": "Invalid account code",
});
const budget = Joi.string().pattern(new RegExp("bdg_.{17}$")).messages({
  "string.pattern.base": "Invalid budget code",
});
const user = Joi.string().pattern(new RegExp("usr_.{17}$")).messages({
  "string.pattern.base": "Invalid user code",
});
const approvalCondition = Joi.string().pattern(new RegExp("apc_.{17}$")).messages({
  "string.pattern.base": "Invalid approval condition code",
});

const approverLevel = Joi.string().pattern(new RegExp("apl_.{17}$")).messages({
  "string.pattern.base": "Invalid approver Level code",
});

const approverCondition = Joi.string().pattern(new RegExp("app_.{17}$")).messages({
  "string.pattern.base": "Invalid approver code",
});

const transaction = Joi.string().pattern(new RegExp("trx_.{17}$")).messages({
  "string.pattern.base": "Invalid transaction code",
});

const schedule = Joi.object({
  schedule: Joi.when("recurring", {
    is: true,
    then: Joi.string().valid("hourly", "weekly", "monthly", "quarterly", "yearly"),
    otherwise: Joi.object({
      minutes: Joi.number().integer().max(59).optional(),
      hours: Joi.number().integer().max(23).optional(),
      dayOfMonth: Joi.number().integer().max(31).optional(),
      month: Joi.string().optional(),
      dayOfWeek: Joi.string().optional(),
    }),
  }),
  startDate: Joi.object({
    date: Joi.string().required(),
    timestamp: Joi.string().required(),
  }),
  expiryDate: Joi.object({
    date: Joi.string().required(),
    timestamp: Joi.string().required(),
  }),
  recurring: Joi.boolean().required().default(true),
});

const conditions = Joi.object({ trigger: trigger.required() })
  .when(".trigger", {
    is: "vendor",
    then: Joi.object({
      operator: Joi.string().valid("is", "any", "not:is", "not:any").required(),
      operands: Joi.array().items(vendor).unique().required(),
    }),
  })
  .when(".trigger", {
    is: "category",
    then: Joi.object({
      operator: Joi.string().valid("is", "any", "not:is", "not:any").required(),
      operands: Joi.array().items(category).unique().required(),
    }),
  })
  .when(".trigger", {
    is: "budget",
    then: Joi.object({
      operator: Joi.string().valid("is", "any", "not:is", "not:any").required(),
      operands: Joi.array().items(budget).unique().required(),
    }),
  })
  .when(".trigger", {
    is: "account",
    then: Joi.object({
      operator: Joi.string().valid("is", "any", "not:is", "not:any").required(),
      operands: Joi.array().items(account).unique().required(),
    }),
  })
  .when(".trigger", {
    is: "amount",
    then: Joi.object({
      operator: operator.required(),
      operands: Joi.number().integer().min(100).required(),
    }),
  })
  .when(".trigger", {
    is: "type",
    then: Joi.object({
      operator: Joi.string().valid("is", "not:is").required(),
      operands: Joi.array().items(allowedTypes).unique().required(),
    }),
  });

const approver = Joi.object({
  code: approverLevel.required(),
  threshold: Joi.alternatives().try(Joi.string().valid("all", "any"), Joi.number().integer().min(2)).optional(),
  approvers: Joi.array().items(user).unique().optional(),
});

const createApprover = Joi.object({
  rank: Joi.number().integer().min(1).required(),
  threshold: Joi.alternatives().try(Joi.string().valid("all", "any"), Joi.number().integer().min(2)).required(),
  reviewers: Joi.array().items(user).unique().required(),
});

module.exports = {
  create: Joi.object({
    name: Joi.string().max(50).required(),
    conditions: Joi.array()
      .items(
        Joi.object({ trigger: trigger.required() })
          .when(".trigger", {
            is: "vendor",
            then: Joi.object({
              operator: Joi.string().valid("is", "any", "not:is", "not:any").required(),
              operands: Joi.array().items(vendor).unique().required(),
            }),
          })
          .when(".trigger", {
            is: "category",
            then: Joi.object({
              operator: Joi.string().valid("is", "any", "not:is", "not:any").required(),
              operands: Joi.array().items(category).unique().required(),
            }),
          })
          .when(".trigger", {
            is: "budget",
            then: Joi.object({
              operator: Joi.string().valid("is", "any", "not:is", "not:any").required(),
              operands: Joi.array().items(budget).unique().required(),
            }),
          })
          .when(".trigger", {
            is: "account",
            then: Joi.object({
              operator: Joi.string().valid("is", "any", "not:is", "not:any").required(),
              operands: Joi.array().items(account).unique().required(),
            }),
          })
          .when(".trigger", {
            is: "amount",
            then: Joi.object({
              operator: operator.required(),
              operands: Joi.number().integer().min(100).required(),
            }),
          })
          .when(".trigger", {
            is: "type",
            then: Joi.object({
              operator: Joi.string().valid("is", "not:is").required(),
              operands: Joi.array().items(allowedTypes).unique().required(),
            }),
          })
      )
      .required(),
    reviews: Joi.array()
      .items(
        Joi.object({
          rank: Joi.number().integer().min(1).required(),
          threshold: Joi.alternatives().try(Joi.string().valid("all", "any"), Joi.number().integer().min(2)).required(),
          reviewers: Joi.array().items(user).unique().required(),
        })
      )
      .unique("rank")
      .required(),
  }),

  update: Joi.object({
    name: Joi.string().max(50).optional(),
    condition: Joi.object({
      oldConditions: Joi.array().items(approvalCondition).unique().optional(),
      newConditions: Joi.array().items(conditions).optional(),
    }).optional(),
    approver: Joi.object({
      oldApprovers: Joi.array().items(approverCondition).optional(),
      newApprovers: Joi.array().items(approver).unique().message("You are adding the same approver twice or multiple times").optional(),
    }).optional(),
  }),

  uniqueApprover: Joi.array().items(user).unique().message("You have an approver appearing more than once").required(),
  uniqueOperator: Joi.array().items(operator).unique().message("You can not have same amount operators").required(),
  createApproverLevels: Joi.array().items(createApprover).unique("rank").required(),
  approverLevel: approverLevel.required(),

  multipleAction: Joi.object({
    requests: Joi.array().items(
      Joi.string().pattern(new RegExp("apr_.{17}$")).messages({
        "string.pattern.base": "Invalid requeest code",
      })
    ),
    status: Joi.string().valid("approved", "declined").required(),
    reason: Joi.string()
      .when("status", {
        is: "declined",
        then: Joi.required(),
        otherwise: Joi.optional(),
      })
      .allow(null, "")
      .optional(),
    actionLater: Joi.boolean(),
  }),

  vendorApprovers: Joi.object({
    vendor: Joi.string()
      .pattern(new RegExp("vdr_.{17}$"))
      .messages({
        "string.pattern.base": "Invalid vendor code sent",
      })
      .required(),
    perPage: Joi.number().integer().optional(),
    page: Joi.number().integer().optional(),
  }),

  reviewApprovalRequest: Joi.object({
    schedule,
    status: Joi.string().valid("approved", "declined").required(),
    reason: Joi.string().allow(null, "").optional(),
    pin: Joi.string().optional(),
    actionLater: Joi.boolean(),
    notification: Joi.boolean(),
    code: Joi.string().pattern(new RegExp("apr_.{17}$")).messages({
      "string.pattern.base": "Invalid requeest code",
    }),
    transactionsToDecline: Joi.array().items(transaction).unique().message("You have a transaction appearing more than once"),
    transactionsToApprove: Joi.array().items(transaction).unique().message("You have a transaction appearing more than once"),
  }).oxor("actionLater", "schedule"),

  resendNotification: Joi.object({
    code: Joi.string()
      .pattern(/^(apr_|frq_|rbs_).{17}$/)
      .messages({
        "string.pattern.base": "Invalid request code",
      }),
  }),
};
