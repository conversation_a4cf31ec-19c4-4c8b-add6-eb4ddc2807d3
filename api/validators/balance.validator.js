const Joi = require("joi");
const allowedCurrency = ["ngn", "usd"];
module.exports = {
  fundUSDWithNaira: Joi.object({
    amount: Joi.number().positive().required().min(1000).messages({
      "number.min": "Minimum amount to fund is $10",
    }),
    baseCurrency: Joi.string().optional(),
    source: Joi.string()
      .pattern(/blc_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid balance code sent",
      }),
    recipient: Joi.string()
      .pattern(/blc_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid balance code sent",
      }),
  }),

  getUSDAccountDetails: Joi.object({
    amount: Joi.number().positive().required().min(10000).messages({
      "number.min": "Minimum amount to fund is $100",
    }),
  }),

  exchangeRate: Joi.object({
    baseCurrency: Joi.string()
      .valid(...allowedCurrency)
      .required(),
    targetCurrency: Joi.string()
      .valid(...allowedCurrency)
      .required(),
    amount: Joi.number().integer().min(1).required(),
  }),

  viewBalance: Joi.object({
    code: Joi.string()
      .pattern(/blc_.{17}$/)
      .required()
      .messages({
        "string.pattern.base": "Invalid balance code sent",
      }),
  }),

  updateBalance: Joi.object({
    name: Joi.string().optional(),
    type: Joi.string().optional(),
    status: Joi.string().optional(),
  }),
};
