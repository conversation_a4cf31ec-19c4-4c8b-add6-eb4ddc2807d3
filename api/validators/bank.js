const Utils = require("../utils");

const Joi = require("joi").extend(require("@joi/date"));

module.exports = {
  create: Joi.object({
    bankName: Joi.string().optional(),
    bankCode: Joi.string()
      .pattern(/^[0-9A-Za-z]{3,7}$/)
      .required()
      .messages({
        "string.pattern.base": "Invalid Bank code sent",
      }),
    accountName: Joi.string().required(),
    number: Joi.string()
      .pattern(/^[0-9]{10}$/)
      .required()
      .messages({
        "string.pattern.base": "Number must be 10 characters long and is required",
      }),
    currency: Joi.string().optional().default("NGN"),
    defaultBank: Joi.boolean().optional().default(false),
  }),
  createVirtualAccount: Joi.object({
    name: Joi.string().required(),
    bankName: Joi.string().optional(),
    bankCode: Joi.alternatives()
      .try(
        Joi.string().pattern(/^[0-9A-Za-z]{3,7}$/), // Allows alphanumeric 3-7 chars
        Joi.string().valid("paystack") // Explicitly allows "paystack"
      )
      .optional()
      .messages({
        "string.pattern.base": "Invalid Bank code sent",
        "any.only": "Invalid Bank code sent", // Handles the "paystack" case
      }),
    purpose: Joi.string().required().allow("revenue", "tax", "expenses"),
    currency: Joi.string().required().default("NGN"),
    parent: Joi.string()
      .pattern(/bnk_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid bank account code sent",
      }),
    beneficiaries: Joi.array()
      .items(
        Joi.object({
          beneficiary: Joi.string()
            .pattern(/(bnf)_.{17}$/)
            .messages({
              "string.pattern.base": "Invalid beneficiary code",
            }),
          designation: Joi.string().valid("Owner", "Manager", "Member").default("Member"),
        })
      )
      .allow(null),
    accountNumber: Joi.string().optional(),
  }),

  update: Joi.object({
    bankCode: Joi.string()
      .pattern(/^[0-9A-Za-z]{3,7}$/)
      .required()
      .messages({
        "string.pattern.base": "Invalid Bank code sent",
      }),
    accountName: Joi.string().optional(),
    number: Joi.string()
      .pattern(/^[0-9]{10}$/)
      .optional()
      .messages({
        "string.pattern.base": "Number must be 10 characters long and is required",
      }),
    currency: Joi.string().optional().default("NGN"),
  }),

  verifyAccountNumber: Joi.object({
    bankCode: Joi.string()
      .pattern(/^[0-9A-Za-z]{3,7}$/)
      .required()
      .messages({
        "string.pattern.base": "Invalid Bank code sent",
      }),
    accountNumber: Joi.string()
      .pattern(/^[0-9]{10}$/)
      .required()
      .messages({
        "string.pattern.base": "Account Number must be 10 characters long and is required",
      }),
  }),

  resolveAccountNumber: Joi.object({
    accountNumber: Joi.string()
      .pattern(/^[0-9]{10}$/)
      .required()
      .messages({
        "string.pattern.base": "Account Number must be 10 characters long and is required",
      }),
  }),

  internalAccountNumberValidator: Joi.object({
    bankCode: Joi.string()
      .pattern(/^[0-9A-Za-z]{3,7}$/)
      .required()
      .messages({
        "string.pattern.base": "Invalid Bank code sent",
      }),
    accountNumber: Joi.string()
      .pattern(/^[0-9]{10}$/)
      .required()
      .messages({
        "string.pattern.base": "Account Number must be 10 characters long and is required",
      }),
    provider: Joi.string().required(),
    attempt: Joi.number().optional(),
  }),

  bulkVerifyAccount: Joi.object({
    accounts: Joi.array()
      .required()
      .items(
        Joi.object({
          bankCode: Joi.string()
            .pattern(/^[0-9A-Za-z]{3,7}$/)
            .required()
            .messages({
              "string.pattern.base": "Invalid Bank code sent",
            }),
          accountNumber: Joi.string()
            .pattern(/^[0-9]{10}$/)
            .required()
            .messages({
              "string.pattern.base": "Account Number must be 10 characters long and is required",
            }),
        })
      ),
  }),

  getAccount: Joi.object({
    code: Joi.string()
      .pattern(/bnk_.{17}$/)
      .required()
      .messages({
        "string.pattern.base": "Invalid Bank code sent",
      }),
  }),

  getAccounts: Joi.object({
    status: Joi.string(),
    requiresReauth: Joi.boolean().default(false),
    from: Joi.string(),
    to: Joi.string(),
    page: Joi.number(),
    perPage: Joi.number().max(100).default(50),
  }),

  createMandateForDirectDebit: Joi.object({
    accountNumber: Joi.string()
      .pattern(/^[0-9]{10}$/)
      .required()
      .messages({
        "string.pattern.base": "Account Number must be 10 characters long and is required",
      })
      .when("bankAccount", {
        not: Joi.exist(),
        then: Joi.required(),
      }),
    startDate: Joi.date().format("YYYY-MM-DD").min(Utils.getFutureDate(3)).required().messages({
      "date.min": "Date must be at least 3 days into the future",
      "date.format": "Invalid date format. Please use the YYYY-MM-DD date format.",
    }),
    endDate: Joi.date().format("YYYY-MM-DD").min(Joi.ref("startDate")).required().messages({
      "date.min": "endDate must be greater than startDate",
      "date.format": "Invalid date format. Please use the YYYY-MM-DD date format.",
    }),
    signature: Joi.string(),
    debitType: Joi.string().valid("variable", "fixed"),
    bankCode: Joi.string()
      .pattern(/^[0-9A-Za-z]{3,7}$/)
      .required()
      .messages({
        "string.pattern.base": "Invalid Bank code sent",
      })
      .when("bankAccount", {
        not: Joi.exist(),
        then: Joi.required(),
      }),
    accountName: Joi.string().when("bankAccount", {
      not: Joi.exist(),
      then: Joi.required(),
    }),
    bankName: Joi.string().optional(),
    amount: Joi.number().positive().required(),
    currency: Joi.string().optional().default("NGN"),
    bankAccount: Joi.string().pattern(new RegExp("bnk_.{17}$")).messages({
      "string.pattern.base": "Invalid bank account code sent",
    }),
  }),

  directDebit: Joi.object({
    bankCode: Joi.string()
      .pattern(/^[0-9A-Za-z]{3,7}$/)
      .required()
      .messages({
        "string.pattern.base": "Invalid Bank code sent",
      }),
    accountNumber: Joi.string()
      .pattern(/^[0-9]{10}$/)
      .required()
      .messages({
        "string.pattern.base": "Account Number must be 10 characters long and is required",
      }),
    amount: Joi.number().positive().required(),
    narration: Joi.string().min(3).max(100).required(),
  }),

  addMembers: Joi.object({
    beneficiaries: Joi.array()
      .items(
        Joi.object({
          beneficiary: Joi.string()
            .pattern(/(bnf)_.{17}$/)
            .messages({
              "string.pattern.base": "Invalid beneficiary code",
            }),
          designation: Joi.string().valid("Owner", "Manager", "Member").default("Member"),
        })
      )
      .allow(null),
  }),

  removeMember: Joi.object({
    member: Joi.string()
      .pattern(/(acm)_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid member code",
      })
      .required(),
    code: Joi.string()
      .pattern(/(blc)_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid balance code",
      })
      .required(),
  }),

  getMandateStatus: Joi.object({
    code: Joi.string()
      .pattern(/(mnd)_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid mandate code",
      })
      .required(),
  }),

  cancelMandateOrSync: Joi.object({
    mandate: Joi.string()
      .pattern(/(mnd)_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid mandate code",
      })
      .when("directDebit", {
        is: true,
        then: Joi.required(),
      }),
    code: Joi.string()
      .pattern(/bnk_.{17}$/)
      .required()
      .messages({
        "string.pattern.base": "Invalid Bank code sent",
      }),
    bankSync: Joi.boolean(),
    directDebit: Joi.boolean(),
  }),
};
