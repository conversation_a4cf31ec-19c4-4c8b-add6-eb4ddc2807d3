const Joi = require("joi");

module.exports = {
  create: Joi.object({
    firstName: Joi.string().required(),
    lastName: Joi.string().required(),
    role: Joi.string().optional(),
    manager: Joi.string().optional(),
    email: Joi.string().email().optional(),
    phoneNumber: Joi.object({
      countryCode: Joi.number().greater(0).required(),
      localFormat: Joi.string().min(6).required(),
    }),
    method: Joi.string().valid("budget", "balance"),
    additionalSeats: Joi.number(),
    budget: Joi.string()
      .pattern(/bdg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid budget code sent",
      })
      .when("method", {
        is: "budget",
        then: Joi.required(),
      }),
    balance: Joi.string()
      .pattern(/blc_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid balance code sent",
      })
      .when("method", {
        is: "balance",
        then: Joi.required(),
      }),
    directDebit: Joi.object({
      bankAccount: Joi.string()
        .pattern(/bnk_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid bank code sent",
        }),
    }).when("method", {
      is: "directdebit",
      then: Joi.required(),
    }),
    budgets: Joi.array()
      .items(
        Joi.string()
          .pattern(/bdg_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid budget code sent",
          })
      )
      .optional()
      .allow(null),
  }).xor("email", "phoneNumber"),

  bulkCreate: Joi.object({
    beneficiaries: Joi.array().items(
      Joi.object({
        firstName: Joi.string().required(),
        lastName: Joi.string().required(),
        role: Joi.string().when("team", {
          is: Joi.exist(),
          then: Joi.required(),
        }),
        manager: Joi.string().optional(),
        email: Joi.string().email().optional(),
        phoneNumber: Joi.object({
          countryCode: Joi.number().greater(0).required(),
          localFormat: Joi.string().min(6).required(),
        }),
        dob: Joi.string(),
        hiredOn: Joi.string(),
        leftOn: Joi.string(),
        team: Joi.string(),
      }).xor("email", "phoneNumber")
    ),
    method: Joi.string().valid("budget", "balance"),
    additionalSeats: Joi.number(),
    budget: Joi.string()
      .pattern(/bdg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid budget code sent",
      })
      .when("method", {
        is: "budget",
        then: Joi.required(),
      }),
    balance: Joi.string()
      .pattern(/blc_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid balance code sent",
      })
      .when("method", {
        is: "balance",
        then: Joi.required(),
      }),
    directDebit: Joi.object({
      bankAccount: Joi.string()
        .pattern(/bnk_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid bank code sent",
        }),
    }).when("method", {
      is: "directdebit",
      then: Joi.required(),
    }),
  }),

  getAll: Joi.object({
    owner: Joi.number().integer().min(1).required(),
    company: Joi.number().integer().min(1).required(),
    page: Joi.number().integer().optional(),
    perPage: Joi.string().optional(),
    from: Joi.string().optional(),
    to: Joi.string().optional(),
    search: Joi.string().optional(),
    role: Joi.string().valid("manager", "admin", "employee").optional(),
  }),

  search: Joi.object({
    company: Joi.number().integer().min(1).required(),
    manager: Joi.string().optional(),
    q: Joi.string().min(3).optional().messages({
      "string.min": "query string must be 3 characters long",
    }),
  }),

  update: Joi.object({
    budgets: Joi.array()
      .items(
        Joi.string().pattern(new RegExp("bdg_.{17}$")).messages({
          "string.pattern.base": "Invalid budget code",
        })
      )
      .optional(),
    status: Joi.string().optional(),
    role: Joi.string().optional(),
    email: Joi.string().email().optional(),
    manager: Joi.string().optional(),
  }),

  listBeneficiaries: Joi.object({
    active: Joi.bool().optional(),
    excludeInactiveBudgets: Joi.bool().optional(),
    page: Joi.number().optional(),
    perPage: Joi.number().optional(),
    search: Joi.string().optional(),
    status: Joi.array().items(Joi.string()),
    from: Joi.date().optional(),
    to: Joi.date().optional(),
    budget: Joi.alternatives()
      .try(
        Joi.string()
          .pattern(/bdg_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid budget code",
          }),
        Joi.array().items(
          Joi.string()
            .pattern(/bdg_.{17}$/)
            .messages({
              "string.pattern.base": "Invalid budget code",
            })
        )
      )
      .optional(),
    role: Joi.array()
      .items(
        Joi.alternatives().try(
          Joi.string(),
          Joi.string()
            .pattern(/rol_.{17}$/)
            .messages({
              "string.pattern.base": "Invalid role sent",
            })
        )
      )
      .optional(),
  }),
};
