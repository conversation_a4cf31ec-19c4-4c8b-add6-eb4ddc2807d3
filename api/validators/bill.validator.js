const Joi = require("joi").extend(require("@joi/date"));

const ALLOWED_DISCOUNT_TYPES = ["amount", "percentage"];
const installments = Joi.object({
  type: Joi.string().required().valid("percentage", "amount"),
  payments: Joi.array()
    .items(
      Joi.object({
        amount: Joi.number().min(1000).optional(),
        percentage: Joi.number().min(1).max(100),
        due_date: Joi.date().required(),
      })
    )
    .required(),
}).optional();

const asset = Joi.string()
  .pattern(/ast_.{17}$/)
  .messages({
    "string.pattern.base": "Invalid asset code",
  });

const scheduledBill = Joi.object({
  schedule: Joi.when("recurring", {
    is: true,
    then: Joi.string().valid("hourly", "weekly", "monthly", "quarterly", "yearly"),
    otherwise: Joi.object({
      minutes: Joi.number().integer().max(59).optional(),
      hours: Joi.number().integer().max(23).optional(),
      dayOfMonth: Joi.number().integer().max(31).optional(),
      month: Joi.string().optional(),
      dayOfWeek: Joi.string().optional(),
    }),
  }),
  startDate: Joi.object({
    date: Joi.string().required(),
    timestamp: Joi.string().required(),
  }),
  expiryDate: Joi.object({
    date: Joi.string().required(),
    timestamp: Joi.string().required(),
  }),
  recurring: Joi.boolean().required().default(true),
}).optional();

module.exports = {
  createBill: Joi.object({
    vendor: Joi.string()
      .pattern(/vdr_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid vendor code",
      })
      .when("isDraft", {
        is: false,
        then: Joi.required(),
      }),
    products: Joi.array().items(
      Joi.object({
        name: Joi.string().required(),
        quantity: Joi.number().min(1).required().messages({
          "number.min": "Product quantity must be greater than 0",
        }),
        unitPrice: Joi.number().required().min(10000).messages({
          "number.min": "Product Price must be greater than N100",
        }),
        currency: Joi.string().optional(),
        discount_type: Joi.string().valid(...ALLOWED_DISCOUNT_TYPES),
        discount: Joi.number().optional(),
      })
    ),
    vat: Joi.number().optional().allow(null),
    vatAmount: Joi.number().optional().allow(null),
    discount_type: Joi.string().valid(...ALLOWED_DISCOUNT_TYPES),
    discount: Joi.number().optional(),
    recurring: Joi.number().valid(0, 1).optional(),
    isDraft: Joi.boolean().default(false).optional(),
    terms: Joi.number().optional().allow(null),
    dueDate: Joi.date().optional(),
    category: Joi.string()
      .pattern(/ctg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid category code",
      }),
    reason: Joi.string().optional().allow(null),
    reference: Joi.string().optional().allow(null),
    minAmountDue: Joi.number().optional().allow(null),
    method: Joi.string().valid("directdebit", "budget", "balance"),
    budget: Joi.string()
      .pattern(/bdg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid budget code sent",
      })
      .when("method", {
        is: "budget",
        then: Joi.required(),
      }),
    balance: Joi.string()
      .pattern(/blc_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid balance code sent",
      })
      .when("method", {
        is: "balance",
        then: Joi.required(),
      }),
    directDebit: Joi.object({
      bankAccount: Joi.string()
        .pattern(/bnk_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid bank code sent",
        }),
    }).when("method", {
      is: "directdebit",
      then: Joi.required(),
    }),
    installments,
    scheduledBill,
    sendForApproval: Joi.boolean().default(false).optional(),
    currency: Joi.string().length(3).required(),
    uploads: Joi.array().items(asset).unique().message("You have an upload appearing more than once"),
  })
    .oxor("balance", "budget", "directDebit")
    .messages({
      "object.oxor": "You must provide either budget, balance or direct debit",
    }),

  updateBill: Joi.object({
    currency: Joi.string().length(3),
    code: Joi.string()
      .pattern(/bil_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid bill code",
      }),
    vendor: Joi.string()
      .pattern(/vdr_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid vendor code",
      }),
    vat: Joi.number().optional().allow(null),
    vatAmount: Joi.number().optional().allow(null),
    discount_type: Joi.string().valid(...ALLOWED_DISCOUNT_TYPES),
    discount: Joi.number().optional(),
    recurring: Joi.number().valid(0, 1).optional(),
    isDraft: Joi.boolean().default(false).optional(),
    terms: Joi.number().optional().allow(null),
    dueDate: Joi.date().optional(),
    category: Joi.string()
      .pattern(/ctg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid category code",
      }),
    reason: Joi.string().optional().allow(null),
    reference: Joi.string().optional().allow(null),
    minAmountDue: Joi.number().optional().allow(null),
    status: Joi.string().valid("pending").optional(),
    method: Joi.string().valid("directdebit", "budget", "balance"),
    budget: Joi.string()
      .pattern(/bdg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid budget code sent",
      }),
    balance: Joi.string()
      .pattern(/blc_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid balance code sent",
      }),
    directDebit: Joi.object({
      bankAccount: Joi.string()
        .pattern(/bnk_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid bank code sent",
        }),
    }),
    scheduledBill,
    sendForApproval: Joi.boolean().default(false).optional(),
    uploads: Joi.array().items(asset).unique().message("You have an upload appearing more than once"),
    products: Joi.array()
      .optional()
      .items(
        Joi.alternatives().try(
          Joi.object({
            code: Joi.string()
              .required()
              .pattern(/bip_.{17}$/)
              .messages({
                "string.pattern.base": "Invalid bill product code",
              }),
            name: Joi.string().optional(),
            quantity: Joi.number().min(1).optional(),
            unitPrice: Joi.number().optional(),
            currency: Joi.string().optional(),
            discount_type: Joi.string().valid(...ALLOWED_DISCOUNT_TYPES),
            discount: Joi.number().optional(),
          }),
          Joi.object({
            name: Joi.string().required(),
            quantity: Joi.number().min(1).required().messages({
              "number.min": "Product quantity must be greater than 0",
            }),
            unitPrice: Joi.number().required().min(10000).messages({
              "number.min": "Product Price must be greater than N100",
            }), // Minimum of 100
            currency: Joi.string().optional(),
            discount_type: Joi.string().valid(...ALLOWED_DISCOUNT_TYPES),
            discount: Joi.number().optional(),
          })
        )
      ),
    installments: Joi.object({
      type: Joi.string().required().valid("percentage", "amount"),
      payments: Joi.array()
        .items(
          Joi.alternatives().try(
            Joi.object({
              amount: Joi.number().min(1000).optional(),
              percentage: Joi.number().min(1).max(100),
              due_date: Joi.date().required(),
            }),
            Joi.object({
              code: Joi.string()
                .pattern(/bin_.{17}$/)
                .required(),
              amount: Joi.number().min(1000).optional(),
              percentage: Joi.number().min(1).max(100),
              due_date: Joi.date().required(),
            })
          )
        )
        .required(),
    }).optional(),
  })
    .oxor("balance", "budget", "directDebit")
    .messages({
      "object.oxor": "You must provide either budget, balance or direct debit",
    }),

  listBills: Joi.object({
    company: Joi.number().required(),
    vendor: Joi.alternatives(
      Joi.string()
        .pattern(/vdr_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid vendor code",
        }),
      Joi.array().items(
        Joi.string()
          .pattern(/vdr_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid vendor code",
          })
      )
    ).messages({
      "alternatives.types": "Invalid vendor code sent",
    }),
    status: Joi.alternatives(
      Joi.string().valid(
        "inactive",
        "paid",
        "pending",
        "partial",
        "declined",
        "draft",
        "active",
        "verifying",
        "overdue",
        "inactive",
        "deleted",
        "approved"
      ),
      Joi.array().items(
        Joi.string().valid(
          "inactive",
          "paid",
          "pending",
          "partial",
          "declined",
          "draft",
          "active",
          "verifying",
          "overdue",
          "inactive",
          "deleted",
          "approved"
        )
      )
    ).messages({
      "alternatives.types": "Invalid status sent",
    }),
    from: Joi.date().optional(),
    to: Joi.date().optional(),
    search: Joi.string().optional(),
    perPage: Joi.number().optional(),
    page: Joi.number().optional(),
    minAmount: Joi.number().optional().positive(),
    maxAmount: Joi.number().positive().optional(),
  }),

  viewBill: Joi.object({
    code: Joi.string()
      .pattern(/bil_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid bill code",
      }),
  }),

  markBillAsPaid: Joi.object({
    code: Joi.string()
      .required()
      .pattern(/bil_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid bill code",
      }),
    installments: Joi.array()
      .items(
        Joi.string()
          .required()
          .pattern(/bin_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid Installment code",
          })
      )
      .optional(),
  }),

  getBillStats: Joi.object({
    from: Joi.date().optional(),
    to: Joi.date().optional(),
    vendor: Joi.alternatives(
      Joi.string()
        .pattern(/vdr_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid vendor code",
        }),
      Joi.array().items(
        Joi.string()
          .pattern(/vdr_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid vendor code",
          })
      )
    ).messages({
      "alternatives.types": "Invalid vendor code sent",
    }),
  }),

  payBill: Joi.object({
    amount: Joi.number(),
    code: Joi.string()
      .required()
      .pattern(/bil_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid bill code",
      }),
  }),

  syncZoho: Joi.object({
    codes: Joi.array()
      .items(Joi.string().pattern(/bil_.{17}$/))
      .unique()
      .required(),
  }),
};
