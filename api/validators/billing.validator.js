const Joi = require("joi").extend(require("@joi/date"));

const currencySchema = Joi.object({
  issuing_fees: Joi.number().required(),
  maintenance_fee: Joi.number().required(),
  funding_fees: Joi.number(),
}).required();

const currencyKeyPattern = Joi.string().regex(/^[A-Z]{3}$/);

const configuration = Joi.object({
  cards: Joi.object()
    .pattern(currencyKeyPattern, currencySchema.required())
    .concat(
      Joi.object({
        max_number: Joi.number().required(),
      })
    )
    .required(),
  budgets: Joi.object({
    max_budgets: Joi.number().required(),
  }).required(),
  bank_account: Joi.object({
    allowed: Joi.boolean().required(),
  }).required(),
  beneficiaries: Joi.object({
    max_users: Joi.number().required(),
  }).required(),
  customPricing: Joi.object({
    transactionFee: Joi.number().required(),
  }).required(),
  transactionFee: Joi.number().required(),
  annual_user_fee: Joi.object().pattern(currencyKeyPattern, Joi.number().required()).required(),
  approval_levels: Joi.number().required(),
  role_management: Joi.string().required(),
  monthly_user_fee: Joi.object().pattern(currencyKeyPattern, Joi.number().required()).required(),
  virtual_accounts: Joi.object()
    .pattern(
      currencyKeyPattern,
      Joi.object({
        allowed: Joi.boolean().required(),
        max_number: Joi.number().required(),
        deposit_fee: Joi.number().required(),
      })
    )
    .required(),
  connectedAccounts: Joi.object({
    canSync: Joi.boolean().required(),
    syncWindow: Joi.number().required(),
    maxRealTimeDataPerDay: Joi.number().required(),
    canRequestRealTimeData: Joi.boolean().required(),
  }).required(),
});

module.exports = {
  subscribe: Joi.object({
    method: Joi.string().valid("budget", "balance", "credits").required(),
    code: Joi.string()
      .pattern(/pln_.{16,17}$/)
      .messages({
        "string.pattern.base": "Invalid plan code sent",
      })
      .required(),
    budget: Joi.string()
      .pattern(/bdg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid budget code sent",
      }),
    balance: Joi.string()
      .pattern(/blc_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid balance code sent",
      }),
    directDebit: Joi.object({
      bankAccount: Joi.string()
        .pattern(/bnk_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid bank code sent",
        }),
    }),
    contactEmail: Joi.string().email(),
    address: Joi.string(),
    state: Joi.string(),
    country: Joi.string(),
    companyName: Joi.string(),
    city: Joi.string(),
    firstName: Joi.string(),
    lastName: Joi.string(),
    billingPeriod: Joi.string().valid("monthly", "annually").required(),
  })
    .oxor("balance", "budget")
    .messages({
      "object.oxor": "You must provide a source of funds",
    }),

  updateSubscription: Joi.object({
    method: Joi.string().valid("budget", "balance", "credits"),
    code: Joi.string()
      .pattern(/sub_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid subscription code sent",
      })
      .required(),
    budget: Joi.string()
      .pattern(/bdg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid budget code sent",
      }),
    balance: Joi.string()
      .pattern(/blc_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid balance code sent",
      }),
    directDebit: Joi.object({
      bankAccount: Joi.string()
        .pattern(/bnk_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid bank code sent",
        }),
    }),
    contactEmail: Joi.string().email(),
    address: Joi.string(),
    state: Joi.string(),
    country: Joi.string(),
    companyName: Joi.string(),
    city: Joi.string(),
    firstName: Joi.string(),
    lastName: Joi.string(),
    billingPeriod: Joi.string().valid("monthly", "annually"),
  })
    .oxor("balance", "budget")
    .messages({
      "object.oxor": "You must provide a source of funds",
    }),
  cancelSubscription: Joi.object({
    code: Joi.string()
      .pattern(/sub_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid subscription code sent",
      })
      .required(),
    reason: Joi.string().max(50).required(),
  }),

  createCustomPlan: Joi.object({
    name: Joi.string().max(50).required(),
    description: Joi.string().max(150).required(),
    currency: Joi.string().default("NGN"),
    amount: Joi.number().positive().required(),
    configuration: configuration.required(),
    company: Joi.string()
      .pattern(/cmp_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid company code sent",
      })
      .required(),
  }),

  updateCustomPlan: Joi.object({
    code: Joi.string()
      .pattern(/pln_.{16,17}$/)
      .messages({
        "string.pattern.base": "Invalid plan code sent",
      })
      .required(),
    name: Joi.string().max(50),
    description: Joi.string().max(150),
    currency: Joi.string().default("NGN"),
    amount: Joi.number().positive(),
    status: Joi.string(),
    configuration,
  }),

  freeTrial: Joi.object({
    method: Joi.string().valid("budget", "balance", "credits"),
    code: Joi.string()
      .pattern(/pln_.{16,17}$/)
      .messages({
        "string.pattern.base": "Invalid plan code sent",
      })
      .required(),
    budget: Joi.string()
      .pattern(/bdg_.{16,17}$/)
      .messages({
        "string.pattern.base": "Invalid budget code sent",
      }),
    balance: Joi.string()
      .pattern(/blc_.{16,17}$/)
      .messages({
        "string.pattern.base": "Invalid balance code sent",
      }),
    directDebit: Joi.object({
      bankAccount: Joi.string()
        .pattern(/bnk_.{16,17}$/)
        .messages({
          "string.pattern.base": "Invalid bank code sent",
        }),
    }),
    contactEmail: Joi.string().email(),
    address: Joi.string(),
    state: Joi.string(),
    country: Joi.string(),
    companyName: Joi.string(),
    city: Joi.string(),
    firstName: Joi.string(),
    lastName: Joi.string(),
    billingPeriod: Joi.string().valid("monthly", "annually"),
    company: Joi.string()
      .pattern(/cmp_.{16,17}$/)
      .messages({
        "string.pattern.base": "Invalid company code sent",
      })
      .required(),
    dueDate: Joi.date().format("YYYY-MM-DD").min("now").messages({
      "date.min": "Due date must be in the future",
      "date.format": "Invalid date format. Please use the YYYY-MM-DD date format.",
    }),
    isFreeTrial: Joi.boolean().default(true),
  })
    .oxor("balance", "budget")
    .messages({
      "object.oxor": "You must provide a source of funds",
    }),
};
