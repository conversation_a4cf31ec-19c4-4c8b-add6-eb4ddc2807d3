const Joi = require("joi");
const { json } = require("../services/response");
const allowCurrencies = [];

const scheduleBudget = Joi.object({
  expiryDate: Joi.object({
    date: Joi.string()
      .pattern(/^\d{4}-\d{2}-\d{2}$/)
      .required()
      .messages({
        "string.pattern.base": 'Date must follow the format "YYYY-MM-DD".',
        "any.required": "Date is required.",
      }),
    timestamp: Joi.string()
      .pattern(/^\d{2}:\d{2}:\d{2}$/)
      .optional()
      .messages({
        "string.pattern.base": 'Timestamp must follow the format "HH:mm:ssZ".',
        "any.required": "Timestamp is required.",
      }),
  }),
  schedule: Joi.string().valid("weekly", "monthly", "quarterly", "yearly"),
});

module.exports = {
  create: Joi.object({
    name: Joi.string().min(3).required(),
    amount: Joi.number().integer().positive().required().allow(0),
    bufferAmount: Joi.number().integer().positive().allow(0).optional(),
    currency: Joi.string().length(3).required().allow("XOF", "XAF", "NGN", "USD", "EUR", "KES", "GHS", "ZAR", "AED", "MAD", "GBP", "CAD", "LBP"),
    type: Joi.number().integer().min(1).required(),
    beneficiaries: Joi.alternatives()
      .try(
        Joi.array().items(
          Joi.string()
            .pattern(/bnf_.{17}$/)
            .messages({
              "string.pattern.base": "Invalid beneficiary code",
            })
        ),
        Joi.array().items(
          Joi.object({
            beneficiary: Joi.string()
              .pattern(/bnf_.{17}$/)
              .messages({
                "string.pattern.base": "Invalid beneficiary code",
              }),
            amount: Joi.number().min(0).default(0),
            isBudgetOwner: Joi.boolean().default(false),
            duration: Joi.string().valid("monthly", "daily", "weekly", "yearly").optional(),
            limit: Joi.number().min(0),
          })
        )
      )
      .optional()
      .allow(null),
    rate: Joi.number().integer().optional(),
    team: Joi.string()
      .pattern(/tms_.{17}$/)
      .optional(),
    balance: Joi.string()
      .pattern(/blc_.{17}$/)
      .optional(),
    scheduleBudget,
  }),

  groupCreate: Joi.object({
    name: Joi.string().min(3).required(),
    amount: Joi.number().integer().positive().required().allow(0),
    bufferAmount: Joi.number().integer().positive().allow(0).optional(),
    currency: Joi.string().length(3).required().allow("XOF", "XAF", "NGN", "USD", "EUR", "KES", "GHS", "ZAR", "AED", "MAD", "GBP", "CAD", "LBP"),
    type: Joi.number().integer().min(1).required(),
    periods: Joi.array().required().items(Joi.number()),
    periodNames: Joi.array().required().items(Joi.string()),
    frequency: Joi.string().required().allow("monthly", "quarterly", "yearly", "on-time"),
    startDate: Joi.date().required(),
    endDate: Joi.date().required(),
    expiryDate: Joi.date().required(),
    subBudgets: Joi.array(),
  }),

  update: Joi.object({
    name: Joi.string().min(3).optional(),
    amount: Joi.number().integer().positive().greater(99),
    type: Joi.number().integer().min(1).optional(),
    status: Joi.number().integer().min(1).optional(),
    bufferAmount: Joi.number().integer().positive().allow(0).optional(),
    scheduleBudget,
    beneficiariesToAdd: Joi.alternatives()
      .try(
        Joi.array().items(
          Joi.string()
            .pattern(/(bnf|ubg)_.{17}$/)
            .messages({
              "string.pattern.base": "Invalid beneficiary code",
            })
        ),
        Joi.array().items(
          Joi.object({
            beneficiary: Joi.string()
              .pattern(/(bnf|ubg)_.{17}$/)
              .messages({
                "string.pattern.base": "Invalid beneficiary code",
              }),
            amount: Joi.number().min(0).default(0),
            isBudgetOwner: Joi.boolean().default(false),
            duration: Joi.string().valid("monthly", "daily", "weekly", "yearly").optional(),
            limit: Joi.number().min(0),
            isUpdate: Joi.boolean(),
          })
        )
      )
      .optional()
      .allow(null),
    beneficiariesToRemove: Joi.array().items(
      Joi.string()
        .pattern(/bnf_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid beneficiary code",
        })
    ),
  }),

  getAll: Joi.object({
    owner: Joi.number().integer().min(1).required(),
    company: Joi.number().integer().min(1).required(),
    page: Joi.number().integer().optional(),
    perPage: Joi.string().optional(),
    from: Joi.string().optional(),
    to: Joi.string().optional(),
    search: Joi.string().optional(),
  }),

  addBeneficiaries: Joi.object({
    amount: Joi.number().min(0),
    beneficiaries: Joi.alternatives()
      .try(
        Joi.array().items(
          Joi.string().pattern(new RegExp("(bnf|ubg)_.{17}$")).messages({
            "string.pattern.base": "Invalid beneficiary code",
          })
        ),
        Joi.array().items(
          Joi.object({
            beneficiary: Joi.string().pattern(new RegExp("(bnf|ubg)_.{17}$")).messages({
              "string.pattern.base": "Invalid beneficiary code",
            }),
            amount: Joi.number().min(0),
            isBudgetOwner: Joi.boolean().default(false),
            duration: Joi.string().valid("monthly", "daily", "weekly", "yearly").optional(),
            limit: Joi.number().min(0),
          })
        )
      )
      .optional()
      .allow(null),
  }),

  addBeneficiary: Joi.object({
    amount: Joi.number().min(0),
    duration: Joi.string().valid("monthly", "daily", "weekly", "yearly"),
    limit: Joi.number().min(0),
    isBudgetOwner: Joi.boolean().default(false),
  }),

  generateAccountStatementNew: Joi.object({
    source: Joi.string().pattern(new RegExp("bdg_.{17}$")).required().messages({
      "string.pattern.base": "Invalid source sent",
    }),
    type: Joi.string().valid("debit", "credit", "all").required().messages({
      "any.only": "Type should either be debit, credit or all",
    }),
    startDate: Joi.date().optional(),
    endDate: Joi.date().optional(),
    page: Joi.number().optional(),
    perPage: Joi.number().optional(),
  }),

  topUpBudget: Joi.object({
    amount: Joi.number(),
    withdrawAll: Joi.boolean().optional(),
    balance: Joi.string()
      .pattern(/blc_.{17}$/)
      .optional()
      .messages({
        "string.pattern.base": "Invalid Balance sent",
      }),
    settlementBalance: Joi.string()
      .pattern(/blc_.{17}$/)
      .optional()
      .messages({
        "string.pattern.base": "Invalid Balance sent",
      }),
  }),
};
