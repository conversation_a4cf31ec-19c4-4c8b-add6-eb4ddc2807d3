const Joi = require("joi");

const asset = Joi.string().pattern(new RegExp("ast_.{17}$")).messages({
  "string.pattern.base": "Invalid asset code",
});

module.exports = {
  businessInformation: Joi.object({
    id: Joi.number().integer().min(1).required(),
    name: Joi.string().required(),
    industry: Joi.string().optional(),
    description: Joi.string().min(15).optional().messages({
      "string.min": `Company's description must be 15 characters long`,
    }),
    url: Joi.string()
      .pattern(/^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/)
      .optional()
      .messages({
        "string.pattern.base": "Please enter a valid website in the format www.example.com",
      }),
    company_code: Joi.string().max(30).required(),
    business_size: Joi.string().required().valid("micro", "small", "medium", "large"),
  }),
  businessAddress: Joi.object({
    id: Joi.number().integer().min(1).required(),
    contact_email: Joi.string().email().message("Email format is wrong").optional(),
    state: Joi.string().required(),
    city: Joi.string().required(),
    address: Joi.string().max(100).required(),
    country_code: Joi.string().max(30).required(),
    company_code: Joi.string().max(30).required(),
    postalCode: Joi.string().optional(),
    phone: Joi.object({
      countryCode: Joi.string().max(3).message("Invalid country code entered").required(),
      localFormat: Joi.string().required(),
    }).required(),
    contact_details: Joi.boolean().optional(),
    utilityBill: asset.optional(),
  }),
  inviteSubmission: Joi.object({
    invite: Joi.string()
      .pattern(/oiv_.{17}$/)
      .required()
      .messages({
        "string.pattern.base": "Invalid invite code sent",
        "any.required": "Invite code is required",
      }),
    firstName: Joi.string().required(),
    lastName: Joi.string().required(),
    email: Joi.string().email().required(),
    phoneNumber: Joi.object({
      countryCode: Joi.string().max(3).message("Invalid country code entered").required(),
      localFormat: Joi.string().required(),
    }).required(),
    documents: Joi.object({
      bnNumber: Joi.string()
        .max(20)
        .messages({
          "string.pattern.base": "BN Number should be prefixed with BN",
        })
        .pattern(/^BN\d+$/i),
      cacITNumber: Joi.string(),
      bnDocument: asset,
      issuingDate: Joi.string(),
      rcNumber: Joi.string()
        .max(10)
        .pattern(/RCs*[0-9]{6,7}$/),
      cacITForm1: asset,
      certificateOfTrustees: asset,
      scumlCertificate: asset,
      incorporationCertificate: asset,
      directorsName: Joi.string(),
      directorsEmail: Joi.string(),
      directorsPhone: Joi.object({
        countryCode: Joi.number().greater(0).required(),
        localFormat: Joi.string().min(6).required(),
      }),
      directorPercentage: Joi.number().integer().min(5).required(),
      dateOfBirth: Joi.date(),
      directorsDocuments: asset,
      memorandumOfAssociation: asset,
      idCopy: asset,
      utilityBill: asset,
      bvn: Joi.string()
        .length(11)
        .pattern(/^[0-9]+$/),
      idType: Joi.string().valid("nin", "ip", "vi", "dl").required(),
      idNumber: Joi.string().pattern(/^[a-zA-Z0-9]+$/),
      cac: asset,
      companyRegistrationDate: Joi.date(),
      cacBn1: asset,
      cacForm3: asset,
      cacForm2: asset,
      cacForm7: asset,
      cacForm1: asset,
      cacStatusReport: asset,
    }).required(),
  }),
  saveDirector: Joi.object({
    firstName: Joi.string().required(),
    lastName: Joi.string().required(),
    email: Joi.string().email().required(),
    phoneNumber: Joi.object({
      countryCode: Joi.string().max(3).message("Invalid country code entered").required(),
      localFormat: Joi.string().required(),
    }).required(),
    documents: Joi.object({
      idCopy: asset.required(),
      bvn: Joi.string()
        .length(11)
        .pattern(/^[0-9]+$/)
        .required(),
      idType: Joi.string().valid("nin", "ip", "vi", "dl").required(),
      idNumber: Joi.string()
        .pattern(/^[a-zA-Z0-9]+$/)
        .required(),
      directorPercentage: Joi.number().integer().min(5).required(),
      dateOfBirth: Joi.date(),
    }).required(),
  }),
  reviewOnboardingDocument: Joi.object({
    documents: Joi.array()
      .items(
        Joi.string()
          .pattern(/doc_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid document code sent",
            "any.required": "Document code is required",
          })
      )
      .required(),
    decision: Joi.string().valid("approved", "rejected").required(),
    reason: Joi.string().when("decision", { is: "approved", then: Joi.forbidden(), otherwise: Joi.required() }),
  }),
};
