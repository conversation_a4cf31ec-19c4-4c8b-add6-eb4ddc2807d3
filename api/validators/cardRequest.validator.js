const Joi = require("joi");

module.exports = {
  createCardRequest: Joi.object({
    beneficiary: Joi.string()
      .pattern(/bnf_.{17}$/)
      .optional(),
    budget: Joi.string()
      .pattern(/bdg_.{17}$/)
      .optional(),
    balance: Joi.string()
      .pattern(/blc_.{17}$/)
      .when("budget", {
        not: Joi.exist(),
        then: Joi.required().messages({
          "any.required": "balance or budget must be provided",
        }),
      }),
    firstName: Joi.string().required(),
    lastName: Joi.string().required(),
    address: Joi.object({
      billingAddress: Joi.string().required(),
      city: Joi.string().optional(),
      state: Joi.string().required(),
      country: Joi.string()
        .pattern(/ctry_.{17}$/)
        .required(),
      postalCode: Joi.string().optional(),
    }).optional(),
    settings: Joi.object({
      onlineTransaction: Joi.boolean().required(),
      atmWithdrawals: Joi.boolean().required(),
      posTransaction: Joi.boolean().required(),
      contactlessTransaction: Joi.boolean().optional(),
      expenseCategory: Joi.string().optional(),
      allowedMerchants: Joi.array()
        .items(Joi.string().pattern(/mcc_.{17}$/))
        .optional(),
      spendingLimitPolicy: Joi.string()
        .pattern(/pol_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid policy type code",
        }),
    }).required(),
    paymentSource: Joi.alternatives()
      .try(
        Joi.string()
          .pattern(/blc_.{17}$/)
          .optional(),
        Joi.string()
          .pattern(/bdg_.{17}$/)
          .optional()
      )
      .required()
      .messages({
        "any.required": "Please specify a payment source",
      }),
  })
    .xor("budget", "balance")
    .messages({
      "object.xor": "You must provide either budget or balance",
    }),
};
