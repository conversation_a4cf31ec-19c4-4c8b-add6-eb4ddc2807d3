const Joi = require("joi");
const safeRegex = require("safe-regex");

const { RULES_EVALUATOR, CATEGORY_CONDITION_OPERATORS } = require("../mocks/constants.mock");

const regexValidation = Joi.string().custom((value, helpers) => {
  try {
    const regex = new RegExp(value);
    if (!safeRegex(regex)) {
      return helpers.error("any.invalid");
    }
    return value;
  } catch (err) {
    return helpers.error("any.invalid");
  }
}, "Regular Expression Validation");

module.exports = {
  create: Joi.object({
    name: Joi.string().optional().trim(),
    description: Joi.string().optional().trim(),
    operator: Joi.string().valid(RULES_EVALUATOR.AND, RULES_EVALUATOR.OR).default(RULES_EVALUATOR.OR),
    category: Joi.string()
      .pattern(/ctg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid category code",
      })
      .required(),
    conditions: Joi.array()
      .items(
        Joi.object({
          operator: Joi.string()
            .valid(
              CATEGORY_CONDITION_OPERATORS.CONTAINS,
              CATEGORY_CONDITION_OPERATORS.ENDS_WITH,
              CATEGORY_CONDITION_OPERATORS.STARTS_WITH,
              CATEGORY_CONDITION_OPERATORS.MATCHES
            )
            .required(),
          operand: Joi.string().when("operator", {
            is: CATEGORY_CONDITION_OPERATORS.MATCHES,
            then: regexValidation.messages({
              "any.invalid": "Invalid regular expression",
            }),
          }),
        })
      )
      .required(),
  }),
  update: Joi.object({
    rule: Joi.string()
      .pattern(/ctr_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid categorization rule code",
      })
      .required(),
    name: Joi.string().optional().trim(),
    description: Joi.string().optional().trim(),
    operator: Joi.string().valid(RULES_EVALUATOR.AND, RULES_EVALUATOR.OR).default(RULES_EVALUATOR.OR),
    category: Joi.string()
      .pattern(/ctg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid category code",
      })
      .required(),
    status: Joi.string().valid("active", "inactive"),
    conditionsToAdd: Joi.array().items(
      Joi.object({
        operator: Joi.string()
          .valid(
            CATEGORY_CONDITION_OPERATORS.CONTAINS,
            CATEGORY_CONDITION_OPERATORS.ENDS_WITH,
            CATEGORY_CONDITION_OPERATORS.STARTS_WITH,
            CATEGORY_CONDITION_OPERATORS.MATCHES
          )
          .required(),
        operand: Joi.string().when("operator", {
          is: CATEGORY_CONDITION_OPERATORS.MATCHES,
          then: regexValidation.messages({
            "any.invalid": "Invalid regular expression",
          }),
        }),
      })
    ),
    conditionsToUpdate: Joi.array().items(
      Joi.object({
        operator: Joi.string()
          .valid(
            CATEGORY_CONDITION_OPERATORS.CONTAINS,
            CATEGORY_CONDITION_OPERATORS.ENDS_WITH,
            CATEGORY_CONDITION_OPERATORS.STARTS_WITH,
            CATEGORY_CONDITION_OPERATORS.MATCHES
          )
          .required(),
        operand: Joi.string().required(),
        condition: Joi.string()
          .pattern(/crc_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid condition code",
          })
          .required(),
      })
    ),
    conditionsToDelete: Joi.array().items(
      Joi.string()
        .pattern(/crc_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid condition code",
        })
    ),
  }),

  listCategorizationRules: Joi.object({
    from: Joi.date(),
    to: Joi.date(),
    page: Joi.number().default(1),
    perPage: Joi.number().max(100).default(50),
    status: Joi.string(),
    search: Joi.string(),
    category: Joi.string()
      .pattern(/ctg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid category code",
      }),
  }),

  getACategory: Joi.object({
    rule: Joi.string()
      .pattern(/ctr_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid categorization rule code",
      })
      .required(),
    category: Joi.string()
      .pattern(/ctg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid category code",
      })
      .required(),
  }),
};
