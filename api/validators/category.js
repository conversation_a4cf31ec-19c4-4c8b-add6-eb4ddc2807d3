const Joi = require("joi");

const create = Joi.object({
  name: Joi.string().required().trim(),
  slug: Joi.string().optional(),
  description: Joi.string().optional(),
  bufferAmount: Joi.number(),
  limit: Joi.number(),
});

const update = create.concat(
  Joi.object({
    name: Joi.string().trim().optional(),
    code: Joi.string()
      .pattern(/ctg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid category code",
      }),
    shouldDelete: Joi.boolean().optional(),
  })
);

module.exports = {
  create: Joi.object({
    name: Joi.string().required().trim(),
    slug: Joi.string().optional(),
    description: Joi.string().optional(),
    bufferAmount: Joi.number(),
    limit: Joi.number(),
    parent: Joi.string()
      .pattern(/ctg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid category code",
      }),
  }),
  bulkCreate: Joi.object({
    categories: Joi.array().items(
      Joi.object({
        name: Joi.string().required().trim(),
        slug: Joi.string().optional(),
        description: Joi.string().optional(),
        bufferAmount: Joi.number(),
        limit: Joi.number(),
        subCategories: Joi.array().items(create),
      })
    ),
  }).required(),
  bulkUpdate: Joi.object({
    categories: Joi.array()
      .items(
        Joi.object({
          code: Joi.string()
            .pattern(/ctg_.{17}$/)
            .messages({
              "string.pattern.base": "Invalid category code",
            }),
          name: Joi.string().trim(),
          slug: Joi.string().optional(),
          description: Joi.string().optional(),
          bufferAmount: Joi.number(),
          limit: Joi.number(),
          shouldDelete: Joi.boolean().optional(),
          subCategories: Joi.array().items(update),
        })
      )
      .required(),
  }),
  update: Joi.object({
    name: Joi.string().min(3).optional().trim(),
    slug: Joi.string().min(5).optional(),
    description: Joi.string().min(15).optional().allow(""),
    status: Joi.alternatives(Joi.string(), Joi.number().positive()).optional(),
    bufferAmount: Joi.number(),
    limit: Joi.number(),
    parent: Joi.string()
      .pattern(/ctg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid category code",
      }),
  }),

  massDelete: Joi.object({
    categories: Joi.array()
      .items(
        Joi.string()
          .pattern(/ctg_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid category code",
          })
      )
      .required(),
  }),
};
