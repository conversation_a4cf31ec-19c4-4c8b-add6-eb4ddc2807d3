const Joi = require("joi");

module.exports = {
  createCategoryMapping: Joi.object({
    categories: Joi.array().items(
      Joi.object({
        bujetiCategory: Joi.string()
          .regex(/^ctg_[A-Za-z0-9]+$/)
          .required()
          .messages({
            "string.base": "Bujeti category must be a string",
            "string.empty": "Bujeti category cannot be empty",
            "any.required": "Bujeti category is required",
            "string.pattern.base": "Bujeti category is not a valid category",
          }),
        quickBooksCategory: Joi.object({
          code: Joi.string().required().messages({
            "string.base": "QuickBooks category code must be a string",
            "string.empty": "QuickBooks category code cannot be empty",
            "any.required": "QuickBooks category code is required",
          }),
          name: Joi.string().required().trim().empty("").messages({
            "string.base": "QuickBooks category name must be a string",
            "string.empty": "QuickBooks category name cannot be empty",
            "any.required": "QuickBooks category name is required",
          }),
        })
          .required()
          .messages({
            "object.base": "QuickBooks category must be an object",
            "any.required": "QuickBooks category is required",
          }),
      })
    ),
  }),
};
