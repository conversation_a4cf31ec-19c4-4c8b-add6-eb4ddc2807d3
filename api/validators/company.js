const Joi = require('joi');

const asset = Joi.string().pattern(new RegExp("ast_.{17}$")).messages({
  "string.pattern.base": "Invalid asset code",
});

module.exports = {
    create: Joi.object({
        name: Joi.string().max(50).required(),
        size: Joi.string().valid('micro', 'small', 'medium', 'large'),
        description: Joi.string().max(250).optional(),
        website: Joi.string().uri().optional(),
        industry: Joi.string().pattern(new RegExp('idt_.{15}$')).optional(),
        address: Joi.object({
            street: Joi.string().optional(),
            countryIso: Joi.string().length(3).optional(),
            postalCode: Joi.string().pattern(/^[0-9]+$/).optional(),
            country: Joi.string().required(),
            state: Joi.string().required(),
            city: Joi.string().max(50).required(),
            address: Joi.string().max(100).required()
        }).optional(),
        email: Joi.string().email().optional(),
        phoneNumber: Joi.object({
            countryCode: Joi.number().greater(0).required(),
            localFormat: Joi.string().min(6).required()
        }).optional(),
        type: Joi.string().valid('enterprise', 'sole proprietorship', 'limited liability', 'ngo', 'partnership').required(),
    }).when(
        '.type', { is: 'sole proprietorship', then: Joi.object({
            document: Joi.object({
                bvn: Joi.string().length(11).pattern(/^[0-9]+$/).required(),
                idType: Joi.string().valid('national id card', 'international passport', 'voter id', 'driver license').required(),
                idNumber: Joi.string().length(10).pattern(/^[a-zA-Z0-9]+$/).required(),
                idCopy: Joi.string().uri().required(),
                cac: Joi.string().uri(),
                issuingDate: Joi.string()
            }).required()
            
        })}
    ).when(
        '.type', {is: 'enterprise', then: Joi.object({
            document: Joi.object({
                bvn: Joi.string().length(11).pattern(/^[0-9]+$/).required(),
                idType: Joi.string().valid('national id card', 'international passport', 'voter id', 'driver license').required(),
                idNumber: Joi.string().length(10).pattern(/^[a-zA-Z0-9]+$/).required(),
                idCopy: Joi.string().uri().required(),
                utilityBill: Joi.string().uri().required(),
                issuingDate: Joi.string()
            }).required()
        })}
    ).when(
        '.type', {is: 'limited liability', then: Joi.object({
            document: Joi.object({
                issuingDate: Joi.string(),
                rcNumber: Joi.string().max(10).pattern(new RegExp('RC\s*[0-9]{6,7}$')).required(),
                incorporationCertificate: Joi.string().uri().required(),
                shareholdersDocument: Joi.string().uri().required(),
                directorsName: Joi.string().required(),
                directorsEmail: Joi.string().required(),
                directorsPhone: Joi.object({
                    countryCode: Joi.number().greater(0).required(),
                    localFormat: Joi.string().min(6).required()
                }).required(),
                directorsDocuments: Joi.string().uri().required(),
            }).required()
        })}
    ).when(
        '.type', {is: 'ngo', then: Joi.object({
            document: Joi.object({
                issuingDate: Joi.string(),
                rcNumber: Joi.string().max(10).pattern(new RegExp('RC\s*[0-9]{6,7}$')).required(),
                cacITForm1: Joi.string().uri().required(),
                certificateOfTrustees: Joi.string().uri().required(),
                scumlCertificate: Joi.string().uri().required(),
            }).required()
        })}
    ).when(
        '.type', {is: 'partnership', then: Joi.object({
            document: Joi.object({
                bnNumber: Joi.string().max(20).required(),
                bnDocument: Joi.string().uri().required(),
                issuingDate: Joi.string()
            }).required()
        })}
    ),

    update: Joi.object({
        name: Joi.string().optional(),
        website: Joi.string().uri().optional(),
        logo: asset.optional(),
        contactEmail: Joi.string().email().optional(),
        size: Joi.string().allow('micro', 'small', 'medium', 'large'),
        industry: Joi.string().pattern(new RegExp('idt_.{15}$')).optional(),
        address: Joi.object({
            street: Joi.string(),
            city: Joi.string(),
            stateOrProvince: Joi.string(),
            country: Joi.string(),
        }).optional(),
    }),
}
