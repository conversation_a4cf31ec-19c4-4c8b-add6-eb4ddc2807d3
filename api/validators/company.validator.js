const Joi = require("joi");
const { REVIEW_DECISION, STATEMENT_FILE_TYPES } = require("../mocks/constants.mock");

const businessTypes = [
  "ngo",
  "sole proprietorship",
  "partnership",
  "enterprise",
  "private limited liability",
  "public limited liability",
  "incorporated trustees",
  "business name",
];

const statementFileType = [STATEMENT_FILE_TYPES.XLSX];

const idNumber = Joi.alternatives().conditional("idType", {
  switch: [
    {
      is: "nin",
      then: Joi.string()
        .length(11)
        .pattern(/^[0-9]+$/)
        .label("NIN"),
    },
    {
      is: "ip",
      then: Joi.string()
        .length(9)
        .pattern(/^[a-zA-Z0-9]+$/)
        .label("International Passport"),
    },
    {
      is: "dl",
      then: Joi.string()
        .length(12)
        .pattern(/^[a-zA-Z0-9]+$/)
        .label("Drivers License"),
    },
    {
      is: "vi",
      then: Joi.string()
        .length(17)
        .pattern(/^[a-zA-Z0-9]+$/)
        .label("Voters Card"),
    },
  ],
});

const idType = Joi.string().valid("nin", "ip", "vi", "dl");
const asset = Joi.string()
  .pattern(/ast_.{17}$/)
  .messages({
    "string.pattern.base": "Invalid asset code",
  });

module.exports = {
  createSubsidiary: Joi.object({
    name: Joi.string().required().messages({
      "any.required": "Please specify your company's name",
    }),
    parent: Joi.string()
      .pattern(/cmp_.{17}$/)
      .required(),
    lookupCode: Joi.string()
      .pattern(/clk_.{17}$/)
      .optional(),
  }),
  create: Joi.object({
    name: Joi.string().required().messages({
      "any.required": "Please specify your company's name",
    }),
    businessType: Joi.string()
      .valid(...businessTypes)
      .required()
      .messages({
        "any.only": `Business type should be one of the following ${businessTypes.join(", ")},`,
      }),
    lookupCode: Joi.string()
      .optional()
      .pattern(/clk_.{17}$/),
    rcNumber: Joi.string()
      .max(10)
      .pattern(/^RC\d+$/i)
      .optional()
      .messages({
        "string.pattern.base": "RC Number should be prefixed with RC",
      }),
    bnNumber: Joi.string()
      .pattern(/^BN[a-zA-Z0-9]+$/i)
      .max(20)
      .messages({
        "string.pattern.base": "BN Number should be prefixed with BN",
      })
      .optional(),
    address: Joi.object({
      countryIso: Joi.string().length(3).optional(),
      postalCode: Joi.string()
        .pattern(/^[0-9]+$/)
        .optional(),
      country: Joi.string()
        .pattern(/ctry_.{17}$/)
        .required(),
      state: Joi.string().required(),
      city: Joi.string().max(50).required(),
      street: Joi.string().max(100).required(),
    }).required(),
    accounts: Joi.boolean().optional(),
    cards: Joi.boolean().optional(),
    invoices: Joi.boolean().optional(),
    bills: Joi.boolean().optional(),
    budgets: Joi.boolean().optional(),
    parent: Joi.string()
      .pattern(/cmp_.{17}$/)
      .optional(),
  })
    .xor("rcNumber", "bnNumber")
    .messages({
      "object.xor": "Please specify either RC Number or BN Number",
      "object.missing": "Please specify either RC Number or BN Number",
    }),

  storeDocs: Joi.object({
    id: Joi.number().integer().min(1).required(),
    company_code: Joi.string().max(30).required(),
    type: Joi.string().valid("enterprise", "sole proprietorship", "limited liability", "ngo", "partnership").required(),
  })
    .when(".type", {
      is: "sole proprietorship",
      then: Joi.object({
        document: Joi.object({
          utilityBill: asset.optional(),
          bnNumber: Joi.string()
            .max(15)
            .pattern(/^BN[a-zA-Z0-9]+$/i)
            .messages({
              "string.pattern.base": "BN Number should be prefixed with BN",
            })
            .required(),
          cac: asset.required(),
          cacBn1: asset.required(),
          companyRegistrationDate: Joi.date().required(),
        }).required(),
      }),
    })
    .when(".type", {
      is: "enterprise",
      then: Joi.object({
        document: Joi.object({
          utilityBill: asset.optional(),
          companyRegistrationDate: Joi.date().required(),
        }).required(),
      }),
    })
    .when(".type", {
      is: "limited liability",
      then: Joi.object({
        document: Joi.object({
          rcNumber: Joi.string()
            .max(10)
            .pattern(/^RC\d+$/i)
            .optional()
            .messages({
              "string.pattern.base": "RC Number should be prefixed with RC",
            })
            .required(),
          incorporationCertificate: asset.required(),
          taxIdentificationNumber: Joi.string().required(),
          memorandumOfAssociation: asset.required(),
          companyRegistrationDate: Joi.date().required(),
          utilityBill: asset.optional(),
          cacForm3: asset.optional(),
          cacForm2: asset.optional(),
          cacForm7: asset.optional(),
          cacForm1: asset.optional(),
          cacStatusReport: asset.optional(),
        }).required(),
      }),
    })
    .when(".type", {
      is: "ngo",
      then: Joi.object({
        document: Joi.object({
          cacITNumber: Joi.string().required(),
          cacITForm1: asset.required(),
          certificateOfTrustees: asset.optional(),
          scumlCertificate: asset.optional(),
          companyRegistrationDate: Joi.date().required(),
          utilityBill: asset.optional(),
        }).required(),
      }),
    })
    .when(".type", {
      is: "partnership",
      then: Joi.object({
        document: Joi.object({
          bvn: Joi.string()
            .length(11)
            .pattern(/^[0-9]+$/)
            .required(),
          bnNumber: Joi.string()
            .max(20)
            .required()
            .messages({
              "string.pattern.base": "BN Number should be prefixed with BN",
            })
            .pattern(/^BN[a-zA-Z0-9]+$/i),
          utilityBill: asset.optional(),
          bnDocument: asset.required(),
          issuingDate: Joi.string(),
          idType: idType.required(),
          idNumber: idNumber.required(),
          idCopy: asset.required(),
          cacBn1: asset.required(),
          companyRegistrationDate: Joi.date().required(),
        }).required(),
      }),
    }),

  cacForms: Joi.object({
    cacForm3: asset.required(),
    cacForm2: asset.required(),
    cacForm7: asset.required(),
  }),

  cacStatus: Joi.object({
    cacStatusReport: asset.required(),
  }),

  cacForm1: Joi.object({
    cacForm1: asset.required(),
  }),
  update: Joi.object({
    name: Joi.string().optional(),
    businessType: Joi.string()
      .valid(...businessTypes)
      .optional()
      .messages({
        "any.only": `Business type should be one of the following ${businessTypes.join(", ")},`,
      }),
    rcNumber: Joi.string()
      .max(10)
      .pattern(/^RC\d+$/i)
      .optional()
      .messages({
        "string.pattern.base": "RC Number should be prefixed with RC",
      }),
    bnNumber: Joi.string()
      .pattern(/^BN[a-zA-Z0-9]+$/i)
      .max(20)
      .messages({
        "string.pattern.base": "BN Number should be prefixed with BN",
      })
      .optional(),
    dateOfRegistration: Joi.date().optional(),
    registeredAddress: Joi.object({
      countryIso: Joi.string().length(3).optional(),
      postalCode: Joi.string()
        .pattern(/^[0-9]+$/)
        .optional(),
      country: Joi.string()
        .pattern(/ctry_.{17}$/)
        .required(),
      state: Joi.string().required(),
      city: Joi.string().max(50).required(),
      street: Joi.string().max(100).required(),
    }).optional(),
    address: Joi.object({
      countryIso: Joi.string().length(3).optional(),
      postalCode: Joi.string()
        .pattern(/^[0-9]+$/)
        .optional(),
      country: Joi.string()
        .pattern(/ctry_.{17}$/)
        .required(),
      state: Joi.string().required(),
      city: Joi.string().max(50).required(),
      street: Joi.string().max(100).required(),
    }).optional(),
    phoneNumber: Joi.object({
      countryCode: Joi.number().greater(0).required(),
      localFormat: Joi.string().min(6).required(),
    }).optional(),
    email: Joi.string().email().optional(),
    website: Joi.string().uri().optional(),
    description: Joi.string().optional(),
    logo: asset.optional(),
    industry: Joi.string()
      .pattern(/idt_.{14,17}$/)
      .optional(),
    size: Joi.string().allow("micro", "small", "medium", "large"),
    settlementType: Joi.string().valid("manual", "auto").optional().messages({
      "any.only": "Settlement type can only be manual or auto",
    }),
    accounts: Joi.boolean().optional(),
    cards: Joi.boolean().optional(),
    invoices: Joi.boolean().optional(),
    bills: Joi.boolean().optional(),
    budgets: Joi.boolean().optional(),
    settlementAccount: Joi.string()
      .pattern(/bnk_.{17}$/)
      .optional(),
    paymentPlan: Joi.string().optional().valid("start", "growth", "scale"),
  }),

  getDetails: Joi.object({
    id: Joi.number().integer().min(1).required(),
    company_code: Joi.string().pattern(new RegExp("cmp_.{17}$")).optional(),
    addDocs: Joi.string().valid("true", "false").optional(),
    addAddress: Joi.string().valid("true", "false").optional(),
    docsCode: Joi.string().pattern(new RegExp("doc_.{17}$")).optional(),
  }),

  reviewDocs: Joi.object({
    id: Joi.number().integer().min(1).required(),
    company_code: Joi.string().pattern(new RegExp("cmp_.{17}$")).required(),
    docsCode: Joi.string().pattern(new RegExp("doc_.{17}$")).required(),
  }),

  rejectedDocs: Joi.object({
    id: Joi.number().integer().min(1).required(),
    company_code: Joi.string().max(30).required(),
    docsCode: Joi.string().pattern(new RegExp("doc_.{17}$")).required(),
    newDocument: Joi.object({
      type: Joi.string().valid("bvn", "nin", "ip", "vi", "dl").optional(),
      number: Joi.string()
        .max(15)
        .pattern(/^[a-zA-Z0-9]+$/)
        .optional(),
      url: Joi.string().uri().optional(),
    }),
    // .xor('number', 'url')
  }),

  generateAccountStatement: Joi.object({
    account: Joi.string().valid("expense", "collection").required().messages({
      "any.only": "Account should either be expense or collection",
    }),
    type: Joi.string().valid("debit", "credit", "all").required().messages({
      "any.only": "Type should either be debit, credit or all",
    }),
    startDate: Joi.date().optional(),
    endDate: Joi.date().optional(),
    page: Joi.number().optional(),
    perPage: Joi.number().optional(),
    budget: Joi.string().pattern(new RegExp("bdg_.{17}$")).optional().messages({
      "string.pattern.base": "Invalid budget code sent",
    }),
  }),

  generateAccountStatementNew: Joi.object({
    source: Joi.string()
      .pattern(/^(general|collection|balance|crd_[A-Za-z0-9]{17}|bdg_[A-Za-z0-9]{17}|blc_[A-Za-z0-9]{17})$/)
      .required()
      .messages({
        "string.pattern.base": "Invalid source sent",
      }),
    type: Joi.string().valid("debit", "credit", "all").required().messages({
      "any.only": "Type should either be debit, credit or all",
    }),
    startDate: Joi.date().optional(),
    endDate: Joi.date().optional(),
    page: Joi.number().optional(),
    perPage: Joi.number().optional(),
    budget: Joi.string().pattern(new RegExp("bdg_.{17}$")).optional().messages({
      "string.pattern.base": "Invalid budget code sent",
    }),
    fileType: Joi.string()
      .allow(...statementFileType)
      .optional(),
  }),

  generateAccountStatementInternal: Joi.object({
    source: Joi.string()
      .pattern(/^(general|collection|balance|bdg_[A-Za-z0-9]{17}|blc_[A-Za-z0-9]{17})$/)
      .required()
      .messages({
        "string.pattern.base": "Invalid source sent",
      }),
    type: Joi.string().valid("debit", "credit", "all").required().messages({
      "any.only": "Type should either be debit, credit or all",
    }),
    startDate: Joi.date().optional(),
    endDate: Joi.date().optional(),
    page: Joi.number().optional(),
    perPage: Joi.number().optional(),
    company: Joi.string()
      .pattern(/cmp_.{17}$/)
      .optional()
      .messages({
        "string.pattern.base": "Invalid company code sent",
      }),
    budget: Joi.string()
      .pattern(/bdg_.{17}$/)
      .optional()
      .messages({
        "string.pattern.base": "Invalid budget code sent",
      }),
  }),

  generateGeneralStatement: Joi.object({
    startDate: Joi.date().optional(),
    endDate: Joi.date().optional(),
    page: Joi.number().optional(),
    perPage: Joi.number().optional(),
  }),

  updateDirector: Joi.object({
    firstName: Joi.string().optional(),
    lastName: Joi.string().optional(),
    email: Joi.string().email().optional(),
    dob: Joi.date().optional(),
    bvn: Joi.string()
      .length(11)
      .pattern(/^[0-9]+$/)
      .optional(),
    phoneNumber: Joi.object({
      countryCode: Joi.number().greater(0).required(),
      localFormat: Joi.string().min(6).required(),
    }).optional(),
    percentageOwned: Joi.number().optional().min(0.1).max(100),
    address: Joi.object({
      postalCode: Joi.string()
        .pattern(/^[0-9]+$/)
        .optional(),
      country: Joi.string()
        .pattern(/ctry_.{17}$/)
        .required(),
      state: Joi.string().required(),
      city: Joi.string().max(50).required(),
      street: Joi.string().max(100).required(),
    }).optional(),
    documents: Joi.object({
      idType: idType.optional(),
      idNumber: idNumber.optional(),
      idCopy: asset.optional(),
      utilityBill: asset.optional(),
    })
      .custom((value, helpers) => {
        const hasDocumentData = !!value.idNumber || !!value.idCopy;
        if (hasDocumentData) {
          if (!value.idType) return helpers.message("Id Type is required");
        }

        if (value.idType) {
          if (!hasDocumentData) return helpers.message("Please specify idType or idNumber");
        }
        return value;
      }, "Conditional ID document validation")
      .optional(),
  }),

  addDirector: Joi.object({
    firstName: Joi.string().required(),
    lastName: Joi.string().required(),
    email: Joi.string().email().optional(),
    dob: Joi.date().optional(),
    bvn: Joi.string()
      .length(11)
      .pattern(/^[0-9]+$/)
      .required(),
    phoneNumber: Joi.object({
      countryCode: Joi.number().greater(0).required(),
      localFormat: Joi.string().min(6).required(),
    }).optional(),
    percentageOwned: Joi.number().optional().min(0.1).max(100),
    address: Joi.object({
      postalCode: Joi.string()
        .pattern(/^[0-9]+$/)
        .optional(),
      country: Joi.string()
        .pattern(/ctry_.{17}$/)
        .required(),
      state: Joi.string().required(),
      city: Joi.string().max(50).required(),
      street: Joi.string().max(100).required(),
    }).optional(),
    documents: Joi.object({
      idType: idType.optional(),
      idNumber: idNumber.optional(),
      idCopy: asset.optional(),
      utilityBill: asset.optional(),
    })
      .custom((value, helpers) => {
        const hasDocumentData = !!value.idNumber || !!value.idCopy;
        if (hasDocumentData) {
          if (!value.idType) return helpers.message("Id Type is required");
        }

        if (value.idType) {
          if (!hasDocumentData) return helpers.message("Please specify idType or idNumber");
        }
        return value;
      }, "Conditional ID document validation")
      .optional(),
  }),
  inviteDirector: Joi.object({
    directors: Joi.array()
      .items(
        Joi.object({
          fullname: Joi.string().required().messages({
            "any.required": '"fullname" is required',
          }),
          email: Joi.string().email().required().messages({
            "any.required": '"email" is required',
            "string.email": '"email" must be a valid email',
          }),
        })
      )
      .required(),
  }),
  invitedDirectorSubmission: Joi.object({
    firstName: Joi.string().optional(),
    lastName: Joi.string().optional(),
    dob: Joi.date().optional(),
    bvn: Joi.string()
      .length(11)
      .pattern(/^[0-9]+$/)
      .optional(),
    phoneNumber: Joi.object({
      countryCode: Joi.number().greater(0).required(),
      localFormat: Joi.string().min(6).required(),
    }).optional(),
    percentageOwned: Joi.number().optional().min(0.1).max(100),
    address: Joi.object({
      postalCode: Joi.string()
        .pattern(/^[0-9]+$/)
        .optional(),
      country: Joi.string()
        .pattern(/ctry_.{17}$/)
        .required(),
      state: Joi.string().required(),
      city: Joi.string().max(50).required(),
      street: Joi.string().max(100).required(),
    }).optional(),
    documents: Joi.object({
      idType: idType.optional(),
      idNumber: idNumber.optional(),
      idCopy: asset.optional(),
      utilityBill: asset.optional(),
    })
      .custom((value, helpers) => {
        const hasDocumentData = !!value.idNumber || !!value.idCopy;
        if (hasDocumentData) {
          if (!value.idType) return helpers.message("Id Type is required");
        }

        if (value.idType) {
          if (!hasDocumentData) return helpers.message("Please specify idType or idNumber");
        }
        return value;
      }, "Conditional ID document validation")
      .optional(),
  }),

  uploadCompanyDocuments: Joi.object({
    incorp_C: asset.optional(),
    cac: asset.optional(),
    cacStatusReport: asset.optional(),
    moa: asset.optional(),
    utilityBill: asset.optional(),
    cacBn1: asset.optional(),
    cacIT1: asset.optional(),
    constitution: asset.optional(),
    scumlCertificate: asset.optional(),
    cacForm3: asset.optional(),
    cacForm2: asset.optional(),
    cacForm7: asset.optional(),
    cacForm1: asset.optional(),
    tin: Joi.string().optional(),
    cacITForm1: asset.optional(),
    cacITNumber: Joi.string().optional(),
    bnNumber: Joi.string()
      .pattern(/^BN[a-zA-Z0-9]+$/i)
      .max(20)
      .messages({
        "string.pattern.base": "BN Number should be prefixed with BN",
      })
      .optional(),
    rcNumber: Joi.string()
      .max(10)
      .pattern(/^RC\d+$/i)
      .optional()
      .messages({
        "string.pattern.base": "RC Number should be prefixed with RC",
      }),
  }),

  reviewCompany: Joi.object({
    company: Joi.string()
      .pattern(/cmp_.{17}$/)
      .required(),
    decision: Joi.string().valid(REVIEW_DECISION.APPROVED, REVIEW_DECISION.REJECTED, REVIEW_DECISION.ACCEPTED).required(),
    reason: Joi.string().when("decision", { is: REVIEW_DECISION.REJECTED, then: Joi.required(), otherwise: Joi.forbidden() }),
  }),

  generateBudgetStatement: Joi.object({
    startDate: Joi.date().optional(),
    endDate: Joi.date().optional(),
    page: Joi.number().optional(),
    perPage: Joi.number().optional(),
    budget: Joi.string()
      .pattern(/bdg_.{17}$/)
      .optional()
      .messages({
        "string.pattern.base": "Invalid budget code sent",
      }),
  }),

  inviteCode: Joi.object({
    inviteCode: Joi.string().max(10).required().messages({
      "any.required": "Invite code is required",
      "string.empty": "Invite code is required",
      "string.max": "Invite code cannot be longer than 7 characters",
    }),
  }),

  companyCode: Joi.object({
    code: Joi.string()
      .pattern(/cmp_.{17}$/)
      .required()
      .messages({
        "string.empty": "Company code is required",
        "string.pattern.base": "Invalid company code format",
      }),
  }),

  resendDirectorInvite: Joi.object({
    company: Joi.string()
      .pattern(/cmp_.{17}$/)
      .required()
      .messages({
        "string.empty": "Company code is required",
        "string.pattern.base": "Invalid company code format",
      }),
    director: Joi.string()
      .pattern(/idv_.{17}$/)
      .required()
      .messages({
        "string.empty": "Director code is required",
        "string.pattern.base": "Invalid director code",
      }),
  }),

  getSingleDirector: Joi.object({
    company: Joi.string()
      .pattern(/cmp_.{17}$/)
      .required()
      .messages({
        "string.empty": "Company code is required",
        "string.pattern.base": "Invalid company code format",
      }),
    director: Joi.string()
      .pattern(/idv_.{17}$/)
      .required()
      .messages({
        "string.empty": "Director code is required",
        "string.pattern.base": "Invalid director code",
      }),
  }),
};
