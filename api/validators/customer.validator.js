const Joi = require("joi");

const create = Joi.object({
  name: Joi.string().optional(),
  firstName: Joi.string().optional(),
  lastName: Joi.string().optional(),
  email: Joi.string().email().optional(),
  category: Joi.string().optional(),
  taxIdentificationNumber: Joi.string().optional(),
  type: Joi.string().optional().valid("individual", "business"),
  phoneNumber: Joi.object({
    countryCode: Joi.number().greater(0).required(),
    localFormat: Joi.string().min(6).required(),
  }).optional(),
  address: Joi.object({
    postalCode: Joi.string()
      .pattern(/^[0-9]+$/)
      .optional(),
    street: Joi.string().required(),
    city: Joi.string().required(),
    state: Joi.string().required(),
    country: Joi.string().required(),
    countryIso: Joi.string(),
  }).optional(),
})
  .with("firstName", "lastName")
  .xor("firstName", "name")
  .messages({
    "object.missing": "name is required",
    "object.xor": "name is required",
  })
  .xor("lastName", "name")
  .messages({
    "object.missing": "name is required",
    "object.xor": "name is required",
  });

const update = Joi.object({
  name: Joi.string().optional(),
  firstName: Joi.string().optional(),
  lastName: Joi.string().optional(),
  email: Joi.string().email().optional(),
  category: Joi.string().optional(),
  taxIdentificationNumber: Joi.string().optional(),
  type: Joi.string().optional().valid("individual", "business"),
  phoneNumber: Joi.object({
    countryCode: Joi.number().greater(0).required(),
    localFormat: Joi.string().min(6).required(),
  }).optional(),

  address: Joi.object({
    postalCode: Joi.string()
      .pattern(/^[0-9]+$/)
      .optional(),
    street: Joi.string().required(),
    city: Joi.string().required(),
    state: Joi.string().required(),
    country: Joi.string().required(),
    countryIso: Joi.string(),
  }).optional(),
})
  // if either firstName or lastName is present, the other must be too
  .with("firstName", "lastName")
  .with("lastName", "firstName")
  // prevent name from coexisting with firstName or lastName
  .oxor("name", "firstName")
  .oxor("name", "lastName")
  .messages({
    "object.oxor": "You cannot use 'name' with 'firstName' or 'lastName'",
    "object.with": "'firstName' and 'lastName' must appear together",
  });

module.exports = {
  create,
  update,
  delete: Joi.object({
    code: Joi.string()
      .pattern(/cus_.{17}$/)
      .required(),
  }),

  search: Joi.object({
    query: Joi.string().optional(),
    company: Joi.number().required(),
  }),

  massCreate: Joi.object({ customers: Joi.array().items(create) }),
};
