const Joi = require('joi');

module.exports = {
    create: Joi.object({
        firstName: Joi.string().required(),
        lastName: Joi.string().required(),
        company: Joi.string().required(),
        email: Joi.string().email().required(),
        website: Joi.string().required(),
        position: Joi.string().optional(),
        companySize: Joi.string().valid("1-10", "11-50", "51-200", "200+").required(),
        reason: Joi.string().valid("demo", "pricing", "others").optional(),
        phoneNumber: Joi.object({
          countryCode: Joi.number().greater(0).required(),
          localFormat: Joi.string().min(6).required(),
        }).optional(),
        message: Joi.string().optional(),
    })
};
