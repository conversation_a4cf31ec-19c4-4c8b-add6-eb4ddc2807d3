const Joi = require("joi");

const asset = Joi.string()
  .pattern(/ast_.{17}$/)
  .messages({
    "string.pattern.base": "Invalid asset code",
  });

const fundRequest = Joi.string()
  .pattern(/frq_.{17}$/)
  .messages({ "string.pattern.base": "Invalid fund request code" });

const schedule = Joi.object({
  schedule: Joi.when("recurring", {
    is: true,
    then: Joi.string().valid("hourly", "weekly", "monthly", "quarterly", "yearly"),
    otherwise: Joi.object({
      minutes: Joi.number().integer().max(59).optional(),
      hours: Joi.number().integer().max(23).optional(),
      dayOfMonth: Joi.number().integer().max(31).optional(),
      month: Joi.string().optional(),
      dayOfWeek: Joi.string().optional(),
    }),
  }),
  startDate: Joi.object({
    date: Joi.string().required(),
    timestamp: Joi.string().required(),
  }),
  expiryDate: Joi.object({
    date: Joi.string().required(),
    timestamp: Joi.string().required(),
  }),
  recurring: Joi.boolean().required().default(true),
});

module.exports = {
  create: Joi.object({
    deadLine: Joi.date().optional().allow(null),
    type: Joi.string().valid("budget", "payment", "top_up").required(),
    amount: Joi.number().integer().positive().required(),
    currency: Joi.string().required(),
    vendor: Joi.string()
      .pattern(/vdr_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid vendor code sent",
      }),
    budget: Joi.string()
      .pattern(/bdg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid budget code sent",
      }),
    balance: Joi.string()
      .pattern(/blc_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid balance code sent",
      }),
    source: Joi.alternatives().try(
      Joi.string()
        .pattern(/bdg_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid budget code sent",
        }),
      Joi.string()
        .pattern(/blc_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid balance code sent",
        })
    ),
    bankAccount: Joi.alternatives().try(
      Joi.string()
        .pattern(/bnk_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid bank account code sent",
        }),
      Joi.string()
        .pattern(/blc_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid balance code sent",
        })
    ),
    card: Joi.string()
      .pattern(/crd_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid card code sent",
      }),
    description: Joi.string().required(),
    receipt: Joi.array().items(asset).unique().message("You have a receipt appearing more than once"),
    meta: Joi.object({
      budget: Joi.string().required(),
    }).when("type", {
      is: "budget",
      then: Joi.required(),
    }),
    category: Joi.string()
      .pattern(/ctg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid category code",
      }),
  }).xor("vendor", "budget", "bankAccount", "meta", "card"),
  update: Joi.object({
    deadLine: Joi.date().optional().allow(null),
    status: Joi.string(),
    vendor: Joi.string()
      .pattern(/vdr_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid vendor code sent",
      }),
    budget: Joi.string()
      .pattern(/bdg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid budget code sent",
      }),
    balance: Joi.string()
      .pattern(/blc_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid balance code sent",
      }),
    source: Joi.alternatives().try(
      Joi.string()
        .pattern(/bdg_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid budget code sent",
        }),
      Joi.string()
        .pattern(/blc_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid balance code sent",
        })
    ),
    bankAccount: Joi.alternatives().try(
      Joi.string()
        .pattern(/bnk_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid bank account code sent",
        }),
      Joi.string()
        .pattern(/blc_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid balance code sent",
        })
    ),
    receipt: Joi.array().items(asset).unique().message("You have a receipt appearing more than once"),
    description: Joi.string(),
    amount: Joi.number().integer().positive(),
    meta: Joi.object({ budget: Joi.string() }),
    category: Joi.string()
      .pattern(/ctg_.{17}$/)

      .messages({
        "string.pattern.base": "Invalid category code",
      }),
    card: Joi.string()
      .pattern(/crd_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid card code sent",
      }),
  }).oxor("vendor", "budget", "bankAccount", "meta", "card"),

  moreInfo: Joi.object({
    note: Joi.string().required(),
  }),

  listFundRequests: Joi.object({
    card: Joi.array()
      .items(
        Joi.string()
          .pattern(/crd_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid card code sent",
          })
      )
      .optional(),
    status: Joi.alternatives().try(Joi.string(), Joi.array()).optional(),
    from: Joi.date().optional(),
    to: Joi.date().optional(),
    deadLine: Joi.date().optional().allow(null),
    search: Joi.string().optional(),
    category: Joi.string()
      .optional()
      .pattern(/ctg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid category code sent",
      }),
    user: Joi.string()
      .optional()
      .pattern(/usr_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid user code sent",
      }),
    perPage: Joi.number().integer().optional(),
    page: Joi.number().integer().optional(),
    currency: Joi.alternatives().try(Joi.string().valid("NGN", "USD"), Joi.array().items(Joi.string().valid("NGN", "USD"))),
    budget: Joi.array()
      .items(
        Joi.string()
          .pattern(/bdg_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid budget code sent",
          })
      )
      .optional(),
    vendor: Joi.string()
      .pattern(/vdr_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid vendor code sent",
      }),
    bankAccount: Joi.string()
      .pattern(/bnk_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid bank account code sent",
      }),
    view_as: Joi.string().optional(),
    min_amount: Joi.number().optional(),
    max_amount: Joi.number().optional(),
  }),

  multipleFundRequestAction: Joi.object({
    requests: Joi.array().items(fundRequest.required()).unique(),
    decision: Joi.string().valid("approve", "decline"),
    reason: Joi.string()
      .when("status", {
        is: "declined",
        then: Joi.required(),
        otherwise: Joi.optional(),
      })
      .allow(null, "")
      .optional(),
    actionLater: Joi.boolean(),
  }),

  massDelete: Joi.object({
    requests: Joi.array().items(fundRequest.required()).unique().required(),
  }),

  validateFundRequest: Joi.object({
    decision: Joi.string().valid("approve", "decline"),
    reason: Joi.string()
      .when("status", {
        is: "declined",
        then: Joi.required(),
        otherwise: Joi.optional(),
      })
      .allow(null, "")
      .optional(),
    actionLater: Joi.boolean(),
    schedule,
    code: fundRequest,
  }).oxor("actionLater", "schedule"),
};
