const Joi = require("joi");

module.exports = {
  phone: Joi.object({
    countryCode: Joi.alternatives()
      .try(Joi.string().max(3), Joi.number().greater(0))
      .messages({
        "alternatives.match": "Invalid country code entered",
      })
      .required(),
    localFormat: Joi.string().required(),
  }),

  address: Joi.object({
    state: Joi.string().required(),
    city: Joi.string().required(),
    street: Joi.string().max(100).required(),
    country: Joi.string().max(30).required(),
    postalCode: Joi.string().optional(),
  }),

  budgetPolicy: Joi.object({
    policy: Joi.number().integer().min(1).required(),
    budget: Joi.number().integer().min(1).required(),
  }),

  policyRestriction: Joi.object({
    policy: Joi.number().integer().min(1).required(),
    policy_type: Joi.number().integer().min(1).required(),
  }),

  accountHolder: Joi.object({
    user: Joi.number().allow(null).optional(),
    company: Joi.number().required(),
    type: Joi.string().optional(),
    main: Joi.number().optional(),
    holderId: Joi.string().required(),
  }),

  approverLevel: Joi.object({
    rank: Joi.number().optional(),
    threshold: Joi.number().required(),
    rule: Joi.number().required(),
    status: Joi.number().optional(),
  }),
};
