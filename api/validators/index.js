/* eslint-disable global-require */
module.exports = {
  MockDataValidator: require("./mockData.validator"),
  BusinessOnboardingValidator: require("./businessOnboarding.validator"),
  CompanyValidator: require("./company.validator"),
  SmileIdValidator: require("./smileIdIntegration.validator"),
  BudgetValidator: require("./budget.validator"),
  BeneficiaryValidator: require("./beneficiary.validator"),
  PaymentValidator: require("./payment.validator"),
  UserBudgetValidator: require("./userBudget.validator"),
  HelperValidator: require("./helper.validator"),
  NotificationValidator: require("./notification.validator"),
  BalanceValidator: require("./balance.validator"),
  PolicyValidator: require("./policy.validator"),
  RoleValidator: require("./role.validator"),
  TeamValidator: require("./team.validator"),
  OnboardingInviteValidator: require("./onboardingInvite.validator"),
  ApprovalValidator: require("./approval.validator"),
  TransactionValidator: require("./transaction.validator"),
  InvoiceValidator: require("./invoice.validator"),
  CustomerValidator: require("./customer.validator"),
  BankValidator: require("./bank"),
  SettlementAccountValidator: require("./settlementAccount.validator"),
  VirtualCardValidator: require("./virtual-card.validator"),
  SettlementValidator: require("./settlement.validator"),
  CategoryMapping: require("./categoryMapping.validator"),
  ScheduledTransactionValidator: require("./scheduledTransactions.validator"),
  ScheduledInvoiceValidator: require("./scheduledInvoice.validator"),
  BillValidator: require("./bill.validator"),
  ScheduledBillValidator: require("./scheduledBill.validator"),
  TaxValidator: require("./tax.validator"),
};
