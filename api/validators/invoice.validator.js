const Joi = require("joi");

const ALLOWED_DISCOUNT_TYPES = ["amount", "percentage"];

const installments = Joi.object({
  type: Joi.string().required().valid("percentage", "amount"),
  payments: Joi.array()
    .items(
      Joi.object({
        amount: Joi.number().min(1000).optional(),
        percentage: Joi.number().min(1).max(100),
        due_date: Joi.date().required(),
      })
    )
    .required(),
}).optional();

const scheduleInvoice = Joi.object({
  schedule: Joi.when("recurring", {
    is: true,
    then: Joi.string().valid("hourly", "weekly", "monthly", "quarterly", "yearly"),
    otherwise: Joi.object({
      minutes: Joi.number().integer().max(59).optional(),
      hours: Joi.number().integer().max(23).optional(),
      dayOfMonth: Joi.number().integer().max(31).optional(),
      month: Joi.string().optional(),
      dayOfWeek: Joi.string().optional(),
    }),
  }),
  startDate: Joi.object({
    date: Joi.string().required(),
    timestamp: Joi.string().required(),
  }),
  expiryDate: Joi.object({
    date: Joi.string().required(),
    timestamp: Joi.string().required(),
  }),
  recurring: Joi.boolean().required().default(true),
}).optional();

module.exports = {
  createInvoice: Joi.object({
    customer: Joi.alternatives()
      .try(
        Joi.object({
          firstName: Joi.string().optional(),
          lastName: Joi.string().optional(),
          name: Joi.string().optional(),
          email: Joi.string().optional(),
          taxIdentificationNumber: Joi.string().optional(),
          category: Joi.string().optional(),
          type: Joi.string().optional().valid("business", "individual"),
          phoneNumber: Joi.object({
            countryCode: Joi.number().greater(0).required(),
            localFormat: Joi.string().min(6).required(),
          })
            .optional()
            .messages({
              "object.base": "Invalid phone number pattern",
            }),
          address: Joi.object({
            postalCode: Joi.string()
              .pattern(/^[0-9]+$/)
              .optional(),
            street: Joi.string(),
            city: Joi.string(),
            state: Joi.string().optional(),
            country: Joi.string(),
            countryIso: Joi.string(),
            address: Joi.string(),
          }).optional(),
        })
          .with("firstName", "lastName")
          .xor("firstName", "name")
          .xor("lastName", "name"),
        Joi.string()
          .pattern(/cus_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid customer code",
          })
      )
      .required(),
    title: Joi.string().optional().max(100),
    terms: Joi.number().optional().allow(null),
    due_date: Joi.date().optional(),
    products: Joi.array()
      .items(
        Joi.object({
          name: Joi.string().required(),
          quantity: Joi.number().min(1).required().messages({
            "number.min": "Product quantity must be greater than 0",
          }),
          unitPrice: Joi.number().required().min(10000).messages({
            "number.min": "Product Price must be greater than N100",
          }), // Minimum of 100
          currency: Joi.string().optional(),
          discount_type: Joi.string().valid(...ALLOWED_DISCOUNT_TYPES),
          discount: Joi.number().optional(),
          description: Joi.string().optional(),
        })
      )
      .required(),
    description: Joi.string().max(100).allow(null),
    vat: Joi.number().optional().allow(null),
    discount_type: Joi.string().valid(...ALLOWED_DISCOUNT_TYPES),
    discount: Joi.number().optional(),
    recurring: Joi.number().valid(0, 1).optional(),
    isDraft: Joi.boolean().default(false).optional(),
    scheduleInvoice,
    installments,
    settlementAccount: Joi.alternatives()
      .try(Joi.string().pattern(/blc_.{17}$/), Joi.string().pattern(/bdg_.{17}$/))
      .messages({
        "alternatives.any": "Please specify either a valid budget or balance code",
      }),
    shouldSplitTax: Joi.boolean().optional().default(false),
    attachments: Joi.array()
      .items(Joi.string().pattern(/ast_.{17}$/))
      .optional()
      .allow(null),
    template: Joi.string()
      .pattern(/ivtmp_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid template",
      })
      .optional(),
    invoiceId: Joi.string().optional().max(100),
  }),

  updateInvoice: Joi.object({
    code: Joi.string()
      .pattern(/inv_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid invoice code",
      }),
    isDraft: Joi.boolean().optional(),
    name: Joi.string().optional(),
    email: Joi.string().optional(),
    title: Joi.string().optional().max(100),
    currency: Joi.string().optional(),
    phoneNumber: Joi.object({
      countryCode: Joi.number().greater(0).required(),
      localFormat: Joi.string().min(6).required(),
    })
      .optional()
      .messages({
        "object.base": "Invalid phone number pattern",
      }),
    due_date: Joi.date().optional(),
    products: Joi.array()
      .optional()
      .items(
        Joi.alternatives().try(
          Joi.object({
            code: Joi.string()
              .required()
              .pattern(/ivp_.{17}$/)
              .messages({
                "string.pattern.base": "Invalid Invoice product code",
              }),
            name: Joi.string().optional(),
            quantity: Joi.number().min(1).optional(),
            unitPrice: Joi.number().optional(),
            currency: Joi.string().optional(),
            discount_type: Joi.string().valid(...ALLOWED_DISCOUNT_TYPES),
            discount: Joi.number().optional(),
          }),
          Joi.object({
            name: Joi.string().required(),
            quantity: Joi.number().min(1).required().messages({
              "number.min": "Product quantity must be greater than 0",
            }),
            unitPrice: Joi.number().required().min(10000).messages({
              "number.min": "Product Price must be greater than N100",
            }), // Minimum of 100
            currency: Joi.string().optional(),
            discount_type: Joi.string().valid(...ALLOWED_DISCOUNT_TYPES),
            discount: Joi.number().optional(),
          })
        )
      ),
    description: Joi.string().max(100).optional(),
    vat: Joi.number().optional(),
    discount_type: Joi.string().valid(...ALLOWED_DISCOUNT_TYPES),
    discount: Joi.number().optional(),
    recurring: Joi.number().valid(0, 1).optional(),
    status: Joi.string().valid("pending").optional(),
    customer: Joi.string()
      .pattern(/cus_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid customer code",
      }),
    terms: Joi.number().optional().allow(null),
    settlementAccount: Joi.string()
      .pattern(/blc_.{17}$/)
      .optional()
      .messages({
        "string.pattern.base": "Invalid account code",
      }),
    installments: Joi.object({
      type: Joi.string().required().valid("percentage", "amount"),
      payments: Joi.array()
        .items(
          Joi.alternatives().try(
            Joi.object({
              amount: Joi.number().min(1000).optional(),
              percentage: Joi.number().min(1).max(100),
              due_date: Joi.date().required(),
            }),
            Joi.object({
              code: Joi.string()
                .pattern(/iin_.{17}$/)
                .required(),
              amount: Joi.number().min(1000).optional(),
              percentage: Joi.number().min(1).max(100),
              due_date: Joi.date().required(),
            })
          )
        )
        .required(),
    }).optional(),
    attachments: Joi.array()
      .items(Joi.string().pattern(/ast_.{17}$/))
      .optional()
      .allow(null),
    template: Joi.string()
      .pattern(/ivtmp_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid template",
      })
      .optional(),
    invoiceId: Joi.string().optional().max(100),
  }),

  listInvoices: Joi.object({
    company: Joi.number().required(),
    status: Joi.alternatives(
      Joi.string().valid("inactive", "paid", "pending", "partial", "declined", "draft", "active", "verifying", "overdue"),
      Joi.array().items(Joi.string().valid("inactive", "paid", "pending", "partial", "declined", "draft", "active", "verifying", "overdue"))
    ).messages({
      "alternatives.types": "Invalid status sent",
    }),
    from: Joi.date().optional(),
    to: Joi.date().optional(),
    customer: Joi.array().optional(),
    search: Joi.string().optional(),
    perPage: Joi.number().optional(),
    page: Joi.number().optional(),
    minAmount: Joi.number().optional().positive(),
    maxAmount: Joi.number().positive().optional(),
    hasAttachment: Joi.boolean().optional(),
    paidOnFrom: Joi.date().optional(),
    paidOnTo: Joi.date().optional(),
  }),

  viewInvoice: Joi.object({
    code: Joi.string()
      .pattern(/inv_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid invoice code",
      }),
  }),

  shareInvoice: Joi.object({
    code: Joi.string().required(),
    method: Joi.string().valid("email", "sms").required(),
  }),

  validateCode: Joi.object({
    code: Joi.string()
      .required()
      .pattern(/inv_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid invoice code",
      }),
  }),

  markInvoiceAsPaid: Joi.object({
    code: Joi.string()
      .required()
      .pattern(/inv_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid invoice code",
      }),
    installments: Joi.array()
      .items(
        Joi.string()
          .required()
          .pattern(/iin_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid Installment code",
          })
      )
      .optional(),
  }),

  generatePaymentReference: Joi.object({
    code: Joi.string()
      .required()
      .pattern(/inv_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid invoice code",
      }),
    installments: Joi.array()
      .items(
        Joi.string()
          .required()
          .pattern(/iin_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid Installment code",
          })
      )
      .optional(),
  }),

  validateReference: Joi.object({
    reference: Joi.string().required().length(17).message({
      "string.length": "Reference must be a string of 17 characters",
    }),
    code: Joi.string()
      .required()
      .pattern(/inv_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid invoice code",
      }),
  }),
};
