const Joi = require("joi");
module.exports = {
  verify: Joi.object({
    user_id: Joi.number().integer(),
    body: Joi.object({
      title: Joi.string().required(),
      body: Joi.object().optional(),
      entity: Joi.string().optional(),
      code: Joi.string().optional(),
      message: Joi.string().required(),
    }).xor("entity", "code"),
    event: Joi.string()
      .valid(
        "fundWallet",
        "kycCompleted",
        "reimbursementRequest",
        "beneficiaryPayment",
        "cardRequest",
        "budgetInvite",
        "reimbursementUpdate",
        "paymentUpdate",
        "approvalRequest",
        "fundRequest",
        "fundRequestUpdate",
        "budgetActivity",
        "budgetOwner",
        "pendingActions",
        "billingSuccessful",
        "upcomingBilling",
        "lateSubscription",
        "dataSync",
        "directDebit",
        "policyViolated",
        "mandateApproved",
        "accountLinking",
        "cardCreated",
        "accountMember",
        "vendorBankAccountAdded",
        "categorizationRulesPrompt",
        "policyViolationPrompt"
      )
      .required(),
  }),

  saveNotification: Joi.object({
    user_id: Joi.number().integer().optional(),
    table: Joi.object({
      entity: Joi.string().optional().allow("", null),
      code: Joi.string().optional().allow("", null),
    }).optional(),
    title: Joi.string().optional(),
    body: Joi.object().optional().allow(null),
    message: Joi.string().required(),
    type: Joi.string().valid("alert", "request", "info", "prompt").optional(),
    badge: Joi.string().optional(),
    reference_code: Joi.string().optional().allow(null),
    event: Joi.string()
      .valid(
        "fundWallet",
        "kycCompleted",
        "reimbursementRequest",
        "beneficiaryPayment",
        "cardRequest",
        "budgetInvite",
        "reimbursementUpdate",
        "paymentUpdate",
        "approvalRequest",
        "scheduledTransaction",
        "policyViolation",
        "fundRequest",
        "fundRequestUpdate",
        "budgetActivity",
        "budgetOwner",
        "pendingActions",
        "billingSuccessful",
        "upcomingBilling",
        "lateSubscription",
        "dataSync",
        "directDebit",
        "policyViolated",
        "upcomingBill",
        "overdueBill",
        "pendingBill",
        "mandateApproved",
        "accountLinking",
        "cardCreated",
        "accountMember",
        "vendorBankAccountAdded",
        "categorizationRulesPrompt",
        "policyViolationPrompt"
      )
      .required(),
  }),

  userChecker: Joi.object({
    user_id: Joi.number().integer().required(),
  }),

  markANotification: Joi.object({
    code: Joi.string().required(),
    user_id: Joi.number().integer().required(),
  }),

  sendEmailNotification: Joi.object({
    template: Joi.string().required(),
    type: Joi.string().optional().valid("email", "push", "sms"),
    subject: Joi.string().optional().max(900),
    from_email: Joi.string().optional(),
    from_name: Joi.string().optional(),
    bcc_address: Joi.string().email().optional(),
    payload: Joi.object().optional().min(1),
    recipients: Joi.alternatives()
      .try(
        Joi.array().items(Joi.string().email()),
        Joi.array().items(
          Joi.object()
            .keys({
              email: Joi.string().email().required(),
            })
            .unknown(true)
        )
      )
      .required(),
  }),
};
