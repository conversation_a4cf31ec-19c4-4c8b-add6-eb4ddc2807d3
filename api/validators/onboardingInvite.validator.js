const Joi = require("joi");

module.exports = {
    create: Joi.object({
        name: Joi.string().regex(/^[a-zA-Z \-]+$/).required().messages({
            "string.pattern.base": "Only alphabets allowed for names",
            "any.required": 'Name is required'
        }),
        email: Joi.string().email().required(),
        note: Joi.string().optional(),
        fields: Joi.array().single(Joi.string()).optional()
    }),

    delete: Joi.object({
        code: Joi.string().pattern(new RegExp('oiv_.{17}$')).required().messages({
            'string.pattern.base': 'Invalid invite code sent',
            'any.required': 'Invite code is required'
        }),
    }),

    update: Joi.object({
        code: Joi.string().pattern(new RegExp('oiv_.{17}$')).required().messages({
            'string.pattern.base': 'Invalid invite code sent',
            'any.required': 'Invite code is required'
        }),
        name: Joi.string().regex(/^[a-zA-Z \-]+$/).optional().messages({
            "string.pattern.base": "Only alphabets allowed for names",
        }),
        email: Joi.string().email().optional(),
        fields: Joi.array().single(Joi.string()),
        note: Joi.string().optional()
    }),

    resend: Joi.object({
        invite: Joi.string().pattern(new RegExp('oiv_.{17}$')).required().messages({
            'string.pattern.base': 'Invalid invite code sent',
            'any.required': 'Invite code is required'
        })
    })
}