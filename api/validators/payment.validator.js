const Joi = require("joi");

const bankAccount = Joi.alternatives(
  Joi.string().pattern(/bnk_.{17}$/),
  Joi.object({
    bankName: Joi.string().required(),
    bankCode: Joi.string()
      .pattern(/^[0-9A-Za-z]{3,7}$/)
      .required()
      .messages({
        "string.pattern.base": "Invalid Bank code sent",
      }),
    accountName: Joi.string().required(),
    number: Joi.string()
      .pattern(/^[0-9]{10}$/)
      .required()
      .messages({
        "string.pattern.base": "Number must be 10 characters long and is required",
      }),
    currency: Joi.string().optional().default("NGN"),
  })
);

const transaction = Joi.object({
  amount: Joi.number().positive().required(),
  recipient: Joi.string().required(),
  email: Joi.string().email().optional().messages({
    "string.email": '"email" must be a valid email',
  }),
  description: Joi.string().required().messages({ "string.required": `description is required` }),
  category: Joi.string().allow("", null).optional(),
  bank_account: bankAccount.required(),
  budget: Joi.string()
    .pattern(/bdg_.{17}$/)
    .messages({
      "string.pattern.base": "Invalid budget code sent",
    }),
  balance: Joi.string()
    .pattern(/blc_.{17}$/)
    .messages({
      "string.pattern.base": "Invalid balance code sent",
    }),
});
const asset = Joi.string()
  .pattern(/ast_.{17}$/)
  .messages({
    "string.pattern.base": "Invalid asset code",
  });

const scheduleTransaction = Joi.object({
  schedule: Joi.when("recurring", {
    is: true,
    then: Joi.string().valid("hourly", "weekly", "monthly", "quarterly", "yearly"),
    otherwise: Joi.object({
      minutes: Joi.number().integer().max(59).optional(),
      hours: Joi.number().integer().max(23).optional(),
      dayOfMonth: Joi.number().integer().max(31).optional(),
      month: Joi.string().optional(),
      dayOfWeek: Joi.string().optional(),
    }),
  }),
  startDate: Joi.object({
    date: Joi.string().required(),
    timestamp: Joi.string().required(),
  }),
  expiryDate: Joi.object({
    date: Joi.string().required(),
    timestamp: Joi.string().required(),
  }),
  recurring: Joi.boolean().required().default(true),
});

module.exports = {
  create: Joi.object({
    amount: Joi.number().integer().required(),
    currency: Joi.string().optional().default("NGN"),
    pin: Joi.string().optional(),
    deadLine: Joi.date().optional().allow(null),
    impactBudget: Joi.string()
      .pattern(/bdg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid budget code sent",
      })
      .optional()
      .allow(null),
    budget: Joi.string()
      .pattern(/bdg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid budget code sent",
      }),
    balance: Joi.string()
      .pattern(/blc_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid balance code sent",
      }),
    recipient: Joi.string().required(),
    notifyRecipient: Joi.boolean().optional(),
    recipientEmail: Joi.when("notifyRecipient", { is: true, then: Joi.string().required() }),
    bank_account: Joi.alternatives(
      Joi.string()
        .pattern(/bnk_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid bank code sent",
        }),
      Joi.object({
        bankName: Joi.string().required(),
        bankCode: Joi.string()
          .pattern(/^[0-9A-Za-z]{3,7}$/)
          .required()
          .messages({
            "any.required": "Bank code is required",
            "string.pattern.base": "Invalid Bank code sent",
          }),
        accountName: Joi.string().required(),
        number: Joi.string()
          .pattern(/^[0-9]{10}$/)
          .required()
          .messages({
            "any.required": "Account number is required",
            "string.pattern.base": "Number must be 10 characters long and is required to contain only numbers",
          }),
        currency: Joi.string().optional().default("NGN"),
      })
    ).required(),
    receipt: Joi.array().items(asset).unique().message("You have a receipt appearing more than once").optional(),
    description: Joi.string().messages({
      "string.required": `description is required`,
    }),
    category: Joi.string()
      .pattern(/ctg_.{17}$/)
      .optional()
      .messages({
        "string.pattern.base": "Invalid category code",
      }),
    scheduleTransaction,
    directDebit: Joi.object({
      bankAccount: Joi.string()
        .pattern(/bnk_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid bank code sent",
        }),
    }),
    saveAsDraft: Joi.boolean().default(false),
    team: Joi.string()
      .pattern(/tms_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid team code sent",
      })
      .optional(),
  })
    .oxor("balance")
    .messages({
      "object.oxor": "You must provide a source of funds",
    }),
  log: Joi.object({
    amount: Joi.number().integer().required(),
    fees: Joi.number().integer().required(),
    currency: Joi.string().optional().default("NGN"),
    budget: Joi.string()
      .pattern(/bdg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid budget code sent",
      }),
    balance: Joi.string()
      .pattern(/blc_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid balance code sent",
      }),
    recipient: Joi.string().required(),
    bank_account: Joi.alternatives(
      Joi.string()
        .pattern(/bnk_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid bank code sent",
        }),
      Joi.object({
        bankName: Joi.string().required(),
        bankCode: Joi.string()
          .pattern(/^[0-9A-Za-z]{3,7}$/)
          .required()
          .messages({
            "any.required": "Bank code is required",
            "string.pattern.base": "Invalid Bank code sent",
          }),
        accountName: Joi.string().required(),
        number: Joi.string()
          .pattern(/^[0-9]{10}$/)
          .required()
          .messages({
            "any.required": "Account number is required",
            "string.pattern.base": "Number must be 10 characters long and is required to contain only numbers",
          }),
        currency: Joi.string().optional().default("NGN"),
      })
    ).required(),
    receipt: Joi.array().items(asset).unique().message("You have a receipt appearing more than once").optional(),
    description: Joi.string().required().messages({
      "string.required": `description is required`,
    }),
    category: Joi.string()
      .pattern(/ctg_.{17}$/)
      .optional()
      .messages({
        "string.pattern.base": "Invalid category code",
      }),
  })
    .or("balance", "budget")
    .messages({
      "object.oxor": "You must provide either budget, balance or both",
    }),

  createPayment: Joi.object({
    amount: Joi.number().positive().required(),
    currency: Joi.string().optional().default("NGN"),
    pin: Joi.string().optional(),
    deadLine: Joi.date().optional().allow(null),
    budget: Joi.string()
      .pattern(/bdg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid budget code sent",
      }),
    balance: Joi.string()
      .pattern(/blc_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid balance code sent",
      }),
    recipient: Joi.string().required(),
    email: Joi.string().optional(),
    bank_account: Joi.alternatives(
      Joi.string()
        .pattern(/bnk_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid bank code sent",
        }),
      Joi.object({
        bankName: Joi.string().required(),
        bankCode: Joi.string()
          .pattern(/^[0-9A-Za-z]{3,7}$/)
          .required()
          .messages({
            "any.required": "Bank code is required",
            "string.pattern.base": "Invalid Bank code sent",
          }),
        accountName: Joi.string().required(),
        number: Joi.string()
          .pattern(/^[0-9]{10}$/)
          .required()
          .messages({
            "any.required": "Account number is required",
            "string.pattern.base": "Number must be 10 characters long and is required to contain only numbers",
          }),
        currency: Joi.string().optional().default("NGN"),
      })
    ).required(),
    directDebit: Joi.object({
      bankAccount: Joi.string()
        .pattern(/bnk_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid bank code sent",
        }),
    }),
    receipt: Joi.string()
      .pattern(/ast_.{17}$/)
      .optional(),
    description: Joi.string().required().messages({
      "string.required": `description is required`,
    }),
    category: Joi.string().allow("", null).optional(),
    team: Joi.string()
      .pattern(/tms_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid team code sent",
      })
      .optional(),
  })
    .oxor("balance")
    .messages({
      "object.oxor": "You must provide a source of funds",
    }),

  bulkPay: Joi.object({
    name: Joi.string().required().messages({
      "string.pattern.base": "You have to name your batch payment",
    }),
    budget: Joi.string()
      .pattern(/bdg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid budget code sent",
      }),
    balance: Joi.string()
      .pattern(/blc_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid balance code sent",
      }),
    currency: Joi.string().optional().default("NGN"),
    pin: Joi.string().optional(),
    deadLine: Joi.date().optional().allow(null),
    transactions: Joi.array().items(transaction).unique().message("You have a transaction appearing more than once").required(),
    directDebit: Joi.object({
      bankAccount: Joi.string()
        .pattern(/bnk_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid bank code sent",
        }),
    }),
    receipt: Joi.array().items(asset).unique().message("You have a receipt appearing more than once").optional(),
  })
    .oxor("balance", "budget")
    .messages({
      "object.oxor": "You must provide a source of funds",
    }),

  paymentValidator: Joi.object({
    receipt: Joi.array().items(asset).unique().message("You have a receipt appearing more than once").required(),
  }),
};
