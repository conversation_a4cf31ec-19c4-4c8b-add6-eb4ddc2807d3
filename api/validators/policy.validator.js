const joi = require("joi");
const allowedCurrency = ["NGN", "USD"];
const allowedDays = ["Sun", "Mon", "<PERSON><PERSON>", "Wed", "Thu", "Fri", "Sat"];

const operator = joi.string().valid("eq", "gt", "lt", "not:eq");
const trigger = joi.string().valid("vendor", "type", "category", "budget", "amount", "account", "member", "subsidiary", "team").required();
const allowedTypes = joi
  .string()
  .valid("reimbursement", "payment", "all", "batchPayment", "fundRequest", "cardRequest", "subaccount", "account", "invoice")
  .required();
const vendor = joi.string().pattern(new RegExp("vdr_.{17}$")).messages({
  "string.pattern.base": "Invalid vendor code",
});
const currencies = joi.string().pattern(new RegExp("[A-Z]{3}$")).messages({
  "string.pattern.base": "Invalid currency ISO code",
});
const category = joi.string().pattern(new RegExp("ctg_.{17}$")).messages({
  "string.pattern.base": "Invalid category code",
});
const account = joi.string().pattern(new RegExp("blc_.{17}$")).messages({
  "string.pattern.base": "Invalid account code",
});
const teams = joi.string().pattern(new RegExp("tms_.{17}$")).messages({
  "string.pattern.base": "Invalid team code",
});
const budget = joi.string().pattern(new RegExp("bdg_.{17}$")).messages({
  "string.pattern.base": "Invalid budget code",
});
const user = joi.string().pattern(new RegExp("usr_.{17}$")).messages({
  "string.pattern.base": "Invalid user code",
});
const approvalCondition = joi.string().pattern(new RegExp("apc_.{17}$")).messages({
  "string.pattern.base": "Invalid approval condition code",
});

const approverLevel = joi.string().pattern(new RegExp("apl_.{17}$")).messages({
  "string.pattern.base": "Invalid approver Level code",
});

const approverCondition = joi.string().pattern(new RegExp("app_.{17}$")).messages({
  "string.pattern.base": "Invalid approver code",
});

const conditions = joi
  .object({ trigger: trigger.required() })
  .when(".trigger", {
    is: "vendor",
    then: joi.object({
      operator: joi.string().valid("is", "any", "not:is", "not:any").required(),
      operands: joi.array().items(vendor).unique().required(),
    }),
  })
  .when(".trigger", {
    is: "category",
    then: joi.object({
      operator: joi.string().valid("is", "any", "not:is", "not:any").required(),
      operands: joi.array().items(category).unique().required(),
    }),
  })
  .when(".trigger", {
    is: "budget",
    then: joi.object({
      operator: joi.string().valid("is", "any", "not:is", "not:any").required(),
      operands: joi.array().items(budget).unique().required(),
    }),
  })
  .when(".trigger", {
    is: "account",
    then: joi.object({
      operator: joi.string().valid("is", "any", "not:is", "not:any").required(),
      operands: joi.array().items(account).unique().required(),
    }),
  })
  .when(".trigger", {
    is: "team",
    then: joi.object({
      operator: joi.string().valid("is", "any", "not:is", "not:any").required(),
      operands: joi.array().items(teams).unique().required(),
    }),
  })
  .when(".trigger", {
    is: "amount",
    then: joi.object({
      operator: operator.required(),
      operands: joi.number().integer().min(100).required(),
    }),
  })
  .when(".trigger", {
    is: "type",
    then: joi.object({
      operator: joi.string().valid("is", "not:is").required(),
      operands: joi.array().items(allowedTypes).unique().required(),
    }),
  })
  .when(".trigger", {
    is: "user",
    then: joi.object({
      operator: joi.string().valid("is", "any", "not:is", "not:any").required(),
      operands: joi.array().items(allowedTypes).unique().required(),
    }),
  });

module.exports = {
  createPolicy: joi
    .object({
      frequency: joi.string().valid("monthly", "daily", "weekly", "yearly", "weekends").optional(),
      period: joi
        .array()
        .items(joi.string().valid(...allowedDays))
        .unique()
        .optional(),
      amount: joi.number().positive().optional().min(1000).messages({
        "number.min": "Minimum amount is 10",
      }),
      amountRange: joi
        .object({
          maxAmount: joi.number().positive().optional().min(1000).messages({
            "number.min": "Minimum amount is 10",
          }),
          minAmount: joi.number().positive().optional().min(1000).messages({
            "number.min": "Minimum amount is 10",
          }),
        })
        .optional()
        .or("maxAmount", "minAmount"),
      currency: joi
        .string()
        .valid(...allowedCurrency)
        .optional(),
      budget: joi
        .array()
        .items(
          joi
            .string()
            .pattern(/bdg_.{17}$/)
            .messages({
              "string.pattern.base": "Invalid budget code",
            })
        )
        .unique()
        .optional(),
      type: joi
        .alternatives(
          joi
            .array()
            .items(
              joi
                .string()
                .pattern(/pty_.{17}$/)
                .messages({
                  "string.pattern.base": "Invalid policy type code",
                })
            )
            .unique(),
          joi
            .string()
            .pattern(/pty_.{17}$/)
            .messages({
              "string.pattern.base": "Invalid policy type code",
            })
        )
        .optional(),
      name: joi.string().min(5).max(50).required().messages({
        "string.min": `Policy's name must be at least 5 characters long`,
        "string.max": `Policy's name can not be more than 50 characters long`,
      }),
      description: joi.string().min(15).max(500).required().messages({
        "string.min": `Policy's description must be at least 15 characters long`,
        "string.max": `Policy's description can not be more than 500 characters long`,
      }),
      strict: joi.boolean().optional(),
      requiresBudget: joi.boolean().optional(),
      requiresCategory: joi.boolean().optional(),
      requiresDescription: joi.boolean().optional(),
      requiresReceipt: joi.boolean().optional(),
      receiptAmount: joi.object({
        condition: joi.string().valid("all", "above", "below").required(),
        value: joi
          .number()
          .positive()
          .min(1000)
          .when("condition", {
            not: "all",
            then: joi.required(),
          })
          .messages({
            "number.min": "Minimum amount is 10",
          }),
      }),

      conditions: joi
        .array()
        .items(
          joi
            .object({ trigger: trigger.required() })
            .when(".trigger", {
              is: "currency",
              then: joi.object({
                operator: joi.string().valid("is", "any", "not:is", "not:any").required(),
                operands: joi.array().items(currencies).unique().required(),
              }),
            })
            .when(".trigger", {
              is: "vendor",
              then: joi.object({
                operator: joi.string().valid("is", "any", "not:is", "not:any").required(),
                operands: joi.array().items(vendor).unique().required(),
              }),
            })
            .when(".trigger", {
              is: "category",
              then: joi.object({
                operator: joi.string().valid("is", "any", "not:is", "not:any").required(),
                operands: joi.array().items(category).unique().required(),
              }),
            })
            .when(".trigger", {
              is: "budget",
              then: joi.object({
                operator: joi.string().valid("is", "any", "not:is", "not:any").required(),
                operands: joi.array().items(budget).unique().required(),
              }),
            })
            .when(".trigger", {
              is: "account",
              then: joi.object({
                operator: joi.string().valid("is", "any", "not:is", "not:any").required(),
                operands: joi.array().items(account).unique().required(),
              }),
            })
            .when(".trigger", {
              is: "amount",
              then: joi.object({
                operator: operator.required(),
                operands: joi.number().integer().min(100).required(),
              }),
            })
            .when(".trigger", {
              is: "subsidiary",
              then: joi.object({
                operator: joi.string().valid("is", "not:is", "any", "not:any").required(),
                operands: joi.array().items(account).unique().required(),
              }),
            })
            .when(".trigger", {
              is: "type",
              then: joi.object({
                operator: joi.string().valid("is", "not:is").required(),
                operands: joi.array().items(allowedTypes).unique().required(),
              }),
            })
        )
        .optional(),
      exceptions: joi
        .array()
        .items(
          joi
            .string()
            .pattern(/usr_.{17}$/)
            .messages({
              "string.pattern.base": "Invalid user code",
            })
        )
        .unique()
        .optional(),
      documents: joi
        .array()
        .items(
          joi
            .string()
            .pattern(/ast_.{17}$/)
            .messages({
              "string.pattern.base": "Invalid document code",
            })
        )
        .unique()
        .optional(),
    })
    .and("frequency", "amountRange"),

  updatePolicy: joi.object({
    frequency: joi.string().valid("monthly", "daily", "weekly", "yearly", "weekends").optional(),
    status: joi.string().valid("pause", "active", "inactive", "archived").optional(),
    period: joi
      .array()
      .items(joi.string().valid(...allowedDays))
      .unique()
      .optional(),
    amount: joi.number().positive().optional().min(1000).messages({
      "number.min": "Minimum amount is 10",
    }),
    amountRange: joi
      .object({
        maxAmount: joi.number().positive().optional().min(1000).messages({
          "number.min": "Minimum amount is 10",
        }),
        minAmount: joi.number().positive().optional().min(1000).messages({
          "number.min": "Minimum amount is 10",
        }),
      })
      .optional()
      .or("maxAmount", "minAmount"),
    currency: joi
      .string()
      .valid(...allowedCurrency)
      .optional(),
    budget: joi
      .array()
      .items(
        joi
          .string()
          .pattern(/bdg_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid budget code",
          })
      )
      .unique()
      .optional(),
    type: joi
      .alternatives(
        joi
          .array()
          .items(
            joi
              .string()
              .pattern(/pty_.{17}$/)
              .messages({
                "string.pattern.base": "Invalid policy type code",
              })
          )
          .unique(),
        joi
          .string()
          .pattern(/pty_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid policy type code",
          })
      )
      .optional(),
    name: joi.string().min(5).max(50).optional().messages({
      "string.min": `Policy's name must be 5 characters long`,
      "string.max": `Policy's name can not be more than 50 characters long`,
    }),
    description: joi.string().min(15).max(500).optional().messages({
      "string.min": `Policy's description must be 15 characters long`,
      "string.max": `Policy's description can not be more than 500 characters long`,
    }),
    strict: joi.boolean().optional(),
    receiptAmount: joi.object({
      condition: joi.string().valid("all", "above", "below").required(),
      value: joi
        .number()
        .positive()
        .min(1000)
        .when("condition", {
          not: "all",
          then: joi.required(),
        })
        .messages({
          "number.min": "Minimum amount is 10",
        }),
    }),
    requiresReceipt: joi.boolean().optional(),
    requiresBudget: joi.boolean().optional(),
    requiresCategory: joi.boolean().optional(),
    requiresDescription: joi.boolean().optional(),
    conditionsToAdd: joi
      .array()
      .items(
        joi
          .object({ trigger: trigger.required() })
          .when(".trigger", {
            is: "currency",
            then: joi.object({
              operator: joi.string().valid("is", "any", "not:is", "not:any").required(),
              operands: joi.array().items(currencies).unique().required(),
            }),
          })
          .when(".trigger", {
            is: "vendor",
            then: joi.object({
              operator: joi.string().valid("is", "any", "not:is", "not:any").required(),
              operands: joi.array().items(vendor).unique().required(),
            }),
          })
          .when(".trigger", {
            is: "category",
            then: joi.object({
              operator: joi.string().valid("is", "any", "not:is", "not:any").required(),
              operands: joi.array().items(category).unique().required(),
            }),
          })
          .when(".trigger", {
            is: "budget",
            then: joi.object({
              operator: joi.string().valid("is", "any", "not:is", "not:any").required(),
              operands: joi.array().items(budget).unique().required(),
            }),
          })
          .when(".trigger", {
            is: "account",
            then: joi.object({
              operator: joi.string().valid("is", "any", "not:is", "not:any").required(),
              operands: joi.array().items(account).unique().required(),
            }),
          })
          .when(".trigger", {
            is: "team",
            then: joi.object({
              operator: joi.string().valid("is", "any", "not:is", "not:any").required(),
              operands: joi.array().items(teams).unique().required(),
            }),
          })
          .when(".trigger", {
            is: "amount",
            then: joi.object({
              operator: operator.required(),
              operands: joi.number().integer().min(100).required(),
            }),
          })
          .when(".trigger", {
            is: "subsidiary",
            then: joi.object({
              operator: joi.string().valid("is", "not:is", "any", "not:any").required(),
              operands: joi.array().items(account).unique().required(),
            }),
          })
          .when(".trigger", {
            is: "type",
            then: joi.object({
              operator: joi.string().valid("is", "not:is").required(),
              operands: joi.array().items(allowedTypes).unique().required(),
            }),
          })
      )
      .optional(),
    conditionsToUpdate: joi
      .array()
      .items(
        joi
          .object({
            trigger: trigger.required(),
            condition: joi
              .string()
              .pattern(/plc_.{17}$/)
              .messages({
                "string.pattern.base": "Invalid policy condition code",
              }),
          })
          .when(".trigger", {
            is: "currency",
            then: joi.object({
              operator: joi.string().valid("is", "any", "not:is", "not:any").required(),
              operands: joi.array().items(currencies).unique().required(),
            }),
          })
          .when(".trigger", {
            is: "vendor",
            then: joi.object({
              operator: joi.string().valid("is", "any", "not:is", "not:any").required(),
              operands: joi.array().items(vendor).unique().required(),
            }),
          })
          .when(".trigger", {
            is: "category",
            then: joi.object({
              operator: joi.string().valid("is", "any", "not:is", "not:any").required(),
              operands: joi.array().items(category).unique().required(),
            }),
          })
          .when(".trigger", {
            is: "budget",
            then: joi.object({
              operator: joi.string().valid("is", "any", "not:is", "not:any").required(),
              operands: joi.array().items(budget).unique().required(),
            }),
          })
          .when(".trigger", {
            is: "account",
            then: joi.object({
              operator: joi.string().valid("is", "any", "not:is", "not:any").required(),
              operands: joi.array().items(account).unique().required(),
            }),
          })
          .when(".trigger", {
            is: "team",
            then: joi.object({
              operator: joi.string().valid("is", "any", "not:is", "not:any").required(),
              operands: joi.array().items(teams).unique().required(),
            }),
          })
          .when(".trigger", {
            is: "amount",
            then: joi.object({
              operator: operator.required(),
              operands: joi.number().integer().min(100).required(),
            }),
          })
          .when(".trigger", {
            is: "subsidiary",
            then: joi.object({
              operator: joi.string().valid("is", "not:is", "any", "not:any").required(),
              operands: joi.array().items(account).unique().required(),
            }),
          })
          .when(".trigger", {
            is: "type",
            then: joi.object({
              operator: joi.string().valid("is", "not:is").required(),
              operands: joi.array().items(allowedTypes).unique().required(),
            }),
          })
      )
      .optional(),
    conditionsToRemove: joi
      .array()
      .items(
        joi
          .string()
          .pattern(/plc_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid policy condition code",
          })
      )
      .unique()
      .optional(),
    exceptions: joi
      .array()
      .items(
        joi
          .string()
          .pattern(/usr_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid user code",
          })
      )
      .unique()
      .optional(),
    documents: joi
      .array()
      .items(
        joi
          .string()
          .pattern(/ast_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid document code",
          })
      )
      .unique()
      .optional(),
  }),

  getPolicies: joi.object({
    budget: joi
      .string()
      .pattern(/bdg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid budget code",
      })
      .optional(),
    category: joi
      .string()
      .pattern(/ctg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid budget code",
      })
      .optional(),
    type: joi
      .alternatives(
        joi
          .string()
          .pattern(/pty_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid policy type code",
          }),
        joi.string().valid("Spending limits", "Receipt Policy", "Calendar limits")
      )
      .optional(),
    search: joi.string().optional(),
    status: joi.string().optional(),
    from: joi.date().optional(),
    to: joi.date().optional(),
  }),

  getPolicy: joi.object({
    code: joi
      .string()
      .pattern(new RegExp("pol_.{17}$"))
      .messages({
        "string.pattern.base": "Invalid policy type code",
      })
      .optional(),
  }),
  addDocument: joi.object({
    code: joi
      .string()
      .pattern(new RegExp("pol_.{17}$"))
      .messages({
        "string.pattern.base": "Invalid policy type code",
      })
      .optional(),
    asset: joi
      .string()
      .pattern(new RegExp("ast_.{17}$"))
      .messages({
        "string.pattern.base": "Invalid policy evidence code",
      })
      .optional(),
  }),

  pauseBudgetPolicy: joi.object({
    code: joi
      .string()
      .pattern(new RegExp("pol_.{17}$"))
      .messages({
        "string.pattern.base": "Invalid policy type code",
      })
      .optional(),
    budgetCode: joi
      .string()
      .pattern(new RegExp("bdg_.{17}$"))
      .messages({
        "string.pattern.base": "Invalid budget code",
      })
      .optional(),
    status: joi.string().valid("pause", "active").optional(),
  }),

  deleteBudgetPolicy: joi.object({
    code: joi
      .string()
      .pattern(new RegExp("pol_.{17}$"))
      .messages({
        "string.pattern.base": "Invalid policy type code",
      })
      .optional(),
    budgetCode: joi
      .string()
      .pattern(new RegExp("bdg_.{17}$"))
      .messages({
        "string.pattern.base": "Invalid budget code",
      })
      .optional(),
  }),

  updateCalendarPolicy: joi.object({
    period: joi
      .array()
      .items(joi.string().valid(...allowedDays))
      .unique()
      .required(),
  }),

  updateSpendingPolicy: joi
    .object({
      frequency: joi.string().valid("monthly", "daily", "weekly", "yearly", "weekends").required().messages({
        "any.required": '"frequency" required for spending limit policy',
      }),
      currency: joi
        .string()
        .valid(...allowedCurrency)
        .optional(),
      amountRange: joi
        .object({
          maxAmount: joi.number().positive().optional().min(1000).messages({
            "number.min": "Minimum amount is 10",
          }),
          minAmount: joi.number().positive().optional().min(1000).messages({
            "number.min": "Minimum amount is 10",
          }),
        })
        .required()
        .or("maxAmount", "minAmount")
        .messages({
          "any.required": '"amountRange" required for spending limit policy',
        }),
    })
    .and("frequency", "amountRange"),

  updateIndividualPolicy: joi.object({
    currency: joi
      .string()
      .valid(...allowedCurrency)
      .optional(),
    amount: joi.number().positive().required().min(1000).messages({
      "number.min": "Minimum amount is 10",
    }),
  }),

  linkBudgetPolicy: joi.object({
    code: joi
      .string()
      .pattern(new RegExp("pol_.{17}$"))
      .messages({
        "string.pattern.base": "Invalid policy type code",
      })
      .required(),
    budgetCode: joi
      .string()
      .pattern(new RegExp("bdg_.{17}$"))
      .messages({
        "string.pattern.base": "Invalid budget code",
      })
      .required(),
  }),
};
