const Joi = require("joi");

const asset = Joi.string()
  .pattern(/ast_.{17}$/)
  .messages({
    "string.pattern.base": "Invalid asset code sent",
  });

const schedule = Joi.object({
  schedule: Joi.when("recurring", {
    is: true,
    then: Joi.string().valid("hourly", "weekly", "monthly", "quarterly", "yearly"),
    otherwise: Joi.object({
      minutes: Joi.number().integer().max(59).optional(),
      hours: Joi.number().integer().max(23).optional(),
      dayOfMonth: Joi.number().integer().max(31).optional(),
      month: Joi.string().optional(),
      dayOfWeek: Joi.string().optional(),
    }),
  }),
  startDate: Joi.object({
    date: Joi.string().required(),
    timestamp: Joi.string().required(),
  }),
  expiryDate: Joi.object({
    date: Joi.string().required(),
    timestamp: Joi.string().required(),
  }),
  recurring: Joi.boolean().required().default(true),
});

module.exports = {
  create: Joi.object({
    beneficiary: Joi.string(),
    amount: Joi.number().integer().positive(),
    currency: Joi.string().required(),
    vendor: Joi.string()
      .pattern(/vdr_.{17}$/)
      .optional()
      .messages({
        "string.pattern.base": "Invalid vendor code sent",
      }),
    budget: Joi.string()
      .pattern(/bdg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid budget code sent",
      }),
    balance: Joi.string()
      .pattern(/blc_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid balance code sent",
      }),
    source: Joi.alternatives().try(
      Joi.string()
        .pattern(/bdg_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid budget code sent",
        }),
      Joi.string()
        .pattern(/blc_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid balance code sent",
        })
    ),
    description: Joi.string().optional(),
    expense_date: Joi.date().optional(),
    deadLine: Joi.date().optional().allow(null),
    receipt: Joi.array().items(asset).unique().message("You have a receipt appearing more than once").optional(),
    category: Joi.string()
      .pattern(/ctg_.{17}$/)
      .optional()
      .messages({
        "string.pattern.base": "Invalid category code",
      }),
    directDebit: Joi.object({
      bankAccount: Joi.string()
        .pattern(/bnk_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid bank code sent",
        }),
    }),
  })
    .oxor("budget", "balance")
    .messages({
      "object.oxor": "You must provide either budget, balance",
    }),
  update: Joi.object({
    status: Joi.string().optional(),
    budget: Joi.string()
      .pattern(/bdg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid budget code sent",
      }),
    balance: Joi.string()
      .pattern(/blc_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid balance code sent",
      }),
    source: Joi.alternatives().try(
      Joi.string()
        .pattern(/bdg_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid budget code sent",
        }),
      Joi.string()
        .pattern(/blc_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid balance code sent",
        })
    ),
    description: Joi.string().optional(),
    expense_date: Joi.date().optional(),
    deadLine: Joi.date().optional().allow(null),
    receipt: Joi.array().items(asset).unique().message("You have uploaded the same file multiple times, please remove one").optional(),
    amount: Joi.number().integer().positive(),
    category: Joi.string()
      .pattern(/ctg_.{17}$/)
      .optional()
      .messages({
        "string.pattern.base": "Invalid category code",
      }),
    directDebit: Joi.object({
      bankAccount: Joi.string()
        .pattern(/bnk_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid bank code sent",
        }),
    }),
  })
    .oxor("budget", "balance", "directDebit")
    .messages({
      "object.oxor": "You must provide either budget or balance",
    }),

  validateCode: Joi.object({
    code: Joi.string()
      .pattern(/rbs_.{17}$/)
      .required()
      .messages({
        "string.pattern.base": "Invalid Reimbursment code",
      }),
  }),

  moreInfo: Joi.object({
    note: Joi.string().required(),
  }),

  multipleReimbursementApproval: Joi.object({
    decision: Joi.string().required().valid("approve", "decline"),
    note: Joi.string().when("decision", {
      is: "decline",
      then: Joi.required(),
      otherwise: Joi.optional(),
    }),
    actionLater: Joi.boolean(),
    reimbursements: Joi.array()
      .required()
      .items(
        Joi.string()
          .pattern(/rbs_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid Reimbursment code",
          })
      ),
  }),

  bulkTransactionInitiation: Joi.object({
    reimbursements: Joi.array()
      .required()
      .items(
        Joi.string()
          .pattern(/rbs_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid Reimbursment code",
          })
      ),
  }),

  validateReimbursement: Joi.object({
    decision: Joi.string().required().valid("approve", "decline"),
    note: Joi.string().when("decision", {
      is: "decline",
      then: Joi.required(),
      otherwise: Joi.optional(),
    }),
    actionLater: Joi.boolean(),
    schedule,
    code: Joi.string()
      .pattern(/rbs_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid Reimbursment code",
      }),
  }).oxor("actionLater", "schedule"),
};
