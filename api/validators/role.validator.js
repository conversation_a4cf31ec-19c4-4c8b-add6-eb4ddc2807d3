const joi = require("joi");

module.exports = {
  getRole: joi.object({
    code: joi.string().pattern(new RegExp("rol_.{17}$")).required(),
  }),
  updateRole: joi.object({
    code: joi.string().pattern(new RegExp("rol_.{17}$")).required(),
    name: joi.string().optional(),
    description: joi.string().optional(),
    permissions: joi
      .array()
      .items(
        joi.string().pattern(new RegExp("perm_.{17}$")).messages({
          "string.pattern.base": "Invalid permission code",
        })
      )
      .optional(),
  }),
  addPermissionsToRole: joi.object({
    code: joi.string().pattern(new RegExp("rol_.{17}$")).required(),
    permission: joi
      .string()
      .pattern(new RegExp("perm_.{17}$"))
      .messages({
        "string.pattern.base": "Invalid permission code",
      })
      .required(),
  }),
  createRole: joi.object({
    name: joi.string().required(),
    description: joi.string().optional(),
    permissions: joi
      .array()
      .items(
        joi.string().pattern(new RegExp("perm_.{17}$")).messages({
          "string.pattern.base": "Invalid permission code",
        })
      )
      .required(),
  }),
};
