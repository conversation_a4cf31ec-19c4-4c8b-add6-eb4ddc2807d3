const Joi = require("joi");

const ALLOWED_DISCOUNT_TYPES = ["amount", "percentage"];

const scheduledBillStatuses = ["completed", "pause", "active", "cancelled", "draft", "overdue"];

const asset = Joi.string()
  .pattern(/ast_.{17}$/)
  .messages({
    "string.pattern.base": "Invalid asset code",
  });

const scheduledBill = Joi.object({
  schedule: Joi.when("recurring", {
    is: true,
    then: Joi.string().valid("hourly", "weekly", "monthly", "quarterly", "yearly"),
    otherwise: Joi.object({
      minutes: Joi.number().integer().max(59).optional(),
      hours: Joi.number().integer().max(23).optional(),
      dayOfMonth: Joi.number().integer().max(31).optional(),
      month: Joi.string().optional(),
      dayOfWeek: Joi.string().optional(),
    }),
  }).required(),
  startDate: Joi.object({
    date: Joi.string().required(),
    timestamp: Joi.string().required(),
  }),
  expiryDate: Joi.object({
    date: Joi.string().required(),
    timestamp: Joi.string().required(),
  }),
  recurring: Joi.boolean().required().default(true),
}).optional();

module.exports = {
  listScheduledBill: Joi.object({
    status: Joi.alternatives().try(Joi.string().valid(...scheduledBillStatuses), Joi.array().items(Joi.string().valid(...scheduledBillStatuses))),
    minAmount: Joi.number().optional(),
    maxAmount: Joi.number().optional(),
    currency: Joi.array().items(Joi.string().valid("NGN", "USD")).optional(),
    from: Joi.date().optional(),
    to: Joi.date().optional(),
    search: Joi.string().optional(),
    perPage: Joi.number().integer().optional(),
    page: Joi.number().integer().optional(),
  }),

  getScheduledBill: Joi.object({
    code: Joi.string()
      .required()
      .pattern(/sbl_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid scheduled bill code sent",
      }),
  }),

  deleteScheduledBill: Joi.object({
    code: Joi.string()
      .required()
      .pattern(/sbl_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid scheduled bill code sent",
      }),
  }),

  updateScheduledBill: Joi.object({
    code: Joi.string()
      .pattern(/sbl_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid scheduled bill code",
      }),
    vendor: Joi.string()
      .pattern(/vdr_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid vendor code",
      }),
    vat: Joi.number().optional().allow(null),
    discount_type: Joi.string().valid(...ALLOWED_DISCOUNT_TYPES),
    discount: Joi.number().optional(),
    recurring: Joi.number().valid(0, 1).optional(),
    isDraft: Joi.boolean().default(false).optional(),
    terms: Joi.number().optional().allow(null),
    dueDate: Joi.date().optional(),
    category: Joi.string()
      .pattern(/ctg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid category code",
      }),
    reason: Joi.string().optional().allow(null),
    status: Joi.string().valid("active", "deleted", "pause").optional(),
    method: Joi.string().valid("directdebit", "budget", "balance"),
    budget: Joi.string()
      .pattern(/bdg_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid budget code sent",
      }),
    balance: Joi.string()
      .pattern(/blc_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid balance code sent",
      }),
    directDebit: Joi.object({
      bankAccount: Joi.string()
        .pattern(/bnk_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid bank code sent",
        }),
    }),
    products: Joi.array()
      .optional()
      .items(
        Joi.alternatives().try(
          Joi.object({
            code: Joi.string()
              .required()
              .pattern(/bip_.{17}$/)
              .messages({
                "string.pattern.base": "Invalid bill product code",
              }),
            name: Joi.string().optional(),
            quantity: Joi.number().min(1).optional(),
            unitPrice: Joi.number().optional(),
            currency: Joi.string().optional(),
            discount_type: Joi.string().valid(...ALLOWED_DISCOUNT_TYPES),
            discount: Joi.number().optional(),
          }),
          Joi.object({
            name: Joi.string().required(),
            quantity: Joi.number().min(1).required().messages({
              "number.min": "Product quantity must be greater than 0",
            }),
            unitPrice: Joi.number().required().min(10000).messages({
              "number.min": "Product Price must be greater than N100",
            }), // Minimum of 100
            currency: Joi.string().optional(),
            discount_type: Joi.string().valid(...ALLOWED_DISCOUNT_TYPES),
            discount: Joi.number().optional(),
          })
        )
      ),
    installments: Joi.object({
      type: Joi.string().required().valid("percentage", "amount"),
      payments: Joi.array()
        .items(
          Joi.alternatives().try(
            Joi.object({
              amount: Joi.number().min(1000).optional(),
              percentage: Joi.number().min(1).max(100),
              due_date: Joi.date().required(),
            }),
            Joi.object({
              code: Joi.string()
                .pattern(/bin_.{17}$/)
                .required(),
              amount: Joi.number().min(1000).optional(),
              percentage: Joi.number().min(1).max(100),
              due_date: Joi.date().required(),
            })
          )
        )
        .required(),
    }).optional(),
    scheduledBill,
    sendForApproval: Joi.boolean().default(false).optional(),
    currency: Joi.string().length(3).optional(),
    uploads: Joi.array().items(asset).unique().message("You have an upload appearing more than once"),
  }),
};
