const Joi = require("joi");

const ALLOWED_DISCOUNT_TYPES = ["amount", "percentage"];

const scheduledInvoiceStatuses = ["completed", "pause", "active", "cancelled", "draft", "overdue"];

const scheduleInvoice = Joi.object({
  schedule: Joi.when("recurring", {
    is: true,
    then: Joi.string().valid("hourly", "weekly", "monthly", "quarterly", "yearly"),
    otherwise: Joi.object({
      minutes: Joi.number().integer().max(59).optional(),
      hours: Joi.number().integer().max(23).optional(),
      dayOfMonth: Joi.number().integer().max(31).optional(),
      month: Joi.string().optional(),
      dayOfWeek: Joi.string().optional(),
    }),
  }).required(),
  startDate: Joi.object({
    date: Joi.string().required(),
    timestamp: Joi.string().required(),
  }),
  expiryDate: Joi.object({
    date: Joi.string().required(),
    timestamp: Joi.string().required(),
  }),
  recurring: Joi.boolean().required().default(true),
}).optional();

module.exports = {
  listScheduledInvoice: Joi.object({
    status: Joi.alternatives().try(
      Joi.string().valid(...scheduledInvoiceStatuses),
      Joi.array().items(Joi.string().valid(...scheduledInvoiceStatuses))
    ),
    minAmount: Joi.number().optional(),
    maxAmount: Joi.number().optional(),
    currency: Joi.array().items(Joi.string().valid("NGN", "USD")).optional(),
    from: Joi.date().optional(),
    to: Joi.date().optional(),
    search: Joi.string().optional(),
    perPage: Joi.number().integer().optional(),
    page: Joi.number().integer().optional(),
  }),

  getScheduledInvoice: Joi.object({
    code: Joi.string()
      .required()
      .pattern(/sci_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid scheduled invoice code sent",
      }),
  }),

  deleteScheduledInvoice: Joi.object({
    code: Joi.string()
      .required()
      .pattern(/sci_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid scheduled invoice code sent",
      }),
  }),

  updateScheduledInvoice: Joi.object({
    code: Joi.string()
      .pattern(/sci_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid Scheduled Invoice code",
      }),
    email: Joi.string().optional(),
    title: Joi.string().optional().max(100),
    terms: Joi.number().optional().allow(null),
    due_date: Joi.date().optional(),
    scheduleInvoice,
    customer: Joi.string()
      .pattern(/cus_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid customer code",
      }),
    products: Joi.array()
      .optional()
      .items(
        Joi.alternatives().try(
          Joi.object({
            code: Joi.string().required(),
            quantity: Joi.number().min(1).optional(),
            unitPrice: Joi.number().optional(),
            currency: Joi.string().optional(),
            discount_type: Joi.string().valid(...ALLOWED_DISCOUNT_TYPES),
            discount: Joi.number().optional(),
          }),
          Joi.object({
            name: Joi.string().optional(),
            quantity: Joi.number().min(1).optional(),
            unitPrice: Joi.number().optional(),
            currency: Joi.string().optional(),
            discount_type: Joi.string().valid(...ALLOWED_DISCOUNT_TYPES),
            discount: Joi.number().optional(),
          })
        )
      ),
    description: Joi.string().max(100).optional(),
    vat: Joi.number().optional(),
    discount_type: Joi.string().valid(...ALLOWED_DISCOUNT_TYPES),
    discount: Joi.number().optional(),
    status: Joi.string().valid("active", "deleted", "pause").optional(),
    settlementAccount: Joi.string()
      .pattern(/blc_.{17}$/)
      .optional()
      .messages({
        "string.pattern.base": "Invalid account code",
      }),
  }),
};
