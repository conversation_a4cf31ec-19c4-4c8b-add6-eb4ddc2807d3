const Joi = require("joi");

const scheduledTransaction = Joi.string().pattern(new RegExp("stc_.{17}$")).messages({
  "string.pattern.base": "Invalid scheduled code",
});

module.exports = {
  update: Joi.object({
    status: Joi.string().valid("completed", "pause", "active", "cancelled"),
    code: scheduledTransaction.required(),
    description: Joi.string(),
    amount: Joi.number().positive(),
    scheduleTransaction: Joi.object({
      schedule: Joi.when("recurring", {
        is: true,
        then: Joi.string().valid("hourly", "weekly", "monthly", "quarterly", "yearly"),
        otherwise: Joi.object({
          minutes: Joi.number().integer().max(59),
          hours: Joi.number().integer().max(23),
          dayOfMonth: Joi.number().integer().max(31),
          month: Joi.string(),
          dayOfWeek: Joi.string(),
        }),
      }),
      startDate: Joi.object({
        date: Joi.string().required(),
        timestamp: Joi.string().required(),
      }),
      expiryDate: Joi.object({
        date: Joi.string().required(),
        timestamp: Joi.string().required(),
      }),
      recurring: Joi.boolean().required(),
    }),
    reason: Joi.string(),
  }),
  listScheduledTransactions: Joi.object({
    status: Joi.alternatives().try(Joi.string(), Joi.array()).optional(),
    budget: Joi.array()
      .items(
        Joi.string().pattern(new RegExp("bdg_.{17}$")).messages({
          "string.pattern.base": "Invalid budget code sent",
        })
      )
      .optional(),
    min_amount: Joi.number().optional(),
    max_amount: Joi.number().optional(),
    currency: Joi.array().items(Joi.string().valid("NGN", "USD")).optional(),
    payer: Joi.array()
      .optional()
      .items(
        Joi.string().pattern(new RegExp("usr_.{17}$")).messages({
          "string.pattern.base": "Invalid user code sent",
        })
      ),
    from: Joi.date().optional(),
    to: Joi.date().optional(),
    search: Joi.string().optional(),
    category: Joi.string().optional().pattern(new RegExp("ctg_.{17}$")).messages({
      "string.pattern.base": "Invalid category code sent",
    }),
    perPage: Joi.number().integer().optional(),
    page: Joi.number().integer().optional(),
  }),
  cancelScheduledTransactions: Joi.object({
    scheduledTransactions: Joi.array()
      .items(
        Joi.string().pattern(new RegExp("stc_.{17}$")).messages({
          "string.pattern.base": "Invalid scheduled payment code sent",
        })
      )
      .required(),
  }),
};
