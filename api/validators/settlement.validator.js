const Joi = require("joi");

module.exports = {
  create: Joi.object({
    amount: Joi.number().integer().required(),
    account: Joi.string()
      .required()
      .pattern(/sac_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid settlement account",
      }),
    balance: Joi.string()
      .required()
      .pattern(/blc_.{17}$/)
      .messages({
        "string.pattern.base": "Invalid balance code",
      }),
  }),
};
