const Joi = require('joi');
module.exports = {
    basicKyc: Joi.object({
        first_name: Joi.string().max(30).required(),
        last_name: Joi.string().required(),
        id_type: Joi.string().valid('BVN', 'NIN', 'NIN_SLIP', 'DRIVERS_LICENSE', 'PHONE_NUMBER', 'VOTER_ID', 'BANK_ACCOUNT').required(),
        id_number: Joi.string()
        .when('id_type', {
            is: 'DRIVERS_LICENSE',
            then: Joi.string().pattern(/^[a-zA-Z]{3}([ -]{1})?[A-Z0-9]{6,12}$/i).required(),
        })
        .when('id_type', {
                is: 'VOTER_ID', 
                then: Joi.string().pattern(/^[a-zA-Z0-9 ]{9,20}$/i).required()
        })
        .when('id_type', {
            is: 'PHONE_NUMBER', 
            then: Joi.string().pattern(/^[0-9]{11}$/).required()
        }).required(),
        partner_params: Joi.object({
            table_code: Joi.string().required(),
            table_id: Joi.string().required(), 
            table_type: Joi.string().valid('business', 'user', 'individual').required(), 
        }).required(),
        gender: Joi.string().max(30).optional(),
    }).when(
        '.id_type', { 
            is: 'DRIVERS_LICENSE', 
            then: Joi.object({
                phone_number: Joi.string().length(11).pattern(/^[0-9]{11}$/).required(),
                dob: Joi.string().max(15).pattern(/^[0-9]+$/).required()
        }).required()
    }),

    businessKyc: Joi.object({
        id_type: Joi.string().valid('TIN', 'CAC').required(),
        id_number: Joi.string()
        .when('id_type', {
            is: 'CAC',
            then: Joi.string(),
        }).required(),
        company: Joi.string()
        .when('id_type', {
            is: 'CAC',
            then: Joi.string().required(),
            otherwise: Joi.string().optional(),
        }),
        partner_params: Joi.object({
            table_code: Joi.string().required(),
            table_id: Joi.string().required(), 
            table_type: Joi.string().valid('business', 'user', 'individual').required(), 
        }).required()
    }),

    documentVerification: Joi.object({
        id_type: Joi.string().valid('PASSPORT', 'DRIVERS_LICENSE', 'NATIONAL_ID', 'VOTER_ID').required(),
        is_image_base64: Joi.boolean().optional(),
        image: Joi.string().uri().required(),
        image_selfie: Joi.string().uri().optional(),
        partner_params: Joi.object({
            table_code: Joi.string().required(),
            table_id: Joi.string().required(), 
            table_type: Joi.string().valid('business', 'user', 'individual').required(), 
        }).required()
    })
}
