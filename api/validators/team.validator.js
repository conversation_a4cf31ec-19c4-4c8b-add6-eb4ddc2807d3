const Joi = require('joi');

module.exports = {
    create: Joi.object({
        name: Joi.string().required(),
        managers: Joi.array().items(Joi.string().pattern(new RegExp('usr_.{17}$')).messages({
            'string.pattern.base': 'Invalid manager code sent',
        })).messages({
            'any.required': 'Please select a manager for this team'
        }).required(),
        members: Joi.array().items(Joi.string().pattern(new RegExp('usr_.{17}$')).messages({
            'string.pattern.base': 'Invalid member code sent',
        })).optional(),
        budgets: Joi.array().items(Joi.string().pattern(new RegExp('bdg_.{17}$')).messages({
            'string.pattern.base': 'Invalid Budget code sent',
        })).optional(),
        description: Joi.string(),
    }),

    addMember: Joi.object({
        team: Joi.string().pattern(new RegExp('tms_.{17}$')).required().messages({
            'string.pattern.base': 'Invalid team sent',
            'any.required': 'Please select a team'
        }),
        members: Joi.array().items(Joi.string().pattern(new RegExp('usr_.{17}$')).messages({
            'string.pattern.base': 'Invalid user code sent',
        })).messages({
            'any.required': 'Please select a manager for this team'
        }).required(),
        role: Joi.string().pattern(new RegExp('rol_.{17}$')).required(),
    }),

    update: Joi.object({
        name: Joi.string().optional(),
        managers: Joi.array().min(1).items(Joi.string().pattern(new RegExp('usr_.{17}$')).messages({
            'string.pattern.base': 'Invalid manager code sent',
        })).messages({
            'any.required': 'Please select a manager for this team',
            "array.min": "A team must have at least one manager"
        }).optional(),
        members: Joi.array().items(Joi.string().pattern(new RegExp('usr_.{17}$')).messages({
            'string.pattern.base': 'Invalid member code sent',
        })).optional(),
        budgets: Joi.array().items(Joi.string().pattern(new RegExp('bdg_.{17}$')).messages({
            'string.pattern.base': 'Invalid Budget code sent',
        })).optional(),
        description: Joi.string().optional(),
        status: Joi.string().optional().valid('active', 'inactive')
    }),

    deleteTeamMember: Joi.object({
        team: Joi.string().pattern(new RegExp('tms_.{17}$')).required().messages({
            'string.pattern.base': 'Invalid team sent',
        }),

        member: Joi.string().pattern(new RegExp('tmb_.{17}$')).required().messages({
            'string.pattern.base': 'Invalid team member sent',
        })
    }),

    updateTeamMember: Joi.object({
        team: Joi.string().pattern(new RegExp('tms_.{17}$')).required().messages({
            'string.pattern.base': 'Invalid team sent',
        }),

        member: Joi.string().pattern(new RegExp('tmb_.{17}$')).required().messages({
            'string.pattern.base': 'Invalid team member sent',
        }),

        status: Joi.string().required().valid('active', 'inactive', 'deleted')
    }),
};
