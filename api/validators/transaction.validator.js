/* eslint-disable prefer-regex-literals */
const Joi = require("joi");

const transaction = Joi.string().pattern(new RegExp("trx_.{17}$")).messages({
  "string.pattern.base": "Invalid transaction code",
});
const budget = Joi.string().pattern(new RegExp("bdg_.{17}$")).messages({
  "string.pattern.base": "Invalid budget code",
});

const balance = Joi.string().pattern(new RegExp("blc_.{17}$")).messages({
  "string.pattern.base": "Invalid balance code sent",
});

module.exports = {
  editTransactionDescription: Joi.object({
    description: Joi.string().required(),
    code: transaction.required(),
  }),

  addBudgetToTransaction: Joi.object({
    budget: budget.required(),
    code: transaction.required(),
  }),

  addBalanceToTransaction: Joi.object({
    balance: balance.required(),
    code: transaction.required(),
  }),

  retryBulkTransaction: Joi.object({
    transactions: Joi.array()
      .single(
        Joi.string().pattern(new RegExp("trx_.{17}$")).messages({
          "string.pattern.base": "Invalid transaction code",
        })
      )
      .required(),
  }),

  massAssignCategories: Joi.object({
    items: Joi.array()
      .items(
        Joi.object({
          transaction: Joi.string()
            .pattern(new RegExp("trx_.{17}$"))
            .messages({
              "string.pattern.base": "Invalid transaction code sent",
            })
            .required(),
          category: Joi.string()
            .pattern(new RegExp("ctg_.{17}$"))
            .messages({
              "string.pattern.base": "Invalid category code sent",
            })
            .required(),
        })
      )
      .required(),
  }),

  massAssignPayers: Joi.object({
    items: Joi.array()
      .items(
        Joi.object({
          transaction: Joi.string()
            .pattern(new RegExp("trx_.{17}$"))
            .messages({
              "string.pattern.base": "Invalid transaction code sent",
            })
            .required(),
          payer: Joi.string()
            .pattern(new RegExp("usr_.{17}$"))
            .messages({
              "string.pattern.base": "Invalid payer code sent",
            })
            .required(),
        })
      )
      .required(),
  }),

  listTransactions: Joi.object({
    card: Joi.array()
      .items(
        Joi.string().pattern(new RegExp("crd_.{17}$")).messages({
          "string.pattern.base": "Invalid card code sent",
        })
      )
      .optional(),
    status: Joi.alternatives().try(Joi.string(), Joi.array()).optional(),
    budget: Joi.array()
      .items(
        Joi.string().pattern(new RegExp("bdg_.{17}$")).messages({
          "string.pattern.base": "Invalid budget code sent",
        })
      )
      .optional(),
    min_amount: Joi.number().optional(),
    max_amount: Joi.number().optional(),
    currency: Joi.array().items(Joi.string().valid("NGN", "USD")).optional(),
    payer: Joi.array()
      .optional()
      .items(
        Joi.string().pattern(new RegExp("usr_.{17}$")).messages({
          "string.pattern.base": "Invalid user code sent",
        })
      ),
    from: Joi.date().optional(),
    to: Joi.date().optional(),
    search: Joi.string().optional(),
    team: Joi.string().optional().pattern(new RegExp("tms_.{17}$")).messages({
      "string.pattern.base": "Invalid team code sent",
    }),
    category: Joi.string().optional().pattern(new RegExp("ctg_.{17}$")).messages({
      "string.pattern.base": "Invalid category code sent",
    }),
    vendor: Joi.alternatives(
      Joi.string().optional().pattern(new RegExp("vdr_.{17}$")).messages({
        "string.pattern.base": "Invalid vendor code sent",
      }),
      Joi.array().items(
        Joi.string().optional().pattern(new RegExp("vdr_.{17}$")).messages({
          "string.pattern.base": "Invalid vendor code sent",
        })
      )
    ),
    source: Joi.string().pattern(new RegExp("blc_.{17}$")).messages({
      "string.pattern.base": "Invalid balance code sent",
    }),
    perPage: Joi.number().integer().optional(),
    page: Joi.number().integer().optional(),
    paidOnFrom: Joi.date().optional(),
    paidOnTo: Joi.date().optional(),
  }),

  massAssignRecipients: Joi.object({
    items: Joi.array()
      .items(
        Joi.object({
          transaction: Joi.string()
            .pattern(new RegExp("trx_.{17}$"))
            .messages({
              "string.pattern.base": "Invalid transaction code sent",
            })
            .required(),
          recipient: Joi.alternatives()
            .try(Joi.string().pattern(new RegExp("vdr_.{17}$")), Joi.string().pattern(new RegExp("usr_.{17}$")))
            .required(),
        })
      )
      .required(),
  }),

  overridePolicy: Joi.object({
    code: transaction.required(),
  }),

  requestDetails: Joi.object({
    code: transaction.required(),
  }),

  payNow: Joi.object({
    code: Joi.alternatives(
      transaction,
      Joi.string()
        .pattern(/btc_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid batch code sent",
        })
    ).required(),
    source: Joi.alternatives().try(
      Joi.string()
        .pattern(/bdg_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid budget code sent",
        }),
      Joi.string()
        .pattern(/blc_.{17}$/)
        .messages({
          "string.pattern.base": "Invalid balance code sent",
        })
    ),
  }),

  bulkPay: Joi.object({
    items: Joi.array()
      .items(
        Joi.object({
          code: Joi.alternatives().try(
            transaction,
            Joi.string()
              .pattern(/rbs_.{17}$/)
              .messages({
                "string.pattern.base": "Invalid reimbursement code sent",
              }),
            Joi.string()
              .pattern(/frq_.{17}$/)
              .messages({
                "string.pattern.base": "Invalid fund request code sent",
              })
          ),
          source: Joi.alternatives().try(
            Joi.string()
              .pattern(/bdg_.{17}$/)
              .allow("", null)
              .messages({
                "string.pattern.base": "Invalid budget code sent",
              }),
            Joi.string()
              .pattern(/blc_.{17}$/)
              .allow("", null)
              .messages({
                "string.pattern.base": "Invalid balance code sent",
              })
          ),
        })
      )
      .unique()
      .required(),
  }),
};
