const Joi = require("joi").extend(require("@joi/date"));
const Utils = require("../utils");

const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9]*)(?=.*[!@#$%^&*_-])(?=.{8,})/;
module.exports = {
  create: Joi.object({
    firstName: Joi.string().required().messages({
      "any.required": "Firstname is required",
    }),
    lastName: Joi.string().required().messages({
      "any.required": "Lastname is required",
    }),
    email: Joi.string().email().required().messages({
      "any.required": "Please enter your work email",
    }),
    phoneNumber: Joi.object({
      countryCode: Joi.number().greater(0).required(),
      localFormat: Joi.string().min(6).required(),
    }),
    password: Joi.string().pattern(passwordRegex).max(70).messages({
      "string.pattern.match": '"password" must be stronger',
      "string.pattern.base": "Password should be more than eight characters long and should contain alphabets, special characters and numbers",
    }),
    confirm_password: Joi.any().equal(Joi.ref("password")).messages({ "any.only": "{{#label}} does not match" }),
    referral: Joi.string().optional(),
    inviteCode: Joi.string().optional(),
  }).with("password", "confirm_password"),

  update: Joi.object({
    firstName: Joi.string().optional(),
    middleName: Joi.string().optional().allow(null),
    lastName: Joi.string().optional(),
    dob: Joi.date().format("YYYY-MM-DD").max(Utils.getYearsAgo(18)).messages({
      "date.max": "You must be at least 18 years old.",
      "date.format": "Invalid date format. Please use the YYYY-MM-DD date format.",
    }),
    phoneNumber: Joi.object({
      countryCode: Joi.number().greater(0).required(),
      localFormat: Joi.string().min(6).required(),
    }).optional(),
    address: Joi.object({
      street: Joi.string().optional(),
      countryIso: Joi.string().length(3).optional().default("NG"),
      postalCode: Joi.string()
        .pattern(/^[0-9]+$/)
        .optional(),
      country: Joi.string().required(),
      state: Joi.string().required(),
      city: Joi.string().max(50).required(),
      address: Joi.string().max(100),
    }).optional(),
    old_password: Joi.string().max(70).optional(),
    password: Joi.string().pattern(passwordRegex).max(70).messages({
      "string.pattern.match": '"password" must be stronger',
      "string.pattern.base": "Password should be more than eight characters long and should contain alphabets, special characters and numbers",
    }),
    company: Joi.string().optional(),
    lookedUpCompany: Joi.string()
      .pattern(/clk_.{17}$/)
      .optional(),
    pin: Joi.string()
      .pattern(/[a-zA-Z0-9]{4,32}$/)
      .optional(),
    newPin: Joi.string()
      .pattern(/[a-zA-Z0-9]{4,32}$/)
      .optional()
      .allow(""),
  })
    .and("password", "old_password")
    .messages({
      "object.and": '"password" and "old_password" are both required',
    }),

  login: Joi.object({
    email: Joi.string().email(),
    phoneNumber: Joi.object({
      countryCode: Joi.number().greater(0).required(),
      localFormat: Joi.string().min(6).required(),
    }),
    password: Joi.string().required().max(70),
    rememberMe: Joi.boolean().optional(),
    refreshToken: Joi.string().optional(),
  }).xor("email", "phoneNumber"),
  reset: Joi.object({
    email: Joi.string().email(),
    phoneNumber: Joi.object({
      countryCode: Joi.number().greater(0).required(),
      localFormat: Joi.string().min(6).required(),
    }),
  }).xor("email", "phoneNumber"),

  setBeneficiaryPassword: Joi.object({
    hash: Joi.string().length(24).required(),
    password: Joi.string().pattern(passwordRegex).max(70).messages({
      "string.pattern.match": '"password" must be stronger',
      "string.pattern.base": "Password should be more than eight characters long and should contain alphabets, special characters and numbers",
    }),
    confirm_password: Joi.any().equal(Joi.ref("password")).messages({ "any.only": "{{#label}} does not match" }),
  }),
};
