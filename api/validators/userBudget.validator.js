const Joi = require('joi');
const { json } = require('../services/response');
const allowCurrencies = [];
module.exports = {
    getAllUserBudget: Joi.object({
        employee: Joi.number().integer().required(),
        budget: Joi.string().pattern(new RegExp("bdg_.{17}$")).optional(),
        amount: Joi.number().integer().min(1).optional(),
        spent: Joi.number().integer().optional(),
        page: Joi.number().integer().optional(),
        perPage: Joi.string().optional(),
        from: Joi.string().optional(),
        to: Joi.string().optional(),
        status: Joi.number().integer().optional()
    }),
};
