const Joi = require("joi");

module.exports = {
  getCompanyDirector: Joi.object({
    company: Joi.string()
      .pattern(/clk_.{17}$/)
      .required(),
  }),
  companyLookup: Joi.object({
    search: Joi.string().min(5).messages({
      "string.min": "Please specify a minimum of 5 characters",
    }),
  }),
  searchBanks: Joi.object({
    search: Joi.string()
      .optional()
      .pattern(/^[a-zA-Z0-9\s]*$/),
  }),
  getLookedUpCompany: Joi.object({
    company: Joi.string()
      .pattern(/clk_.{17}$/)
      .required(),
  }),
};
