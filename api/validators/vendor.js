const Joi = require("joi");
const { VENDORS, CURRENCY } = require("../mocks/constants.mock");

const extraBankAccountSchema = Joi.object({
  routingNumber: Joi.string().when(Joi.ref("currency"), {
    is: CURRENCY.USD,
    then: Joi.string().required().messages({
      "any.required": "Routing number is required",
    }),
  }),
  accountType: Joi.string().when(Joi.ref("currency"), {
    is: CURRENCY.USD,
    then: Joi.string().default("business").valid("business", "personal").messages({
      "any.only": "Account type must be either business or personal",
    }),
  }),
  routingType: Joi.string().when(Joi.ref("currency"), {
    is: CURRENCY.USD,
    then: Joi.string().valid("swift").required().messages({
      "any.required": "Routing type is required",
    }),
  }),
  wireType: Joi.string().when(Joi.ref("currency"), {
    is: CURRENCY.USD,
    then: Joi.string().required().valid("wire").messages({
      "any.required": "Wire type is required",
    }),
  }),
  beneficiaryAddress: Joi.when(Joi.ref("currency"), {
    is: CURRENCY.USD,
    then: Joi.object({
      line1: Joi.string().required(),
      line2: Joi.string().optional(),
      city: Joi.string().required(),
      state: Joi.string().required(),
      postalCode: Joi.string().required(),
      country: Joi.string().optional().default("US"),
    }),
  }),
  bankAddress: Joi.when(Joi.ref("currency"), {
    is: CURRENCY.USD,
    then: Joi.object({
      line1: Joi.string().required(),
      line2: Joi.string().optional(),
      city: Joi.string().required(),
      state: Joi.string().required(),
      postalCode: Joi.string().required(),
      country: Joi.string().optional().default("US"),
    }),
  }),
});

module.exports = {
  create: Joi.object({
    email: Joi.string().email().optional().allow(null),
    website: Joi.string().email().optional().allow(null),
    phoneNumber: Joi.object({
      countryCode: Joi.number().greater(0).required(),
      localFormat: Joi.string().min(6).required(),
    })
      .optional()
      .allow(null),
    name: Joi.string().required(),
    description: Joi.string().optional(),
    industry: Joi.string()
      .pattern(/idt_.{17}$/)
      .allow(null),
    categories: Joi.array()
      .items(
        Joi.string()
          .pattern(/ctg_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid category code",
          })
      )
      .optional()
      .allow(null),
    address: Joi.object({
      street: Joi.string(),
      city: Joi.string(),
      stateOrProvince: Joi.string(),
      country: Joi.string(),
      lga: Joi.string().optional(),
      postalCode: Joi.string().optional(),
      countryIso: Joi.string().optional().default("NG"),
    }).optional(),
    isAnUpdate: Joi.boolean().optional().default(false),
    taxIdentificationNumber: Joi.string().optional(),
    registrationNumber: Joi.string()
      .pattern(/^[RC|BN]{9}$/)
      .optional(),
    taxWithHolding: Joi.number().optional(),
    bankAccount: Joi.object({
      bankName: Joi.string()
        .regex(/^[a-zA-Z ()]+$/)
        .required()
        .messages({
          "string.pattern.base": "",
        }),
      bankCode: Joi.string()
        .pattern(/^[0-9A-Za-z]{3,7}$/)
        .required()
        .messages({
          "any.required": "Bank code is required",
          "string.pattern.base": "Invalid Bank code sent",
        }),
      accountName: Joi.string().required(),
      number: Joi.string()
        .pattern(/^[0-9]{10}$/)
        .required()
        .messages({
          "any.required": "Account number is required",
          "string.pattern.base": "Number must be 10 characters long and is required to contain only numbers",
        }),
      currency: Joi.string().optional().default(CURRENCY.NGN),
    }).concat(extraBankAccountSchema),
  }),
  bulkCreate: Joi.array().items(
    Joi.object({
      email: Joi.string().email().optional().allow(null),
      website: Joi.string().email().optional().allow(null),
      name: Joi.string().required(),
      phoneNumber: Joi.string().optional(),
      taxIdentificationNumber: Joi.string().optional(),
      registrationNumber: Joi.string()
        .pattern(/^[RC|BN]{9}$/)
        .optional(),
      taxWithHolding: Joi.number().optional(),
      bankName: Joi.string()
        .regex(/^[a-zA-Z ()]+$/)
        .optional()
        .messages({
          "string.pattern.base": "",
        }),
      accountNumber: Joi.string()
        .pattern(/^[0-9]{10}$/)
        .optional()
        .messages({
          "any.required": "Account number is required",
          "string.pattern.base": "Number must be 10 characters long and is required to contain only numbers",
        }),
    })
      .or("email", "phoneNumber")
      .messages({
        "object.missing": "You must provide either email or phone number",
      })
  ),

  massUpdate: Joi.object({
    vendors: Joi.array()
      .items(
        Joi.string()
          .pattern(/vdr_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid vendor code",
          })
          .required()
      )
      .required()
      .messages({
        "array.includesRequiredUnknowns": "Please include a vendor code",
      })
      .min(1),
    action: Joi.string()
      .valid(...Object.values(VENDORS.ACTIONS))
      .required(),
  }),
  syncZoho: Joi.object({
    codes: Joi.array()
      .items(Joi.string().pattern(/vdr_.{17}$/))
      .unique()
      .required(),
  }),
  update: Joi.object({
    description: Joi.string().optional(),
    email: Joi.string().email().optional(),
    website: Joi.string().email().optional(),
    phoneNumber: Joi.object({
      countryCode: Joi.number().greater(0).required(),
      localFormat: Joi.string().min(6).required(),
    })
      .optional()
      .allow(null),
    name: Joi.string().optional(),
    address: Joi.object({
      street: Joi.string().required(),
      city: Joi.string().required(),
      stateOrProvince: Joi.string().required(),
      country: Joi.string().required(),
      lga: Joi.string().optional(),
      postalCode: Joi.string().optional(),
    }).optional(),
    categories: Joi.array()
      .items(
        Joi.string()
          .pattern(/ctg_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid category code",
          })
      )
      .optional()
      .allow(null),
    registrationNumber: Joi.string().optional(),
    taxIdentificationNumber: Joi.string().optional(),
    taxWithHolding: Joi.number().optional(),
    bankAccount: Joi.object({
      bankName: Joi.string()
        .regex(/^[a-zA-Z ()]+$/)
        .required()
        .messages({
          "string.pattern.base": "",
        }),
      bankCode: Joi.string()
        .pattern(/^[0-9A-Za-z]{3,7}$/)
        .required()
        .messages({
          "any.required": "Bank code is required",
          "string.pattern.base": "Invalid Bank code sent",
        }),
      accountName: Joi.string().required(),
      number: Joi.string()
        .pattern(/^[0-9]{10}$/)
        .required()
        .messages({
          "any.required": "Account number is required",
          "string.pattern.base": "Number must be 10 characters long and is required to contain only numbers",
        }),
      currency: Joi.string().optional().default("NGN"),
    }),
  }),
};
