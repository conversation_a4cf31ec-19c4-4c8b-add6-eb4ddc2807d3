const Joi = require("joi");

module.exports = {
  dashboard: {
    create: Joi.object({
      type: Joi.string().allow("virtual").required(),
      name: Joi.string().required(),
      budget: Joi.string(),
      balance: Joi.string()
        .pattern(/blc_.{17}$/)
        .when("budget", {
          not: Joi.exist(),
          then: Joi.required().messages({
            "any.required": "balance or budget must be provided",
          }),
        }),
      beneficiary: Joi.string().optional().allow(null),
      currency: Joi.string().max(3).optional().required(),
      amount: Joi.number()
        .positive()
        .when("currency", {
          is: "NGN",
          then: Joi.number().optional().min(10000).messages({
            "number.min": "Minimum amount to fund card with is N100",
          }),
        }),
      settings: {
        onlineTransaction: Joi.boolean().optional(),
        atmWithdrawals: Joi.boolean().optional(),
        posTransaction: Joi.boolean().optional(),
        contactlessTransaction: Joi.boolean().optional(),
        expenseCategory: Joi.string().optional(),
        allowedMerchants: Joi.array()
          .items(Joi.string().pattern(/mcc_.{17}$/))
          .optional(),
        spendingLimitPolicy: Joi.string()
          .pattern(/pol_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid policy type code",
          }),
      },
    })
      .xor("budget", "balance")
      .messages({
        "object.xor": "You must provide either budget or balance",
      }),
    update: Joi.object({
      type: Joi.string().allow("virtual", "physical"),
      name: Joi.string(),
      budget: Joi.string(),
      beneficiary: Joi.string().optional().allow(null),
      currency: Joi.string().max(3).optional(),
      impactBudget: Joi.string()
        .pattern(/bdg_.{17}$/)
        .optional()
        .messages({
          "string.pattern.base": "Invalid budget code sent",
        }),
      amount: Joi.number()
        .positive()
        .when("currency", {
          is: "NGN",
          then: Joi.number().optional().min(10000).messages({
            "number.min": "Minimum amount to fund card with is N100",
          }),
        }),
      settings: {
        onlineTransaction: Joi.boolean().optional(),
        atmWithdrawals: Joi.boolean().optional(),
        posTransaction: Joi.boolean().optional(),
        contactlessTransaction: Joi.boolean().optional(),
        expenseCategory: Joi.string().optional(),
        allowedMerchants: Joi.array()
          .items(Joi.string().pattern(/mcc_.{17}$/))
          .optional(),
        spendingLimitPolicy: Joi.string()
          .pattern(/pol_.{17}$/)
          .messages({
            "string.pattern.base": "Invalid policy type code",
          }),
      },
    }),
    topUp: Joi.object({
      code: Joi.string()
        .pattern(/crd_.{17}$/)
        .required()
        .messages({
          "string.pattern.base": "Invalid card code sent",
          "any.required": "Card is required",
        }),
      amount: Joi.number().positive().required(),
      budget: Joi.string()
        .pattern(/bdg_.{17}$/)
        .required()
        .messages({
          "string.pattern.base": "Invalid budget code sent",
          "any.required": "Budget is required",
        }),
    }),
    cardReassignment: Joi.object({
      beneficiary: Joi.string()
        .required()
        .pattern(/bnf_.{17}$/),
    }),
  },
  flutterwave: {
    create: Joi.object({
      currency: Joi.string().length(3).required(),
      amount: Joi.number().integer().positive().optional(),
      billing_name: Joi.string().required(),
      billing_address: Joi.string().required(),
      billing_city: Joi.string().required(),
      billing_state: Joi.string().required(),
      billing_postal_code: Joi.string().required(),
      billing_country: Joi.string().length(2),
      callback_url: Joi.string().uri(),
    }),
    block: {
      id: Joi.string().uuid(),
      status: Joi.string().allow("block", "blocked"),
    },
    unblock: {
      id: Joi.string().uuid(),
      status: Joi.string().allow("unblock", "unblocked"),
    },
    fund: {
      id: Joi.string().uuid(),
      amount: Joi.number().positive().greater(1),
      currency: Joi.string().length(3).optional(),
    },
  },
  createCardValidations: {},
  simulateCharge: Joi.object({
    card: Joi.string()
      .regex(/^crd_[A-Za-z0-9]{17}$/)
      .required(),
    amount: Joi.number().positive().required(),
  }),
};
