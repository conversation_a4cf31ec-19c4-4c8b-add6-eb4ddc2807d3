const { log, Log } = require("../utils/logger.utils");
const { Asset, Document, PolicyDocument } = require("../models");
const { BackgroundService } = require("../services");
const { BACKGROUND } = require("../mocks/constants.mock");
const FileService = require("../services/FileService");
const { CompanyRepo } = require("../repositories/index.repo");

const assetCleanUpWorker = BackgroundService.initWorker(BACKGROUND.QUEUES.ASSET_CLEANUP, async (job) => {
  try {
    if (!job?.data?.code) return null;

    const asset = await Asset.findOne({
      where: {
        code: job.data.code,
      },
    });

    if (!asset.key) return null;

    const assetIsLinked = !!asset.entityId;

    if (assetIsLinked) return null;

    const assetBelongsToDocument = await Document.findOne({
      where: {
        asset: asset.id,
      },
    });

    // handle for company documents
    if (assetBelongsToDocument) return null;

    const assetBelongsToPolicyDocument = await PolicyDocument.findOne({
      where: {
        asset: asset.id,
      },
    });

    // handle for policy documents
    if (assetBelongsToPolicyDocument) return null;

    const assetIsCompanyLogo = await CompanyRepo.getOneCompany({
      queryParams: {
        logo: asset.id,
      },
    });

    // handle for company logo
    if (assetIsCompanyLogo) return null;

    // cleanup on aws
    await FileService.deleteFile(asset.key);

    return Asset.destroy({
      where: {
        code: job.data.code,
      },
    });
  } catch (err) {
    return log(Log.bg.red, { message: `Failed to execute job: ${job.id}, due to err: ${err.message}`, err: String(err) });
  }
});

module.exports = assetCleanUpWorker;
