const { BackgroundService } = require("../services");
const { BACKGROUND } = require("../mocks/constants.mock");
const { log, Log } = require("../utils/logger.utils");
const {
  BudgetAccountRepo,
  BankAccountRepo,
  BudgetRepo,
  BudgetLedgerRepo,
  TransferRepo,
  BalanceRepo,
  BalanceLedgerRepo,
  CompanyRepo,
} = require("../repositories/index.repo");
const { ValidationError, NotFoundError } = require("../utils/error.utils");
const SettingsService = require("../services/settings");
const Providers = require("../services/providers");
const NotificationService = require("../services/notification");
const Utils = require("../utils");
const RedisService = require("../services/redis");

const balanceReconcilerWorker = BackgroundService.initWorker(BACKGROUND.QUEUES.BALANCE_RECONCILIATION, async (job) => {
  if (!job.data) return null;

  const { relationships, included } = job.data || {};
  const bookTransferObject = Array.from(included).find((includedObject) => includedObject.type === "BOOK_TRANSFER") || {};

  const { sourceSubAccount, account } = relationships || {};
  const debitedAccount = sourceSubAccount || account;

  const { id: debitedAccountId, type: debitedAccountType } = debitedAccount.data;

  try {
    const hasAlreadyBeenSeen = await RedisService.get(`balance_check:${debitedAccountId}`);
    if (hasAlreadyBeenSeen) return hasAlreadyBeenSeen;

    const { attributes } = bookTransferObject;
    const { reference } = attributes || {};

    if (!reference) throw new ValidationError("Reference not found in Book Transfer Object");

    const foundTransfer = await TransferRepo.getTransfer({ filter: { reference } });

    if (!foundTransfer) throw new NotFoundError("Transfer");

    let bankAccount;
    if (debitedAccountType === "SubAccount") {
      bankAccount = await BudgetAccountRepo.getBudgetAccount({
        filter: { externalIdentifier: debitedAccountId },
      });
    } else {
      bankAccount = await BankAccountRepo.getOneBankAccount({
        queryParams: { externalBankAccountId: debitedAccountId },
        selectOptions: ["owner"],
      });
    }

    const company = bankAccount.company || bankAccount.owner;
    const foundCompany = await CompanyRepo.getOneCompany({ queryParams: { id: company }, selectOptions: ["name"] });
    const { payment } = SettingsService.get("providers");
    const providerToUse = payment[company] || payment.defaultProvider;
    let currentBalance = 0;
    let providerBalance = 0;
    let balanceName = "";
    let accountCurrency = "";

    if (debitedAccountType === "SubAccount") {
      const previousLedger = await BudgetLedgerRepo.getBudgetLedger({
        criteria: { transfer: foundTransfer.id, ...(foundTransfer.budget && { budget: foundTransfer.budget }) },
      });
      if (!previousLedger) throw new NotFoundError("Budget Ledger");

      const foundBudget = await BudgetRepo.getOneBudget({
        queryParams: { id: previousLedger.budget },
        selectOptions: ["isNewBudget", "available", "owner", "currency", "name"],
      });

      currentBalance = await BudgetLedgerRepo.getBudgetAvailableBalance(foundBudget);
      const { data, error } = await Providers[providerToUse].virtualAccount.getSubAccountBalance(debitedAccountId);
      if (error) throw Providers[providerToUse].throwProviderError(data);

      const { availableBalance } = data;
      providerBalance = availableBalance;
      balanceName = foundBudget.name;
      accountCurrency = foundBudget.currency;
    } else {
      const previousBalanceLedger = await BalanceLedgerRepo.getBalanceLedger({
        filter: { transfer: foundTransfer.id, company },
      });
      if (!previousBalanceLedger) throw new NotFoundError("Balance ledger");

      const foundBalance = await BalanceRepo.getBalance({
        filter: { id: previousBalanceLedger.balance },
      });

      currentBalance = await BalanceRepo.getAvailableBalance({
        company,
        currency: previousBalanceLedger.currency,
        id: previousBalanceLedger.balance,
      });

      const { data, error } = await Providers[providerToUse].virtualAccount.getDepositAccountBalance(debitedAccountId);
      if (error) throw Providers[providerToUse].throwProviderError(data);

      const { availableBalance } = data;
      providerBalance = availableBalance;
      balanceName = foundBalance.name;
      accountCurrency = foundBalance.currency;
    }

    if (!Utils.money(currentBalance).eq(providerBalance)) {
      const formattedCurrentBalance = `${Utils.getSymbolFromCurrency(accountCurrency)}${Utils.formatAmount(currentBalance).toLocaleString()}`;
      const formattedProviderBalance = `${Utils.getSymbolFromCurrency(accountCurrency)}${Utils.formatAmount(providerBalance).toLocaleString()}`;
      const message = `Balance mismatch detected for company: ${foundCompany.name} on account: ${balanceName} (${debitedAccountId}), currentBalance: ${formattedCurrentBalance}, providerBalance: ${formattedProviderBalance}`;

      log(Log.bg.red, {
        message,
      });

      const isProd = Utils.isProd();

      // notify engineering team
      const notificationPayload = {
        currentBalance: formattedCurrentBalance,
        providerBalance: formattedProviderBalance,
        companyName: foundCompany.name,
        externalIdentifier: debitedAccountId,
        balanceName,
      };

      let emails = [SettingsService.get("notification_email")];

      if (!isProd) {
        emails = ["<EMAIL>", "<EMAIL>"];
      }

      emails.forEach((email) => {
        NotificationService.notifyUser({ email }, "balance-mismatch-report", notificationPayload, {
          subject: `[Alert] ${foundCompany.name} has a balance mismatch 😑`,
        });
      });

      RedisService.setex(`balance_check:${debitedAccountId}`, 1, 60 * 60 * 24);
    }

    return null;
  } catch (err) {
    const message = `Failed to execute job: ${job.id}, due to err: ${err.message}`;
    log(Log.bg.red, { message, err: String(err) });
    throw err;
  } finally {
    RedisService.delete(`balance_reconcile:${debitedAccountId}`);
  }
});

module.exports = balanceReconcilerWorker;
