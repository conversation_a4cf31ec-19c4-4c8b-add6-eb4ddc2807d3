const { BackgroundService } = require("../services");
const { BACKGROUND } = require("../mocks/constants.mock");
const { log, Log } = require("../utils/logger.utils");
const { CompanyRepo, VirtualCardRepo } = require("../repositories/index.repo");
const SettingsService = require("../services/settings");
const Providers = require("../services/providers");
const NotificationService = require("../services/notification");
const Utils = require("../utils");
const RedisService = require("../services/redis");

const cardBalanceReconcilerWorker = BackgroundService.initWorker(BACKGROUND.QUEUES.CARD_BALANCE_RECONCILIATION, async (job) => {
  if (!job.data) return null;

  const { card: externalIdentifier } = job.data || {};

  try {
    const hasAlreadyBeenSeen = await RedisService.get(`balance_check:${externalIdentifier}`);
    if (hasAlreadyBeenSeen) return hasAlreadyBeenSeen;

    const foundCard = await VirtualCardRepo.find({ externalIdentifier });

    // eslint-disable-next-line consistent-return
    if (!foundCard) return;

    const {
      company,
      CardIssuer: { name: provider },
      currency,
      name: cardName,
    } = foundCard;

    const { balance: providerBalance } = await Providers[String(provider).toLowerCase()].getCardBalance({ externalIdentifier, company });

    // Get Card balance from System
    const [currentBalance, foundCompany] = await Promise.all([
      VirtualCardRepo.getAvailableBalance(foundCard),
      CompanyRepo.getCompany({ queryParams: { id: company } }),
    ]);

    if (!Utils.money(currentBalance).eq(providerBalance)) {
      const formattedCurrentBalance = `${Utils.getSymbolFromCurrency(currency)}${Utils.formatAmount(currentBalance).toLocaleString()}`;
      const formattedProviderBalance = `${Utils.getSymbolFromCurrency(currency)}${Utils.formatAmount(providerBalance).toLocaleString()}`;
      const message = `Balance mismatch detected for company: ${foundCompany.name} on Card: ${cardName}, currentBalance: ${formattedCurrentBalance}, providerBalance: ${formattedProviderBalance}`;

      log(Log.bg.red, {
        message,
      });

      const isProd = Utils.isProd();

      // notify engineering team
      const notificationPayload = {
        currentBalance: formattedCurrentBalance,
        providerBalance: formattedProviderBalance,
        companyName: foundCompany.name,
        externalIdentifier,
        cardName,
      };

      let emails = [SettingsService.get("notification_email")];

      if (!isProd) {
        emails = ["<EMAIL>", "<EMAIL>"];
      }

      emails.forEach((email) => {
        NotificationService.notifyUser({ email }, "card-balance-mismatch-report", notificationPayload, {
          subject: `[Alert] ${foundCompany.name} has a balance mismatch for their card 😑`,
        });
      });

      RedisService.setex(`balance_check:${externalIdentifier}`, 1, 60 * 60 * 24);
    }

    return null;
  } catch (err) {
    const message = `Failed to execute job: ${job.id}, due to err: ${err.message}`;
    log(Log.bg.red, { message, err: String(err) });
    throw err;
  } finally {
    RedisService.delete(`balance_reconcile:${externalIdentifier}`);
  }
});

module.exports = cardBalanceReconcilerWorker;
