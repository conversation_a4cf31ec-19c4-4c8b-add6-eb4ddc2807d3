const { Op } = require("sequelize");
const { BackgroundService } = require("../services");
const { BACKGROUND } = require("../mocks/constants.mock");
const { log, Log } = require("../utils/logger.utils");
const { Transaction } = require("../models");
const { CategorizationRuleRepo, CompanyRepo } = require("../repositories/index.repo");
const NotificationService = require("../services/notification");

const categorizationRulesPrompt = BackgroundService.initWorker(BACKGROUND.QUEUES.CATEGORIZATION_RULES_PROMPT, async (job) => {
  try {
    if (!job.data) return null;

    const categorizationRule = await CategorizationRuleRepo.getCategorizationRule({
      queryParams: { code: job.data.code },
    });

    if (!categorizationRule) return null;

    const transactions = await Transaction.findAll({
      where: {
        category: {
          [Op.eq]: null,
        },
        company: categorizationRule.company,
      },
    });

    if (!transactions.length) return null;

    let transactionsToCategorize = 0;
    await Promise.allSettled(
      transactions.map(async (transaction) => {
        // try to auto-categorize transaction
        if (!transaction.description) return null;
        const canBeAutocategorized = await CategorizationRuleRepo.autoCategorizer(transaction.description, transaction.company);

        if (canBeAutocategorized) {
          transactionsToCategorize += 1;
        }

        return transactionsToCategorize;
      })
    );

    if (!transactionsToCategorize) return null;

    const systemNotificationPayload = {
      company: categorizationRule.company,
      type: `prompt`,
      badge: `prompt`,
      title: `Transactions to categorize`,
      message: `There ${transactionsToCategorize === 1 ? "is" : "are"} ${transactionsToCategorize} transaction${
        transactionsToCategorize !== 1 ? "s" : ""
      } that need${transactionsToCategorize === 1 ? "s" : ""} to be categorized based on categorization rule: ${categorizationRule.name}`,
      table: {
        code: categorizationRule.code,
        entity: "CategorizationRule",
      },
      event: "categorizationRulesPrompt",
    };

    const companyWithAdmins = await CompanyRepo.getCompanyWithAdmins({ id: categorizationRule.company }, true);

    if (!companyWithAdmins) return null;

    const admins = companyWithAdmins?.Users || [];

    return Promise.allSettled(
      admins.map(async (admin) => {
        return NotificationService.saveNotification({ ...systemNotificationPayload, user_id: admin.id });
      })
    );
  } catch (err) {
    log(Log.bg.red, { message: `Failed to execute job: ${job.id}, due to err: ${err.message}`, err: String(err) });
    throw err;
  }
});

module.exports = categorizationRulesPrompt;
