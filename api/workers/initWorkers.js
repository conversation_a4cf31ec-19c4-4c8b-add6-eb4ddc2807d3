const { log, Log } = require("../utils/logger.utils");
const assetCleanUpWorker = require("./assetCleanUp");
const balanceReconcilerWorker = require("./balanceReconciler");
const categorizationRulesPrompt = require("./categorizationRulesPrompt");
const policyPrompt = require("./policyPrompt");
const kybDocumentUploadWorker = require("./kybDocumentUploader");
const cardBalanceWorker = require("./cardBalanceReconciler");
const statementWorker = require("./statementWorker");

const workers = [
  assetCleanUpWorker,
  balanceReconcilerWorker,
  kybDocumentUploadWorker,
  cardBalanceWorker,
  statementWorker,
  categorizationRulesPrompt,
  policyPrompt,
];

const initWorkers = async () => {
  return Promise.all(
    workers.map(
      (worker) =>
        new Promise((resolve, reject) => {
          log(Log.bg.blue, { message: `worker ${worker.name} started successfully` });

          worker.on("completed", (job) => {
            log(Log.bg.green, { message: `Executed job: ${job.id} successfully` });
          });

          worker.on("failed", (job, err) => {
            log(Log.bg.red, {
              message: `Failed to execute job: ${job.id} on ${worker.name}, due to err: ${err.message}`,
              err: String(err),
            });
            reject(err); // Rejects the promise, allowing Promise.all to catch the error
          });

          resolve(worker); // Resolves the promise when the worker is successfully initialized
        })
    )
  );
};

module.exports = initWorkers;
