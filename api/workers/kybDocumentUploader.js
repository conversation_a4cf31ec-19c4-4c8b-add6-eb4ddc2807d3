const { BackgroundService } = require("../services");
const { BACKGROUND } = require("../mocks/constants.mock");
const { log, Log } = require("../utils/logger.utils");
const { ValidationError } = require("../utils/error.utils");
const Providers = require("../services/providers");

const kybDocumentUploaderWorker = BackgroundService.initWorker(BACKGROUND.QUEUES.KYB_DOCUMENT_UPLOADER, async (job) => {
  if (!job.data) return null;

  try {
    const { provider, documentId, customerId, company } = job.data || {};

    const { status, data } = await Providers[provider].customer.submitSingleDocument({ documentId, customerId, company });

    if (status !== 200) {
      // Check Failure reason
      const { errors = [] } = data || {};

      const { status: errorStatus, detail: errorMessage = null } = errors[0] || {};

      if (Number(errorStatus) === 412 && String(errorMessage).toLowerCase().includes("document has been verified")) return true; // Document has already been approved
      throw new ValidationError(errorMessage);
    }

    return null;
  } catch (err) {
    const message = `Failed to execute job: ${job.id}, due to err: ${err.message}`;
    log(Log.bg.red, { message, err: String(err) });
    throw err;
  }
});

module.exports = kybDocumentUploaderWorker;
