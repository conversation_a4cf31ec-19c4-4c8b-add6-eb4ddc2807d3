const { PolicyRepo, CompanyRepo } = require("../repositories/index.repo");
const { STATUSES } = require("../models/status");
const { BackgroundService } = require("../services");
const PolicyService = require("../services/policy.service");
const { Transaction, Asset, FundRequest, Reimbursement } = require("../models");
const { log, Log } = require("../utils/logger.utils");
const { BACKGROUND } = require("../mocks/constants.mock");
const NotificationService = require("../services/notification");

const policyPrompt = BackgroundService.initWorker(BACKGROUND.QUEUES.POLICIES_PROMPT, async (job) => {
  try {
    if (!job.data) return null;

    const policy = await PolicyRepo.getPolicy({
      queryParams: { code: job.data.code },
    });

    if (!policy) return null;

    const assetCriteria = {
      model: Asset,
      as: "TransactionAssets",
      required: false,
      where: {
        "$TransactionAssets.entityType$": "Transaction",
      },
    };
    const fundRequestAssets = {
      model: FundRequest,
      required: false,
      include: [
        {
          model: Asset,
          via: "receipt",
          as: "FundRequestAssets",
          where: {
            "$FundRequest.FundRequestAssets.entityType$": "fundRequest",
          },
          required: false,
        },
      ],
    };
    const reimbursementCriteria = {
      model: Reimbursement,
      required: false,
      include: [
        {
          model: Asset,
          required: false,
          as: "ReimbursementAssets",
          where: {
            "$Reimbursement.ReimbursementAssets.entityType$": "Reimbursement",
          },
        },
      ],
    };

    const transactions = await Transaction.findAll({
      where: {
        status: [STATUSES.PENDING, STATUSES.APPROVED, STATUSES.PROCESSING],
        company: policy.company,
      },
      include: [assetCriteria, fundRequestAssets, reimbursementCriteria],
    });

    if (!transactions.length) return null;

    let transactionsViolatingPolicy = 0;

    await Promise.allSettled(
      transactions.map(async (transaction) => {
        // try to enforce policy
        const hasViolatedPolicy = await PolicyService.policyEnforcer({
          company: transaction.company,
          overrideStrictness: true,
          code: policy.code,
          entity: {
            receipt: transaction.TransactionAssets || transaction.FundRequestAssets || transaction.ReimbursementAssets,
            description: transaction.description,
            category: transaction.category,
            budget: transaction.budget,
            amount: transaction.amount,
            user: transaction.payer,
            account: transaction.balance,
            currency: transaction.currency,
            vendor: transaction.recipient,
            type: "payment",
          },
        });

        if (hasViolatedPolicy?.length) {
          transactionsViolatingPolicy += 1;
        }

        return transactionsViolatingPolicy;
      })
    );

    if (!transactionsViolatingPolicy) return null;

    const systemNotificationPayload = {
      company: policy.company,
      type: `prompt`,
      badge: `prompt`,
      title: `Transactions violating policy`,
      message: `There ${transactionsViolatingPolicy === 1 ? "is" : "are"} ${transactionsViolatingPolicy} transaction${
        transactionsViolatingPolicy !== 1 ? "s" : ""
      } that ${transactionsViolatingPolicy === 1 ? "has" : "have"} violated policy: ${policy.name}`,
      table: {
        code: policy.code,
        entity: "Policy",
      },
      event: "policyViolationPrompt",
    };

    const companyWithAdmins = await CompanyRepo.getCompanyWithAdmins({ id: policy.company }, true);

    if (!companyWithAdmins) return null;

    const admins = companyWithAdmins?.Users || [];

    return Promise.allSettled(
      admins.map(async (admin) => {
        return NotificationService.saveNotification({ ...systemNotificationPayload, user_id: admin.id });
      })
    );
  } catch (err) {
    log(Log.bg.red, { message: `Failed to execute job: ${job.id}, due to err: ${err.message}`, err: String(err) });
    throw err;
  }
});

module.exports = policyPrompt;
